<?php

function smarty_modifier_to_unicode($title){
  // $dict = array(
  //   '【' => '<span style="letter-spacing:-0.2rem;">&#58935;</span>',
  //   '】' => '<span style="letter-spacing:-0.13rem;padding-left: 0.01rem;">&#58934;</span>',
  //   '《' => '<span style="letter-spacing:-0.12rem;">&#58937;</span>',
  //   '》' => '<span style="letter-spacing:-0.13rem;padding-left: 0.02rem;">&#58936;</span>',
  //   '（' => '(',
  //   '）' => ')'
  // );
  $str = $title;
  $str = preg_replace('/^【/','<span class="yike-sell-fonts_square-brackets-left"></span>', $str);
  $str = preg_replace('/(?<=[\x{4e00}-\x{9fa5}])【/u','<span class="yike-sell-fonts_square-brackets-left" style="padding-left: 0.1rem;"></span>', $str);
  $str = preg_replace('/】/U','<span class="yike-sell-fonts_square-brackets-right" style="padding-right: 0.1rem;"></span>', $str);
  $str = preg_replace('/^《/U','<span class="yike-sell-fonts_book-mark-left"></span>', $str);
  $str = preg_replace('/(?<=[\x{4e00}-\x{9fa5}])《/u','<span class="yike-sell-fonts_book-mark-left" style="padding-left: 0.1rem;"></span>', $str);
  $str = preg_replace('/》/U','<span class="yike-sell-fonts_book-mark-right" style="padding-right: 0.1rem;"></span>', $str);
  $str = preg_replace('/（/U','(', $str);
  $str = preg_replace('/）/U',')', $str);

  // return strtr($title, $dict);
  return $str;
}
