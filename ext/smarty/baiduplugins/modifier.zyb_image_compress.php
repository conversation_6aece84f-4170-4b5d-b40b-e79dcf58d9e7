<?php

/**
 * Copyright (c) 2014-2017 Zuoyebang, All rights reserved.
 * @fileoverview 作业帮图片压缩工具
 * <AUTHOR> | <EMAIL>
 * @version 1.0 | 2016-08-24 | HouQuan    // 初始版本。
 * @version 1.1 | 2016-08-25 | HouQuan    // 添加 f 参数，避免未知的压缩效果
 * @version 1.2 | 2017-03-01 | HouQuan    // 添加对测试环境图片压缩的支持
 *
 * @method zyb_image_compress($imageUrl, $width, $height, $quality)    // 方法：生成图片 src.
 *   @param imageUrl {String}                                          // 参数：原始 url(必选)。
 *   @param width {Number}                                             // 参数：压缩后的图片宽度(可选)。
 *   @param height {Number}                                            // 参数：压缩后图片的高度(可选)。
 *   @param quality {Number}                                           // 参数：压缩的质量(可选，默认值：80，可选值：(0-100])。
 *   @return {String}                                                  // 返回：生成的图片 src.
 *
 * @description    // 附加说明。
 *   1) 因后台直接上传的图片未经压缩，所以前端可以通过配置压缩参数来压缩图片。
 *   2) 本组件的 JS 版本： common:widget/util/zybImgSrc.js.
 *      本组件的 Jsmart 版本： common:widget/lib/jsmart/jsmt_plugins.js.
 *      更改时请同时维护此 JS/Jsmart 版本。
 *
 * @example    // 典型的调用示例。
    <img src="{%$pid|zyb_image_src|zyb_image_compress:720:400:80%}" />
 */

function smarty_modifier_zyb_image_compress($imageUrl, $width = 0, $height = 0, $quality = 80){
  $params = array();
  $params[] = 'f_jpg';
  $width && $params[] = 'w_' . $width;
  $height && $params[] = 'h_' . $height;
  $params[] = 'q_' . $quality;
  return preg_match('/^https?:\/\/(?:test)?img.zuoyebang.cc/i', $imageUrl) ? $imageUrl . '@' . join(',', $params) : $imageUrl;
}