<?php


/**
 * Copyright (c) 2014-2016 Zuoyebang, All rights reseved.
 * @fileoverview  显示价格
 * <AUTHOR> | <EMAIL>
 * @version 1.0 | 2016-08-09 | chenkai    // 初始版本。
 *
 * @method zyb_to_price($num)    // 生成显示价格函数
 *   @param num {int||float}                    // 参数：原始价格(必填)。
 *
 * @example    // 典型的调用示例。
     <div>￥<em>{%$lesson.cost|zyb_to_price%}</em></div>
 */
function smarty_modifier_zyb_to_price($num)
{
  if(is_float($num))
  {
    return number_format($num,2);
  }else{
    return number_format($num);
  }
}