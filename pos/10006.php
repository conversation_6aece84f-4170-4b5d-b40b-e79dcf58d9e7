<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * Created by PhpStorm.
 * File : 10000.php
 * Author: <EMAIL>
 * Date: 2018/8/6
 * Time: 16:59
 * Desc: 推广卡片 接口 | 目前用不到
 */

class Oplib_Pos_10006 extends Oplib_Common_BasePos
{

    public function invoke($arrCommand)
    {

        return [];
        $posId = intval($arrCommand['posId']);
        $utype = intval($arrCommand['utype']);
        $grade = intval($arrCommand['grade']);
        $appType  = strval($arrCommand['appType']);
        $province = strval($arrCommand['province']);
        $city  = strval($arrCommand['city']);
        $arrOutput = array('list'=>array());
        $cacheValue = Oplib_Common_Cache::getCacheByPosGradeId($posId, $grade);

        //根据城市名称获取城市编号
        $arrPC = Oplib_Const_Position::getPositionIdByName($province,$city);

        $provinceCode = $arrPC['provinceCode'];
        $cityCode     = $arrPC['cityCode'];

        $sort = array();
        foreach($cacheValue as $cache){
            $arrUtype = explode(',',$cache['utype']);
            if(!in_array($utype, $arrUtype)){
                continue;
            }

            $arrAppType =  explode(',',$cache['appType']);
            if(!in_array($appType,$arrAppType)){
                continue;
            }



            //如果不是全部
            if( intval($cache['province']) !== 0 ){
                //判断省份是否对应
                if( intval($provinceCode) === intval($cache['province']) ){

                    //判断城市是否是全部
                    if( intval($cache['city']) !== 0 ){

                        //判断是否是该城市
                        if( intval($cityCode) !== intval($cache['city'])){
                            continue;
                        }
                    }
                }elseif ( intval($provinceCode) !== intval($cache['province']) ){
                    continue;
                }
            }

            $sort[] = $cache['rank'];
            $arrOutput['list'][] = array(
                'adId' => $cache['id'],
                'name' => $cache['name'],
                'clickType' => $cache['clickType'],
                'clickUrl'  => $cache['clickUrl'],
                'cardTag'    => strval($cache['ext']['cardTag']),
                'rank'      => $cache['rank'],
                'courseType'=> (in_array($cache['courseType'],[21,24])) ? Oplib_Const_Operation::TYPE_PRIVATE_LONG : $cache['courseType'],
                'lastfrom'  => Oplib_Const_Operation::$LAST_FROM_ARRAY[$posId] . intval($cache['id']),
            );
        }
        array_multisort($sort,SORT_ASC,$arrOutput['list']);
        return $arrOutput;
    }
}
