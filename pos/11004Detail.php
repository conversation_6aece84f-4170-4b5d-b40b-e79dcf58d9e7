<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * Created by PhpStorm.
 * File : 11004Detail.php
 * Author: <EMAIL>
 * Date: 2018/12/3
 * Time: 15:04
 * Desc: 学科页班课卡片（春） | 19.01月版本
 */
class Oplib_Pos_11004Detail extends Oplib_Common_BasePos {

    public function invoke($arrCommand){

        $posId = intval(Oplib_Const_Operation::POS_PLAN_CARD_XK);   //代理到11007
        $adId  = intval($arrCommand['adId']);
        $arrOutput = array('info' => array());
        if(0 >= $adId){
            throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::PARAM_ERROR,'',array(
                'adId' => $adId,
            ));
        }

        $info = Oplib_Common_Cache::getAdDetailByPosIdAdId($posId,$adId,false);

        if(empty($info) || $info['status'] != Oplib_Ds_OperationPosAd::STATUS_ONLINE){
            throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::OP_POS_AD_NOT_EXIST,'',array(
                'adId' => $adId,
            ));
        }
        $arrOutput['info'] = array(
            'name'       => $info['name'],
            'subject'    => intval($info['subject']),
            'courseType' => Oplib_Const_Operation::TYPE_PRIVATE_LONG,
            'skuDesc'    => $info['skuDesc'],
            'arrSpuId'   => ($info['extData']['skuId'][0] !== 0 ) ? $info['extData']['skuId'] : [],
            'condition'  => !empty($info['condition']) ? json_decode($info['condition'],true) : [],
            'lastfrom'   => Oplib_Const_Operation::$LAST_FROM_ARRAY[$posId] . intval($adId),

        );

        return $arrOutput;
    }

}
