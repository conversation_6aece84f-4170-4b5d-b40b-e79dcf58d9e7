<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * Created by PhpStorm.
 * File : 10019.php
 * Author: <EMAIL>
 * Date: 2018/11/24
 * Time: 17:16
 * Desc: 热词推荐
 */
class Oplib_Pos_10019 extends Oplib_Common_BasePos{


    public function invoke($arrCommand)
    {
        // TODO: Implement invoke() method.
        $posId = intval($arrCommand['posId']);
        $grade = intval($arrCommand['grade']);
        $utype = intval($arrCommand['utype']);

        $arrOutput = array('list'=>array(),);

        $cacheValue = Oplib_Common_Cache::getCacheByPosGradeId($posId, $grade);

        $sort = array();
        foreach($cacheValue as $cache){
            //根据用户类型筛选
            if($utype > 0){
                $arrUtype = explode(',',$cache['utype']);
                if(!in_array($utype,$arrUtype)){
                    continue;
                }
            }
            $sort[] = $cache['rank'];
            $arrOutput['list'][] = array(
                'adId'      =>$cache['id'],
                'posId'     => $posId,
                'name'      => $cache['name'],
                'clickUrl'  => $cache['clickUrl'],
                'rank'      =>$cache['rank'],
                'lastfrom'  => Oplib_Const_Operation::$LAST_FROM_ARRAY[$posId] . intval($cache['id']),
                'orifrom'   => Oplib_Const_Operation::$ORI_FROM_ARRAY[$posId] . intval($cache['id']),
            );
        }
        array_multisort($sort,SORT_ASC,$arrOutput['list']);
        return $arrOutput;
    }
}
