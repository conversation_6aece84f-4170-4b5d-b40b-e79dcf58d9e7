<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * Created by PhpStorm.
 * File : 11000Detail.php
 * Author: <EMAIL>
 * Date: 2019/2/21
 * Time: 11:12
 * Desc: 课程分类
 */
class Oplib_Pos_11000Detail extends Oplib_Common_BasePos {

    public function invoke($arrCommand){
        $posId = intval($arrCommand['posId']);
        $adId  = intval($arrCommand['adId']);
        $arrOutput = array('info' => array());

        if(0 >= $adId){
            throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::PARAM_ERROR,'',array(
                'adId' => $adId,
            ));
        }

        $info = Oplib_Common_Cache::getCacheByPosIdInfo($posId,$adId);

        if(empty($info) || $info['status'] != Oplib_Ds_OperationPosAd::STATUS_ONLINE){
            throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::OP_POS_AD_NOT_EXIST,'',array(
                'adId' => $adId,
            ));
        }
        $arrOutput['info'] = array(
            'courseType' => $info['courseType'],
            'arrSkuId'   => ($info['arrSkuId'][0] !== 0 ) ? $info['arrSkuId'] : [],
        );

        return $arrOutput;
    }


}
