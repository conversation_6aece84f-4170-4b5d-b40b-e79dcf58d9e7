<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * Created by PhpStorm.
 * File : 11005.php
 * Author: <EMAIL>
 * Date: 2018/12/3
 * Time: 15:05
 * Desc: 学科页班课卡片（秋） | 19.01月版本
 */
class Oplib_Pos_11010 extends Oplib_Common_BasePos
{

    public function invoke($arrCommand)
    {
        $posId   = intval($arrCommand['posId']);
        $utype   = intval($arrCommand['utype']);
        $grade   = intval($arrCommand['grade']);
        $appType = strval($arrCommand['appType']);
        $subject = intval($arrCommand['subject']);
        $arrOutput = array('title' => '','list'=>array(),);

        //从模块管理里面获取数据
        $moduleName = '班课（秋）';
        $arrModuleInfo = Oplib_Common_Cache::getCacheByPosGradeId(Oplib_Const_Operation::POS_MODULE, $grade);
        if($arrModuleInfo){
            foreach ($arrModuleInfo as $moduleInfo){
                if( (intval($moduleInfo['ext']['moduleId']) === intval($posId)) && ( intval($moduleInfo['subject']) === intval($subject) )){
                    $moduleName = $moduleInfo['name'];
                }else{
                    continue;
                }
            }
        }

        $arrOutput['title']  = $moduleName;

        //学科卡片信息统一走11007
        $cacheValue = Oplib_Common_Cache::getAdListByPosIdGrade(Oplib_Const_Operation::POS_PLAN_CARD_XK, $grade);
        $sort = array();
        foreach($cacheValue as $cache){

            if($cache['appType']){  //为了兼容旧数据
                //应用类型
                $arrAppType = explode(',',$cache['appType']);
                if(!in_array($appType,$arrAppType)){
                    continue;
                }
            }

            //用户类型
            $arrUtype = explode(',',$cache['utype']);
            if(!in_array($utype, $arrUtype)){
                continue;
            }

            //筛选非专题课
            if($cache['courseType'] !== Oplib_Const_Operation::TYPE_PRIVATE_LONG || $cache['learnSeason'] !== Oplib_Const_Operation::LEARN_SEASON_AUTUMN){
               continue;
            }

            if(intval($subject) !== intval($cache['subject'])){
                continue;
            }

            $sort[] = $cache['rank'];
            $arrOutput['list'][] = array(
                'adId'          => $cache['id'],
                'name'          => $cache['name'],
                'posId'         => $posId,
                'subject'       => intval($cache['subject']),
                'courseType'    => Oplib_Const_Operation::TYPE_PRIVATE_LONG,
                'learnSeason'   => Oplib_Const_Operation::LEARN_SEASON_SUMMER,
                'tagName'       => Oplib_Const_Operation::$SUBJECT_TAG[$cache['ext']['subjectTag']],
                'tagId'         => $cache['ext']['subjectTag'],
                'hotTagUrl'     => Oplib_Const_Operation::$OLD_SUBJECT_CARD_HOT_TAG[$cache['ext']['cardTag']],
                'rank'          => $cache['rank'],
                'star'          => intval($cache['ext']['star']),
                'desc'          => $cache['skuDesc'],
                'headImage'     => Hk_Util_Image::getImgUrlBySrc($cache['pid']),
                'bottomTag'     => json_decode($cache['ext']['bottomTag'],true),
                'lastfrom'      => Oplib_Const_Operation::$LAST_FROM_ARRAY[$posId] . intval($cache['id']),
                'orifrom'       => Oplib_Const_Operation::$ORI_FROM_ARRAY[$posId] . intval($cache['id']),
            );
        }
        array_multisort($sort,SORT_ASC,$arrOutput['list']);

        return $arrOutput;
    }
}
