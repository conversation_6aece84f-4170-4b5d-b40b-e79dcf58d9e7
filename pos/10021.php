<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * Created by PhpStorm.
 * File : 10021.php
 * Author: <EMAIL>
 * Date: 2019/1/9
 * Time: 10:37
 * Desc: 首页长期班(春) | 这块有坑，端上的PM（潘琳，刘刚） 设计逻辑是卡片跳到卡片
 */
class Oplib_Pos_10021 extends Oplib_Common_BasePos
{

    public function invoke($arrCommand)
    {
        $posId   = intval($arrCommand['posId']);
        $utype   = intval($arrCommand['utype']);
        $grade   = intval($arrCommand['grade']);
        $appType = strval($arrCommand['appType']);
        $subject = strval('-1');

        $arrOutput = array('title' => '','list'=>array(),);


        //从模块管理里面获取数据
        $moduleName = '班课卡片（春）';
        $arrModuleInfo = Oplib_Common_Cache::getCacheByPosGradeId(Oplib_Const_Operation::POS_MODULE, $grade);
        if($arrModuleInfo){
            foreach ($arrModuleInfo as $moduleInfo){
                if( intval($moduleInfo['ext']['moduleId']) === intval($posId) ){
                    $moduleName = $moduleInfo['name'];
                }else{
                    continue;
                }
            }
        }

        $arrOutput['title']  = $moduleName;
        $cacheValue = Oplib_Common_Cache::getCacheByPosGradeId(Oplib_Const_Operation::POS_PLAN_CARD_XK, $grade);
        $sort = array();
        foreach($cacheValue as $cache){

            if($cache['appType']){  //为了兼容旧数据
                //应用类型
                $arrAppType = explode(',',$cache['appType']);
                if(!in_array($appType,$arrAppType)){
                    continue;
                }
            }

            //用户类型
            $arrUtype = explode(',',$cache['utype']);
            if(!in_array($utype, $arrUtype)){
                continue;
            }

            //跳过非首页的课程

            if( $subject !== $cache['subject']){
                continue;
            }

            if($cache['courseType'] !== Oplib_Const_Operation::TYPE_PRIVATE_LONG){
                if($cache['courseType'] !== Oplib_Const_Operation::TYPE_PRIVATE_LONG_C){
                    continue;
                }
            }elseif ($cache['learnSeason'] !== Oplib_Const_Operation::LEARN_SEASON_SPRING){
                continue;
            }

            $sort[] = $cache['rank'];
            $arrOutput['list'][] = array(
                'adId'        => $cache['id'],
                'posId'       => $posId,
                'name'        => $cache['name'],
                'subject'     => intval($cache['subject']),
                'courseType'  => Oplib_Const_Operation::TYPE_PRIVATE_LONG,
                'learnSeason' => Oplib_Const_Operation::LEARN_SEASON_SPRING,
                'tagName'     => Oplib_Const_Operation::$SUBJECT_TAG[$cache['ext']['subjectTag']],
                'tagId'       => $cache['ext']['subjectTag'],
                'hotTagUrl'   => Oplib_Const_Operation::$OLD_SUBJECT_CARD_HOT_TAG[$cache['ext']['cardTag']],
                'rank'        => $cache['rank'],

                /* 和刘刚确认，打点字段由负责打点的pm 和 rd 统一来搞 */
                'lastfrom'  => Oplib_Const_Operation::$LAST_FROM_ARRAY[$posId] . intval($cache['id']),
                'orifrom'   => Oplib_Const_Operation::$ORI_FROM_ARRAY[$posId] . intval($cache['id']),
            );
        }
        array_multisort($sort,SORT_ASC,$arrOutput['list']);


        return $arrOutput;
    }
}
