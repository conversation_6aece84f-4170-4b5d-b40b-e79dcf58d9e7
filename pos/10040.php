<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * brief: 首页-学季聚合-春
 * @author: <EMAIL>
 */
class Oplib_Pos_10040 extends Oplib_Common_BasePos
{

    public function invoke($arrCommand)
    {
        $learnSeason = 1;
        return Oplib_Pos_10040::getXk2021Info($learnSeason, $arrCommand);
    }

    public static function getXk2021Info($learnSeason, $arrCommand) {
        $posId = intval($arrCommand['posId']);
        $grade = intval($arrCommand['grade']);
        $seasonDesc = Opmis_Const_Course::$LEARN_SEASON_CAT_MAP[$learnSeason] . '季';
        $gradeName = Hkzb_Const_FudaoGradeMap::$gradeTextMap[$grade];
        $arrOutput = array(
            'title' => "{$gradeName}{$seasonDesc}双师系统班",
            'list' => [],
			'fatherTitle' => '热卖系统班',
        );
        // 从模块管理里面获取名称
        $arrModuleInfo = Oplib_Common_Cache::getCacheByPosGradeId(Oplib_Const_Operation::POS_MODULE, $grade) ?? [];
        foreach ($arrModuleInfo as $moduleInfo){
			if (intval($moduleInfo['ext']['moduleId']) == Oplib_Const_Operation::POS_CARD_XK_2021) {
				$fatherTitle = $moduleInfo['name'];
				continue;
			}
            if (intval($moduleInfo['ext']['moduleId']) !== $posId) {
                continue;
            }
            $arrOutput['title'] = $moduleInfo['name'];
        }
        // 获取卡片信息
        $cacheValue = Oplib_Common_Cache::getAdListByPosIdGrade(Oplib_Const_Operation::POS_CARD_XK_2021, $grade);
        $sort = array();
        foreach($cacheValue as $cache){
            if ($learnSeason !== intval($cache['learnSeason'])) {
                continue;
            }

            $sort[] = $cache['rank'];
            $arrOutput['list'][] = array(
                'adId'        => $cache['id'],
                'name'        => $cache['name'],
                'posId'       => $posId,
                'year'        => $cache['year'],
                'courseType'  =>  (in_array($cache['courseType'],[21,24])) ? Oplib_Const_Operation::TYPE_PRIVATE_LONG : $cache['courseType'],
                'learnSeason' => intval($cache['learnSeason']),
                'bottomTag'   => json_decode($cache['ext']['bottomTag'], true),
                'spreadTag'   => json_decode($cache['ext']['spreadTag'], true),
            );
        }
        array_multisort($sort,SORT_ASC,$arrOutput['list']);
        return $arrOutput;
    }

}
