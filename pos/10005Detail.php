<?php
/**
 * @file 10005Detail.php
 * <AUTHOR>
 * @date 2018-06-04
 * @brief 年级首页-B资源位
 **/
class Oplib_Pos_10005Detail extends Oplib_Common_BasePos {
    public function invoke($arrCommand){
        $posId = intval($arrCommand['posId']);
        $adId  = intval($arrCommand['adId']);
        $arrOutput = array('info' => array());
        if(0 >= $adId){
            throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::PARAM_ERROR,'',array(
                'adId' => $adId,
            ));
        }
        $info = Oplib_Common_Cache::getCacheByPosIdInfo($posId,$adId);
        if(empty($info) || $info['status'] != Oplib_Ds_OperationPosAd::STATUS_ONLINE){
            throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::OP_POS_AD_NOT_EXIST,'',array(
                'adId' => $adId,
            ));
        }
        $arrOutput['info'] = array(
            'courseType' => $info['courseType'],
            'skuDesc'    => $info['skuDesc'],
            'name'       => strval($info['name']),//糖豆一师大咖才有
            'arrSkuId'   => $info['arrSkuId'],
            'lastfrom'  => Oplib_Const_Operation::$LAST_FROM_ARRAY[$posId] . intval($adId),
        );
        return $arrOutput; 
    }
}
