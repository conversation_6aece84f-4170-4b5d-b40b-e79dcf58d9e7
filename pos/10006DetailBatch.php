<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * Created by PhpStorm.
 * File : 10006DetailBatch.php
 * Author: <EMAIL>
 * Date: 2018/11/19
 * Time: 11:55
 * Desc: 课程卡片批量接口
 */
class Oplib_Pos_10006DetailBatch extends Oplib_Common_BasePos {

    public function invoke($arrCommand){

        $posId = intval($arrCommand['posId']);
        $grade   = intval($arrCommand['grade']);
        $arrAdId = is_array($arrCommand['arrAdId']) ? $arrCommand['arrAdId'] : [];
        $bCache  = $arrCommand['bCache'];   //默认true
        $status  = $arrCommand['status'];  //默认false
        $arrOutput = array('info' => array());
        $arrPosInfo = Oplib_Common_Cache::getOperationInfoBatch($posId,$grade,$bCache,$status);
        foreach ($arrPosInfo as $adInfo){
            if(!in_array($adInfo['id'],$arrAdId)){
                continue;
            }
            $arrOutPut['list'][$adInfo['id']] = [
                'courseType' => (in_array($adInfo['courseType'],[21,24])) ? Oplib_Const_Operation::TYPE_PRIVATE_LONG : $adInfo['courseType'],
                'name'       => $adInfo['name'],
                'skuDesc'    => $adInfo['skuDesc'],
                'skuBgImg'   => Hk_Util_Image::getImgUrlBySrc( $adInfo['ext']['skuBgImg']),
                'arrSkuId'   => $adInfo['arrSkuId'],
                'condition'  => $adInfo['condition'],
            ];

        }

        return $arrOutPut;

    }
}
