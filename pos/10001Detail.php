<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * Created by PhpStorm.
 * File : 10000Detail.php
 * Author: <EMAIL>
 * Date: 2018/8/9
 * Time: 11:54
 * Desc: 弹窗
 */

class Oplib_Pos_10001Detail extends Oplib_Common_BasePos {

    public function invoke($arrCommand){

        $posId = intval($arrCommand['posId']);
        $adId  = intval($arrCommand['adId']);
        $arrOutput = array('info' => array());
        if(0 >= $adId){
            throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::PARAM_ERROR,'',array(
                'adId' => $adId,
            ));
        }

        $info = Oplib_Common_Cache::getCacheByPosIdInfo($posId,$adId);

        if(empty($info) || $info['status'] != Oplib_Ds_OperationPosAd::STATUS_ONLINE){
            throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::OP_POS_AD_NOT_EXIST,'',array(
                'adId' => $adId,
            ));
        }
        $arrOutput['info'] = array(
            'courseType' => $info['courseType'],
            'skuDesc'    => $info['skuDesc'],
            'name'       => strval($info['name']),//糖豆一师大咖才有
            'arrSkuId'   => ($info['arrSkuId'][0] !== 0 ) ? $info['arrSkuId'] : [],
            'condition'  => isset($info['condition']) ? json_decode($info['condition'],true) : [],
            'lastfrom'   => Oplib_Const_Operation::$LAST_FROM_ARRAY[$posId] . intval($adId),

        );

        return $arrOutput;

    }

}