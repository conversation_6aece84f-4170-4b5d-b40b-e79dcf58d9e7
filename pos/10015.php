<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * Created by PhpStorm.
 * File : 10015.php
 * Author: <EMAIL>
 * Date: 2018/10/30
 * Time: 11:36
 * Desc: 中间运营位
 */

class Oplib_Pos_10015 extends Oplib_Common_BasePos {
    public function invoke($arrCommand){
        $uid   = intval($arrCommand['uid']);
        $posId = intval($arrCommand['posId']);
        $appType = strval($arrCommand['appType']);
        $utype   = intval($arrCommand['utype']);
        $grade   = intval($arrCommand['grade']);
        $arrOutput  = array();
        $onlineList = Oplib_Common_Cache::getCacheByPosGradeId($posId,$grade);
        $result = array();
        foreach($onlineList as $online){
            //应用
            $arrAppType = explode(',',$online['appType']);
            if(!in_array($appType,$arrAppType)){
                continue;
            }
            //用户类型
            $arrUtype = explode(',',$online['utype']);
            if(!in_array($utype,$arrUtype)){
                continue;
            }
            $result = $online;
            break;
        }
        $arrOutput['info'] = array(
            'adId'      => intval($result['id']),
            'posId'     => $posId,
            'cardImg'   =>  Hk_Util_Image::getImgUrlBySrc($result['pid']),
            'clickUrl'  => strval($result['clickUrl']),
            'lastfrom'  => Oplib_Const_Operation::$LAST_FROM_ARRAY[$posId] . intval($result['id']),
            'orifrom'   => Oplib_Const_Operation::$ORI_FROM_ARRAY[$posId] . intval($result['id']),
        );
        return $arrOutput;
    }
}
