<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * Created by PhpStorm.
 * File : 11008Detail.php
 * Author: <EMAIL>
 * Date: 2019/1/15
 * Time: 17:20
 * Desc:
 */
class Oplib_Pos_11008Detail extends Oplib_Common_BasePos {

    public function invoke($arrCommand){

        $posId = intval(Oplib_Const_Operation::POS_PLAN_CARD_XK);   //代理到11007
        $adId  = intval($arrCommand['adId']);
        $arrOutput = array('info' => array());
        if(0 >= $adId){
            throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::PARAM_ERROR,'',array(
                'adId' => $adId,
            ));
        }

        $info = Oplib_Common_Cache::getAdDetailByPosIdAdId($posId,$adId);

        if(empty($info) || $info['status'] != Oplib_Ds_OperationPosAd::STATUS_ONLINE){
            throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::OP_POS_AD_NOT_EXIST,'',array(
                'adId' => $adId,
            ));
        }
        $arrOutput['info'] = array(
            'name'       => $info['name'],
            'subject'    => intval($info['subject']),
            'courseType' => $info['courseType'],
            'skuDesc'    => $info['skuDesc'],
            // 'arrSkuId'   => ($info['arrSkuId'][0] !== 0 ) ? $info['arrSkuId'] : [],
            'condition'   => !empty($info['condition']) ? json_decode($info['condition'],true) : [],

        );

        return $arrOutput;
    }

}

