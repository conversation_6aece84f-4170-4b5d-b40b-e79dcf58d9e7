<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * Created by PhpStorm.
 * File : 10021DetailBatch.php
 * Author: <EMAIL>
 * Date: 2019/1/9
 * Time: 20:44
 * Desc:
 */
class Oplib_Pos_10021DetailBatch extends Oplib_Common_BasePos {

    public function invoke($arrCommand){

        $posId = intval(Oplib_Const_Operation::POS_PLAN_CARD_XK);   //代理到11007
        $grade   = intval($arrCommand['grade']);
        $arrAdId = is_array($arrCommand['arrAdId']) ? $arrCommand['arrAdId'] : [];
        $arrOutput = array('info' => array());
        $arrPosInfo = Oplib_Common_Cache::getOperationInfoBatch($posId,$grade);

        foreach ($arrPosInfo as $adInfo){
            if(!in_array($adInfo['id'],$arrAdId)){
                continue;
            }
            if(!is_array($adInfo['condition']['seasonList']) || empty($adInfo['condition']['seasonList'])){
                $adInfo['condition']['seasonList'] = array(
                    Zb_Const_LearnSeason::LEARN_SEASON_SPRING_1,
                    Zb_Const_LearnSeason::LEARN_SEASON_SPRING_2,
                    Zb_Const_LearnSeason::LEARN_SEASON_SPRING_3,
                    Zb_Const_LearnSeason::LEARN_SEASON_SPRING_4,
                );
            }
            $arrOutPut['list'][$adInfo['id']] = [
                'courseType'   => Oplib_Const_Operation::TYPE_PRIVATE_LONG,
                'name'         => $adInfo['name'],
                'skuDesc'      => $adInfo['skuDesc'],
                'skuBgImg'     => Hk_Util_Image::getImgUrlBySrc( $adInfo['ext']['skuBgImg']),
                'arrSkuId'     => $adInfo['arrSkuId'],
                'condition'    => $adInfo['condition'],
            ];

        }

        return $arrOutPut;

    }
}
