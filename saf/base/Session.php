<?php


/**
 * 处理zyb通用用户信息&验证类
 *
 * @since 1.3 2019-01-09 切换redis-session集群
 * @since 1.2 2016-11-23
 *
 * @filesource saf/base/Session.php
 * <AUTHOR> @date 2011/02/21
 * <AUTHOR>
 * @version 1.3
 * @date    2019-01-09
 */
class Saf_Base_Session {

    /**
     * @brief 通过zybuss获取登录状态，只能获取当前登录状态
     *
     * <AUTHOR>
     * @date 2015/7/8 18:20
     * @return mixed:array|boolean
     **/
    public static function checkZybLogin() {
        $userInfo = array(
            "uid"         => 0,
            "isLogin"     => false,
            "phone"       => "",
            "loginApp"    => "homework",    # 登录app，默认homework
            "loginfrom"   => "zuoyebang",   # 登录来源，默认zuoyebang
            "lastLogTime" => 0,
        );
        $session  = self::getSession();
        if (false === $session) {           # session获取失败，直接返回uid = 0
            return $userInfo;
        }

        if ($session['uid'] > 0) {          # 拥有uid的session才是合法的session
            $userInfo            = array_merge($userInfo, $session);
            $userInfo['isLogin'] = true;
        }
        return $userInfo;
    }

    /**
     * 获取当前zybuss对应的session的状态<br>
     * 1、session失效（redis无任何记录），将返回false<br>
     * 2、session有效（redis有记录），将分为以下情况处理<br>
     *    a、session为正常的session，将返回true<br>
     *    b、session为过期数据（即被设置为异常，uid = 0），将返回异常的信息<br>
     * 异常session返回的数据结构如下：<br>
     * <code>
     * array(
     *     "uid"      => int,
     *     "kickUid"  => int,
     *     "kickNo"   => int,
     *     "kickTime" => int,
     * );
     * </code>
     *
     * @return mixed:array|boolean
     */
    public static function checkSessionStatus() {
        $session = self::getSession();
        if (false === $session) {
            return false;
        }

        $uid = $session["uid"];
        if (0 === $uid) {           # session已经被踢出，返回被踢出的信息
            return array(
                "uid"      => 0,
                "kickUid"  => $session["kickUid"],
                "kickNo"   => $session["kickNo"],
                "kickMsg"  => $session["kickMsg"],
                "kickTime" => $session["kickTime"],
            );
        }
        return true;
    }

    /**
     * 获取用户session
     *
     * @return mixed:array|boolean
     */
    private static function getSession() {
        $zybuss  = $_COOKIE['ZYBUSS'];
        if (strlen($zybuss) <= 0) {
            return false;
        }
        if (true) {
            $session = self::getSessionByRpc($zybuss);
        } else {
            $session = self::getSessionByRedis($zybuss);
        }
        return $session;
    }

    /**
     * 直连redis获取zybuss对应的session数据，如果失败返回false
     *
     * @since 兼容转换存储方案，防止太多用户被自动踢出
     *
     * @param string       $zybuss
     * @return mixed array|boolean
     */
    private static function getSessionByRedis($zybuss) {
        $redis = Hk_Service_RedisClient::getInstance("session");
        if (false === $redis) {
            return false;
        }
        $ret   = $redis->get($zybuss);
        if (false === $ret) {
            Bd_Log::addNotice("sessionErr", "expire");
            return false;
        }

        $session = @json_decode($ret, true);
        if (empty($session)) {
            Bd_Log::addNotice("sessionErr", "decode");
            return false;
        }
        return $session;
    }

    /**
     * 通过rpc请求获取zybuss对应的session数据，如果失败返回false
     *
     * @deprecated
     *
     * @param string       $zybuss
     * @return mixed:array|boolean
     */
    private static function getSessionByRpc($zybuss) {
        $header = array(
            'pathinfo' => "/session/api/safchecklogin",
            //把zybuss传给接口,其余忽略
            'cookie'   => array('ZYBUSS'=>$_COOKIE['ZYBUSS']),
        );
        $postdata = array();
        $ret    = ral('zybsession', 'POST', $postdata, 123, $header);
        if (empty($ret)) {
            $errno    = ral_get_errno();
            $errmsg   = ral_get_error();
            $protCode = ral_get_protocol_code();
            Bd_Log::addNotice("zybsessionRalErrno",$errno);
            Bd_Log::addNotice("zybsessionRalErrmsg",$errmsg);
            Bd_Log::addNotice("zybsessionRalProtocolStatus",$protCode);
            Bd_Log::addNotice("sessionErr", "expire");
            return false;
        }

        $ret    = json_decode($ret, true);
        $errno  = intval($ret['errNo']);
        $errmsg = strval($ret['errstr']);
        if ($errno > 0 || empty($ret['data'])) {
            Bd_Log::addNotice("zybsessionRespErrno",$errno);
            Bd_Log::addNotice("sessionErr", "decode");
            return false;
        }
        return $ret['data'];
    }

    /**
     * @brief 从PASSPORT获得用户登录状态
     * @deprecated
     *
     * <AUTHOR>
     * @date 2011/02/21
    **/
    public static function checkLogin() {
        $safConf     = Bd_Conf :: getAppConf('/saf');
        if (empty($safConf)) {
            $safConf = Bd_Conf :: getConf('/saf');
        }
        $strSessionType = intval($safConf['session_type']);
        $intAutoReset = intval($safConf['auto_reset_cookie']);
        if ($_COOKIE['BDUSS'] == '') {
            //passport not login
            return false;
        }
        $re = array();
        $incomplete =  0;
        $loginParam = array();

        //通过用户自定义hook获取Bd_Passport :: checkUserLogin需要的参数，并调用该接口
        //主要针对Bd_Passport :: checkUserLogin接口有参数频繁变动的情况
        if(isset($safConf['passport']['hook_name']) && !empty($safConf['passport']['hook_name'])){
            $hook_name = $safConf['passport']['hook_name'];
            if(is_callable($hook_name)){
                $loginParam = call_user_func($hook_name);

                //the second param is incompltete
                if(isset($loginParam[1])){
                    $incomplete = $loginParam[1];
                }
                $re = call_user_func_array("Bd_Passport :: checkUserLogin", $loginParam);
            }
            else{
                Saf_SmartMain :: setSafLog("call $hook_name failed, function is not callable", 2);
                return false;
            }
        }
        //通过读取配置文件的方式，获取Bd_Passport :: checkUserLogin需要的参数，并调用该接口
        else{
            //新版session1 session2同时兼容
            $need_cinfo =  0;
            $quick_user =  0;
            $voice_user =  0;
            $weak_bduss =  0;

            if(isset($safConf['passport']['incomplete_user']) && $safConf['passport']['incomplete_user'] === '1'){
                $incomplete = 1;
            }

            if(isset($safConf['passport']['need_cinfo']) && $safConf['passport']['need_cinfo'] === '1'){
                $need_cinfo = 1;
            }

            if(isset($safConf['passport']['quick_user']) && $safConf['passport']['quick_user'] === '1'){
                $quick_user = 1;
            }

            if(isset($safConf['passport']['voice_user']) && $safConf['passport']['voice_user'] === '1'){
                $voice_user = 1;
            }

            if(isset($safConf['passport']['weak_bduss']) && $safConf['passport']['weak_bduss'] === '1'){
                $weak_bduss = 1;
            }

            if(isset($safConf['passport']['need_cinfo']) || isset($safConf['passport']['quick_user'])
                || isset($safConf['passport']['voice_user']) || isset($safConf['passport']['weak_bduss']))
            {
                // 依赖于passport库 1.0.19.0 及以上版本
                $re = Bd_Passport :: checkUserLogin(null, $incomplete, $need_cinfo, $quick_user, $voice_user, $weak_bduss);
            }else{

                $incomplete =  isset($safConf['passport']['incomplete_user']);
                if ($incomplete && ($safConf['passport']['incomplete_user'] === '1')) {
                    //依赖于passport库的 1.0.10.0 及以上版本
                    $re = Bd_Passport :: checkUserLogin(null, 1);
                } else {
                    $re = Bd_Passport :: checkUserLogin();
                }
            }
        }

        if ($re === false) {
            Saf_SmartMain :: setSafLog("获取session链接到passport服务器出错(" . var_export($re, true) . ")", 2);
            //Bd_Passport checkUserLogin接口的返回结果没有区分$ssnData['uid']=0用户未登录情况，为避免误报注释以下代码
            //Saf_Base_Hook :: warningAction('get_session_passport', 'system busy');
            return false;
        }

        //passport半帐号功能支持
        if ($incomplete && (!empty($re['gdata']))) {
            $gdata = Bd_Passport :: parseGData($re['gdata']);
            if (false === $gdata) {
                Bd_Log::warning("Passport parseGData error",
                    Bd_Passport :: getCode(),
                    Bd_Passport :: getMessage());
            }
            //pass异常情况，gdata解析失败，当做正常账号处理
            $re['isIncomplete'] = empty($gdata['incomplete_user']) ? 0 : intval($gdata['incomplete_user']);
        }
        // check reset_cookie
        if ($intAutoReset > 0 && is_array($re)) {
            if ($strSessionType == 2) {
                $intNeedReset = $re['need_set_cookie'];
            } else {
                $intNeedReset = $re['need_reset_cookie'];
            }
            if ($intNeedReset == 1) {
                if ($intAutoReset == 1 || $re['bduss'] == '') {
                    setcookie('BDUSS', '', time()-3600, '/', '.baidu.com', false, true);
                    return false;
                } else {
                    setcookie('BDUSS', $re['bduss'], time() + 6*3600, '/', '.baidu.com', false, true);
                }
            }
        }
        return $re;
    }

    /**
     * @brief 保存用户信息到passport
     *
     * @deprecated
     *
     * <AUTHOR>
     * @date 2011/02/21
    **/
    public static function saveModDatat()
    {
        $arrSavePassport = Saf_SmartMain :: getSavePassport();
        if (is_array($arrSavePassport) &&
            count($arrSavePassport) > 0) {
                $safConf = Bd_Conf :: getAppConf('/saf');
                if(empty($safConf)){
                    $safConf = Bd_Conf :: getConf('/saf');
                }
                $intPassportSaveLen = intval($safConf['passport_save_len']);
                if ($intPassportSaveLen == 0) {
                    $intPassportSaveLen = 32 ;
                }
                Bd_Passport :: initDataBuf($arrCachePass, $intPassportSaveLen);

                foreach ($arrSavePassport as $bit => $v) {
                    Bd_Passport :: modDataBufByBit($arrCachePass , intval($bit), intval($v));
                }
                $arrOutput = Bd_Passport :: modData($_COOKIE['BDUSS'] , '' ,'' , $arrCachePass['data'] , $arrCachePass['mask']);

                if ($arrOutput['status'] !=0 || $arrOutput == false) {
                    Saf_SmartMain :: setSafLog("保存到passport服务器出错(" . var_export($arrOutput, true) . ")", 2);
                    Saf_Base_Hook :: warningAction('save_passport', 'system busy');
                    return false;
                }
            }
        return true;
    }
}

/* vim: set ts=4 sw=4 sts=4 tw=100 */
