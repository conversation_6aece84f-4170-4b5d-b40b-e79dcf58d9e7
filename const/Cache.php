<?php

/**
 * @file Cache.php
 * <AUTHOR>
 * @date 2018-06-05
 * @brief 运营cache前缀
 *
 **/

class Oplib_Const_Cache {
    const OP_MC_POS_INFO                    = 'OP_MC_POS_INFO_%d'; // 运营位缓存
    const OP_MC_POS_INFO_TIME               = 300;  // 运营位缓存过期时间
    const UCEP_UID_KEY_PRE                  = 'UCEP_MULTI_AD_USER_PORT_%s'; // ucep用户维度前缀
    const UCEP_UID_KEY_TIME                 = 217728000;    // 30天
    const UCEP_ADG_KEY_PRE                  = 'UCEP_MULTI_AD_ADG_%d_%d'; // ucep广告组前缀
    const UCEP_ADG_KEY_CNT                  = 100;  // ucep广告组缓存数量 访问量较大 cp多分
    const UCEP_ADG_KEY_TIME                 = 3600;  // ucep广告组缓存时间 可以长一点
    const DIRECTIONAL_AD_POP_PRE            = 'ZHIBOKE_DIRCTIONAL_AD_POP_';           // 定投广告存储
    const DIRECTIONAL_AD_POP_SHAKE_PRE      = 'ZHIBOKE_DIRCTIONAL_AD_POP_SHAKE_';     // 防抖动
    const DIRECTIONAL_AD_POP_SEND_PRE       = 'ZHIBOKE_DIRCTIONAL_AD_POP_SEND_%d_%d'; // 活动级别发送记录缓存
    const UCEP_UID_DAO_KE_TI_XING_KEY_PRE   = 'UCEP_USER_DAO_KE_TI_XING_COURSE_%d';   // ucep用户到课提醒course信息
    const UCEP_EXT_LIST_KEY                 = 'UCEP_USER_EXT_LIST';// ucep用户扩展信息消费列表 离线使用

    //运营位首页ABB糖果一课大咖缓存
    const OP_INDEX_POS_LIST                 = 'OP_INDEX_POS_LIST_%d';//首页运营位列表数据缓存
    const OP_INDEX_POS_RANK                 = 'OP_INDEX_POS_RANK_%d_%d';//首页运营位按天排序数据缓存

    const OP_INDEX_POS_DETAIL_LIST          = 'OP_INDEX_POS_DETAIL_LIST_%d_%d';  //批量获取广告位数据缓存
    // 置顶课程缓存
    const OP_TOP_SKU_G_S                    = 'OP_TOP_SKU_%d_%d'; // 置顶课程缓存，分年级学科
    const OP_TOP_SKU_G_S_TIME               = 120; // 置顶课程缓存120s


    ///////// mc
    const COURSE_COMMAND_TIMES              = 40; //年级学科key少且流量不均匀，复制多份使memcached实例流量会相对均匀。
    const COURSE_COMMAND_KEY                = 'course_recommend_%d_%d_%d';
    const COURSE_COMMAND_ACTIVITY_KEY       = 'course_recommend_activity_%d_%d';
    const COURSE_COMMAND_COURSE_KEY         = 'course_recommend_course_%d';
    const COURSE_COMMAND_COURSE_STYLE       = 'course_recommend_course_%d_%d';
    const COURSE_COMMAND_GONGZHONGHAO_TIMES = 10; //年级学科key少且流量不均匀，复制多份使memcached实例流量会相对均匀。
    const COURSE_COMMAND_GONGZHONGHAO_KEY   = 'course_recommend_gongzhonghao_%d';
    const ACT_COMMAND_STYLE_MID_KEY         = 'act_recommend_mid_%d';
    const ACT_COMMAND_STYLE_MID_TIMES       = 10; // 中间页流量较大，复制多份使memcached实例流量会相对均匀。
    const UCEP_REC_COURSE_PRE               = 'ucep_rec_course_pre_'; // ucep推荐课程缓存前缀
    const UCEP_REC_COURSE_TIME              = 120; // ucep推荐课程缓存 120s
    const ACT_COMMAND_STYLE_DATI_KEY        = 'act_recommend_dati_%d'; // 搜题卡片页 答题页
    const ACT_COMMAND_STYLE_DATI_TIMES      = 10;
    const ACT_COMMAND_STYLE_DAOKE_KEY       = 'act_recommend_daoke_%d'; // 搜题卡片页 到课页
    const ACT_COMMAND_STYLE_DAOKE_TIMES     = 10;
    const ACT_COMMAND_STYLE_TUAN_KEY        = 'act_recommend_tuan_%d'; // 搜题卡片页 到课页
    const ACT_COMMAND_STYLE_TUAN_TIMES      = 10;
    const ACT_COMMAND_STYLE_WEIKE_KEY       = 'act_recommend_weike_%d'; // 搜题卡片页 名师微课页
    const ACT_COMMAND_STYLE_WEIKE_TIMES     = 10;

    //------------------------------拼团项目缓存信息----------------------------------
    const TUAN_MEMCACHE_SERVER_NAME = 'zhiboke';

    const OP_TUAN_ST_KEY           = 'OP_TUAN_ST_KEY_%d';
    const OP_TUAN_SKU_KEY           = 'OP_TUAN_SKU_KEY_%d';

    const OP_TUAN_KEY              = 'OP_TUAN_KEY_%d';
    const OP_TUAN_MEMBER_KEY       = 'OP_TUAN_MEMBER_KEY_%d';
    const OP_CITY_TUAN_KEY         = 'OP_CITY_TUAN_KEY_%s_%d';
    const OP_CITY_ROBOT_TUAN_KEY   = 'OP_CITY_ROBOT_TUAN_KEY_%s_%d';
    const OP_TUAN_COMMENT_LIST     = 'OP_TUAN_COMMENT_LIST_%d_%d';
    const OP_TUAN_COMMENT_LIST_TTL = 3600;
    const TUAN_ST_SKU_KEY          = 'TUAN_ST_SKU_SET_%d';
    const TUAN_ST_SKU_KEY_TTL      = 3600;
    const TUAN_ST_LOCK_KEY         = 'TUAN_ST_LOCK_%d';
    const TUAN_ST_LOCK_TTL         = 600;

    // 缓存配置文件
    const BUFFER_CONF_FILE = '/hk/redis/zhiboke';
    // 拼团活动列表
    const OP_PINTUAN_ACTIVITY_LIST = 'OP_PINTUAN_ACTIVITY_LIST_%d';
    // 拼团活动列表缓存过期时间
    const OP_PINTUAN_ACTIVITY_LIST_TTL = 60;
    // 首页胶囊位key
    const OP_POS_JIAONANG_AD_KEY  = 'op_pos_jiaonang_ad_key_';
    const OP_POS_JIAONANG_AD_TIME = 60; // 60s的缓存
    // 加群服务
    const OP_PINTUAN_QQ_INFO = 'OP_PINTUAN_QQ_INFO_%d';
    //------------------------------拼团项目缓存信息----------------------------------

    const UCEP_CANDY_POSITION_UID_KEY_PRE   = 'UCEP_CANDY_POSITION_UID:%d:%d'; // ucep糖果位用户维度前缀
    const UCEP_CANDY_POSITION_TIME          = 217728000;    // 30天

    //首页广告位推广
    const APP_INDEX_AD_POSITION             = 'app_index_ad_position_%d_%d';//首页广告位配置
    //banner 视频
    const BANNER_VIDEO_STATUS               = 'BANNER_VIDEO_STATUS_%d_%d';   //banner 位视频

    //学科页卡片
    const POS_AD_LIST                       = 'POS_AD_LIST_%d_%d';    //广告位广卡片
    const POS_AD_DETAIL                     = 'POS_AD_DETAIL_%d_%d';  //广告卡片详情

    //体验一课
    const EXP_ONE_CLASS                     = 'EXP_ONE_CLASS_%d_%d';
    const EXP_ONE_CLASS_DB                  = 'EXP_ONE_CLASS_DB_%d_%d';

    // 特惠课,试听课活动
    const TEHUIKE_ACT_INFO = 'TEHUIKE_ID_%d';   // 活动详情str
    const TEHUIKE_GRADE_MAP = 'TEHUIKE_%d_%d';   // 年级班型zset
    const TEHUIKE_USER_ABANDON = 'TEHUIKE_ABANDON_%d';  // 放弃状态

    // 活动配置
    const OP_ACT_CONFIG_KEY = 'OP_ACT_CONFIG_KEY_%s';

    const OPMIS_CLASSING_GPUID = 'OPMIS_CLASSING_GPUID_%d';
    const OPMIS_CLASSING_GPUID_EXPIRE = 3600;


    //班课ing
    const CLASSING_LESSON_SEE_NUM       = 'CLASSING_LESSON_SEE_NUM_%d'; //班课ing观看次数
    const CLASSING_LESSON_SEE_NUM_DB    = 'CLASSING_LESSON_SEE_NUM_DB_%d'; //班课ing观看DB
    const CLASSING_PAGE_INDEX           = 'CLASSING_PAGE_INDEX_%d'; //班课ind落地

    // 批次信息
    const OPMIS_CLASSING_BATCH_INFO = 'OPMIS_CLASSING_BATCH_INFO';
    const OPMIS_CLASSING_BATCH_INFO_EXPIRE = 86400;

    const OPMIS_PAGETEMPLATE       = 'OPMIS_PAGETEMPLATE_%d';

    const TUAN_ORDER_OLD        = 'PIN_TUAN_ORDER_OLD'; // 拼团到老支付页

    // 新蜂鸟fengniao的缓存
    const FENGNIAO_ACT_INFO     = 'FENGNIAO_ACTID_%d'; // 蜂鸟活动缓存
    const FENGNIAO_PAGE_INFO    = 'FENGNIAO_PAGEID_%d_VERSION_%d'; // 蜂鸟活动页面缓存
    const FENGNIAO_PAGE_HTML    = 'FENGNIAO_HTML_PAGEID_%d_VERSION_%d'; // 蜂鸟活动页面缓存

    // 答题服务缓存
    const FN_EXAM_PAPER           = 'FN_EXAM_PAPER_%d'; // 试卷缓存
    const FN_EXAM_PAPER_RANDOM    = 'FN_EXAM_PAPER_RANDOM_%d'; // 随机试卷缓存
    const FN_EXAM_PAPER_ANSWERNUM = 'FN_EXAM_PAPER_ANSWERNUM_%d'; // 试卷答题人数缓存
    
    const FENGNIAO_LOTTERY_INFO = 'FENGNIAO_LOTTERY_INFO_%d'; // 蜂鸟抽奖信息

}

