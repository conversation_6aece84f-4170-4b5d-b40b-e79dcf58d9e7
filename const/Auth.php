<?php

/**
 * Created by IntelliJ IDEA.
 * @Description TODO
 * @Date 2020/8/6 5:42 下午
 * @<NAME_EMAIL>
 */
class Qdlib_Const_Auth
{

    protected static $objPromotePlan;
    protected static $objAuthUser;
    protected static $objAuthOrg;
    protected static $objAccountUname;
    protected static $objAccount;

    public static function getAccount()
    {
        if (empty(self::$objAccount)) {
            self::$objAccount = new Qdlib_Ds_QudaoAccount();
        }
        return self::$objAccount;
    }

    public static function getAccountUname()
    {
        if (empty(self::$objAccountUname)) {
            self::$objAccountUname = new Qdlib_Ds_AccountUname();
        }
        return self::$objAccountUname;
    }

    public static function getPromotePlanObj()
    {
        if (empty(self::$objPromotePlan)) {
            self::$objPromotePlan = new Qdlib_Ds_QudaoPromotePlan();
        }
        return self::$objPromotePlan;
    }

    public static function getUserObj()
    {
        if (!(self::$objAuthUser instanceof Qdlib_Ds_AuthUser)) {
            self::$objAuthUser = new Qdlib_Ds_AuthUser();
        }
        return self::$objAuthUser;
    }

    public static function getOrgObj()
    {
        if (!(self::$objAuthOrg instanceof Qdlib_Ds_AuthOrg)) {
            self::$objAuthOrg = new Qdlib_Ds_AuthOrg();
        }
        return self::$objAuthOrg;
    }

    /**
     * 获取lastfrom 所属人、所属部门
     * @param $lastfrom
     * @param $channel
     * @return array 所属人、所属部门 [$uname, $orgId]
     */
    public static function getUnameByLastFrom($lastfrom, $channel)
    {
        $arr = explode('_', $lastfrom);
        $planNum = array_pop($arr);
        $frPrefix = implode('_', $arr);
        return self::getUnameByLastFromInfo($frPrefix, $planNum, $channel);
    }

    /**
     * 获取lastfrom 所属人、所属部门
     * @param $frPrefix
     * @param $planNum
     * @param $channel
     * @return array 所属人、所属部门 [$uname, $orgId]
     */
    public static function getUnameByLastFromInfo($frPrefix, $planNum, $channel)
    {
        $info = self::getPromotePlanObj()->getPromotePlanByLastfrom($frPrefix, $planNum, $channel);
        return array(
            $info['uname'],
            $info['orgId'] ?? -1,
        );
    }

    /**
     * 获取有权限的lastfromId
     * @param $uname
     * @return array
     */
    public static function getAuthLastFromId($uname)
    {
        list($orgIds, $unames) = self::getAuthOrgByUname($uname);
        $where = Qdlib_Util_DB::whereIn('uname', $unames);
        if (!empty($orgIds)) {
            $where = $where . ' or ' . Qdlib_Util_DB::whereIn('org_id', $orgIds);
        }
        $arrConds = array(
            $where,
        );
        $ret = self::getPromotePlanObj()->getPromotePlanAll($arrConds, ['id']);
        if (!empty($ret)) {
            $ret = array_column($ret, 'id');
        }
        return $ret;
    }

    /**
     * 返回有权限组织及个人planId
     * @param $uname
     * @return array [orgIds, unames, planIds]
     */
    public static function getAuthOrgPlanId($uname,$plan=true)
    {
        $ret = self::getAuthOrgByUname($uname);
        list($orgIds, $unames) = $ret;
        if (empty($unames)) {
            $ret[] = [];
            return $ret;
        }
        if($plan===true){
            $newUnames = implode("','", $unames);
            $ids = self::getPromotePlanObj()->getPromotePlanAll(["uname in ('$newUnames')"], ['id']);
            if (!empty($ids)) {
                $ids = array_column($ids, 'id');
            }
            $ret[] = $ids;
        }else{
            $ret[] = [];
        }
        return $ret;
    }

    /**
     * 返回用户有权限的组织和用户 or查询
     * @param $uname
     * @return array
     */
    public static function getAuthOrgByUname($uname)
    {
        $user = self::getUserObj()->getAuthUserList(['uname' => $uname,'status' => 1], ['uname', 'orgId', 'highAuth']);
        if(empty($user)){
            return  [[],[]];
        }
        $user = $user[0];
        if (empty($user['highAuth'])) {
            return array([], [$uname]);
        }
        $orgId = $user['orgId'] ?? -1;
        if ($orgId == -1) {
            return array([], [$uname]);
        }
        list($orgIds, $unames) = self::getOrgAndUname(explode(',', $user['highAuth']));
        $unames[] = $uname;
        $orgs = $orgIds;
        foreach ($orgIds as $orgId) {
            $orgs = array_merge($orgs, self::getChildrenOrgIds($orgId));
        }
        if (in_array(90002, $orgs)) {
            $orgs = array_merge($orgs, ['0', -1]);
        }
        if (in_array(39, $orgs) && Qdlib_Util_Tool::getEnviron() != 'test') {
            $orgs = array_merge($orgs, ['0', -1]);
        }
        return [array_unique($orgs), array_unique($unames)];
    }

    /**
     * 获取子部门ids
     * @param $orgId int 部门id
     * @param $hasDelete bool
     * @return array
     */
    public static function getChildrenOrgIds($orgId, $hasDelete = false)
    {
        //获取子组织
        $where = array(
            'deleted' => 0,
        );
        if ($hasDelete) {
            $where = empty($orgId) ? null : [];
        }
        if (!empty($orgId)) {
            $where[] = "id = {$orgId} or lv1 = {$orgId} or lv2 = {$orgId} or lv3 = {$orgId}";
        }
        $orgs = self::getOrgObj()->getAuthOrgList($where, ['id']);
        return empty($orgs) ? [] : array_column($orgs, 'id');
    }

    public static function getOrgAndUname($orgs)
    {
        $orgIds = $unames = [];
        foreach ($orgs as $org) {
            if (is_numeric($org)) {
                $orgIds[] = $org;
            } else {
                $unames[] = $org;
            }
        }
        return [$orgIds, $unames];
    }

    /**
     * 是否可查看花费
     * @param $uname
     * @return bool
     */
    public static function showCost($uname)
    {
        $ret = self::getUserObj()->getAuthUserList(['uname' => $uname], ['showCost']);
        $showCost = $ret[0]['showCost'] ?? 0;
        return $showCost == 1;
    }

    /**
     * 目标/监控 获取账号
     * 获取所有有权限的uname
     * @param $uname
     * @return array
     */
    public static function getAuthUnames($uname)
    {
        list($orgIds, $unames) = self::getAuthOrgByUname($uname);
        if(empty($unames)){
            return [];
        }
        $where = array(
            'status'    => 1,
        );
        $orgUnames = [];
        if (!empty($orgIds)) {
            $where[] = Qdlib_Util_DB::whereIn('org_id', $orgIds);
            $orgUnames = self::getUserObj()->getAuthUserList($where, 'uname');
            if (!empty($orgUnames)) {
                $orgUnames = array_column($orgUnames, 'uname');
            }
        }
        return array_unique(array_merge([$uname], $orgUnames, $unames));
    }

    public static function getAuthAccounts($uname, $channel = '', $business = '', $agent = 0, $status = 0, $accounts = [], $runMode = 0)
    {
        $unames = self::getAuthUnames($uname);
        if(empty($unames)){
            return [];
        }
        //账号归属
        $where = array(
            Qdlib_Util_DB::whereIn('uname', $unames)
        );
        $aids = self::getAccountUname()->getAccountUnameList($where, ['aid'], ['group by aid']);
        if (empty($aids)) {
            return [];
        }
        $aids = array_column($aids, 'aid');
        $accConds = array(
            Qdlib_Util_DB::whereIn('id', $aids)
        );
        if (!empty($channel)) {
            $accConds['channel'] = $channel;
        }
        if (!empty($business)) {
            $accConds['business'] = $business;
        }
        if (!empty($agent)) {
            $accConds['agentId'] = $agent;
        }
        if (!empty($accounts)) {
            $accConds[] = Qdlib_Util_DB::whereIn('account', $accounts);
        }
        if ($status == 1) {
            $accConds['deleted'] = 0;
            $accConds['isExpired'] = 0;
        }
        if ($status == 2) {
            $accConds[] = "deleted = 1 or is_expired = 1";
        }
        if (!empty($runMode)) {
            $accConds['runMode'] = $runMode;
        }
        return self::getAccount()->getAllAccountList($accConds, ['id', 'business', 'channel', 'agentId', 'account', 'accountId', 'descr', 'deleted', 'isExpired', 'relatedCompany']);
    }

     /* 目标/监控 获取账号
     * 线上写死 yk 业务线    账号渠道 pyq，gdt，jrtt，账号获取方法使用新权限 ，uname访问tblAuthUser表获取org_id，
     * 根据org_id查unames，根据uanmes查询tblQudaoAccountUname账号id，根据账号ids查询account，channel
     * @param $uname
     * @param string $channel
     * @param string $business
     * @param int $agent
     * @param int $status 0无状态，1开启，2关闭
     *
     */
    public static function getAdsMonitorAccount($uname, $channel = '', $business = '', $agent = 0, $status = 0, $isNeedSelf = false)
    {
        $user = self::getUserObj()->getAuthUserList(['uname' => $uname,'status' => 1], ['uname', 'orgId', 'highAuth']);
        if(empty($user)){
            return [];
        }
        $user = $user[0];

        //权限uname
        if (empty($user['highAuth'])) {
            $unames = [$uname];
        } else {
            list($orgIds, $unames) = self::getOrgAndUname(explode(',', $user['highAuth']));
            if ($isNeedSelf) {
                $unames[] = $uname;
            }
            $orgs = $orgIds;
            foreach ($orgIds as $orgId) {
                $orgs = array_merge($orgs, self::getChildrenOrgIds($orgId));
            }
            if (in_array(90002, $orgs)) {
                $orgs = array_merge($orgs, ['0', -1]);
            }
            if (in_array(39, $orgs) && Qdlib_Util_Tool::getEnviron() != 'test') {
                $orgs = array_merge($orgs, ['0', -1]);
            }
            $orgUnames = [];
            if (!empty($orgs)) {
                $orgs = array_unique($orgs);
                $orgUnames = self::getUserObj()->getAuthUserList(['org_id in (' . implode(',',$orgs) . ')'], ['uname']);
            }
            $unames = empty($orgUnames) ? $unames : array_unique(array_merge(array_column($orgUnames,'uname'),$unames));
        }
        //账号归属
        $aids = self::getAccountUname()->getAccountUnameList(["uname in ('" . implode("','", $unames) . "')"], ['aid']);
        if (empty($aids)) {
            return [];
        }
        $aids = array_unique(array_column($aids, 'aid'));
        $accConds[] = "id in (" . implode(',', $aids) . ")";

        if (!empty($channel)) {
            $accConds['channel'] = $channel;
        }
        if (!empty($business)) {
            $accConds['business'] = $business;
        }
        if (!empty($agent)) {
            $accConds['agentId'] = $agent;
        }
        if (!empty($accounts)) {
            $accConds[] = Qdlib_Util_DB::whereIn('account', $accounts);
        }
        if ($status == 1) {
            $accConds['deleted'] = 0;
            $accConds['isExpired'] = 0;
        }
        if ($status == 2) {
            $accConds[] = "deleted = 1 or is_expired = 1";
        }
        return self::getAccount()->getAllAccountList($accConds, ['id', 'business', 'channel', 'agentId', 'account', 'accountId', 'descr', 'deleted', 'isExpired', 'relatedCompany']);
    }

    public static function getCacheAccounts($channel, $accountType)
    {
        $key = sprintf('s:batch:valid_account:%s:%d', $channel, $accountType);
        $redis = Qdlib_Util_Cache::getQudaoRedis();
        return $redis->sMembers($key);
    }

    /**
     * 获取批处理给
     * @param $uname string uname
     * @param $channel string 渠道
     * @param $accountType int 账号类型 1.有消耗有成单 2.有消耗无成单 3.全部 4.自定义
     * @return array|bool|false
     */
    public static function getBatchAccounts($uname, $channel, $accountType, $range = 0)
    {
        $accounts = [];
        if(!$range){
            $accounts = self::getCacheAccounts($channel, $accountType);
            if(empty($accounts)){
                return [];
            }
        }
        return self::getAuthAccounts($uname, $channel, '', 0, 0, $accounts);
    }

     /**
     * lastfrom 新权限where逻辑
     */
    public static function getFrListWhere($uname)
    {
        if (empty($uname)) {
            $where = "uname in ()";
            return $where;
        }
        list($orgs, $unames) = self::getAuthOrgByUname($uname);

        if (!empty($orgs)) {
            $where = "(org_id in (" . implode(',', $orgs) . ") or uname in ('" . implode("','", $unames) . "'))";
        } else {
            $where = "uname in ('" . implode("','", $unames) . "')";
        }

        return $where;
    }


    public static function getChildrenOrgIdsByPid($orgId)
    {
        //获取子组织
        $where = array(
            'deleted' => 0,
        );

        if (!empty($orgId)) {
            $where[] = "lv1 = {$orgId} and level=2";
        }
        $orgs = self::getOrgObj()->getAuthOrgList($where, ['id']);
        return empty($orgs) ? [] : array_column($orgs, 'id');
    }
}