<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file Redis.php
 * <AUTHOR>
 * @date 2018/6/28 20:26:46
 * @brief 直播课Redis Key
 *
 **/

class Zb_Const_Redis {
    const DAS_ASYNC_QUEUE       = 'DAS_ASYNC_QUEUE';  //课程实体
    const DAS_ASYNC_QUEUE_NAME  = 'ZB_DAS_LESSON_ASYNC_QUEUE';  //课程实体

    const DAS_ASYNCUP_QUEUE_NAME  = '{ZB_DAS_LESSON_ASYNCUP_QUEUE}';   //存储任务的队列key前缀
    const DAS_ASYNC_QUEUE_KEY     = '{ZB_DAS_LESSON_ASYNC_QUEUE_KEY}'; //队列加锁key前缀
    const DAS_ASYNC_QUEUE_TASK    = '{ZB_DAS_LESSON_ASYNC_QUEUE_TASK}';//队列监控key前缀
    const DAS_ASYNC_QUEUE_NO      = 'ZB_DAS_LESSON_ASYNC_QUEUE_NO';    //获取队列编号使用

    const ZB_REDIS_SERVER_NAME    = '/hk/redis/zhiboke';

}
