<?php

class Qdlib_Const_Hetu_AfterType
{
    //*Const
    const RESEND = 1;//补寄 
    const EXCHANGE_GOODS = 2;//退换货 
    const REFUND_CANCEL = 3;//未履约退款 
    const REFUND_REFUSE = 4;//拒收退款 
    const REFUND_RETURN = 5;//退货退款 
    const REFUND_ONLY = 6;//仅退款 
    const REFUND_MULTIPLE = 7;//多次退款 
    const COMPENSATION = 8;//补偿 
    const INDEMNITY = 9;//赔偿 
    const VIRTUAL_REFUND_RECLAIM = 10;//虚拟商品回收退款 
    const TRANSFER_COURSE = 11;//转班
    const FULL_REFUND_RETURN = 12;//全额退货退款
    const VIRTUAL_REFUND_RETURN = 14; //虚拟退货退款
    const NONE = -1;//无售后类型

    //*Map
    public static $map = [
        self::NONE => "无",
        self::RESEND => "补寄",
        self::EXCHANGE_GOODS => "退换货",
        self::REFUND_CANCEL => "未履约退款",
        self::REFUND_REFUSE => "拒收退款",
        self::REFUND_RETURN => "退货退款",
        self::REFUND_ONLY => "仅退款",
        self::REFUND_MULTIPLE => "多次退款",
        self::COMPENSATION => "补偿",
        self::INDEMNITY => "赔偿",
        self::VIRTUAL_REFUND_RECLAIM => "退款",
        self::TRANSFER_COURSE => "转班",
        self::FULL_REFUND_RETURN => '全额退货退款',
        self::VIRTUAL_REFUND_RETURN => '虚拟退货退款',
    ];

    //*KeyMap - 字符串map
    public static $keyMap = [
        self::RESEND => "resend",
        self::EXCHANGE_GOODS => "exchange_goods",
        self::REFUND_CANCEL => "refund_cancel",
        self::REFUND_REFUSE => "refund_refuse",
        self::REFUND_RETURN => "refund_return",
        self::REFUND_ONLY => "refund_only",
        self::REFUND_MULTIPLE => "refund_multiple",
        self::COMPENSATION => "compensation",
        self::INDEMNITY => "indemnity",
        self::VIRTUAL_REFUND_RECLAIM => "virtual_refund_reclaim",
        self::TRANSFER_COURSE => "transfer_course",
        self::FULL_REFUND_RETURN => 'full_refund_return',
        self::VIRTUAL_REFUND_RETURN => 'virtual_refund_return',
    ];
}