<?php
/**
 * @file Material.php
 * @Description 河图与金丝雀素材部分公共常量
 * @Date 2021/06/30 19:12 下午
 * @Created by zhang<PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 */

class Qdlib_Const_Hetu_Material
{
    //物料类型
    const SUPPLIES_TYPE_XUANCHUAN = 1;
    const SUPPLIES_TYPE_KECHENG = 2;
    const SUPPLIES_TYPE_QUDAO = 3;
    const SUPPLIES_TYPE_OTHER = 4;

    public static $suppliesTypeMap = [
        self::SUPPLIES_TYPE_XUANCHUAN => '宣传物料',
        self::SUPPLIES_TYPE_KECHENG => '课程信息',
        self::SUPPLIES_TYPE_QUDAO => '渠道规范',
        self::SUPPLIES_TYPE_OTHER => '其他',
    ];

    //素材类型
    const MATERIA_KIND_IMAGE = 1;
    const MATERIA_KIND_VIDEO = 2;
    const MATERIA_KIND_TEXT = 3;

    public static $materialKindMap = [
        self::MATERIA_KIND_IMAGE => '图片',
        self::MATERIA_KIND_VIDEO => '视频',
        self::MATERIA_KIND_TEXT => '文案',
    ];

    //使用状态
    const USED_STATUS_OK = 1;
    const USED_STATUS_OFF = 2;

    public static $usedStatusMap = [
        self::USED_STATUS_OK => '正常',
        self::USED_STATUS_OFF => '禁用',
    ];

    //创意中心审核状态
    const CHECK_STATUS_DRAFT = 1;
    const CHECK_STATUS_DOING = 2;
    const CHECK_STATUS_OK = 3;
    const CHECK_STATUS_REJECT = 4;

    public static $checkStatusMap = [
        self::CHECK_STATUS_DRAFT => '草稿',
        self::CHECK_STATUS_DOING => '审核中',
        self::CHECK_STATUS_OK => '审核通过',
        self::CHECK_STATUS_REJECT => '审核拒绝',
    ];
}