<?php

class Qdlib_Const_ChannelResourceMap
{
    const CONNECT_DELIMITER = '-'; // 资源位不同层级间的连接符

    const SPLIT_DELIMITER = '|'; // 资源位同层级间的分隔符

    const JRTT_RESOURSE_LV1_MAP = [
        'DEFAULT' => '默认',
        'UNION' => '只投放到资讯联盟（穿山甲）',
        'UNIVERSAL' => '通投智选',
    ];

    const JRTT_RESOURSE_LV1_MAP_V2 = [
        'MANUAL' => '首选媒体',
        'UNIVERSAL_SMART' => '通投智选',
    ];

    const JRTT_RESOURSE_LV2_MAP = [
        'INVENTORY_FEED' => '头条信息流',
        'INVENTORY_TEXT_LINK' => '头条文章详情页（已废弃）',
        'INVENTORY_VIDEO_FEED' => '西瓜信息流',
        'INVENTORY_HOTSOON_FEED' => '火山信息流',
        'INVENTORY_AWEME_FEED' => '抖音信息流',
        'INVENTORY_UNION_SLOT' => '穿山甲',
        'UNION_BOUTIQUE_GAME' => '穿山甲精选休闲游戏',
        'INVENTORY_UNION_SPLASH_SLOT' => '穿山甲开屏广告',
        'INVENTORY_AWEME_SEARCH' => '搜索广告——抖音位',
        'INVENTORY_SEARCH' => '搜索广告——头条位',
        'INVENTORY_UNIVERSAL' => '通投智选',
        'INVENTORY_BEAUTY' => '轻颜相机',
        'INVENTORY_PIPIXIA' => '皮皮虾',
        'INVENTORY_AUTOMOBILE' => '懂车帝',
        'INVENTORY_STUDY' => '好好学习',
        'INVENTORY_FACE_U' => 'faceu',
        'INVENTORY_TOMATO_NOVEL' => '番茄小说',
    ];

    const JRTT_RESOURSE_LV2_MAP_V2 = [
        'INVENTORY_FEED' => '头条信息流',
        'INVENTORY_TEXT_LINK' => '头条文章详情页（已废弃）',
        'INVENTORY_VIDEO_FEED' => '西瓜信息流',
        'INVENTORY_HOTSOON_FEED' => '火山信息流',
        'INVENTORY_AWEME_FEED' => '抖音信息流',
        'INVENTORY_UNION_SLOT' => '穿山甲',
        'UNION_BOUTIQUE_GAME' => 'ohayoo精品游戏',
        'INVENTORY_UNION_SPLASH_SLOT' => '穿山甲开屏广告',
        'INVENTORY_SEARCH' => '搜索广告',
        'INVENTORY_UNIVERSAL' => '通投智选',
        'INVENTORY_BEAUTY' => '轻颜相机',
        'INVENTORY_PIPIXIA' => '皮皮虾',
        'INVENTORY_AUTOMOBILE' => '懂车帝',
        'INVENTORY_STUDY' => '好好学习',
        'INVENTORY_FACE_U' => 'faceu',
        'INVENTORY_TOMATO_NOVEL' => '番茄小说',
    ];

    const GDT_RESOURSE_LV1_SET = [
        '广点通',
    ];

    const GDT_RESOURSE_LV2_SET = [
        'XS',
        'XQ',
        '优量汇',
        '其他',
        '应用宝',
    ];

    const GDT_RESOURSE_LV3_MAP = [
        'SITE_SET_TENCENT_NEWS' => ['广点通', 'XS', '腾讯新闻'],
        'SITE_SET_TENCENT_VIDEO' => ['广点通', 'XS', '腾讯视频'],
        'SITE_SET_QQCOM' => ['广点通', 'XS', '腾讯网'],
        'SITE_SET_QQCLIENT' => ['广点通', 'XQ', 'QQ客户端'],
        'SITE_SET_QZONE' => ['广点通', 'XQ', 'QQ空间'],
        'SITE_SET_KANDIAN' => ['广点通', 'XQ', 'QQ看点'],
        'SITE_SET_MUSIC' => ['广点通', 'XQ', 'QQ音乐'],
        'SITE_SET_TENCENT_KUAIBAO' => ['广点通', 'XQ', '天天快报'],
        'SITE_SET_QQ_MUSIC_GAME' => ['广点通', 'XQ', 'QQ、腾讯音乐及游戏'],
        'SITE_SET_MOBILE_INNER' => ['广点通', 'XQ', '移动内部站点，移动站点'],
        'SITE_SET_MINI_GAME_QQ' => ['广点通', 'XQ', '手机 QQ 生态内的小游戏场景'],
        'SITE_SET_KUAISHOU' => ['广点通', '优量汇', '快手'],
        'SITE_SET_MOBILE_GAME' => ['广点通', '优量汇', '集合腾讯游戏和优量汇联盟生态的手机端游戏'],
        'SITE_SET_MOBILE_UNION' => ['广点通', '优量汇', '优量汇等其他资源，非常多'],
        'SITE_SET_MINI_GAME_WECHAT' => ['广点通', '其他', '微信生态内的小游戏场景'],
        'SITE_SET_WECHAT' => ['广点通', '其他', '微信，移动站点'],
        'SITE_SET_MOBILE_YYB' => ['广点通', '应用宝', '应用宝'],
        'SITE_SET_MOBILE_MYAPP' => ['广点通', '应用宝', '应用宝移动，移动站点'],
        'SITE_SET_MOMENTS'      => ['微信', '朋友圈', '朋友圈-大投放'],

        //找不到资源位，按照日志补充，咨询PM
        'SITE_SET_SEARCH_SCENE' => ['广点通', '搜索场景', '搜索场景'],
        'SITE_SET_CHANNELS' => ['广点通', '微信视频号', '微信视频号'],
        'SITE_SET_WECHAT_PLUGIN' => ['广点通', '微信新闻插件', '微信新闻插件'],
    ];

    const PYQ_RESOURSE_LV3_MAP = [
        'yk_xxl_pyq' => ['微信', '朋友圈', '朋友圈直投'],
        'yk_dtf_gdt' => ['微信', '朋友圈', '朋友圈大投放'],
        'yk_gzh_pyq' => ['微信', '公众号', '公众号直投'],
        'yk_gzh_gdt' => ['微信', '公众号', '朋友圈直投'],
    ];

    /**
    1:百度信息流
    2:贴吧信息流
    4:百青藤移动
    8:好看视频（好看视频流量目前在实验阶段，仅限已开通该流量的账户使用）
    64:百度小说
    128:百青藤计算机
    32: 爱奇艺（爱奇艺流量目前在实验阶段，仅限已开通该流量的账户使用） 65536: 信息流 131072:插屏和横幅 262144:app开屏 524288:激励视频
    注意： 百度信息流、好看视频、贴吧信息流属于“自定义”类 “自定义”、“百青藤移动”、“百青藤计算机”、“优选流量”不可以同时选择。
    已下线投放流量的单元投放可继续投放，但该单元不可编辑。
     *
     */

    const BDXXL_RESOURSE_LV1_MAP = [
        0     => '不限',
        1     => '百度信息流',
        2     => '贴吧信息流',
        4     => '百青藤移动',
        8     => '好看视频',
        64    => '百度小说',
        128   => '百青藤计算机',

    ];

    const BDXXL_RESOURSE_LV2_MAP = [
        0     => '不限',
        1     => '列表页',
        2     => '详情页',
    ];

    /**
    1：优选广告位；
    2：按场景选择广告位-信息流广告（旧广告位，包含上下滑大屏广告）；
    3：视频播放页广告-便利贴广告；
    6：上下滑大屏广告；
    7：信息流广告（不包含上下滑大屏广告）
     */

    const KUAISHOU_RESOURSE_LV1_MAP = [
        1 => '优选广告位',
        2 => '按场景选择广告位-信息流广告',
        3 => '视频播放页广告-便利贴广告',
        6 => '上下滑大屏广告',
        7 => '信息流广告',
    ];

}