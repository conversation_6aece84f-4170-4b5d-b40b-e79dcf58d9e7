<?php
/**
 * Created by IntelliJ IDEA.
 * @Description 静态常量
 * @Date 2020/9/14 11:07 上午
 * @<NAME_EMAIL>
 */

class Qdlib_Const_Static
{
    //sku接口类别  -- category字段
    const SKU_CATEGORY_NORMAL   = 0;    //普通接口
    const SKU_CATEGORY_OMS      = 1;    //oms接口

    const ACCOUNT_RUN_MODE_AD_CREATE    = 2;    //自投放

    const LAST_FROM_CATCH_KEY = 's:toufang:lastfrom:info:%s';

    protected static $objSkuConf;

    public static function getSkuConfObj()
    {
        if (empty(self::$objSkuConf)) {
            self::$objSkuConf = new Qdlib_Ds_SkuidConf();
        }
        return self::$objSkuConf;
    }

    /**
     * 获取oms完整lastfrom
     * @param $lastfrom string lastfrom前四级
     * @param $skuId    int skuId
     * @return bool|string
     */
    public static function getOmsLastfrom($lastfrom, $skuId)
    {
        if (empty($lastfrom) || empty($skuId)) {
            return false;
        }
        $db = Qdlib_Util_DB::getQudaoDb();

        $sql = sprintf("select p.id,p.frPrefix,p.plan_num_min,p.plan_num_max,p.project_lv1,p.project_lv2 as num from tblSkuidConf c inner join tblQudaoPromotePlan p on c.id=p.plan_num_min where c.category=%d and JSON_CONTAINS(c.skuid_list, '[%d]') and p.frPrefix='%s' and p.project_lv1=c.project_lv1 and p.attr_page_type='type_oms' order by p.id asc", self::SKU_CATEGORY_OMS, $skuId, $lastfrom);

        $ret = $db->query($sql);
        if (empty($ret)) {
            Qdlib_Util_Log::warning('oms', 'getOmsLastfrom', 'getSkuidConfList', '未获取到oms sku接口组', json_encode(compact('sql', 'ret')));
            return $lastfrom;
        }
        $lastfrom = $ret[0];
        return sprintf('%s_%02d', $lastfrom['frPrefix'], $lastfrom['plan_num_min']);
    }

    /**
     * 获取lastfrom信息
     * @param string $lastfrom
     * @return array|bool|mixed
     */
    public static function getLastFrom($lastfrom)
    {
        if (empty($lastfrom)) {
            return false;
        }
        $lArr = explode('_', $lastfrom);
        $cateLv5 = array_pop($lArr);
        return self::getLastFromByFrPrefix(implode('_', $lArr), $cateLv5);
    }

    /**
     * 获取lastfrom信息
     * @param string $frPrefix lastfrom前4级
     * @param string $cateLv5 lastfrom第5级
     * @return array|mixed
     */
    public static function getLastFromByFrPrefix($frPrefix, $cateLv5 = '')
    {
        $key = sprintf(self::LAST_FROM_CATCH_KEY, $frPrefix);
        $redis = Qdlib_Util_Cache::getQudaoRedis();
        $ret = $redis->get($key);
        if (empty($ret)) {
            $list = self::cacheFrPrefix($frPrefix);
        } else {
            $list = json_decode($ret, true);
        }
        if (empty($list)) {
            return [];
        }
        $num = intval($cateLv5);
        foreach ($list as $val) {
            if ($num >= $val['planNumMin'] && $num <= $val['planNumMax']) {
                return $val;
            }
        }
        return [];
    }

    /**
     * 缓存lastfrom信息
     * @param string $frPrefix lastfrom前4级
     * @param array $row 强制缓存数据（防止主从延迟）
     * @return array|bool|false
     */
    public static function cacheFrPrefix($frPrefix, $row = [])
    {
        $where = array(
            'frPrefix'      => $frPrefix,
            'isRun'         => 0,
        );
        $list = Qdlib_Const_Auth::getPromotePlanObj()->getPromotePlanAll($where);
        if (empty($list)) {
            return false;
        }
        $ids = array_column($list, 'id');
        if (!in_array($row['id'], $ids)) {
            $list[] = $row;
        }
        $key = sprintf(self::LAST_FROM_CATCH_KEY, $frPrefix);
        $redis = Qdlib_Util_Cache::getQudaoRedis();
        $redis->set($key, json_encode($list), 3600 * 24 * 7);
        return $list;
    }

    /**
     * 清除lastfrom缓存
     * @param string $frPrefix lastfrom前4级
     * @return int
     */
    public static function clearPrefixCache($frPrefix)
    {
        $key = sprintf(self::LAST_FROM_CATCH_KEY, $frPrefix);
        $redis = Qdlib_Util_Cache::getQudaoRedis();
        return $redis->del($key);
    }
}