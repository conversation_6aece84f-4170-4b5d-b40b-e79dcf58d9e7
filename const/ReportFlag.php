<?php
/**
 * Created by PhpStorm.
 * @file Flag.php
 * <AUTHOR>
 * @date 19-3-28
 * @version
 * @brief
 *
 **/


class Qdlib_Const_ReportFlag
{

    const FLAG_LEVEL_DAY_CHANNEL = 'day_channel';
    const FLAG_LEVEL_DAY_ACCOUNT = 'day_account';
    const FLAG_LEVEL_DAY_CAMPAIGN = 'day_campaign';
    const FLAG_LEVEL_DAY_AD = 'day_ad';
    const FLAG_LEVEL_DAY_GROUP = 'day_group';
    const FLAG_LEVEL_DAY_CREATIVE = 'day_creative';

    const FLAG_LEVEL_HOUR_CHANNEL = 'hour_channel';
    const FLAG_LEVEL_HOUR_ACCOUNT = 'hour_account';
    const FLAG_LEVEL_HOUR_CAMPAIGN = 'hour_campaign';
    const FLAG_LEVEL_HOUR_AD = 'hour_ad';
    const FLAG_LEVEL_HOUR_GROUP = 'hour_group';
    const FLAG_LEVEL_HOUR_CREATIVE = 'hour_creative';

    static $flagMap = array(
        self::FLAG_LEVEL_DAY_CHANNEL => 'qd_channel_day',
        self::FLAG_LEVEL_DAY_ACCOUNT => 'qd_account_day',
        self::FLAG_LEVEL_DAY_CAMPAIGN => 'qd_campaignid_day',
        self::FLAG_LEVEL_DAY_AD => 'qd_adid_day',
        self::FLAG_LEVEL_DAY_GROUP => 'qd_groupid_day',
        self::FLAG_LEVEL_DAY_CREATIVE => 'qd_creativeid_day',

        self::FLAG_LEVEL_HOUR_CHANNEL => 'qd_channel_hour',
        self::FLAG_LEVEL_HOUR_ACCOUNT => 'qd_account_hour',
        self::FLAG_LEVEL_HOUR_CAMPAIGN => 'qd_campaignid_hour',
        self::FLAG_LEVEL_HOUR_AD => 'qd_adid_hour',
        self::FLAG_LEVEL_HOUR_GROUP => 'qd_groupid_hour',
        self::FLAG_LEVEL_HOUR_CREATIVE => 'qd_creativeid_hour',

    );
}