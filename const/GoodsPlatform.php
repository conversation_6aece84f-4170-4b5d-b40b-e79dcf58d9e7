<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   GoodsPlatform.php
 * <AUTHOR>
 * @date   2019/5/11 上午10:19
 * @brief  商品平台
 **/

class Zb_Const_GoodsPlatform {

    #业务方ID
    const PRODUCT_COURSE_APP_ID = 1; //一课
    const PRODUCT_HX_APP_ID     = 2; //浣熊
	const PRODUCT_SCORE_APP_ID  = 3; //积分商城
	const PRODUCT_YAYAXIZI_APP_ID = 101; //鸭鸭写字
	const PRODUCT_YAYAYINGYU_APP_ID = 102; //鸭鸭英语
	const PRODUCT_YAYAYUWEN_APP_ID = 103; //鸭鸭语文
	const PRODUCT_MALL_APP_ID   = 5; //商城
	const PRODUCT_NEIBUPEIXUN_APP_ID   = 111; //内部培训辅导


    public static $appIdMap = [
        self::PRODUCT_COURSE_APP_ID     => '一课',
        self::PRODUCT_HX_APP_ID         => '浣熊',
	    self::PRODUCT_SCORE_APP_ID      => '积分商城',
		self::PRODUCT_YAYAXIZI_APP_ID   => '鸭鸭写字',
		self::PRODUCT_YAYAYINGYU_APP_ID => '鸭鸭英语',
        self::PRODUCT_YAYAYUWEN_APP_ID  => '鸭鸭语文',
	    self::PRODUCT_MALL_APP_ID       => '商城',
        self::PRODUCT_NEIBUPEIXUN_APP_ID=> '培训辅导',
    ];

    #商品是否组合商品
    const COMBINATION_TYPE_NO          = 1;
    const COMBINATION_TYPE_YES         = 2;
    public static $combinationTypeMap = [
    	self::COMBINATION_TYPE_NO  => "非组合",
    	self::COMBINATION_TYPE_YES => "组合",
    ];

    #sku 是否预售
    const  PRESELL_STATUS_YES         = 152003;
    const  PRESELL_STATUS_NO          = 152004;
    public static $presellStatusMap = [
        self::PRESELL_STATUS_YES  => "预售",
        self::PRESELL_STATUS_NO   => "非预售",
    ];

    #spu上下架状态
    const SPU_ONLINE_STATUS  = 1;
    const SPU_OFFLINE_STATUS = 2; 
    const SPU_PART_ONLINE_STATUS = 3;
    
    public static $spuStatusMap = [
        self::SPU_ONLINE_STATUS   => '上架',
        self::SPU_OFFLINE_STATUS  => '下架',
        self::SPU_PART_ONLINE_STATUS  => '部分上架',  //只spu下部分商品上架 | spu 属于下架状态
    ];

    #sku上下架状态
	const SKU_ONLINE_STATUS  = 1;
	const SKU_OFFLINE_STATUS = 0;

	public static $skuStatusMap = [
		self::SKU_ONLINE_STATUS   => '上架',
		self::SKU_OFFLINE_STATUS  => '下架',
	];

    #内外部
	const IS_INNER_STATUS = 1;
	const IS_OUTSIDE_STATUS = 0;

	public static $goodsStatusMap = [
		self::IS_INNER_STATUS   => "内部",
		self::IS_OUTSIDE_STATUS => "外部"
	];

	#货币类型
	const RMB_CURRENCY     = 1;
	const CREDIT_CURRENCY  = 2;
	const BB_CURRENCY      = 3;

	public static $currencyMap = [
		self::RMB_CURRENCY    => '人民币',
		self::CREDIT_CURRENCY => '学分',
		self::BB_CURRENCY     => '帮帮币',
	];

    #寄送类型
	const SEND_TIME_TYPE_RELATIVE = 1;   // 绝对时间
	const SEND_TIME_TYPE_ABSOLUTELY = 2; // 相对时间

    //商品软删除操作
    const GOODS_DELETE_YES      = 1;    //删除
    const GOODS_DELETE_NOT      = 0;    //正常

	public static $sendTimeType = [
		self::SEND_TIME_TYPE_RELATIVE   => "绝对时间",
		self::SEND_TIME_TYPE_ABSOLUTELY => "相对时间",
	];

	#服务 | 履约项
	const YK_COURSE_TYPE  = 1; //中台课程
	const HX_COURSE_TYPE  = 2; //浣熊课程
	const PF_ENTITY_TYPE  = 3; //中台实物
	const YK_PRIVILEGE_TYPE= 4; //一课特权
	const YK_PENDANT_TYPE = 5; //一课挂件
	const YK_COUPON_TYPE  = 6; //一课优惠券
	const QQ_COIN_TYPE    = 7; //Q币充值
	const PHONE_BILL_TYPE = 8; //话费充值
	const NO_TYPE         = 9; //无须履约项 | 第三方优惠券
	const YK_PET          = 10;//一课宠物
	const STATIONERY_COUPON = 11;//文具券
	const ONLY_COACH_COUPON = 12;//1对1辅导券
	const ONLY_ZYB_VIP = 14;//作业帮VIP


	public static $channelInfoMap = [
		self::YK_COURSE_TYPE => [
			"type"        => self::YK_COURSE_TYPE,
			"channelName" => "中台课程",
			"checkRuleInfo"=>[]
		],
		self::HX_COURSE_TYPE => [
			"type"        => self::HX_COURSE_TYPE,
			"channelName" => "浣熊课程",
			"checkRuleInfo"=>[]
		],
		self::PF_ENTITY_TYPE => [
			"type"        => self::PF_ENTITY_TYPE,
			"channelName" => "中台实物",
			"checkRuleInfo"=>[
				[
					"key"		  => "userName",
					"isMust"	  => 1, // 1必填；0非必填
					"checkRule"   => "",
				],
				[
					"key"		  => "phoneNumber",
					"isMust"	  => 1, // 1必填；0非必填
					"checkRule"   => "/^(11[156]|13[0-9]|14[156789]|15[012356789]|16[2567]|17[01345678]|18[0-9]|19[13589])\d{8}$/",
				],
				[
					"key"		  => "province",
					"isMust"	  => 1, // 1必填；0非必填
					"checkRule"   => "",
				],
				[
					"key"		  => "city",
					"isMust"	  => 1, // 1必填；0非必填
					"checkRule"   => "",
				],
				[
					"key"		  => "prefecture",
					"isMust"	  => 1, // 1必填；0非必填
					"checkRule"   => "",
				],
				[
					"key"		  => "town",
					"isMust"	  => 1, // 1必填；0非必填
					"checkRule"   => "",
				],
				[
					"key"		  => "address",
					"isMust"	  => 1, // 1必填；0非必填
					"checkRule"   => "",
				],
			]
		],
		self::YK_PRIVILEGE_TYPE => [
			"type"        => self::YK_PRIVILEGE_TYPE,
			"channelName" => "一课特权",
			"checkRuleInfo"=>[]
		],
		self::YK_PENDANT_TYPE => [
			"type"        => self::YK_PENDANT_TYPE,
			"channelName" => "一课挂件",
			"checkRuleInfo"=>[]
		],
		self::YK_COUPON_TYPE => [
			"type"        => self::YK_COUPON_TYPE,
			"channelName" => "一课优惠券",
			"checkRuleInfo"=>[]
		],
		self::QQ_COIN_TYPE => [
			"type"        => self::QQ_COIN_TYPE,
			"channelName" => "Q币充值",
			"checkRuleInfo"=>[
				[
					"key"		  => "qqNumber",
					"isMust"	  => 1, // 1必填；0非必填
					"checkRule"   => "/^[1-9][0-9]{4,}$/"
				]
			]
		],
		self::PHONE_BILL_TYPE => [
			"type"        => self::PHONE_BILL_TYPE,
			"channelName" => "话费充值",
			"checkRuleInfo"=>[
				[
					"key"		  => "phoneNumber",
					"isMust"	  => 1, // 1必填；0非必填
					"checkRule"   => "/^(11[156]|13[0-9]|14[15678]|15[012356789]|166|17[01345678]|18[0-9]|19[189])\d{8}$/",
				]
			]
		],
		self::NO_TYPE => [
			"type"        => self::NO_TYPE,
			"channelName" => "无需履约项",
			"checkRuleInfo"=>[]
		],
		self::YK_PET => [
			"type"        => self::YK_PET,
			"channelName" => "一课宠物",
			"checkRuleInfo"=>[]
		],
		self::STATIONERY_COUPON => [
			"type"        => self::STATIONERY_COUPON,
			"channelName" => "文具券",
			"checkRuleInfo"=>[]
		],
		self::ONLY_COACH_COUPON => [
			"type"        => self::ONLY_COACH_COUPON,
			"channelName" => "1对1辅导券",
			"checkRuleInfo"=>[]
		],
        self::ONLY_ZYB_VIP => [
            "type"        => self::ONLY_ZYB_VIP,
            "channelName" => "作业帮VIP",
            "checkRuleInfo"=>[]
        ],
	];

	#业务线 & 履约服务项MAP
	public static $sourceChannelMap = array(
		self::PRODUCT_COURSE_APP_ID => [
			self::YK_COURSE_TYPE,
			self::HX_COURSE_TYPE,
			self::PF_ENTITY_TYPE,
            self::NO_TYPE,
		],
		self::PRODUCT_HX_APP_ID    => [
			self::HX_COURSE_TYPE,
			self::PF_ENTITY_TYPE,
		],
		self::PRODUCT_SCORE_APP_ID => [
			self::PF_ENTITY_TYPE,
			self::YK_PRIVILEGE_TYPE,
			self::YK_PENDANT_TYPE,
			self::YK_COUPON_TYPE,
			self::QQ_COIN_TYPE,
			self::PHONE_BILL_TYPE,
			self::NO_TYPE,
			self::YK_PET,
			self::STATIONERY_COUPON,
			self::ONLY_COACH_COUPON,
            self::ONLY_ZYB_VIP
		],

		self::PRODUCT_YAYAXIZI_APP_ID => [
			self::YK_COURSE_TYPE,
			self::PF_ENTITY_TYPE,
		],
		self::PRODUCT_YAYAYINGYU_APP_ID => [
			self::YK_COURSE_TYPE,
            self::PF_ENTITY_TYPE,
		],
		self::PRODUCT_YAYAYUWEN_APP_ID => [
			self::YK_COURSE_TYPE,
            self::PF_ENTITY_TYPE,
		],
        self::PRODUCT_NEIBUPEIXUN_APP_ID=>[
            self::YK_COURSE_TYPE,
            self::PF_ENTITY_TYPE,
        ],

		self::PRODUCT_MALL_APP_ID => [
			self::PF_ENTITY_TYPE
		]
	);

	#对外接口 | 分类聚合属性展示渠道配置
	const ATTRIBUTE_DEFAULT_SHOW_LIST  = 0; //默认值
	const ATTRIBUTE_SHOW_LIST  = 1; //列表页 展示
	const ATTRIBUTE_SHOW_ORDER = 2; //交易履约 展示
	const ATTRIBUTE_SHOW_DETAIL= 3; //详情页 展示
	const ATTRIBUTE_SHOW_DICTIONARY = 4; //数据字典 展示
	const ATTRIBUTE_SHOW_ALL   = 5; //所有渠道 展示
	const ATTRIBUTE_SHOW_NULL  = 6; //所有渠道不 展示

	public static $interfaceListMap = array(
		self::ATTRIBUTE_SHOW_LIST   => '列表页展示',
		self::ATTRIBUTE_SHOW_ORDER  => '交易履约展示',
		self::ATTRIBUTE_SHOW_DETAIL => '详情页展示',
		self::ATTRIBUTE_SHOW_DICTIONARY  => '数据字典展示',
	);

	public static $interfaceCacheList = array(
		self::ATTRIBUTE_DEFAULT_SHOW_LIST => '默认值',
		self::ATTRIBUTE_SHOW_LIST   => '列表页展示',
		self::ATTRIBUTE_SHOW_ORDER  => '交易履约展示',
		self::ATTRIBUTE_SHOW_DETAIL => '详情页展示',
		self::ATTRIBUTE_SHOW_DICTIONARY  => '数据字典展示',
		self::ATTRIBUTE_SHOW_ALL    => '所有渠道展示',
		self::ATTRIBUTE_SHOW_NULL   => '所有渠道不展示',
	);

	const GOODS_SKU_SHOW = 1; //显示
	const GOODS_SKU_HIDE = 2; //隐藏
	public static $goodsSkuIsShowMap = array(
		self::GOODS_SKU_SHOW => "显示",
		self::GOODS_SKU_HIDE => "隐藏",
	);

	//渠道
	const DHM_CHANNEL_TYPE = 2;
	const OMS_CHANNEL_TYPE = 3;
	const RGZB_CHANNEL_TYPE = 4;
	const LX_CHANNEL_TYPE = 5;
	const WX_CHANNEL_TYPE = 6;
	const LEC_CHANNEL_TYPE = 7;
	const OL_CHANNEL_TYPE  = 8;
	const OFFLINE_SALE_CHANNEL_TYPE  = 9;
    const OL_CHANNEL_OFFLINE = 10; //OL异业
    const OFFLINE_SALE_K12_LX_CHANNEL_TYPE = 11; // 线下分销-K12拉新
    const OFFLINE_SALE_K12_DG_CHANNEL_TYPE = 12; // 线下分销-K12对公
    const LX_BUSINESS_CHANNEL_TYPE = 13;         // 拉新-商务渠道
    const ONLINE_SALE_OLD_TAKE_NEW = 14;         // 线上分销-老带新
    const RGZB_SALE_K12_LX_CHANNEL_TYPE = 15;   // 人工转单-K12低价课
    const ONLINE_SALE_PRIMARY_OLD_TAKE_NEW_BIZ_TYPE = 16;   // 线上分销-小学老带新运营策略组
    const KEFU_SENT_TYPE = 17;   // 客服赠送
    const RGZB_SALE_K12_PUBLIC_CHANNEL_TYPE = 18;   // 人工转单-k12对公
    const NEW_MEDIA_CHANNEL_TYPE = 19; // 新媒体创新生态
    const BRAND_SPREAD_CHANNEL_TYPE = 20; // 品牌传播组
    const DX_LX_KK_CHANNEL_TYPE = 21; // 督学拉新扩科渠道
    const DISTRIBUTOR_CHANNEL_TYPE = 22; // 经销商售卖
    const INTERNAL_PROCUREMENT_CHANNEL_TYPE = 23; // 内部采购
    const HIGH_TRAFFIC_PULL_NEW_BUSINESS_CHANNEL_TYPE = 28; // 高中流量运营-拉新业务
    const KOC_OPERATION_GROUP_CHANNEL_TYPE = 29; // 有赞小店（KOC运营）

	const COMMON_CHANNEL_TYPE = 99;
	const RESERVE_CHANNEL_TYPE = 10000; //预留库存渠道
    const PRE_SELL_CHANNEL_TYPE = 10001; //预售渠道
	public static $channelMap = [
		self::DHM_CHANNEL_TYPE      => "兑换码渠道",
		self::OMS_CHANNEL_TYPE      => "oms对接渠道",
		self::RGZB_CHANNEL_TYPE     => "人工转单渠道",
		self::LX_CHANNEL_TYPE       => "拉新渠道",
		self::WX_CHANNEL_TYPE       => "微信生态",
		self::LEC_CHANNEL_TYPE      => "LEC",
		self::OL_CHANNEL_TYPE       => "OL",
		self::COMMON_CHANNEL_TYPE   => "普通渠道",
		self::RESERVE_CHANNEL_TYPE  => "预留库存",
        self::OFFLINE_SALE_CHANNEL_TYPE => '线下分销-K12低价课',
        self::OL_CHANNEL_OFFLINE => 'OL异业',
        self::OFFLINE_SALE_K12_LX_CHANNEL_TYPE => '线下分销-K12拉新',
        self::OFFLINE_SALE_K12_DG_CHANNEL_TYPE => '线下分销-K12对公',
        self::LX_BUSINESS_CHANNEL_TYPE => '拉新-商务渠道',
        self::ONLINE_SALE_OLD_TAKE_NEW => '线上分销-老带新',
        self::RGZB_SALE_K12_LX_CHANNEL_TYPE => '人工转单-K12低价课',
        self::ONLINE_SALE_PRIMARY_OLD_TAKE_NEW_BIZ_TYPE => '线上分销-小学老带新运营策略组',
        self::KEFU_SENT_TYPE => '客服赠送',
        self::RGZB_SALE_K12_PUBLIC_CHANNEL_TYPE => '人工转单-k12对公',
        self::NEW_MEDIA_CHANNEL_TYPE => '新媒体创新生态',
        self::BRAND_SPREAD_CHANNEL_TYPE => '品牌传播组',
        self::DX_LX_KK_CHANNEL_TYPE => '督学拉新扩科渠道',
        self::DISTRIBUTOR_CHANNEL_TYPE => '经销商售卖',
        self::INTERNAL_PROCUREMENT_CHANNEL_TYPE => '内部采购',
        self::HIGH_TRAFFIC_PULL_NEW_BUSINESS_CHANNEL_TYPE => '高中流量运营-拉新业务',
        self::KOC_OPERATION_GROUP_CHANNEL_TYPE => '有赞小店（KOC运营）',
	];
    // 特殊渠道
    public static $specialChannelMap = [
        self::RESERVE_CHANNEL_TYPE  => "预留库存",
        self::PRE_SELL_CHANNEL_TYPE => '预售库存',
    ];
}

