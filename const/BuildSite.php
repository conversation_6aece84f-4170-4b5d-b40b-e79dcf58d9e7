<?php
/**
 * Created by IntelliJ IDEA.
 * User: <EMAIL>
 * Date: 2019/7/18
 * Time: 2:33 PM
 */
class Qdlib_Const_BuildSite
{
    private static $privateKey = '7lP8lZFx';
    // 加密字符串
    private static $strbase = '7ad8k9w6olxybqvj2gsf5zc0u1mh4ipe3rtn';
    private static $key = 2543.5415412812;
    private static $codeLen = '7ad8k9w6';
    private static $codeNums = 'olxybqvj2g';
    private static $codeExt = 'sf5zc0u1mh4ipe3rtn';
    private static $length = 8;

    protected static $objSitePage = null;
    protected static $objPublicUrl = null;

    const SHARE_INFO_REDIS_KEY      = 's:site_page_share_info:%s';
    const ADDRESS_INFO_REDIS_KEY    = 's:site_page_address_info:%s';
    const SHARE_ADDRESS_INFO_REDIS_KEY    = 's:site_page_share_address_info:%s';
    const CLOSE_STATUS_REDIS_KEY    = 's:site_page_close_status:%s';
    const WHITE_CITY_LIST_REDIS_KEY = 's:site_page_white_city_list:%s';

    const MAX_LENGTH = 5;

    const ACT_TYPE_BUILD            = 1;                //执行编译
    const ACT_TYPE_TIPS             = 2;                //发布到tips
    const ACT_TYPE_ONLINE           = 3;                //发布到线上
    const ACT_TYPE_TEST             = 4;                //测试

    public static $sitePageActType = array(
        self::ACT_TYPE_BUILD         => 'user_page_build',
        self::ACT_TYPE_TIPS          => 'user_page_tips',
        self::ACT_TYPE_ONLINE        => 'user_page_online',
        self::ACT_TYPE_TEST          => 'user_page_test',
    );

    public static $actPublishTypeMap = array(
        self::ACT_TYPE_BUILD => 0,
        self::ACT_TYPE_TIPS => 1,
        self::ACT_TYPE_ONLINE => 2,
        self::ACT_TYPE_TEST => 3,
    );

    public static function getPublishType($actType)
    {
        if (!isset(self::$actPublishTypeMap[$actType])) {
            return false;
        }
        return self::$actPublishTypeMap[$actType];
    }

    public static function getActType($actType)
    {
        return self::$sitePageActType[$actType];
    }

    //发布状态 10未发布 20发布中 30发布失败 40已发布
    const SITE_PAGE_PUBLISH_STATUS_NO_PUB       = 10;
    const SITE_PAGE_PUBLISH_STATUS_PUBLISHING   = 20;
    const SITE_PAGE_PUBLISH_STATUS_PUB_ERROR    = 30;
    const SITE_PAGE_PUBLISH_STATUS_PUB_SUCCESS  = 40;
    public static $sitePagePublishStatus = array(
        self::SITE_PAGE_PUBLISH_STATUS_NO_PUB       => '未发布',
        self::SITE_PAGE_PUBLISH_STATUS_PUBLISHING   => '发布中',
        self::SITE_PAGE_PUBLISH_STATUS_PUB_ERROR    => '发布失败',
        self::SITE_PAGE_PUBLISH_STATUS_PUB_SUCCESS  => '已发布',
    );


    // 上线类型 0发布 1回滚
    const ONLINE_TYPE_PUBLISH   = 0;
    const ONLINE_TYPE_ROLLBACK  = 1;

    //上线任务进行中状态
    public static $runningStatus = array(
        self::ONLINE_STATUS_TEST_RUNNING,
        self::ONLINE_STATUS_TIPS_RUNNING,
        self::ONLINE_STATUS_ONLINE_RUNNING,
        self::ONLINE_STATUS_BUILDING,
    );

    // 上线状态
    //编译
    const ONLINE_STATUS_BUILDING            = 10;
    const ONLINE_STATUS_BUILD_FAILED        = 20;
    const ONLINE_STATUS_BUILD_SUCCESS       = 30;
    //tips
    const ONLINE_STATUS_TIPS_RUNNING        = 40;
    const ONLINE_STATUS_TIPS_FAILED         = 50;
    const ONLINE_STATUS_TIPS_SUCCESS        = 60;
    //测试
    const ONLINE_STATUS_TEST_RUNNING       = 64;
    const ONLINE_STATUS_TEST_FAILED        = 65;
    const ONLINE_STATUS_TEST_SUCCESS       = 66;
    //全量上线
    const ONLINE_STATUS_ONLINE_RUNNING      = 70;
    const ONLINE_STATUS_ONLINE_FAILED       = 80;
    const ONLINE_STATUS_ONLINE_SUCCESS      = 90;

    //小程序发布状态
    const XCX_STATUS_ONLINE_VALID           = 0;
    const XCX_STATUS_ONLINE_PUBLISHING      = 1;
    //步骤状态
    const STEP_STATUS_NOT_RUN       = 0;
    const STEP_STATUS_RUNNING       = 10;
    const STEP_STATUS_FAILED        = 20;
    const STEP_STATUS_SUCCESS       = 30;

    //投放状态 10未投 20在投 30停投
    const SITE_PAGE_ADVERTISE_STATUS_NO_ADVERTISE   = 10;
    const SITE_PAGE_ADVERTISE_STATUS_ADVERTISED     = 20;
    const SITE_PAGE_ADVERTISE_STATUS_STOPPED        = 30;

    //学部
    public static $grade = array(
        Qdlib_Const_ProjectAttr::FACULTY_OTHER => '其他',
        1 => '小学',
        20 => '初中',
        30 => '高中',//答疑，直播
        70 => '成人'//成人
    );

    //学科
    public static $subject = array(
        2 => '数学',
        1 => '语文',
        3 => '英语',
        4 => '物理',
        5 => '化学',
        6 => '生物',
    );

    const CLASS_TYPE_TEST = 4;

    public static $classType = array(
        1 => '特惠课',  //特惠课
        2 => '短训班',  //短训班
        3 => '正价课',  //正价课
        self::CLASS_TYPE_TEST => '页面测试',
        22 => '36元短训班',
        23 => '3元短训班',//3元短训班
        33 => '0元短训班',
        31 => '49元双周课',
        32 => '0元体验课',
        24 => '9元短训班',
        25 => '8元短训班',
        34 => '初高0元体验课',//初高0元体验课
        35 => '初高9元短训班',//初高9元短训班
        36 => '初高8元短训班',//初高8元短训班
        37 => '小初0元体验课',//小初0元体验课
        38 => '小初9元短训班',//小初9元短训班
        39 => '小初8元短训班',//小初8元短训班
        40 => '小初高0元体验课',//小初高0元体验课
        41 => '小初高9元短训班',//小初高9元短训班
        42 => '小初高8元短训班',//小初高8元短训班
        43 => '初高29元短训班', //初高29元短训班
        44 => '小初高29元短训班', //小初高29元短训班
        45 => '小初29元短训班',//小初29元短训班
        46 => '初高30元短训班',//初高30元短训班
        47 => '小初高30元短训班', //小初高30元短训班
        48 => '小初30元短训班',//小初30元短训班
        49 => '1元体验课',//1元体验课
//        5 => '体验课',  //体验课   4建站测试
        6 => '表单',    //20201021 表单
    );

    //页面分类
    const SITE_CATEGORY_BUILD      = 1;
    const SITE_CATEGORY_DEVELOP    = 2;
    const SITE_CATEGORY_MERGE      = 3;
    public static $siteCategory = array(
        self::SITE_CATEGORY_BUILD      => '建站页面',
        self::SITE_CATEGORY_DEVELOP    => '开发页面',
        self::SITE_CATEGORY_MERGE      => '三合一页面',
    );

    //页面类型
    const PAGE_TYPE_XCX = 'type_xcx';
    const PAGE_TYPE_H5  = 'type_page_h5';
    const PAGE_TYPE_APP = 'type_page_app';
    public static $pageType = array(
        self::PAGE_TYPE_H5          => 'H5页面',
        self::PAGE_TYPE_XCX         => '小程序',
        self::PAGE_TYPE_APP         => 'APP',
    );

    //用于冗余字段(sitePageType)
    const SITE_PAGE_TYPE_CPS    = 'type_cps';
    const SITE_PAGE_TYPE_WX     = 'type_wx';
    public static $sitePageType = array(
        self::PAGE_TYPE_H5          => 'H5页面',
        self::PAGE_TYPE_XCX         => '小程序',
        self::SITE_PAGE_TYPE_CPS    => 'CPS页面',
        self::SITE_PAGE_TYPE_WX     => '微信生态页面',
        self::PAGE_TYPE_APP         => 'APP',
    );

    //页面用途
    const PAGE_USE_NORMAL   = 1;
    const PAGE_USE_CPS      = 2;
    const PAGE_USE_WX       = 3;
    const PAGE_USE_ZERO     = 4; // 0转正
    public static $useType = array(
        //self::PAGE_USE_NORMAL       => 'H5页面', //普通页面
        self::PAGE_USE_NORMAL       => '端外投放页面', //普通页面
        self::PAGE_USE_CPS          => 'CPS页面',
        self::PAGE_USE_WX           => '微信生态页面',
        self::PAGE_USE_ZERO         => '0转正页面',
    );
    public static $pageUseType = array(
        self::PAGE_USE_CPS      => self::SITE_PAGE_TYPE_CPS,
        self::PAGE_USE_WX       => self::SITE_PAGE_TYPE_WX,
    );

    //课程投放类型
    const CLASS_PUT_CLASS   = 1;
    const CLASS_PUT_ZERO    = 2;
    const CLASS_PUT_APP     = 3;
    const CLASS_PUT_GOODS   = 4;
    public static $classPut = array(
        self::CLASS_PUT_CLASS       => '课程投放',
        self::CLASS_PUT_ZERO        => '“0转正”投放',
        self::CLASS_PUT_APP         => 'APP推广',
        self::CLASS_PUT_GOODS       => '商品投放',
    );

    //设备类型
    const DEVICE_TYPE_IOS       = 1;
    const DEVICE_TYPE_ANDROID   = 2;

    public static $deviceType = array(
        self::DEVICE_TYPE_IOS       => 'IOS',
        self::DEVICE_TYPE_ANDROID   => '安卓',
    );

    /**
     * 获取sitePageType的值
     * @param $pageType
     * @param int $useType
     * @return mixed
     */
    public static function getSitePageType($pageType, $useType = self::PAGE_USE_NORMAL)
    {
        if ($useType == self::PAGE_USE_NORMAL) {
            return $pageType;
        }
        return self::$pageUseType[$pageType];
    }

    /**
     * 获取建站页面id
     * @param $pageId
     * @return bool|string
     */
    public static function getPageIdStr($pageId)
    {
        return self::encode($pageId);
    }

    /**
     * 获取页面id
     * @param $pageIdStr
     * @return string
     */
    public static function getPageId($pageIdStr)
    {
        return self::decode($pageIdStr);
    }

    //解码
    public static function decode($code){
        if (empty($code)) {
            return '';
        }
        $begin = substr($code,0,1);
        $rtn = '';
        $len = strpos(self::$codeLen, $begin);
        if($len!== false){
            $len++;
            $arrnums = str_split(substr($code,-$len));
            foreach ($arrnums as $v) {
                $rtn .= strpos(self::$codeNums,$v);
            }
        }

        return $rtn;
    }

    // 编码
    public static function encode($nums){
        $rtn = "";
        $numslen = strlen($nums);
        //密文第一位标记数字的长度
        $begin = substr(self::$codeLen,$numslen - 1,1);
        //密文的扩展位
        $extlen = self::$length - $numslen - 1;
        $temp = str_replace('.', '', $nums / self::$key);
        $temp = substr($temp,-$extlen);
        $arrextTemp = str_split(self::$codeExt);
        $arrext = str_split($temp);
        foreach ($arrext as $v) {
            $rtn .= $arrextTemp[$v];
        }
        $arrnumsTemp = str_split(self::$codeNums);
        $arrnums = str_split($nums);
        foreach ($arrnums as $v) {
            $rtn .= $arrnumsTemp[$v];
        }
        return $begin.$rtn;
    }

    //获取node服务签名
    public static function getNodeSign($pageId)
    {
        return self::base64url_encode(self::$privateKey . '_' . $pageId . '_' . time());
    }

    //签名验证
    public static function checkSign($pageId, $sign)
    {
        $sign = self::base64url_decode($sign);
        if (empty($sign)) {
            return false;
        }
        $signArr = explode('_', $sign);
        if (count($signArr) != 3) {
            return false;
        }
        if ($signArr[0] != self::$privateKey) {
            return false;
        }
        if (time() - intval($signArr[2]) > 600) {  //有效期10分钟
            return false;
        }
        if ($pageId != $signArr[1]) {
            return false;
        }
        return true;
    }

    public static function base64url_encode($data)
    {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }

    public static function base64url_decode($data)
    {
        return base64_decode(str_pad(strtr($data, '-_', '+/'), strlen($data) % 4, '=', STR_PAD_RIGHT));
    }

    //检测线上页面版本
    public static function checkOnlinePage($pageId, $taskId)
    {
        $url = self::getPageUrl($pageId);
        Qdlib_Util_Log::addNotice('checkOnlineSitePage_url', json_encode(['url'=>$url]));
        //$url = 'https://www.zuoyebang.com/static/hy/cornucopia/8mm0llqq.html';
        $ret = '';
        for ($i = 0; $i < 3; $i++) {
            list($ret, $error) = Qdlib_Util_Curl::_get($url, 5);
            if (empty($error)) {
                break;
            }
        }
        if (!empty($error)) {
            Qdlib_Util_Log::warning('site', 'checkOnlineSitePage', '检测线上页面版本curl error', json_encode(compact('ret', 'error', 'pageId', 'taskId')));
            return false;
        } else {
            Qdlib_Util_Log::addNotice('checkOnlineSitePage curl ret', json_encode(compact('ret', 'error', 'pageId', 'taskId')));
        }

//        $ret = explode('<script>', $ret);
//        $ret[5] = '----pageId----kc0xjl2l----taskId----4853----';
//        $ret = implode('<script>', $ret);

        $pattern = '|----pageId----(\w+)----taskId----(\d+)----|i';
        if (!preg_match($pattern, $ret, $matches)) {
            return false;

        }
        if (!is_array($matches) || count($matches) != 3) {
            return false;
        }
        $checkPageId = self::getPageId($matches[1]);
        $checkTaskId = $matches[2];
        if ($pageId != $checkPageId || $checkTaskId != $taskId) {
            Qdlib_Util_Log::warning('site', 'checkOnlineSitePage', '检测线上页面版本,版本异常', json_encode(compact('pageId', 'checkPageId', 'checkTaskId', 'taskId')));
            return false;
        }
        return true;
    }

    /**
     * 获取页面url
     * @param $pageId
     * @return string
     */
    public static function getPageUrl($pageId)
    {
        if (is_numeric($pageId)) {
            $pageId = Qdlib_Const_BuildSite::getPageIdStr($pageId);
        }
        $host = self::getPageHost();
        $base = '%s/static/hy/cornucopia/%s.html';
        return sprintf($base, $host, $pageId);
    }

    public static function getPageIdByUrl($url)
    {
        $pattern = '|/static/hy/cornucopia/([a-z0-9]+).html|i';
        $matches = [];
        $ret = preg_match($pattern, $url, $matches);
        if ($ret === false || count($matches) != 2) {
            return false;
        }
        return $matches[1];
    }

    public static function isUrl($path)
    {
        $pattern = "#(http|https)://(.*\.)?.*\..*#i";
        if (preg_match($pattern, $path)) {
            return true;
        }
        return false;
    }

    //获取测试域名
    public static function getTestHost()
    {
        #return 'http://toufangmis-base-e.suanshubang.com';
        $env = Hk_Util_Env::getRunEnv();
        if($env == Hk_Util_Env::RunEnvTest){
            $referUrl = $_SERVER['HTTP_REFERER'];
            Bd_Log::addNotice("HTTP_REFERER_url",$referUrl);
            $optionsInfo = parse_url($referUrl);
            $host = 'https://'.$optionsInfo['host'];
            return str_replace('.cc','.com',$host);
        }
        $host = 'https://'.Qdlib_Util_Tool::getHttpHost();
        return str_replace('.cc','.com',$host);
    }

    //获取线上页面域名
    public static function getPageHost()
    {
        if (Qdlib_Util_Tool::getEnviron() == 'test') {
            return self::getTestHost();
        }
        return 'https://tf.zuoyebang.com';
    }

    public static function getSitePages($pageType = '', $advertiseStatus = self::SITE_PAGE_ADVERTISE_STATUS_ADVERTISED, $groupId = 0, $publishStatus = self::SITE_PAGE_PUBLISH_STATUS_PUB_SUCCESS, $getAll = false, $arrAppends = [])
    {
        $objSitePage = new Qdlib_Ds_SitePage();

        if (!$getAll) {
            $arrConds['advertiseStatus'] = $advertiseStatus;
            $arrConds['publishStatus'] = $publishStatus;
            $arrConds['isDelete'] = 0;
        }

        if (!empty($groupId)) {
            $arrConds['groupId'] = $groupId;
        }
        if (!empty($pageType)) {
            $arrConds['pageType'] = $pageType;
        }
        $arrConds[] = "publish_time <> 0";
//        $arrConds[] = "project_name <> ''";
        //雨燕后台不读取建站测试页面。
        $conds[] = 'class_type <> 4';
        $objSiteGroup = new Qdlib_Ds_SitePageGroup();
        $posGroupId = $objSiteGroup->getSitePageGroupList($conds, ['id']);
        $posGroupId = array_column($posGroupId, 'id');
        $posGroupId[] = 0;
        $posGroupIdStr = implode(',', $posGroupId);
        $arrConds[] = 'group_id in (' . $posGroupIdStr . ')';

        if (empty($arrAppends)) {
            $arrAppends[] = "order by id desc";
        }

        $list = $objSitePage->getSitePageList($arrConds, ['id', 'pageIdStr', 'pageName', 'groupId', 'charact', 'process', 'xcxName', 'pageUrl', 'projectName', 'pageType','publishTime','businessLines'], $arrAppends);

        return $list;
    }

    public static function getSitePageByIds($pageIds)
    {
        if (empty($pageIds)) {
            return [];
        }
        $objSitePage = new Qdlib_Ds_SitePage();

        $arrConds = array(
            Qdlib_Util_DB::whereIn('page_id_str', $pageIds),
        );

        $arrAppends[] = 'order by id desc';
        $list = $objSitePage->getSitePageList($arrConds, ['id', 'pageIdStr', 'pageName', 'groupId', 'charact', 'process', 'xcxName', 'pageUrl', 'projectName', 'pageType','projectLv1','projectLv2','logId'],$arrAppends);
        if (empty($list)) {
            return [];
        }
        $groupIds = array_diff(array_unique(array_column($list, 'groupId')), [0]);
        if (empty($groupIds)) {
            return $list;
        }
        $groups = self::getSitePageGroups($groupIds);
        $ret = [];
        foreach ($list as $val) {
            $val['pageTitle'] = $val['pageName'];
            $val['pageName'] = $val['projectName'];
            $val['groupInfo'] = $groups[$val['groupId']] ?? [];
            $ret[] = $val;
        }
        return $ret;
    }

    public static function getSitePageGroupNames($groupIds = [])
    {
        if (is_string($groupIds)) {
            $groupIds = explode(',', $groupIds);
        }
        $groupIds = array_unique($groupIds);
        $groupIds = array_diff($groupIds, [0]);
//        if (empty($groupIds)) {
//            return [];
//        }
        $objSiteGroup = new Qdlib_Ds_SitePageGroup();
        $where = [];
        if (!empty($groupIds)) {
            $where[] = 'id in (' . implode(',', $groupIds) .')';
        }
        //雨燕后台不读取建站测试页面。
        $where[] = 'class_type <> 4';
        $arrAppends[] = 'order by id desc';
        $list = $objSiteGroup->getSitePageGroupList($where, ['id', 'name'],$arrAppends);

        if (empty($list)) {
            return [];
        }
        return array_column($list, 'name', 'id');
    }

    public static function getSitePageGroups($groupIds = [])
    {
        if (is_string($groupIds)) {
            $groupIds = explode(',', $groupIds);
        }
        $groupIds = array_unique($groupIds);
        $groupIds = array_diff($groupIds, [0]);
//        if (empty($groupIds)) {
//            return [];
//        }
        $objSiteGroup = new Qdlib_Ds_SitePageGroup();
        $where = [];
        if (!empty($groupIds)) {
            $where[] = 'id in (' . implode(',', $groupIds) . ')';
        }
        //雨燕后台不读取建站测试页面。
        $where[] = 'class_type <> 4';
        $arrAppends[] = 'order by id desc';
        $list = $objSiteGroup->getSitePageGroupList($where,[],$arrAppends);

        if (empty($list)) {
            return [];
        }
        $ret = [];
        foreach ($list as $val) {
            $ret[$val['id']] = self::formatGroupInfo($val);
        }
        return $ret;
    }

    public static function formatGroupInfo($groupInfo)
    {
        if (empty($groupInfo)) {
            return [];
        }
        $groupInfo['classType'] = array('key' => $groupInfo['classType'], 'value' => self::$classType[$groupInfo['classType']] ?? '');
        $grade = explode(',', $groupInfo['grade']);
        $groupInfo['grade'] = [];
        foreach ($grade as $val) {
            if (isset(self::$grade[$val])) {
                $groupInfo['grade'][] = array(
                    'key'       => $val,
                    'value'     => self::$grade[$val],
                );
            }
        }
        $groupInfo['subject'] = array(
            [
                'key'       => $groupInfo['subject'],
                'value'     => Qdlib_Const_LastfromAttr::$attrSubject[$groupInfo['subject']],
            ]
        );
        return $groupInfo;
    }

    public static function getSitePageGroupsV2($groupIds = [])
    {
        if (is_string($groupIds)) {
            $groupIds = explode(',', $groupIds);
        }
        $groupIds = array_unique($groupIds);
        $groupIds = array_diff($groupIds, [0]);
//        if (empty($groupIds)) {
//            return [];
//        }
        $objSiteGroup = new Qdlib_Ds_SitePageGroup();
        $where = [];
        if (!empty($groupIds)) {
            $where[] = 'id in (' . implode(',', $groupIds) . ')';
        }
        //雨燕后台不读取建站测试页面。
        $where[] = 'class_type <> 4';
        $arrAppends[] = 'order by id desc';
        $list = $objSiteGroup->getSitePageGroupList($where,[],$arrAppends);

        if (empty($list)) {
            return [];
        }

        $ret = [];
        foreach ($list as $val) {
            $ret[$val['id']] = $val;
        }
        return $ret;
    }

    public static function getSitePageObj()
    {
        if (is_null(self::$objSitePage)) {
            self::$objSitePage = new Qdlib_Ds_SitePage();
        }
        return self::$objSitePage;
    }

    public static function getPublicUrlObj()
    {
        if (is_null(self::$objPublicUrl)) {
            self::$objPublicUrl = new Qdlib_Ds_SitePagePubUrl();
        }
        return self::$objPublicUrl;
    }

    // 获取页面缓存key
    protected static function getCacheKey($key, $pageIdStr)
    {
        switch ($key) {
            case 'shareInfo':
                $redisKey = self::SHARE_INFO_REDIS_KEY;
                break;
            case 'globalConfigInfo':
                $redisKey = self::ADDRESS_INFO_REDIS_KEY;
                break;
            case 'isClose':
                $redisKey = self::CLOSE_STATUS_REDIS_KEY;
                break;
            case 'cityList':
                $redisKey = self::WHITE_CITY_LIST_REDIS_KEY;
                break;
            default:
                $redisKey = '';
        }
        return sprintf($redisKey, $pageIdStr);
    }

    /**
     * 缓存页面分享、跳转链接信息，默认90天有效期
     * @param $pageIdStr
     * @param array $data
     * @return bool
     */
    public static function cachePageExtData($pageIdStr, $data = [])
    {
        $redis = Qdlib_Util_Cache::getQudaoRedis();
        foreach ($data as $key => $value) {
            $redisKey = self::getCacheKey($key, $pageIdStr);
            $ret = $redis->set($redisKey, $value, 3600 * 24 * 90);
            if (!$ret) {
                return false;
            }
        }
        return true;
    }

    /**
     * db获取分享信息，获取到重写缓存
     * @param $pageIdStr
     * @param $cacheFields
     * @return array
     */
    public static function getPageExtFromDB($pageIdStr, $cacheFields = ['shareInfo', 'globalConfigInfo', 'isClose', 'cityList'])
    {
        $pageId = self::getPageId($pageIdStr);
        $fields = ['id', 'pageIdStr'];
        $fields = array_merge($fields, $cacheFields);
        $info = self::getSitePageObj()->getSitePageOnlineInfoById($pageId, $fields);
        $data = [];
        foreach ($cacheFields as $key) {
            $data[$key] = $info[$key] ?? '';
        }
        $ret = self::cachePageExtData($pageIdStr, $data);
        if (!$ret) {
            Qdlib_Util_Log::error('qudao', 'SitePage', 'getPageExtFromDB', '重写缓存失败', json_encode(['id' => $pageId, 'pageIdStr' => $pageIdStr, 'data' => $data]));
        }
        return $data;
    }

    /**
     * db获取分享信息，获取到重写缓存
     * @param $pageIdStr
     * @return array
     */
    public static function getPublicUrlFromDB($urlId)
    {
        $info = self::getPublicUrlObj()->getSitePagePubUrlInfoById($urlId, ['id', 'pubUrlName', 'pubUrl']);

        $ret = self::cachePublicUrl($info['id'], $info['pubUrl']);
        if (!$ret) {
            Qdlib_Util_Log::error('qudao', 'SitePage', 'getPublicUrlFromDB', '重写缓存失败', json_encode(['id' => $urlId, 'publicUrl' => $info['pubUrl']]));
        }
        return [$info['pubUrl'], $info['pubUrlName']];
    }
    public static function cachePublicUrl($urlId, $url){
        $redis = Qdlib_Util_Cache::getQudaoRedis();
        $shareKey = sprintf(self::SHARE_ADDRESS_INFO_REDIS_KEY, $urlId);
        $ret = $redis->set($shareKey, $url, 3600 * 24 * 90);
        if (!$ret) {
            return false;
        }
    }
    /**
     * @param $pageIdStr
     * @return string $shareInfo 分享信息json
     */
    public static function getPageShareInfo($pageIdStr)
    {
        $redis = Qdlib_Util_Cache::getQudaoRedis();
        $shareKey = sprintf(self::SHARE_INFO_REDIS_KEY, $pageIdStr);
        $shareInfo = $redis->get($shareKey);
        if (empty($shareInfo)) {
            $shareInfo = self::getPageExtFromDB($pageIdStr, ['shareInfo']);
            $shareInfo = $shareInfo['shareInfo'] ?? '';
        }
        return $shareInfo;
    }

    /**
     * @param $pageIdStr
     * @return string $globalConfigInfo 跳转信息json
     */
    public static function getPageglobalConfigInfo($pageIdStr)
    {
        $redis = Qdlib_Util_Cache::getQudaoRedis();
        $addressKey = sprintf(self::ADDRESS_INFO_REDIS_KEY, $pageIdStr);
        $globalConfigInfo = $redis->get($addressKey);
        if (empty($globalConfigInfo)) {
            $globalConfigInfo = self::getPageExtFromDB($pageIdStr, ['globalConfigInfo']);
            $globalConfigInfo = $globalConfigInfo['globalConfigInfo'] ?? '';
        }
        $globalConfigInfoNew = json_decode($globalConfigInfo,true);
        if($globalConfigInfoNew['returnUrl']['isDiyUrl'] === 2){
             $globalConfigInfoNew['returnUrl']['url'] = self::getReturnUrlNew($globalConfigInfoNew['returnUrl']['id']);
             $globalConfigInfo = json_encode($globalConfigInfoNew);
        }
        return $globalConfigInfo;
    }

    public static function getReturnUrlNew($publicUrlId){
        $redis = Qdlib_Util_Cache::getQudaoRedis();
        $publicUrlKey = sprintf(self::SHARE_ADDRESS_INFO_REDIS_KEY, $publicUrlId);
        $publicUrlValue = $redis->get($publicUrlKey);
        if(empty($publicUrlValue)){
            list($publicUrlValue, $urlName) = self::getPublicUrlFromDB($publicUrlId);
        }
        return $publicUrlValue;
    }
    /*
     * lastfrom配置 获取 建站页面，页面组
     */
    public static function dealSitePage($arrParams = [],$arrAppends = NULL)
    {
        //过滤历史测试数据
        $where[] = "project_name <> ''";
        $where[] = "publish_time <> 0";
        $where[] = "channels <> ''";
        $where['useType'] = 1;//普通h5类型页面
        //在投页面
        if ($arrParams['isRun']) {
            $where['advertiseStatus'] = Qdlib_Const_BuildSite::SITE_PAGE_ADVERTISE_STATUS_ADVERTISED;
            $where['publishStatus'] = Qdlib_Const_BuildSite::SITE_PAGE_PUBLISH_STATUS_PUB_SUCCESS;
        }
        if ($arrParams['attrPageType']) {
            $where['pageType'] = $arrParams['attrPageType'];
        }
        if (!empty($arrParams['business'])) {
            $business = $arrParams['business'];
            if(!is_numeric($arrParams['business'])){
                $business = array_flip(Qdlib_Const_LastFrom::$businessMap)[$arrParams['business']];
            }
            $where['business_lines'] = $business;

        }
        if ($arrParams['process']) {
            $where[] = "process like '%" . $arrParams['process'] . "%'";
        }
        if ($arrParams['idStrs']) {
            $where[] = "page_id_str in ('" . $arrParams['idStrs'] . "') or log_id in ('" . $arrParams['idStrs'] . "')";
        }
        //雨燕后台不读取建站测试页面。
        $conds[] = 'class_type <> 4';
        $objSiteGroup = new Qdlib_Ds_SitePageGroup();
        $allgroupInfo = $objSiteGroup->getSitePageGroupList($conds, ['id', 'name','grade','subject','classType']);
        $groupIds = array_column($allgroupInfo, 'id');
        $posGroupIdStr = implode(',', $groupIds);
        //app类型页面无页面组
        $where[] = 'group_id in (' . $posGroupIdStr . ') or group_id = 0';
        //按页面的业务线权限过滤页面组
        $objSitePage = new Qdlib_Ds_SitePage();
        $pageRet = $objSitePage->getSitePageList($where, ['id', 'projectName','xcxName', 'process', 'groupId', 'pageType', 'logId', 'pageIdStr','pageUrl','businessLines','deviceType','projectLv1','projectLv2'],$arrAppends);

        $pagegroupIds = array_unique(array_column($pageRet, 'groupId'));
        $groupInfo = [];
        foreach ($allgroupInfo as $key => $info) {
            if(in_array($info['id'],$pagegroupIds)){
                $groupInfo[] = $info;
            }
        }

        return [$pageRet, $groupInfo];

    }

    public static function getXcxidstrByLogid($logId)
    {
        if (empty($logId)) {
            return "";
        }
        //在投已发布仅1条小程序
        $where = [
            'logId' => trim($logId),
            'pageType' => 'type_xcx',
            'advertiseStatus' => self::SITE_PAGE_ADVERTISE_STATUS_ADVERTISED,
            'publishStatus' => self::SITE_PAGE_PUBLISH_STATUS_PUB_SUCCESS,
        ];
        $objSitePage = new Qdlib_Ds_SitePage();
        $pageRet = $objSitePage->getSitePageList($where, ['pageIdStr']);
        if (!empty($pageRet)) {
            return $pageRet[0]['pageIdStr'];
        } else {
            return "";
        }
    }

    /**
     * 获取缓存信息
     * @param $pageIdStr
     * @param $key
     * @return string
     */
    public static function getCacheInfo($pageIdStr, $key)
    {
        $redis = Qdlib_Util_Cache::getQudaoRedis();
        $redisKey = self::getCacheKey($key, $pageIdStr);
        $info = $redis->get($redisKey);
        if ($info === false) {
            $info = self::getPageExtFromDB($pageIdStr, [$key]); // 读db并更新缓存
            $info = $info[$key] ?? '';
        }
        return $info;
    }

    /**
     * 获取缓存信息（多个key）
     * @param string $pageIdStr
     * @param array $keys  例：['isClose', 'cityList']
     * @return array 例：['isClose' => 0, 'cityList' => []]
     */
    public static function getCacheInfos($pageIdStr, $keys = [])
    {
        $redis = Qdlib_Util_Cache::getQudaoRedis();
        $reCache = false;
        $ret = [];
        foreach ($keys as $key) {
            $redisKey = self::getCacheKey($key, $pageIdStr);
            $ret[$key] = $redis->get($redisKey);
            if ($ret[$key] === false) {
                $reCache = true;
            }
        }
        if (!$reCache) {
            return $ret;
        }
        return self::getPageExtFromDB($pageIdStr, $keys); // 读db并更新缓存
    }

}
