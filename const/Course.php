<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   Course.php
 * <AUTHOR>
 * @date   2017/11/10 下午3:47
 * @brief  课程常量
 **/

class Zb_Const_Course {

    /**
     * 课程类型
     *
     * 注意：公开课、家长课在业务上废弃已久，请不要使用
     */
    const TYPE_PRIVATE              = 0;   // 专题课
    const TYPE_PRIVATE_LONG         = 2;   // 班课
    const TYPE_PRE_LONG             = 4;   // 试听课
    const TYPE_PUBLIC               = 1;   // 公开课（已废弃）
    const TYPE_PARENT_COURSE        = 3;   // 家长课（已废弃）
    const TYPE_BB_CHILD             = 6;   //帮帮英语低幼
    const TYPE_YY_WRITE_CLASS       = 7;   //鸭鸭写字正价课
    const TYPE_YY_WRITE_EXPERIENCE  = 8;   //鸭鸭写字体验课
    const TYPE_YY_ENGLISH_WEEK      = 9;   //鸭鸭英语周课
    const TYPE_YY_ENGLISH_TWO_WEEK  = 10;  //鸭鸭英语双周课
    const TYPE_YY_ENGLISH_YEAR      = 11;  //鸭鸭英语年课
    const TYPE_YY_CHINESE_WEEK      = 14;  //鸭鸭语文周课
    const TYPE_YY_CHINESE_TWO_WEEK  = 15;  //鸭鸭语文双周课
    const TYPE_YY_CHINESE_MONTH     = 16;  //鸭鸭语文月课
    const TYPE_YY_CHINESE_HALF_YEAR = 17;  //鸭鸭语文半年课
    const TYPE_YY_CHINESE_YEAR      = 18;  //鸭鸭语文年课
    const TYPE_SYK = 53;//
    const TYPE_YY_WRITE_HALF = 20;//
    const TYPE_YY_WRITE_HALF_NEXT = 21;
    const TYPE_YY_WRITE_ART = 31;
    const TYPE_YY_BC_TY = 57;
    const TYPE_YY_BC_XT = 58;


    public static $arrTypeMap = array(
        self::TYPE_PRIVATE               => '专题课',
        self::TYPE_PUBLIC                => '公开课',
        self::TYPE_PRIVATE_LONG          => '班课',
        self::TYPE_PARENT_COURSE         => '家长课',
        self::TYPE_PRE_LONG              => '试听课',
        self::TYPE_BB_CHILD              => '帮帮英语低幼',
        self::TYPE_YY_WRITE_CLASS        => '鸭鸭写字系统课',
        self::TYPE_YY_WRITE_EXPERIENCE   => '鸭鸭写字体验课',
        self::TYPE_YY_ENGLISH_WEEK       => '鸭鸭英语周课',
        self::TYPE_YY_ENGLISH_TWO_WEEK   => '鸭鸭英语双周课',
        self::TYPE_YY_ENGLISH_YEAR       => '鸭鸭英语年课',
        self::TYPE_YY_CHINESE_WEEK       => '鸭鸭语文周课',
        self::TYPE_YY_CHINESE_TWO_WEEK   => '鸭鸭语文双周课',
        self::TYPE_YY_CHINESE_MONTH      => '鸭鸭语文月课',
        self::TYPE_YY_CHINESE_HALF_YEAR  => '鸭鸭语文半年课',
        self::TYPE_YY_CHINESE_YEAR       => '鸭鸭语文年课',
        self::TYPE_SYK => '素养课',//
        self::TYPE_YY_WRITE_HALF => '鸭鸭写字上半年课',//'鸭鸭写字上半年课'
        self::TYPE_YY_WRITE_HALF_NEXT => '鸭鸭写字下半年课',//
        self::TYPE_YY_WRITE_ART => '鸭鸭美术系统课',//'鸭鸭美术系统课'
        self::TYPE_YY_BC_TY =>  '小鹿编程体验课',//'小鹿编程体验课'
        self::TYPE_YY_BC_XT => '小鹿编程系统课',//'小鹿编程系统课'
    );

    //是否内部课程
    const INNER_NO  = 0; //外部课程
    const INNER_YES = 1; //内部课程
    public static $arrInnerMap = array(
        self::INNER_NO  => '外部课程',
        self::INNER_YES => '内部课程',
    );

    //课程服务
    const SERVICES_TEACHING_MATERIAL = 19;
    public static $arrServiceMap = array(
        self::SERVICES_TEACHING_MATERIAL => '教材',
    );
	//支付
    const PAY_SOURCE = 'zyb_fudao';
    const PAY_SECRET = 'zybcouselesson';
    //courselessontype定义
    const TYPE_COURSELESSONTYPE_CORE  = 1;//核心课
    const TYPE_COURSELESSONTYPE_OTHER = 2;//提升课

    //配置
    const GLOBAL_GRADE             = 1; //年级
    const GLOBAL_SUBJECT           = 2; //科目
    const GLOBAL_COURSE_SYSTEM     = 3; //体系
    const GLOBAL_CLASS_TYPE        = 4; //班型
    const GLOBAL_BOOK_VER          = 5; //教材版本
    const GLOBAL_COURSE_MODULE     = 6; //课程模块
    const GLOBAL_COURSE_PROPERTY   = 7; //课程属性
    const GLOBAL_COURSE_DIFFICULTY = 8; //课程难度
    const GLOBAL_GRADE_STAGE       = 9; //学级
    public static $arrSourceMap = array(
        self::GLOBAL_GRADE             => '年级',
        self::GLOBAL_SUBJECT           => '科目',
        self::GLOBAL_COURSE_SYSTEM     => '体系',
        self::GLOBAL_CLASS_TYPE        => '班型',
        self::GLOBAL_BOOK_VER          => '教材版本',
        self::GLOBAL_COURSE_MODULE     => '课程模块',
        self::GLOBAL_COURSE_PROPERTY   => '课程属性',
        self::GLOBAL_COURSE_DIFFICULTY => '课程难度',
        self::GLOBAL_GRADE_STAGE       => '学级',
    );
    public static $arrSourceTypeMap = array(
        self::GLOBAL_GRADE             => 'grade',
        self::GLOBAL_SUBJECT           => 'subject',
        self::GLOBAL_COURSE_SYSTEM     => 'courseSystem',
        self::GLOBAL_CLASS_TYPE        => 'classType',
        self::GLOBAL_BOOK_VER          => 'bookVer',
        self::GLOBAL_COURSE_MODULE     => 'courseModule',
        self::GLOBAL_COURSE_PROPERTY   => 'courseProperty',
        self::GLOBAL_COURSE_DIFFICULTY => 'courseDifficulty',
        self::GLOBAL_GRADE_STAGE       => 'gradeStage',
    );

    //课程状态
    const STATUS_UNFINISH = 0; //未结束
    const STATUS_FINISHED = 1; //已结束（正常结束）
    const STATUS_DELETED  = 2; //已删除（异常结束）
    public static $arrStatusMap = array(
        self::STATUS_UNFINISH => '未结束',
        self::STATUS_FINISHED => '已结束',
        self::STATUS_DELETED  => '已删除',
    );

    //课程品牌
    const BRAND_YIKE    = 1; // 一课
    const BRAND_HX      = 2; // 浣熊
    const BRAND_BB      = 3; // 帮帮
    const BRAND_ID_YAYA = 4; // 鸭鸭
    public static $arrBrandMap = array(
        self::BRAND_YIKE    => '一课',
        self::BRAND_HX      => '浣熊',
        self::BRAND_BB      => '帮帮',
        self::BRAND_ID_YAYA => '鸭鸭',
    );

    //是否有教辅服务
    const HAS_MATERIAL_NO = 0; // 无教辅服务
    const HAS_MATERIAL_YES = 1; // 有教辅服务

    //是否是特惠课
    const SPECIAL_SELL_TYPE_NO = 0; // 非特惠课
    const SPECIAL_SELL_TYPE_YES = 1; // 特惠课

    //新课程类型
    const NEW_COURSE_TYPE_ZERO_YUN = 13; // 0元课

    const TEMPLATE_SYNC     = 1;   //同步
    const TEMPLATE_SPECIAL  = 2;   //专题
    const TEMPLATE_ABILITY  = 3;   //能力
    const TEMPLATE_SYK  = 4;   //素养
    const TEMPLATE_AI_SYNC = 5;//ai同步
    const TEMPLATE_AI_SPECIAL = 6;//ai专题
    public static $TEMPLATE_DESC_MAP = array(
        self::TEMPLATE_SYNC     => '同步',
        self::TEMPLATE_SPECIAL  => '专题',
        self::TEMPLATE_ABILITY  => '能力',
        self::TEMPLATE_SYK      => '素养',
        self::TEMPLATE_AI_SYNC => 'AI同步',
        self::TEMPLATE_AI_SPECIAL => 'AI专题',
    );
}
