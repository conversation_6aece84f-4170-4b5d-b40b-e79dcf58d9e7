<?php

/**
 * Class Qdmis_Const_AdsEnum_EnumKuaishou
 * 快手
 */
class QdLib_Const_AdsEnum_Lv3_Kuaishou 
{
    // 字段
    const FIELD_CREATIVE_MATERIAL_TYPE      = 'creativeMaterialType';
    const FIELD_ZYB_VIDEO_COVER_TYPE        = 'zybVideoCoverType';
    const FIELD_IMAGE_TOKENS                = 'imageTokens';
    const FIELD_SHORT_SLOGAN                = 'shortSlogan';
    const FIELD_ZYB_UNIT_TYPE               = 'zybUnitType';

    public static $fieldDesc = [
        self::FIELD_CREATIVE_MATERIAL_TYPE      => '广告位',
        self::FIELD_ZYB_VIDEO_COVER_TYPE        => '封面素材',
    ];


    /**
     * 投放状态 （操作结果）
     */
    const PUT_STATUS_ENABLE         = 1;
    const PUT_STATUS_DISABLE        = 2;
    const PUT_STATUS_DELETE         = 3;
    const PUT_STATUS = [
        self::PUT_STATUS_ENABLE  => "投放中",
        self::PUT_STATUS_DISABLE => "暂停",
        self::PUT_STATUS_DELETE  => "删除",
    ];

    /**
     * 素材类型
     */
    const CREATIVE_MATERIAL_HISTORY                 = 0;
    const CREATIVE_MATERIAL_VERTICAL_VIDEO          = 1;
    const CREATIVE_MATERIAL_HORIZONTAL_VIDEO        = 2;
    const CREATIVE_MATERIAL_HISTORY_SINGLE_IMG      = 3;
    const CREATIVE_MATERIAL_STICKY_SINGLE_IMG       = 4;
    const CREATIVE_MATERIAL_HORIZONTAL_IMG          = 6;
    const CREATIVE_MATERIAL_SMALL_IMG               = 9;
    const CREATIVE_MATERIAL_GROUP_IMG               = 10;
    const CREATIVE_MATERIAL_TYPE = [
        self::CREATIVE_MATERIAL_HISTORY                 => '历史创意未作区分',
        self::CREATIVE_MATERIAL_VERTICAL_VIDEO          => '竖版视频',
        self::CREATIVE_MATERIAL_HORIZONTAL_VIDEO        => '横版视频',
        self::CREATIVE_MATERIAL_HISTORY_SINGLE_IMG      => '后贴片单图图片创意',
        self::CREATIVE_MATERIAL_STICKY_SINGLE_IMG       => '便利贴单图图片创意',
        self::CREATIVE_MATERIAL_HORIZONTAL_IMG          => '横版图片',
        self::CREATIVE_MATERIAL_SMALL_IMG               => '小图',
        self::CREATIVE_MATERIAL_GROUP_IMG               => '组图',
    ];

    const CREATIVE_MATERIAL_TYPE_ENUM = [
        self::CREATIVE_MATERIAL_HISTORY,
        self::CREATIVE_MATERIAL_VERTICAL_VIDEO,
        self::CREATIVE_MATERIAL_HORIZONTAL_VIDEO,
        self::CREATIVE_MATERIAL_HISTORY_SINGLE_IMG,
        self::CREATIVE_MATERIAL_STICKY_SINGLE_IMG,
        self::CREATIVE_MATERIAL_HORIZONTAL_IMG,
        self::CREATIVE_MATERIAL_SMALL_IMG,
        self::CREATIVE_MATERIAL_GROUP_IMG,
    ];

    /**
     * 封面素材
     */
    const ZYB_VIDEO_COVER_TYPE_OWN              = 1;
    const ZYB_VIDEO_COVER_TYPE_INTELLIGENT      = 2;
    const ZYB_VIDEO_COVER_TYPE = [
        self::ZYB_VIDEO_COVER_TYPE_OWN              => '自定义',
        self::ZYB_VIDEO_COVER_TYPE_INTELLIGENT      => '智能抽帧',
    ];

    const APP_PLATFORM_ANDROID          = 1;

}