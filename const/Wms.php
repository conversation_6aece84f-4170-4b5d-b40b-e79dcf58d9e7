<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   Wms.php
 * <AUTHOR>
 * @date   2018/10/9
 * @brief  物流系统配置类
 */
class Zb_Const_Wms{

    //*物流状态
    const STATUS_NOTSEND            = -1;//未履约,不发货
    const STATUS_WAITSEND           = 0; // 待发货
    //const STATUS_PACKAGE            = 1; // 已组合打包
    //const STATUS_PACKDONE           = 2; // 打包完成
    const STATUS_ADDRESSERROR       = 3; // 地址异常
    const STATUS_DELIVERY           = 4; // 待出库
    const STATUS_SENDOUT            = 5; // 运输中
    const STATUS_RECEIVE            = 6; // 已签收
    //const STATUS_PACKFAIL           = 7; // 打包失败
    const STATUS_SENDFAIL           = 8; // 退签
    const STATUS_WAITOFFLINESEND    = 9; // 待线下寄送
    const STATUS_TERMINATION        = 10;// 寄送终止
    const STATUS_PKG_COMPLETED      = 13;// 打包完成


    const SERVICELINE_JINGDONG = 1; // 京东快递
    const SERVICELINE_SHUNFENG = 2; // 顺丰快递
    const SERVICELINE_EMS      = 3; // EMS
    
    // 上线兼容老逻辑， 等ofc上线后删掉
    const ACTION_TYPE_UPDATE  = 0;//修改信息
    const ACTION_TYPE_ADDRESS = 1;//修改地址
    const ACTION_TYPE_REMIND  = 2;//催单


    // 物流对外的服务业务线
    /**
     * 旧版一课业务线 | 中台标准化之前旧版本
     * 注：以后均使用 SERVICE_LINE_YIKE_SOURCE
     */
    const SERVICE_LINE_YIKE = 1; //一课
    /**
     * 一课业务线 | 中台标准化对齐数据字典
     * http://ued.zuoyebang.cc/documents/docs/dds/source.html
     */
    const SERVICE_LINE_YIKE_SOURCE = 4; //直播课

    /**
     * 旧版浣熊业务线 | 中台标准化之前旧版本
     * 注：以后均使用 SERVICE_LINE_HUAN_SOURCE
     */
    const SERVICE_LINE_HUAN = 2; //浣熊
    /**
     * 浣熊业务线 | 中台标准化对齐数据字典
     * http://ued.zuoyebang.cc/documents/docs/dds/source.html
     */
    const SERVICE_LINE_HUAN_SOURCE = 7; //浣熊

    /**
     * 旧版商城业务线 | 中台标准化之前旧版本
     * 注：以后均使用 SERVICE_LINE_SHOP_SOURCE
     */
    const SERVICE_LINE_SHOP = 3; //商城
    /**
     * 商城业务线 | 中台标准化对齐数据字典
     * http://ued.zuoyebang.cc/documents/docs/dds/source.html
     */
    const SERVICE_LINE_SHOP_SOURCE = 5; //商城

    /**
     * 旧版积分商城业务线 | 中台标准化之前旧版本
     * 注：以后均使用 SERVICE_LINE_POINT_MALL_SOURCE
     */
    const SERVICE_LINE_POINT_MALL   = 4; //积分商城
    /**
     * 积分商城业务线 | 中台标准化对齐数据字典
     * http://ued.zuoyebang.cc/documents/docs/dds/source.html
     */
    const SERVICE_LINE_POINT_MALL_SOURCE   = 10; //积分商城

    const SERVICE_LINE_YYXZ = 101; // 鸭鸭写字
    const SERVICE_LINE_YYYY = 102; // 鸭鸭英语
    const SERVICE_LINE_YYYW = 103; // 鸭鸭语文
    const SERVICE_LINE_YYXZ_SCORE_MALL = 105; // 鸭鸭习字积分商城
    const SERVICE_LINE_YYYY_SCORE_MALL = 106; // 鸭鸭英语积分商城
    const SERVICE_LINE_YYYW_SCORE_MALL = 107; // 鸭鸭语文积分商城
    const SERVICE_LINE_ADULT_ENGLISH           = 112; //成人英语
    const SERVICE_LINE_ADULT_FINANCE           = 113; //成人财务
    const SERVICE_LINE_ADULT_TEACHER           = 114; //成人教师
    const SERVICE_LINE_FAMOUS_TEACHER          = 115; //名师
    const SERVICE_LINE_YYSW                    = 117; //鸭鸭思维
    const SERVICE_LINE_ADULT_GONGKAO           = 118; //成人公考
    const SERVICE_LINE_YAYA_DIYOU              = 121; //鸭鸭低幼
    const SERVICE_LINE_IMC_XIAOBAN             = 127; //IMC小班课
    const SERVICE_LINE_CR_KAOYAN               = 128; //成教考研
    const SERVICE_LINE_JIAOFU                  = 130; //教辅业务
    const SERVICE_LINE_LANDING                 = 131; //蓝钉
    const SERVICE_LINE_BBSZJFMALL              = 132; //帮帮识字积分商城
    const SERVICE_LINE_ZYBCR_ZYJY              = 135; //成人教育-职业教育

    /**
     * 中台标准化之前 旧版本业务线相关map 【废弃不在维护】
     * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     */
    //物流业务线【废弃不在维护】
    public static $SERVICE_LINES = [
        self::SERVICE_LINE_YIKE => '一课',
        self::SERVICE_LINE_HUAN => '浣熊',
        self::SERVICE_LINE_SHOP => '商城',
        self::SERVICE_LINE_POINT_MALL => '积分商城',
        self::SERVICE_LINE_YYXZ => '鸭鸭写字',
        self::SERVICE_LINE_YYYY => '鸭鸭英语',
        self::SERVICE_LINE_YYYW => '鸭鸭语文',
        self::SERVICE_LINE_YYXZ_SCORE_MALL => '鸭鸭习字积分商城',
        self::SERVICE_LINE_YYYY_SCORE_MALL => '鸭鸭英语积分商城',
        self::SERVICE_LINE_YYYW_SCORE_MALL => '鸭鸭语文积分商城',
    ];

    //物流业务线【废弃不在维护】
    public static $SERVICE_LINE_KEYS = [
        self::SERVICE_LINE_YIKE => 'e57bd2f8-49fd-410f-bb50-1d6cdacb68ff',
        self::SERVICE_LINE_HUAN => 'f0cf77de-5553-4c46-8206-ac914c6b06dc',
        self::SERVICE_LINE_SHOP => 'a328d728-fffa-4a73-83b4-48abb2a936fe',
        self::SERVICE_LINE_POINT_MALL => '6d9db6fd-b072-4425-b390-983316c4e45a',
        self::SERVICE_LINE_YYXZ => 'db27648b-6194-449e-b719-25410e2e1f3d',
	    self::SERVICE_LINE_YYYY => '3d89b37f-ece1-420e-a9e0-15c57101145a',
	    self::SERVICE_LINE_YYYW => 'a15df950-f5f2-5eae-a087-0bf03172a1d9',
        self::SERVICE_LINE_YYXZ_SCORE_MALL => '9d4b1028-d8b3-2e80-c238-4b2305f63933',
        self::SERVICE_LINE_YYYY_SCORE_MALL => '21fca934-a338-5298-29b6-e0eee6f5c731',
        self::SERVICE_LINE_YYYW_SCORE_MALL => '203674c7-8a61-d302-c469-a51a0e646775',

    ];
    /**
     * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     */

    /**
     * 中台标准化平台化版本 | 2020-07-01 后业务线均使用如下枚举值
     * -----------------------------------------------------------------
     */
    public static $sourceMap = [
        self::SERVICE_LINE_YIKE_SOURCE => '一课',
        self::SERVICE_LINE_HUAN_SOURCE => '浣熊',
        self::SERVICE_LINE_SHOP_SOURCE => '商城',
        self::SERVICE_LINE_POINT_MALL_SOURCE => '积分商城',
        self::SERVICE_LINE_YYXZ => '鸭鸭写字',
        self::SERVICE_LINE_YYYY => '鸭鸭英语',
        self::SERVICE_LINE_YYYW => '鸭鸭语文',
        self::SERVICE_LINE_YYXZ_SCORE_MALL => '鸭鸭习字积分商城',
        self::SERVICE_LINE_YYYY_SCORE_MALL => '鸭鸭英语积分商城',
        self::SERVICE_LINE_YYYW_SCORE_MALL => '鸭鸭语文积分商城',
        self::SERVICE_LINE_ADULT_ENGLISH            => "成人英语",
        self::SERVICE_LINE_ADULT_FINANCE            => "成人财务",
        self::SERVICE_LINE_ADULT_TEACHER            => "成人教师",
        self::SERVICE_LINE_FAMOUS_TEACHER           => "好课-名师课",
        self::SERVICE_LINE_YYSW                     => "鸭鸭思维",
        self::SERVICE_LINE_ADULT_GONGKAO            => "成人公考",
        self::SERVICE_LINE_YAYA_DIYOU               => "鸭鸭低幼",
        self::SERVICE_LINE_IMC_XIAOBAN              => "IMC小班课",
        self::SERVICE_LINE_CR_KAOYAN                => "成教考研",
        self::SERVICE_LINE_JIAOFU                   => "教辅业务",
        self::SERVICE_LINE_LANDING                  => "蓝钉",
        self::SERVICE_LINE_BBSZJFMALL               => "帮帮识字积分商城",
        self::SERVICE_LINE_ZYBCR_ZYJY               => "成人教育-职业教育",
    ];

    public static $sourceKeyMap = [
        self::SERVICE_LINE_YIKE_SOURCE => 'e57bd2f8-49fd-410f-bb50-1d6cdacb68ff',
        self::SERVICE_LINE_HUAN_SOURCE => 'f0cf77de-5553-4c46-8206-ac914c6b06dc',
        self::SERVICE_LINE_SHOP_SOURCE => 'a328d728-fffa-4a73-83b4-48abb2a936fe',
        self::SERVICE_LINE_POINT_MALL_SOURCE => '6d9db6fd-b072-4425-b390-983316c4e45a',
        self::SERVICE_LINE_YYXZ => 'db27648b-6194-449e-b719-25410e2e1f3d',
        self::SERVICE_LINE_YYYY => '3d89b37f-ece1-420e-a9e0-15c57101145a',
        self::SERVICE_LINE_YYYW => 'a15df950-f5f2-5eae-a087-0bf03172a1d9',
        self::SERVICE_LINE_YYXZ_SCORE_MALL => '9d4b1028-d8b3-2e80-c238-4b2305f63933',
        self::SERVICE_LINE_YYYY_SCORE_MALL => '21fca934-a338-5298-29b6-e0eee6f5c731',
        self::SERVICE_LINE_YYYW_SCORE_MALL => '203674c7-8a61-d302-c469-a51a0e646775',
        self::SERVICE_LINE_ADULT_ENGLISH            => "277bde2f-647d-4647-adb3-56e3e3064b16",
        self::SERVICE_LINE_ADULT_FINANCE            => "803136b1-f2b0-439d-b9d2-11431fd9a812",
        self::SERVICE_LINE_ADULT_TEACHER            => "ca83a47f-52aa-4269-b3c3-4f38ad83cdd8",
        self::SERVICE_LINE_FAMOUS_TEACHER           => "419d205e-59ed-46d8-9fcb-d61344f7742f",
        self::SERVICE_LINE_YYSW                     => "3b7f944e-aae2-5840-3e9d-6a106ea8a170",
        self::SERVICE_LINE_ADULT_GONGKAO            => "843ed6b0-8fa6-2c92-7749-a125bf6bb413",
        self::SERVICE_LINE_YAYA_DIYOU               => "69587f10-3534-403c-947f-0d7cccb3ff77",
        self::SERVICE_LINE_IMC_XIAOBAN              => "805df7a2-c1a6-4324-a04a-8db6ba857f65",
        self::SERVICE_LINE_CR_KAOYAN                => "b82d2bf7-2e78-493b-b899-c61810d6c86d",
        self::SERVICE_LINE_JIAOFU                   => "dccce79c-0c02-4650-af25-0c4fb8580f78",
        self::SERVICE_LINE_LANDING                  => "dccce79c-0c02-4650-af25-0c4fb8580f78",
        self::SERVICE_LINE_BBSZJFMALL               => "dccce79c-0c02-4650-af25-0c4fb8580f78",
        self::SERVICE_LINE_ZYBCR_ZYJY               => "dccce79c-0c02-4650-af25-0c4fb8580f78",
    ];
    /**
     * -------------------------------------------------------------------
     */

    // 物流修改动作分类
    const ACTION_TYPE_MODIFY_ADDRESS    = 1; //修改地址
    const ACTION_TYPE_MODIFY_SEND_TIME  = 2; //修改地址
    const ACTION_TYPE_MODIFY_BATCH_ID   = 3; //修改批次号
    const ACTION_TYPE_MODIFY_CLASSIC    = 4; //修改类型
    const ACTION_TYPE_MODIFY_REMIND     = 5; //催单
    const ACTION_TYPE_MODIFY_EXPRESS_COMPANY    = 6; //修改物流公司

    // 物流修改动作是否开启事务
    const TRANSACTION_DEFAULT   = 1; //在拦截或修改多个子订单时，允许存在失败的子订单
    const TRANSACTION_ALL       = 2; //在拦截或修改多个子订单事，必须所有子订单都拦截成功，才认为拦截成功


    //*寄送类型
    public static $sendTypeMap = array(
        self::SERVICELINE_JINGDONG  => '京东快递',
        self::SERVICELINE_SHUNFENG  => '顺丰快递',
        self::SERVICELINE_EMS       => 'EMS',
    );

    //*物流状态结果集
    public static $statusMap = array(
        self::STATUS_WAITSEND            => '待发货',
        self::STATUS_ADDRESSERROR        => '地址异常',
        self::STATUS_DELIVERY            => '待出库',
        self::STATUS_SENDOUT             => '运输中',
        self::STATUS_RECEIVE             => '已签收',
        self::STATUS_SENDFAIL            => '退签',
        self::STATUS_WAITOFFLINESEND     => '待线下邮寄',
        self::STATUS_TERMINATION         => '寄送终止',
        self::STATUS_PKG_COMPLETED       => '打包完成',
    );

}
