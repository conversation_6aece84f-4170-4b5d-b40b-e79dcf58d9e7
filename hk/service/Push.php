<?php

/**
 * @deprecated
 *
 * @since 2018-12-13，所有接口全部返回false
 * @since 2018-08-01，所有push都走新通道
 *
 * 对应客户端：Hk_Service_PushClient
 * <AUTHOR>
 * @date 2017-06-15
 * @brief 基于Message服务和fdc服务封装的(直播课)推送服务，用于取代Zhibo_Service_Push和Hk_Ds_Fudao_SysPush
 * 全部推送最终都走Message服务，不再走LCS服务
 * 支持特性：
 * 1. 支持单uid、单cuid、多cuid、多uid推送
 *    多uid和多cuid推送注意有数量限制
 *    单个uid和cuid推送时，也需要放在数组里
 * 2. 支持推送策略控制
 *    支持直接推送，即直接调用Hk_Service_Message服务推送
 *    支持异步推送，即先发给nmq暂存，然后由fdc模块处理发送（fdc也是调用的Hk_Service_Message服务推送），量大时使用
 *    支持控制推送，先发给nmq暂存，然后由fdc模块处理发送（经过fdc的控制策略），有控制策略需求时使用，如优惠券过期推送
 *    控制推送中，fdc模块支持的控制信息如下：
 *        needRetry[默认0]——如发送条件不满足，是否保存然后重试，如为0会被直接丢弃
 *        reqTime[默认now]——预设推送时间，需配合needRetry使用（需设置为1）
 *        expireTime[默认now+2h]——消息发送有效期，过期后没发送成功会被丢弃
 *        rank[默认0]——消息发送优先级，0~9，值越大优先级别越高
 *        forceSend[默认0]——是否不过策略强制发送，默认需要过策略
 * 3. 支持推送目标选择策略控制
 *    支持多app中随机选择策略
 *    支持多app全部推送策略
 *    支持多app按顺序优先有效推送策略，默认一课优先（直播业务的策略）
 * 4. 支持纯文本信息push、带跳转链接push、IM消息push等，其他类型可以扩展
 *    推送URL时注意，如果是'homework://','airclass://'等协议，需用APP_SCHEMA替换，'app://'协议暂不处理
 * 5. 服务也可以支持直播外的其他业务，不过目标选择策略需注意调整
 *
 **/
class Hk_Service_Push {

    // 单次批量推送数量限制
    const PUSH_UID_LIMIT        = 50;
    const PUSH_CUID_LIMIT       = 50;

    // 推送策略控制
    const PUSH_STRATEGY_SYNC    = 1; // 直接同步发送
    const PUSH_STRATEGY_ASYNC   = 2; // 通过nmq异步发送
    const PUSH_STRATEGY_CONTROL = 3; // 通过fdc模块控制策略异步发送

    // 推送方式
    const PUSH_MODE_UID     = 1; // 支持批量uid推送
    const PUSH_MODE_CUID    = 2; // 支持批量cuid推送
    const PUSH_MODE_ALL     = 3; // cuid、uid推送均支持

    // 推送目标选择策略
    const PRODUCT_SELECT_RANDOM     = 1;    // 随机选一个
    const PRODUCT_SELECT_ALL        = 2;    // 推送所有
    const PRODUCT_SELECT_TOP        = 3;    // 依顺序选择一个有效的，涉及有效性判断

    // url schema替换标识，用product对应的schema名称替换（保留名称，除schema外url中就不能再出现这个名称了）
    const APP_SCHEMA    = "appschema";

    // APP产品url schema映射
    private static $productSchemaMap    = array(
        "napi" => "homework",
        "airclass" => "airclass",
        "airteacher" => "airteacher",
    );

    const UIDLOGIN_INFO             = "UIDLOGIN_INFO";
    const DEVICES_CONTENT_KEY       = "DEVICES_CONTENT_KEY";


    // 支持的推送cmdNo以及对应配置
    public static $cmdConfigMap = array(
        //纯文本推送，应该是跳转到首页
        20 => array(
            'params'    => array(                       //所需的必要参数
                'title'     => ['android_title'],       //调用Hk_Service_Message所需的字段映射
                'content'   => ['ios_alert', 'android_content'],
            ),
            'ext'       => array(                       //调用Hk_Service_Message所需的其他必要字段
                'ruid'      => 0,
                'sysmid'    => 0,
                'noSave'    => 1,
            ),
            'pushMode'  => self::PUSH_MODE_ALL,         // 支持的推送方式
        ),
        //url推送，跳转到url链接
        10 => array(
            'params'    => array(
                'title'     => ['android_title'],
                'content'   => ['ios_alert', 'android_content'],
                'url'       => ['url'],
            ),
            'ext'       => array(
                'cuids'     => '',
            ),
            'pushMode'  => self::PUSH_MODE_ALL,
        ),
        //IM消息批量cuid推送
        103 => array(
            'params'    => array(
                'title'     => ['android_title'],
                'content'   => ['msg'],
                'url'       => ['url'],
            ),
            'ext'       => array(
                'cuids'     => '',
            ),
            'pushMode'  => self::PUSH_MODE_CUID,
        ),
        //IM消息批量uid推送
        104 => array(
            'params'    => array(
                'title'     => ['android_title'],
                'content'   => ['msg'],
            ),
            'ext'       => array(
                'uids'      => '',
            ),
            'pushMode'  => self::PUSH_MODE_UID,
        ),
        //一课老师app质检push
        131 => array(
            'params'    => array(
                'title'     => ['android_title'],
                'content'   => ['ios_alert', 'android_content'],
                'url'       => ['url'],
            ),
            'ext'       => array(
                'cuids'     => '',
            ),
            'pushMode'  => self::PUSH_MODE_ALL,
        ),
        //一课老师 待上课提醒
        132 => array(
            'params'    => array(
                'title'     => ['android_title'],
                'content'   => ['ios_alert', 'android_content'],
                'url'       => ['url'],
            ),
            'ext'       => array(
                'cuids'     => '',
            ),
            'pushMode'  => self::PUSH_MODE_ALL,
        ),
        //一课老师 讲义上传提醒
        133 => array(
            'params'    => array(
                'title'     => ['android_title'],
                'content'   => ['ios_alert', 'android_content'],
                'url'       => ['url'],
            ),
            'ext'       => array(
                'cuids'     => '',
            ),
            'pushMode'  => self::PUSH_MODE_ALL,
        ),
        //一课老师 讲义未通过审核提醒
        134 => array(
            'params'    => array(
                'title'     => ['android_title'],
                'content'   => ['ios_alert', 'android_content'],
                'url'       => ['url'],
            ),
            'ext'       => array(
                'cuids'     => '',
            ),
            'pushMode'  => self::PUSH_MODE_ALL,
        ),
    );

    public static function pushByUids($cmdNo, array $msgInput, array $uids,
            $strategy = self::PUSH_STRATEGY_SYNC,
            $control  = array(),
            $products = ["airclass", "napi", "airteacher"],
            $selector = self::PRODUCT_SELECT_TOP) {
        return false;
    }

    public static function pushByCuids($cmdNo, array $msgInput, array $cuids,
            $strategy = self::PUSH_STRATEGY_SYNC,
            $control  = array(),
            $products = ["airclass", "napi"],
            $selector = self::PRODUCT_SELECT_TOP) {
        return false;
    }

    public static function push($cmdNo, $msgInput, $strategy = self::PUSH_STRATEGY_SYNC, $control = array()) {
        return false;
    }

    public static function pushFdc($arrCmd) {
        return false;
    }
}
