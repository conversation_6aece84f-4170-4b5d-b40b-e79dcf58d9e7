<?php


/**
 * rpc请求基类，负责数据解包和逻辑执行
 *
 * @deprecated 迁移到独立repo管理，如需使用请联系**********************
 *
 * @since 2.0 整理重构代码，并迁移rpc配置文件到指定位置
 * @since 1.0 初始化
 *
 * @filesource hk/core/base/ApiController.php
 * <AUTHOR>
 * @version 2.0
 * @date    2018-10-30
 */
abstract class Hk_Core_Base_ApiController extends Ap_Controller_Abstract {


    private $cflag;

    public function init() {
        $this->cflag = "respBody";      # 注册的返回值标示
    }

    /**
     * 此函数不能被重载<br>
     * 增加和requestIdFilter插件对应的requestId缓存机制，需要注意
     *
     * @return boolean
     */
    public final function callAction() {
        $respType  = true === Ap_Registry::has($this->cflag) ? "cache" : "normal";      # 根据是否有缓存response内容判断请求类型
        Bd_Log::addNotice("responseType", $respType);         # 标示requestId请求内容来源

        $format    = !isset($_GET['format']) ? "json" : strval($_GET['format']);        # 目前只支持json
        $params    = $this->parseParams($format);

        $method    = $this->getRequest()->getParam('method'); # 获取请求的api对应的方法名称
        $apiDetail = $this->getApiMethodDetail($method);
        $service   = strval($apiDetail['service']);           # 请求方法对应的service
        $func      = strval($apiDetail['func']);              # 请求方法映射的函数名称
        $paramDesc = $apiDetail['params'];                    # 参数列表描述
        $this->getAddHeader();                                # 获取调用方透传的一些必要数据到日志中

        $input     = array();                                 # 参数校验
        try {
            $input = Hk_Core_Util_Valid::inputValid($paramDesc, $params);
        } catch (Hk_Core_ApiError $error) {
            $errno   = $error->getErrno();
            $errmsg  = $error->getErrDetail();
            $errData = $error->getErrData();
            return $this->message($errno, $errmsg, $errData, $respType);
        }

        Saf_SmartMain::setCgi($input);                        # 设置上下文参数
        try {
            $srvObj = new $service();
        } catch (Hk_Core_Error $e) {
            $errno  = $e->getErrno();
            $errmsg = $e->getErrmsg();
            $data   = array();
            return $this->message($errno, $errmsg, $data, $respType);
        }
        Bd_Log::addNotice('input', @json_encode($input));
        Bd_Log::addNotice('service', $func);

        # requestId缓存机制，若不启用requestId插件则此处不会执行
        if ("cache" === $respType) {
            $ret    = Ap_Registry::get($this->cflag);         # 从缓存中获取上次的请求内容
            $errno  = $ret["errno"];
            $errmsg = $ret["errmsg"];
            $data   = $ret["data"];
        } else {            # 普通请求
            $ret    = call_user_func_array(array($srvObj, $func), $input);
            $errno  = Hk_Core_ErrorCodes::SUCC;
            $errmsg = Hk_Core_ErrorCodes::$codes[$errno];
            $data   = NULL === $ret ? array() : $ret;
        }
        return $this->message($errno, $errmsg, $data);
    }

    /**
     * 从http header中获取透传的数据<br>
     * 这些参数是通过http header透传过来的
     */
    private function getAddHeader() {
        $reqModule = isset($_SERVER["HTTP_X_BD_MODULE"]) ? $_SERVER["HTTP_X_BD_MODULE"] : "unknown-app";
        Bd_Log::addNotice("reqModule", $reqModule);
    }

    /**
     * api调用后，输出指定的错误格式的数据。<br>
     * 1、如果是普通请求，返回数据需要注册到框架<br>
     * 2、如果是缓存请求，获取缓存之后需要将数据删除
     *
     * @param int          $errno
     * @param string       $errmsg
     * @param array        $data
     * @param string       $respType
     * @param string       $type
     * @return boolean
     */
    private function message($errno, $errmsg, $data = array(), $respType = "normal", $type = "json") {
        $output = json_encode(array(
            'errno'  => $errno,
            'errmsg' => $errmsg,
            'data'   => $data,
        ));
        if ("normal" === $respType) {
            Ap_Registry::set($this->cflag, $output);         # 注册返回值
        } else {
            Ap_Registry::del($this->cflag);                  # 删除返回值
        }

        Bd_Log::addNotice('output', strlen($output) <= 200 ? $output : substr($output, 0, 200) . "...");        # 限制日志长度
        header('Content-Type: application/json; charset=utf-8');
        print $output;
        if (0 === $errno) {
            return true;
        }
        return false;
    }

    /**
     * 解析传递上的参数格式，默认json，目前也只支持json。
     *
     * @param string     $format
     * @return array
     */
    private function parseParams($format) {
        $input = file_get_contents('php://input');
        if ($format === "json") {
            $params = @json_decode($input, true);
        } else {
            $params = @json_decode($input, true);
        }
        return $params;
    }

    /**
     * 获取请求rpc配置
     *
     * @throws Hk_Core_ApiError
     *
     * @param string     $method
     * @return array
     */
    protected function getApiMethodDetail($method) {
        $conf = Bd_Conf::getAppConf("rpc/api/api", MAIN_APP);
        if (false === $conf) {
            $conf = Bd_Conf::getAppConf("api/api", MAIN_APP);           # 兼容以前配置位置
        }
        if (false === $conf) {
            throw new Hk_Core_ApiError(Hk_Core_ErrorCodes::CONF_NOT_EXIST);
        }
        if (empty($method) || !isset($conf[$method])) {
            $errno  = Hk_Core_ErrorCodes::METHOD_NOT_EXIST;
            $errmsg = strval(@Hk_Core_ErrorCodes::$codes[$errno]);
            $data   = array("method" => $method);
            throw new Hk_Core_ApiError($errno, $data);
        }
        return $conf[$method];
    }
}

/* vim: set ft=php expandtab ts=4 sw=4 sts=4 tw=0: */
