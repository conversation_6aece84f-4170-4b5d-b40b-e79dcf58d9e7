<?php


/**
 * rpc错误捕获展示，被捕获的各类异常统一处理
 *
 * @deprecated 迁移到独立repo管理，如需使用请联系**********************
 *
 * @filesource hk/core/base/ErrorController.php
 * <AUTHOR>
 * @version 1.6
 * @date    2018-10-26
 */
class Hk_Core_Base_ErrorController extends Ap_Controller_Abstract {


    protected $uri;

    public function init() {
        $this->uri = strval($this->getRequest()->getRequestUri());
    }

    public function errorAction($exception) {
        $this->showApiError($exception);
    }

    /**
     * 接口抛出异常之后捕获并通过固定的json格式返回给调用方
     **/
    protected function showApiError($exception) {
        if ($exception instanceof Hk_Core_Error) {              # api抛出内部异常，不对外暴露
            $errno   = Hk_Core_ErrorCodes::INTERNAL_ERROR;
            $errmsg  = strval(Hk_Core_ErrorCodes::$codes[$errno]);
            $errData = array();
            Bd_Log::fatal('Get Internal Exception! ' . $exception->getErrmsg() . ' at ' . $exception->getFile() . ':' . $exception->getLine(), $exception->getErrno());
        } elseif ($exception instanceof Hk_Core_ApiError) {     # api抛出的异常
            $errno   = $exception->getErrno();
            $errmsg  = $exception->getErrmsg();
            $errData = $exception->getErrData();
        } elseif ($exception instanceof Hk_Util_Exception) {    # hk异常
            $errno   = $exception->getErrNo();
            $errmsg  = $exception->getErrStr();
            $errData = array();
        } else {                                                # 内部异常，禁止抛出
            $errno   = Hk_Core_ErrorCodes::INTERNAL_ERROR;
            $errmsg  = strval(Hk_Core_ErrorCodes::$codes[$errno]);
            $errData = array();
            Bd_Log::fatal('Get Internal Exception! ' . $exception->getMessage() . ' at ' . $exception->getFile() . ':' . $exception->getLine(), $exception->getCode());
        }

        $ret = array(
            'errno'  => $errno,
            'errmsg' => $errmsg,
            'data'   => $errData,
        );
        header('Content-Type: application/json; charset=utf-8');
        echo @json_encode($ret);
    }
}

/* vim: set ft=php expandtab ts=4 sw=4 sts=4 tw=0: */
