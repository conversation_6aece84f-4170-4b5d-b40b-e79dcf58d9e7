<?php


/**
 * requestId通用插件，解析rpc的requestId，requestId并标识请求内容是否重复或者过期<br>
 * requestId标识唯一的一次请求，根据requestId存储并过滤重复的请求<br>
 * requestId格式：uuid_timestamp<br>
 * - uuid用于标示请求的唯一性<br>
 * - timestamp用于标示请求时间，根据时间判断请求是否过期<br>
 * 1、在routerShutdown阶段，会判断requestId是否重复，并且是否过期；并根据策略进行相应的处理<br>
 * 2、在dispatchLoopShutdown阶段，会获取相应的请求结果，并缓存相应的结果<br>
 *
 * @deprecated 迁移到独立repo管理，如需使用请联系**********************
 *
 * @since 1.2 2018-07-21 整理代码，增加app标示，防止冲突
 * @since 1.0 2016-06 初始化
 *
 * @filesource hk/core/plugin/RequestIdFilter.php
 * <AUTHOR>
 * @version 1.2
 * @date    2018-07-21
 */
class Hk_Core_Plugin_RequestIdFilter extends Ap_Plugin_Abstract {


    protected $expire;          # 请求过期时间

    private $skip;              # 是否跳过验证
    private $appId;             # 产品线标示

    private $requestId;
    private $requestTime;
    private $mcClient;
    private $cflag = "respBody";

    public function __construct($name, $appId = "default") {
        $this->appId    = $appId;
        $this->expire   = 180;
        $this->mcClient = Hk_Service_Memcached::getInstance($name);
        if (false === $this->mcClient) {
            Bd_Log::warning("can't get mc instance, request filter stop work");
        }

        $reqId      = isset($_REQUEST["requestId"]) ? $_REQUEST["requestId"] : "";
        $this->skip = isset($_REQUEST["skip"]) && 1 == $_REQUEST["skip"] ? true : false;
        Bd_Log::addNotice("reqId", $this->skip ? "skip" : $reqId);

        list($this->requestId, $this->requestTime) = explode("_", $reqId);
        $this->requestId   = strval($this->requestId);
        $this->requestTime = intval($this->requestTime);
    }

    /**
     * 判断用户的这次请求是不是重复请求，根据以下策略进行判断：<br>
     * 0、是否跳过requestId请求，开发使用<br>
     * 1、从memcache中获取相应的请求id缓存内容，判断是否是新请求<br>
     * 2、是新请求，走正常的请求流程<br>
     * 3、是旧请求，判断mc缓存的内容<br>
     * a、值为-1，代表上次请求并未有返回值，提醒调用方返回数据失败<br>
     * b、值为请求内容，将内容注册到ap框架中，并在ApiController中尝试获取
     *
     * @param Ap_Request_Abstract  $request
     * @param Ap_Response_Abstract $response
     * @throws Hk_Core_ApiError
     */
    public function routerShutdown(Ap_Request_Abstract $request, Ap_Response_Abstract $response) {
        if ($this->isSkip($request->controller)) {
            return;
        }

        if (empty($this->requestId)) {                               # rpc请求必须带requestId，否则将抛出异常
            throw new Hk_Core_ApiError(Hk_Core_ErrorCodes::RPC_ERR_REQUESTID);
        } elseif (time() - $this->requestTime > $this->expire) {     # 请求过期
            throw new Hk_Core_ApiError(Hk_Core_ErrorCodes::RPC_REQUEST_EXPIRED);
        } elseif (false === $this->mcClient) {                       # 无法获取mc状态，放弃requestId校验
            return;
        }

        $key = $this->getKey();
        $ret = $this->mcClient->get($key);
        if (false === $ret) {              # 无法获取requestId状态，新请求/mc失效，直接放行
            $this->mcClient->set($key, -1, $this->expire);
            return;
        } elseif (-1 === $ret) {           # requestId对应一个旧请求，但上次请求并未给返回值，这里直接判断用户请求已失效，需重新请求
            throw new Hk_Core_ApiError(Hk_Core_ErrorCodes::RPC_REQUEST_REPEAT);
        } else {                           # requestId对应一个旧请求，请求有结果，注册cacheResp，后面的流程可直接获取结果，在ApiController中使用
            Ap_Registry::set($this->cflag, json_decode($ret, true));
            return;
        }
    }

    /**
     * 缓存请求方存储的返回结果，缓存住请求内容
     *
     * @param Ap_Request_Abstract  $request
     * @param Ap_Response_Abstract $response
     */
    public function dispatchLoopShutdown(Ap_Request_Abstract $request, Ap_Response_Abstract $response) {
        if ($this->isSkip($request->controller)) {
            return;
        }
        $key  = $this->getKey();
        $resp = Ap_Registry::get($this->cflag); # 在ApiController中set的值，返回值的json串
        if (false === $resp) {                  # 无法获取注册的返回值
            return;
        }
        $this->mcClient->set($key, $resp, $this->expire);
        return;
    }

    /**
     * 是否跳过插件逻辑
     *
     * @return boolean
     */
    private function isSkip($controller) {
        if ("Rpc" !== $controller) {   # rpc请求才执行
            return true;
        } elseif ($this->skip) {       # 开发调试时使用此标记可以跳过
            return true;
        }
        return false;
    }

    private function getKey() {
        return "{$this->appId}_zybrpc_" . $this->requestId;
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */