<?php

/**
 * @file ExamCategory.php
 * <AUTHOR>
 * @date 2017-06-06
 * @version $Revision$-
 * @brief 试卷分类管理
 * 
 * @deprecated  已废弃 2020/06/18
 *
 **/

class Hk_Dao_Practice_ExamCategory extends Hk_Common_BaseDao {
    public function __construct() {
        $this->_dbName      = "zybpractice/practice";
        $this->_table       = "tblExamCategory";
        $this->arrFieldsMap = array(
            'id'           => 'id',
            'term'         => 'term',
            'typeId'       => 'typeId',
            'provinceId'   => 'provinceId',
            'provinceName' => 'provinceName',
            'cityId'       => 'cityId',
            'cityName'     => 'cityName',
            'areaId'       => 'areaId',
            'areaName'     => 'areaName',
            'examId'       => 'examId',
            'ext'          => 'ext',
        );

        $this->arrTypesMap = array(
            'id'           => Hk_Service_Db::TYPE_INT,    
            'term'         => Hk_Service_Db::TYPE_STR,    
            'typeId'       => Hk_Service_Db::TYPE_INT, //试卷类型1-期中,2-期末,3-中考模拟,4-高考模拟
            'provinceId'   => Hk_Service_Db::TYPE_INT,     
            'provinceName' => Hk_Service_Db::TYPE_STR,    
            'cityId'       => Hk_Service_Db::TYPE_INT,    
            'cityName'     => Hk_Service_Db::TYPE_STR,    
            'areaId'       => Hk_Service_Db::TYPE_INT,    
            'areaName'     => Hk_Service_Db::TYPE_STR,    
            'examId'       => Hk_Service_Db::TYPE_INT, 
            'ext'          => Hk_Service_Db::TYPE_JSON,    
        );
    }

    /**
     * 获取指定学科+年级+学期+试卷类型+定位的试卷id
     * 读db
     * @param  string $term    courseId_gradeId_semesterId
     * @param  int    $typeId  试卷类型
     * @param  int    $provId  省id
     * @param  int    $cityId  市id
     * @param  int    $areaId  区id
     * @return mix
     */
    public function getLbsExamid($term, $typeId, $provId, $cityId=-1, $areaId=-1) {
        $term = trim($term);
        if (("" == $term) || (0 >= $typeId) || (0 >= $provId)) {
            return false;
        }

        $arrFields = array('examId');

        //查询字段的转换
        $arrFields = Hk_Service_Db::mapField($arrFields, $this->arrFieldsMap, true);
        $strFields = implode(",", $arrFields);

        $strLbs = array(" "); //控制前面的AND
        $strLbs[] = "provinceId=".intval($provId);
        if (-1 < $cityId) {
            $strLbs[] = "cityId=".intval($cityId);
        }
        if (-1 < $areaId) {
            $strLbs[] = "areaId=".intval($areaId);
        } 
        $strLbs = implode(" AND ", $strLbs);

        $sql = sprintf("SELECT %s FROM %s WHERE term=unhex('%s') AND typeId=%d %s LIMIT 500", 
            $strFields,
            $this->_table,
            bin2hex($term),
            intval($typeId),
            $strLbs
        );

        $data = $this->query($sql);
        if (false === $data) {
            Bd_Log::warning("Error[dbError] Detail[db select failed, sql:{$sql}");
            return false;
        }
        return $data;
    }


    /**
     * 获取指定学科+年级+学期+试卷类型+教材的默认试卷id
     * 直接读conf文件
     * @param  string $term    courseId_gradeId_semesterId
     * @param  int    $typeId  试卷类型
     * @param  array  $vers    需要的版本id
     * @return mix
     */
    public function getDefaultExamidByVers($term, $typeId, $vers) {
        $term = trim($term);
        if (("" == $term) || !is_array($vers) || empty($vers)) {
            return false;
        }

        //获取conf文件
        $path = "/evaluation_default_exams/{$term}/{$typeId}/";
        $conf = Bd_Conf::getAppConf($path);
        if (empty($conf)) {
            return false;
        }

        $data = array();
        $arrVids = array_flip($vers);
        foreach ($conf as $vid => $item) {
            if (isset($arrVids[$vid])) {
                $arrExams = is_array($item)? $item : array();
                $data[$vid] = array_keys($arrExams);
            }
        }

        return $data;
    }
}
