<?php


/**
 * @brief 知识点详细内容
 */
class Hk_Dao_Tiku_PointVideo extends Hk_Common_BaseDao
{
    public function __construct(){
        $this->_dbName = "homework/homework";
        //$this->_db = Hk_Service_Db::getDB($this->_dbName);
        $this->_db = null;
        $this->_table = "tblPointVideo";
        $this->arrFieldsMap = array(
            'id'      => 'id',
            'pointId' => 'pointId',
            'vid'     => 'vid',
        );

        $this->arrTypesMap = array(
            'id'      => Hk_Service_Db::TYPE_INT,
            'pointId' => Hk_Service_Db::TYPE_INT,
            'vid'     => Hk_Service_Db::TYPE_INT,
        );
    }
}
