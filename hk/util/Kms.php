<?php

class Hk_Util_Kms {

    private $isDev;
    private $host;

    const defaultHost = "unix:/usr/local/var/run/kms.sock";
    const retry = 2;
    const timeout = 0.1;
    const connTimeout = 0.1;
    const errPrefix = "ERROR:";
    const devCipherKey = "123456";
    const kmsDevPrefix = "ZYBKMS10000210400000016746573742e7a7962";
    function __construct() {
        $this->isDev = !Hk_Util_Env::isDockerPlatform();
        $this->host = getenv("KMS_SOCK") == "" ? self::defaultHost : getenv("KMS_SOCK");
    }


    public function encrypt($text = '') {
        if ($this->isDev) {
            return base64_encode(self::kmsDevPrefix.openssl_encrypt($text, 'AES-128-ECB', self::dev<PERSON>ipher<PERSON><PERSON>, 0));
        }
        return $this->send($text, "encrypt");

    }

    public function decrypt($text = '') {
        if ($this->isDev) {
            return openssl_decrypt(substr(base64_decode($text), strlen(self::kmsDevPrefix)), 'AES-128-ECB', self::devCipherKey, 0);
        }
        return $this->send($text, "decrypt");
    }

    private function send($text= '', $url = '') {
        // 超时时间追加到header中
        $header["timeout"] = self::timeout;
        $header["connectTimeOut"] = self::connTimeout;

        $req = [
            "convert"  => "form",
            "host"     => $this->host,
            "url"      => "http://localhost/".$url,
            "method"   => Ext_Ral_HttpCurl::METHOD_POST,
            "input"    => [
                "text" => $text,
            ],
            "retry"    => self::retry,
            "reqProxy" => 0,
        ];
        $ret = Ext_Ral_HttpCurl::instance()->curl($req);
        if (!$ret) {
            $ralArg['errno'] = -1;
            $ralArg['errmsg'] = "unknown error";
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return $text;
        }
        if (substr($ret["data"], 0, strlen(self::errPrefix)) == self::errPrefix) {
            $ralArg['errno'] = -1;
            $ralArg['errmsg'] = substr($ret["data"], strlen(self::errPrefix));
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return $text;
        }
        return $ret["data"];
    }
}