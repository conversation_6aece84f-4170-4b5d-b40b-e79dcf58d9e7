<?php

/**
 * @file UserExam.php
 * <AUTHOR>
 * @date 2017-06-06
 * @version $Revision$-
 * @brief 用户-评测练习记录
 *
 * @deprecated  已废弃 2020/06/18
 **/

class Hk_Ds_Practice_UserExam {

    private $_objDaoUserExam;

    public function __construct() {
        $this->_objDaoUserExam = new Hk_Dao_Practice_UserExam();
    }

    /**
     * 根据examId去轮循20次表，拼装数据后返回
     * @param int   $examId
     * @return array|bool
     */
    public function getAllByExamId($examId) {
        if ($examId <= 0) {
            Bd_Log::warning("Error[paramError] Detail[examId:{$examId}]");
            return false;
        }

        Hk_Util_Log::start('ds_getAllByExamId');
        $ret = $this->_objDaoUserExam->getAllByExamId($examId);
        Hk_Util_Log::stop('ds_getAllByExamId');

        if (!empty($ret)) {
            foreach ($ret as $r_key => &$r_val) {
                if (isset($r_val['tidList'])) {
                    $r_val['tidList'] = json_decode($r_val['tidList'], true);
                }
                if (isset($r_val['pidList'])) {
                    $r_val['pidList'] = json_decode($r_val['pidList'], true);
                }
                if (isset($r_val['diffStatis'])) {
                    $r_val['diffStatis'] = json_decode($r_val['diffStatis'], true);
                }
                if (isset($r_val['graph'])) {
                    $r_val['graph'] = json_decode($r_val['graph'], true);
                }
                if (isset($r_val['ext'])) {
                    $r_val['ext'] = json_decode($r_val['ext'], true);
                }
            }
            unset($r_val);
        }

        return $ret;
    }

    /**
     * 获取做过的试卷id(examId去重)
     * @param integer $uid
     * @param string  $cuid  pc端舍弃不用
     * @param integer $examIds
     * @return mix
     */
    public function getExamInfoByExamId($uid, $cuid='', $examId) {
        if(empty($examId) || ($uid <= 0)) {
            Bd_Log::warning("Error[paramError] Detail[uid:{$uid} cuid:{$cuid}]");
            return false;
        }

        Hk_Util_Log::start('ds_getExamInfoByExamId');
        $ret = $this->_objDaoUserExam->getExamInfoByExamId($uid, $cuid, $examId);
        Hk_Util_Log::stop('ds_getExamInfoByExamId');
        if (!empty($ret)) {
            foreach ($ret as $r_key => &$r_val) {
                if (isset($r_val['tidList'])) {
                    $r_val['tidList'] = json_decode($r_val['tidList'], true);
                }
                if (isset($r_val['pidList'])) {
                    $r_val['pidList'] = json_decode($r_val['pidList'], true);
                }
                if (isset($r_val['diffStatis'])) {
                    $r_val['diffStatis'] = json_decode($r_val['diffStatis'], true);
                }
                if (isset($r_val['graph'])) {
                    $r_val['graph'] = json_decode($r_val['graph'], true);
                }
                if (isset($r_val['ext'])) {
                    $r_val['ext'] = json_decode($r_val['ext'], true);
                }
            }
            unset($r_val);
        }

        return $ret;
    }


    /**
     * 获取做过的试卷id(examId去重)
     * @param integer $uid
     * @param string  $cuid
     * @param integer $examIds
     * @return mix
     */
    public function getUniqExamIdByExamIds($uid, $cuid, $examIds) {
        if(!is_array($examIds) || empty($examIds) || ($uid <= 0 && strlen($cuid) <= 0)) {
            Bd_Log::warning("Error[paramError] Detail[uid:{$uid} cuid:{$cuid}]");
            return false;
        }

        Hk_Util_Log::start('ds_getUniqExamIdByExamIds');
        $ret = $this->_objDaoUserExam->getUniqExamIdByExamIds($uid, $cuid, $examIds);
        Hk_Util_Log::stop('ds_getUniqExamIdByExamIds');

        return $ret;
    }


    /**
     * 获取用户做过的试卷
     * @param integer $uid
     * @param string  $cuid
     * @param array   $dbIds
     * @param  array  $arrFields  需要查询的字段名数组，格式必须为数组
     * @return mix
     */
    public function getDetailByDbIds($uid, $cuid, $dbIds, $arrFields=array()) {
        if( !is_array($dbIds) || empty($dbIds) || ($uid <= 0 && strlen($cuid) <= 0) ) {
            Bd_Log::warning("Error[paramError] Detail[uid:{$uid} cuid:{$cuid}]");
            return false;
        }

        Hk_Util_Log::start('ds_getDetailByDbIds');
        $ret = $this->_objDaoUserExam->getByDbIds($uid, $cuid, $dbIds, $arrFields);
        Hk_Util_Log::stop('ds_getDetailByDbIds');
        if (!empty($ret)) {
            foreach ($ret as $r_key => &$r_val) {
                if (isset($r_val['tidList'])) {
                    $r_val['tidList'] = json_decode($r_val['tidList'], true);
                }
                if (isset($r_val['pidList'])) {
                    $r_val['pidList'] = json_decode($r_val['pidList'], true);
                }
                if (isset($r_val['diffStatis'])) {
                    $r_val['diffStatis'] = json_decode($r_val['diffStatis'], true);
                }
                if (isset($r_val['graph'])) {
                    $r_val['graph'] = json_decode($r_val['graph'], true);
                }
                if (isset($r_val['ext'])) {
                    $r_val['ext'] = json_decode($r_val['ext'], true);
                }
            }
            unset($r_val);
        }

        return $ret;
    }


    /**
     * 获取诊断记录列表
     * @param integer $uid
     * @param string  $cuid
     * @param integer $typeId
     * @param integer $pn
     * @param integer $rn
     * @param array   $arrFields
     * @return mix
     */
    public function getListByUser($uid, $cuid, $typeId=0, $pn=0, $rn=20, $arrFields=array()) {
        if( (0 >= $uid) && (0 >= strlen($cuid)) || (0 > $pn) || (0 >= $rn) ) {
            Bd_Log::warning("Error[paramError] Detail[uid:{$uid} cuid:{$cuid} pn:{$pn} rn:{$rn}]");
            return false;
        }

        if (200 < $rn) {
            Bd_Log::warning("Error[paramAlarm] Detail[really need a plenty of rn:{$rn}?]");
            return false;
        }

        Hk_Util_Log::start('ds_getListByUser');
        $ret = $this->_objDaoUserExam->getListByUser($uid, $cuid, $typeId, $pn, $rn, $arrFields);
        Hk_Util_Log::stop('ds_getListByUser');
        if (!empty($ret)) {
            foreach ($ret as $r_key => &$r_val) {
                if (isset($r_val['tidList'])) {
                    $r_val['tidList'] = json_decode($r_val['tidList'], true);
                }
                if (isset($r_val['pidList'])) {
                    $r_val['pidList'] = json_decode($r_val['pidList'], true);
                }
                if (isset($r_val['diffStatis'])) {
                    $r_val['diffStatis'] = json_decode($r_val['diffStatis'], true);
                }
                if (isset($r_val['graph'])) {
                    $r_val['graph'] = json_decode($r_val['graph'], true);
                }
                if (isset($r_val['ext'])) {
                    $r_val['ext'] = json_decode($r_val['ext'], true);
                }
            }
            unset($r_val);
        }
        return $ret;
    }


    /**
     * 新增评测练习记录
     * @param integer $uid
     * @param integer $cuid
     * @param array   $arrParams {
     *    'examId'     => 100,
     *    'type'       => 1,
     *    'isFinish'   => 1,
     *    'score'      => 213,
     *    'scoreTotal' => 421,
     *    'title'      => 'sda',
     *    'subtitle'   => 'asxzcz',
     *    'courseId'   => 1,
     *    'rankTotal'  => 231,
     *    'rankNo'     => 22,
     *    'tidList'    => array(tid1=>array('choice'=>int,'judge'=>0/1), array(...)),
     *    'pidList'    => array(pid1=>array('total'=>int,'score'=>int,'avg'=>int), array(...)),
     *    'diffStatis' => array(diff1=>array('score'=>int,'lost'=>int), array(...)),
     *    'graph'      => array(key1=>array('score'=>int,'rankNo'=>int), array(...)),
     *    'bit'        => 0,
     *    'ext'        => array("bookId"=>int, 'redisRankMember'=>'xasds'),
     *    'gradeId'    => 0,
     *    'semesterId' => 0,
     *    'mark'       => 0,
     *    'solveTime'  => 0,
     * @return mix
     **/
    public function addExam($uid, $cuid = '', $arrParams) {
        $ret = array('errno' => 1);

        if( ((0 >= $uid) ) || (0 >= $arrParams['examId']) || empty($arrParams['tidList']) ) {
            Bd_Log::warning("Error[paramError] Detail[uid:{$uid} cuid:{$cuid} examId:{$arrParams['examId']}]");
            return false;
        }

        $paras = $this->_parseParams($arrParams);
        if (empty($paras)) {
            Bd_Log::warning("Error[paramError] Detail[addExam parse paras failed]");
            return false;
        }
        Hk_Util_Log::start('ds_addExam');
        $result = $this->_objDaoUserExam->add($uid, $cuid, $paras);
        Hk_Util_Log::stop('ds_addExam');
        if (0 >= $result) {
            return false;
        }

        $ret['errno'] = 0;
        $ret['dbId']  = intval($result);

        return $ret;
    }


    /**
     * 批量新增诊断记录（[内部]为单个用户导数据使用）
     * @param integer $uid
     * @param integer $cuid
     * @param array   $items 
     * @return mix
     **/
    public function insertRecordBatch($uid, $cuid, $items) {
        if ( ((0 >= $uid) && (0 >= strlen($cuid))) || empty($items) || (60 < count($items)) ) {
            Bd_Log::warning("Error[paramError] Detail[uid:{$uid} cuid:{$cuid} items:".count($items)."]");
            return false;
        }

        $records = array();
        foreach ($items as $key => $val) {
            if(0 >= $val['examId']) {
                Bd_Log::warning("Error[paramError] Detail[examId:{$val['examId']}]");
                return false;
            }
            $paras = array(
                'uid'        => intval($uid),
                'cuid'       => strval($cuid),
                'examId'     => intval($val['examId']),
                'type'       => intval($val['type']),
                'mtime'      => intval($val['mtime']),
                'isFinish'   => intval($val['isFinish']),
                'score'      => intval($val['score']),
                'scoreTotal' => intval($val['scoreTotal']),
                'title'      => strval($val['title']),
                'subtitle'   => strval($val['subtitle']),
                'courseId'   => intval($val['courseId']),
                'rankTotal'  => intval($val['rankTotal']),
                'rankNo'     => intval($val['rankNo']),
                'bit'        => intval($val['bit']),
                'gradeId'    => intval($val['gradeId']),
                'semesterId' => intval($val['semesterId']),
                'mark'       => intval($val['mark']),
                'solveTime'  => intval($val['solveTime']),
            );
            if (isset($val['tidList'])) {
                $paras['tidList'] = json_encode((array)$val['tidList']);
            }
            if (isset($val['pidList'])) {
                $paras['pidList'] = json_encode((array)$val['pidList']);
            }
            if (isset($val['diffStatis'])) {
                $paras['diffStatis'] = json_encode((array)$val['diffStatis']);
            }
            if (isset($val['graph'])) {
                $paras['graph'] = json_encode((array)$val['graph']);
            }
            if (isset($val['ext'])) {
                $paras['ext'] = json_encode((array)$val['ext']);
            }
            $records[] = $paras;
        }

        Hk_Util_Log::start('ds_addExamBatch');
        $result = $this->_objDaoUserExam->addBatch($uid, $cuid, $records);
        Hk_Util_Log::stop('ds_addExamBatch');
        if (!$result) {
            return false;
        }

        return $result;
    }


    /**
     * 批量删除诊断记录（为单个用户导数据使用）
     * @param integer $uid
     * @param integer $cuid
     * @param array   $dbIds 
     * @return mix
     **/
    public function delByDbIds($uid, $cuid, $dbIds) {
        if ( ((0 >= $uid) && (0 >= strlen($cuid))) || empty($dbIds) || (200 < count($dbIds))) {
            Bd_Log::warning("Error[paramError] Detail[uid:{$uid} cuid:{$cuid} dbIds:".count($dbIds)."]");
            return false;
        }

        Hk_Util_Log::start('ds_delByDbids');
        $result = $this->_objDaoUserExam->delByDbIds($uid, $cuid, $dbIds);
        Hk_Util_Log::stop('ds_delByDbids');
        if (!$result) {
            return false;
        }

        return $result;
    }


    /**
     * 通用处理参数
     * @param array   $data {
     *    'examId'     => 100,
     *    'type'       => 1,
     *    'isFinish'   => 1,
     *    'score'      => 213,
     *    'scoreTotal' => 421,
     *    'title'      => 'sda',
     *    'subtitle'   => 'asxzcz',
     *    'courseId'   => 1,
     *    'rankTotal'  => 231,
     *    'rankNo'     => 22,
     *    'tidList'    => array(tid1=>array('choice'=>int,'judge'=>0/1), array(...)),
     *    'pidList'    => array(pid1=>array('total'=>int,'score'=>int,'avg'=>int), array(...)),
     *    'diffStatis' => array(diff1=>array('score'=>int,'lost'=>int), array(...)),
     *    'graph'      => array(key1=>array('score'=>int,'rankNo'=>int), array(...)),
     *    'bit'        => 0,
     *    'ext'        => array("bookId"=>int, 'redisRankMember'=>'xasds'),
     *    'gradeId'    => 0,
     *    'semesterId' => 0,
     *    'mark'       => 0,
     *    'solveTime'  => 0,
     * @return mix
     **/
    private function _parseParams($data) {
        if ( empty($data) || (0 >= $data['examId']) ) {
            return false;
        }

        //组装所需要的各个字段
        $paras = array();

        $paras['examId']     = intval($data['examId']);
        $paras['type']       = intval($data['type']);
        $paras['isFinish']   = intval($data['isFinish']);
        $paras['score']      = intval($data['score']);
        $paras['scoreTotal'] = intval($data['scoreTotal']);
        $paras['title']      = strval($data['title']);
        $paras['subtitle']   = strval($data['subtitle']);
        $paras['courseId']   = intval($data['courseId']);
        $paras['rankTotal']  = intval($data['rankTotal']);
        $paras['rankNo']     = intval($data['rankNo']);
        $paras['bit']        = intval($data['bit']);
        $paras['gradeId']    = intval($data['gradeId']);
        $paras['semesterId'] = intval($data['semesterId']);
        $paras['mark']       = intval($data['mark']);
        $paras['solveTime']  = intval($data['solveTime']);


        $paras['tidList'] = array();
        if (is_array($data['tidList'])) {
            foreach ($data['tidList'] as $keyTid => $val) {
                if ((0 >= $keyTid) || !isset($val['choice']) || !isset($val['judge'])) {
                    Bd_Log::warning("Error[paramError] Detail[answer para invalid, tid:{$keyTid} choice:{$val['choice']} judge:{$val['judge']}]");
                    return false;
                }
                $paras['tidList'][$keyTid] = array('c' => intval($val['choice']), 'j' => intval($val['judge']), 'du' => intval($val['duration']));
            }
        }

        $paras['pidList'] = array();
        if (is_array($data['pidList'])) {
            foreach ($data['pidList'] as $keyPid => $val) {
                if ( (0 >= $keyPid) || !isset($val['total']) || !isset($val['score']) || !isset($val['avg']) ) {
                    Bd_Log::warning("Error[paramError] Detail[answer para invalid, tid:{$keyTid} total:{$val['total']} score:{$val['score']} avg:{$val['avg']}]");
                    return false;
                }
                $paras['pidList'][$keyPid] = array(
                    'num' => intval($val['num']),
                    'tot' => intval($val['total']), 
                    's'   => intval($val['score']), 
                    'avg' => intval($val['avg'])
                );
            }
        }

        $paras['diffStatis'] = array();
        if (is_array($data['diffStatis'])) {
            foreach ($data['diffStatis'] as $keyDiff => $val) {
                if ( (0 >= $keyDiff) || !isset($val['score']) || !isset($val['lost']) ) {
                    Bd_Log::warning("Error[paramError] Detail[answer para invalid, tid:{$keyTid} score:{$val['score']} lost:{$val['lost']}]");
                    return false;
                }
                $paras['diffStatis'][$keyDiff] = array(
                    's' => intval($val['score']), 
                    'l' => intval($val['lost'])
                );
            }
        }

        $paras['graph'] = array();
        if (is_array($data['graph'])) {
            foreach ($data['graph'] as $keyGrkey => $val) {
                if ( !isset($val['score']) || !isset($val['rankNo']) ) {
                    Bd_Log::warning("Error[paramError] Detail[answer para invalid, tid:{$keyTid} score:{$val['score']} rankNo:{$val['rankNo']}]");
                    return false;
                }
                $paras['graph'][$keyGrkey] = array(
                    's'  => intval($val['score']), 
                    'no' => intval($val['rankNo'])
                );
            }
        }

        $paras['ext'] = is_array($data['ext'])? $data['ext'] : array();

        //json格式化
        $paras['tidList']    = json_encode($paras['tidList']);
        $paras['pidList']    = json_encode($paras['pidList']);
        $paras['diffStatis'] = json_encode($paras['diffStatis']);
        $paras['graph']      = json_encode($paras['graph']);
        $paras['ext']        = json_encode($paras['ext']);

        return $paras;
    }
}
