<?php


/**
 * UDA平台提供的实时版本激活人数相关接口查询
 *
 * @since 2.0 2017-10-09 接口全部变更，uda接口新增taskId参数，vcname支持多个，新增获取uda统计状态接口
 * @since 1.1 2017-06-16 uda接口新增appId参数
 * @since 1.0 2017-01-04 初始化
 *
 * @filesource hk/ds/publish/UdaLogger.php
 * <AUTHOR>
 * @version 2.0
 * @date    2017-10-09
 */
//class Hk_Ds_Publish_UdaLogger {
//
//
//    const UDA_ENV    = "http://uda.zuoyebang.cc/uda/api";            # uda.zuoyebang.cc,2020-03-02域名切换
//    const UDA_CREATE = '%s/platmispublish?av=%s&vc=%s&appId=%s&taskId=%s';
//    const UDA_DELETE = '%s/platmisclose?id=%s';
//    const UDA_STATUS = '%s/platmisinfo?id=%s';
//
//    const UDA_REDIS_IP   = '*************';//redis服务ip更换,2020-06-03
//    const UDA_REDIS_PORT = 8700;
//
//    /**
//     * 根据taskId, vcname列表, vc, appId创建uda平台激活统计<br>
//     * 返回udaId列表(vcname => udaId)<br>
//     * uda返回数据结构：<br>
//     * <code>
//     * array(<br>
//     *     "errno" => 0,<br>
//     *     "ret"   => array(<br>
//     *         "vcname" => "udaId",<br>
//     *         ...<br>
//     *     ),<br>
//     * );
//     * </code>
//     *
//     * @param int         $taskId
//     * @param int         $vc
//     * @param array       $vcnames
//     * @param int         $appId
//     * @return mixed:array|boolean
//     */
//    public static function create($taskId, $vc, $vcnames, $appId = "homework") {
//        $vcname = implode(",", $vcnames);
//        $url    = sprintf(self::UDA_CREATE, self::UDA_ENV, $vcname, $vc, $appId, $taskId);
//        $ret    = self::xcurl($url);
//        if (empty($ret)) {
//            return false;
//        }
//        $resp   = @json_decode($ret, true);
//        if (0 !== $resp["errno"]) {
//            return false;
//        }
//        $udaIds = array();
//        foreach ($resp["ret"] as $key => $val) {
//            $udaIds[$key] = intval($val);
//        }
//        return $udaIds;
//    }
//
//    /**
//     * 删除uda平台的实时统计任务
//     *
//     * @param array        $udaIds
//     * @return boolean
//     */
//    public static function delete($udaIds) {
//        if (empty($udaIds)) {
//            Bd_Log::addNotice("udaOutput", "noDel");
//            return true;
//        }
//
//        $url  = sprintf(self::UDA_DELETE, self::UDA_ENV, implode(",", $udaIds));
//        $resp = self::xcurl($url);
//        if (empty($resp)) {
//            return false;
//        }
//        $resp = @json_decode($resp, true);
//        if (0 !== $resp["errno"]) {
//            return false;
//        }
//        return true;
//    }
//
//    /**
//     * 获取udaId对应任务的状态<br>
//     * uda返回数据结构：<br>
//     * <code>
//     * array(<br>
//     *     "errno" => 0,<br>
//     *     "ret"   => array(<br>
//     *         "vcname" => "status",<br>
//     *         ...<br>
//     *     ),<br>
//     * );<br>
//     * </code>
//     * errno=0正常返回, status=0 已开启, status=1 已关闭, status=2
//     *
//     * @param array       $udaIds
//     * @return mixed:array|boolean
//     */
//    public static function getUdaStatus($udaIds) {
//        $udaId = implode(",", $udaIds);
//        $url   = sprintf(self::UDA_STATUS, self::UDA_ENV, $udaId);
//        $ret   = self::xcurl($url);
//        if (empty($ret)) {
//            return false;
//        }
//
//        $resp  = @json_decode($ret, true);
//        if (0 !== $resp["errno"]) {
//            return false;
//        }
//        $status = array();
//        foreach ($resp["ret"] as $key => $val) {
//            $status[$key] = intval($val);
//        }
//        return $status;
//    }
//
//    /**
//     * 查找当前指定版本的实时激活人数
//     *
//     * @param int         $udaId
//     * @return mixed:int|boolean
//     */
//    public static function getActivateNum($udaId) {
//        if (0 === intval($udaId)) {
//            Bd_Log::addNotice("udaOutput", "noDel");
//            return 0;
//        }
//
//        $udaRedis   = new Redis();
//        $udaRedis->connect(self::UDA_REDIS_IP, self::UDA_REDIS_PORT, 1);
//        try {
//            $actNum = $udaRedis->scard($udaId);
//        } catch (RedisException $e) {
//            $actNum = false;
//        }
//        return false === $actNum ? false : intval($actNum);
//    }
//
//    /**
//     * 网络请求
//     *
//     * @param string      $url
//     * @param array       $post
//     * @param int         $timeout
//     * @return mixed:string|boolean
//     */
//    private static function xcurl($url, $post = array(), $timeout = 2) {
//        $ch = curl_init();
//        curl_setopt($ch, CURLOPT_URL, $url);
//        curl_setopt($ch, CURLOPT_PROXY, "proxy.zuoyebang.com:80");
//        curl_setopt($ch, CURLOPT_AUTOREFERER, true);
//        curl_setopt($ch, CURLOPT_HEADER, 0);
//        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
//        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
//        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
//        if (count($post) > 0) {
//            curl_setopt($ch, CURLOPT_POST, 1);
//            curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
//        }
//        $output = curl_exec($ch);
//        Bd_Log::addNotice("udaOutput",  $output);
//        Bd_Log::addNotice('udaCurlErr', curl_error($ch));
//        curl_close($ch);
//        return $output;
//    }
//}
