<?php
/***************************************************************************
 * 
 * Copyright (c) 2014 baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Friend.php
 * <AUTHOR>
 * @date 2014-8-11 
 * @brief 好友
 *
 **/

class Hk_Ds_Friend_Friend
{
    private $_objDaoFriend;
	const USER_FRIENDLIST_PREFIX     = "friendlist:";
	//好友
    const FRIEND_TYPE_NORMAL        = 0; //好友表type字段
    const FRIEND_STATUS_NORMAL      = 0; //好友表status字段  好友状态
    const FRIEND_STATUS_DELETE      = 9; //好友表status字段  删除状态
    const FRIEND_GROUP_ID_DEFAULT   = 0; //好友默认分组
	const FRIEND_MAX_NUM            = 2000; //好友最大数量

    public function __construct()
    {
        $this->_objDaoFriend = new Hk_Dao_Friend_Friend();
    }


    /**
     * @brief 获取类型和状态
     * @param int $intUid 用户uid
     * @param int $intFid 好友fid
     * @return array type类型 status状态 chatId聊天id
     **/
    public function getFriendInfo($intUid, $intFid)
    {
        $arrOutput = array(
            'type'      => -1,
            'status'    => -1,
            'chatId'	=> -1,
        );
        if($intUid <= 0 || $intFid <= 0)
        {
            return $arrOutput;
        }

        $arrFields = array('type', 'status', 'chatId');
        $arrConds = array(
            'uid'   => $intUid,
            'fid'   => $intFid,
        );

        $arrRes = $this->getFriendList($arrFields, $arrConds);
        if(count($arrRes[0]) > 0)
        {
            $arrOutput = array(
                'type'      => intval($arrRes[0]['type']),
                'status'    => intval($arrRes[0]['status']),
                'chatId'    => intval($arrRes[0]['chatId']),
            );
        }
        return $arrOutput;
    }


    /**
     * @brief 获取好友状态
     * @param int $intUid 用户uid
     * @param int $intFid 好友fid
     * @return int $intStatus 
     **/
    public function getFriendStatus($intUid, $intFid)
    {
        $intStatus = -1;
        if($intUid <= 0 || $intFid <= 0)
        {
            return $intStatus;
        }

        $arrFields = array('status');
        $arrConds = array(
            'uid'   => $intUid,
            'fid'   => $intFid,
        );

        $arrRes = $this->getFriendList($arrFields, $arrConds);
        if(isset($arrRes[0]['status']))
        {
            $intStatus = intval($arrRes[0]['status']);
        }
        return $intStatus;
    }
    
    /**
     * @brief 获取好友数量
     *
     * @param int $intUid 用户id
     * @param int $intType 类型
     **/
    public function getFriendTotal($intUid)
    {
        $intTotal = 0;
        $arrConds = array(
            'uid'       => $intUid,
            'status'	=> self::FRIEND_STATUS_NORMAL,
        );
        $res = $this->getFriendCount($arrConds);
        $intTotal = intval($res);
        return $intTotal;
    }

	 /**
     * @brief 获取最近添加的top若干个好友
     *
     * @param int $intUid 用户id
	 * @param int $createTime 时间戳，表示最近时间，大于某个时间戳
     * @param int $intLimit 查询数量
     **/
	public function getUserNewFriendList($intUid, $createTime, $intLimit){
		$arrConds = array(
			'uid' => $intUid,
			'status' => self::FRIEND_STATUS_NORMAL,
			'createTime > '.$createTime ." or updateTime > ".$createTime,
		);
        $arrAppends = array(
            "ORDER BY id DESC",
            "LIMIT $intLimit OFFSET 0",
        );
		$arrFields = array('fid', 'createTime', 'updateTime');
		$res = $this->getFriendList($arrFields, $arrConds, $arrAppends);
		return $res;
	}
    
    /**
     * @brief 获取好友列表(默认不返回已删除的好友，按添加顺序倒序排)
     *
     * @param int $intUid 用户id
     * @param int $intLimit 查询距离
     * @param int $intOffset 查询偏移量
     * @param bool $boolCache 是否查询缓存
     **/
    public function getUserFriendList($intUid, $intLimit = 0, $intOffset = 0, $boolCache = true)
    {
        $intLimit = intval($intLimit) <= 0 ? self::FRIEND_MAX_NUM : intval($intLimit);
        $intOffset = intval($intOffset);
		//读缓存
		if($boolCache){
			$objCache = Hk_Service_Memcached::getInstance('ucloud');
			$cacheKey = self::USER_FRIENDLIST_PREFIX.$intUid;
			$arrRes = $objCache->get($cacheKey);
		}else{
			$arrRes = null;
		}
		if(is_array($arrRes) && count($arrRes) > 0){
			//命中缓存
			if(count($arrRes) > $intLimit){
				$arrOutput = array_slice($arrRes, $intOffset, $intLimit);
			}elseif($intOffset > 0){
				$arrOutput = array_slice($arrRes, $intOffset);
            }else{
				$arrOutput = $arrRes;
			}
			return $arrOutput;
		} 
		
        $arrOutput = array();
        $arrFields = array('fid');
		$arrConds = array(
			'uid' => $intUid,
			'status' => self::FRIEND_STATUS_NORMAL,
		);
        $arrAppends = array(
            "ORDER BY id DESC",
            "LIMIT $intLimit OFFSET $intOffset",
        );
        $res = $this->getFriendList($arrFields, $arrConds, $arrAppends);
        if(is_array($res))
        {
			$arrOutput = $res;
			if($boolCache){
				$objCache->set($cacheKey, $res, 3600);
			}
        }
        return $arrOutput;
    }

	/**
     * @brief 获取推荐好友列表, 有共同好友的推荐
     *
     * @param int $intUid 用户id
     **/
	public function getRecommendByCommon($uid){
		//加一个缓存策略 to do
		$limit = 20;
		$res = $this->getUserFriendList($uid);
		if(count($res) <= 0){
			return array();
		}
		shuffle($res);
		//获取我的好友
		$arrFriends = array();
		foreach($res as $one){
			$arrFriends[$one['fid']] = $one['fid'];
		}
		//获取我的好友的好友，且不是我的好友,也不是我自己
		$arrOther = array();
		foreach($arrFriends as $fid => $him){
				//加一个缓存策略 to do	
			$fidFriend = $this->getUserFriendList($fid);
			foreach($fidFriend as $one){
				//去重
				if(isset($arrOther[$one['fid']])){
					continue;
				}
				if(isset($arrFriends[$one['fid']])){
					continue;
				}
				if($one['fid'] == $uid)continue;
				$arrOther[$one['fid']] = $one['fid'];
			}
			unset($fidFriend);
			if(count($arrOther) > $limit)break;
		}
		if(count($arrOther) <= 0){
			return $arrOther;
		}
		foreach($arrOther as $oid => $him){
			//有多少共同好友
			$himFriend = $this->getUserFriendList($oid);
			$total = 0;
			foreach($himFriend as $one){
				if(isset($arrFriends[$one['fid']])){
					$total++;
				}
			}
			$arrOther[$oid] = $total;
			unset($himFriend);
		}
		return $arrOther;
	}

	public function getFriendList($arrFields, $arrConds, $arrAppends = null)
	{
		$res = $this->_objDaoFriend->getFriendListByConds($arrFields, $arrConds, $arrAppends);
        return $res;
    }

    public function getFriendCount($arrConds, $arrAppends = null)
    {
        $res = $this->_objDaoFriend->getFriendCountByConds($arrConds, $arrAppends);
        return $res;
    }
}




 
