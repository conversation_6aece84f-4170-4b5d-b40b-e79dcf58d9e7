<?php


/**
 * 反作弊token信息，使用redis存储
 *
 * @filesource hk/ds/user/Antispam.php
 * <AUTHOR>
 * @version 1.0
 * @date    2018-11-28
 */
class Hk_Ds_User_Antispam {

    private $tokens;
    private $redis;

    private static $inst;

    const ANTISPAM_SERVER_NAME = "antispam_server";
    const ANTISPAM_SERVER_REDIS_FAIL = 5001;

    private function __construct() {
        $this->redis = Hk_Service_RedisClient::getInstance("antispam");
    }

    public static function getInstance() {
        if (empty(self::$inst)) {
            self::$inst = new Hk_Ds_User_Antispam();
        }
        return self::$inst;
    }


    /**
     * 通过appId、设备cuid从antispam-server获取设备注册反作弊信息
     *
     * @param string $cuid
     * @return mixed:array|boolean
     */
    public function getSignToken($appId, $cuid) {
        if (isset($this->tokens[$appId][$cuid])) {
            return $this->tokens[$appId][$cuid];
        }

        $path =
            array(
                "pathinfo" => "/antispam-server/gettoken",
            );

        $data = array(
            "appId" => $appId,
            "cuid" => $cuid,
        );

        $ret = ral(self::ANTISPAM_SERVER_NAME, "POST", $data, [], $path);
        if (false === $ret) {
            return false;
        }
        $ret = @json_decode($ret, true);
        if (!empty($ret) && isset($ret["errNo"]) && $ret["errNo"] === 0 && isset($ret["data"])) {
            $this->tokens[$appId][$cuid] = $ret["data"];
            return $ret["data"];
        }
        // 5001为redis挂掉了，降级为通过
        if (!empty($ret) && isset($ret["errNo"]) && $ret["errNo"] === self::ANTISPAM_SERVER_REDIS_FAIL) {
            return false;
        }
        return array();
    }



    /**
     * 为产品线设备注册反作弊token<br>
     * 成功返回新反作弊token<br>
     * 失败返回false
     *
     * @param string      $appId
     * @param string      $cuid
     * @return mixed:string|boolean
     */
    public function registRandomKey($appId, $cuid) {
        if (false === $this->redis) {
            return false;
        }

        $charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";    # 加密选项
        $newRand = '';
        $charlen = strlen($charset) - 1;
        for ($i = 0; $i < 10; $i++) {
            $newRand .= $charset[mt_rand(0, $charlen)];
        }
        $expire  = Bd_Conf::getConf('/hk/antispam/common/expire');
        if (false === $expire) {            # 未配置默认1个月过期时间
            $expire = 2592000;
        }

        $key  = $this->getKey($appId, $cuid);
        $data = array(
            "randomKey"  => $newRand,
            "expireTime" => time() + $expire,
        );
        $ret  = $this->redis->setEx($key, $expire, @json_encode($data));
        if (true === $ret) {
            Bd_Log::addNotice("registRandKey", "succ");
            return $newRand;
        }
        return false;
    }

    private function getKey($appId, $cuid) {
        return "{$appId}:{$cuid}";
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */