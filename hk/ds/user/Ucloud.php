<?php


/**
 * 获取用户信息基础方法
 *
 * @since final 2020-09-21 后续Ucloud方法迁移到hk/service/Ucloud中统一走Rpc方式调用，此文件只处理bug不再做功能新增
 * @since 2.0 2019-11-06 切换缓存到redis，双写预热 tangyang
 * @since 1.5 2017-09-04 删除无用的代码 tangyang
 * @since 1.0 2015-09-08 初始化
 *
 * @filesource ds/user/Ucloud.php
 * <AUTHOR>
 * <AUTHOR>
 * @update  jiangyingjie(<EMAIL>)
 * @version 2.0
 * @date    2019-11-06
 */
class Hk_Ds_User_Ucloud {

    const CACHE_EXPIRE = 259200; 

    private $redis;
    private static $userData = array();
    private $rpcUcloud;

    public function __construct($saasTenantcode = '', $saasAk = '', $saasSk = '') {
        //saas平台分配的租户id ak sk
        $this->rpcUcloud = new Hk_Service_Ucloud($saasTenantcode, $saasAk, $saasSk);
        //$this->redis     = Hk_Service_RedisClient::getInstance("ucloud");
    }

    /**
     * $uid 可以是整型，可以是数组。返回值根据参数不同，可能是一维数组，可能是二维数组。
     * 获取用户基本数据，尽量用这个。逐步解除ext依赖.
     *
     * @param mixed      $uid
     * @return array
     */
    public function getUserInfo($uid, $useCache = true, $useWDb = false) {
        if (is_scalar($uid)) {
            $single = true;
            $uid    = array($uid);
        }
        $data = $this->getUserInfoByArray($uid, $useCache, $useWDb);
        foreach ($data as & $d) {           // 本接口不返回ext
            unset($d['ext']);
        }
        // 如果是取单个用户信息，返回一维数组
        if ($single) {
            return array_pop($data);
        }
        return $data;
    }

    /**
     * $uid 可以是整型，可以是数组。取全部属性，包含ext数组。
     * 获取用户全部数据，有ext。尽量不要调用这个。缺少的数据可以在DAO层extFieldsMap里加，可以筛选掉已经废弃的ext字段。
     *
     * @param mixed      $uid
     * @retuern array
     */
    public function getUserInfoAll($uid, $useCache = true, $useWDb = false) {
        if (is_scalar($uid)) {
            $single = true;
            $uid    = array($uid);
        }

        $data = $this->getUserInfoByArray($uid, $useCache, $useWDb);
        if ($single) {          // 如果是取单个用户信息，返回一维数组
            return array_pop($data);
        }
        return $data;
    }

    /**
     * 用uid数组获取用户基本数据
     *
     * @param array      $uids
     * @return array
     */
    public function getUserInfoByArray($arrUids, $useCache = true, $useWDb = false) {
        $arrOutput = array();
        if (empty($arrUids)) {
            return $arrOutput;
        }

        /**
         * 用户信息没有做影子表
         * 压测时传输偏移过的UID
         * 使用时需要回归到原始UID
         */
        /* -------   Regress Start   ---------- */
        $aUid    = $arrUids;
        $arrUids = array();
        foreach ($aUid as $uid) {
            $arrUids[] = Hk_Util_Navigator::regressUid($uid);
        }
        /* -------   Regress End   ------------ */

        if (count($arrUids) <= 50 && $useCache) {
            $cache    = $this->getUserCacheByArray($arrUids);
            $leftUids = array();
            foreach ($cache as $uid => $one) {
                if (!empty($one)) {
                    if (count(self::$userData) < 1000) {        // 设置static缓存，避免重复取
                        self::$userData[$uid] = $one;
                    }
                    $one = $this->buildData($one);
                    $arrOutput[$uid] = $one;
                } else {
                    $leftUids[] = $uid;
                }
            }
        } else {
            $leftUids = $arrUids;
        }

        // 在cache里全部取到数据，就不用往下走了
        if (empty($leftUids)) {
            return $arrOutput;
        }
        $more = $this->getDaoUserInfoByArray($leftUids, $useWDb);
        if (empty($more)) {
            return $arrOutput;
        }
        foreach ($more as $one) {
            $this->setUserCache($one['uid'], $one);
            $one = $this->buildData($one);
            $arrOutput[$one['uid']] = $one;
        }
        return $arrOutput;
    }

    /**
     * 是否为封禁用户  要判断用getUserInfo
     * $baned 存储形式为  封禁结束时间戳_封禁状态 （0_1 为永久封禁 0_0未封禁）
     */
    private function isBannedUser($baned) {
        list($endTime, $forbidFlag) = explode('_', $baned);
        if ($forbidFlag) {
            if (empty($endTime) || $endTime > time()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 是否为封禁用户,新封禁状态
     * $baned 存储形式为  状态_封禁结束时间戳
     */
    private function isBannedAccount($baned) {
        $status = Hk_Service_Ucloud::getAccountStatus($baned);
        if(Hk_Service_Ucloud::ACC_STATUS_LOCK === $status){
            return true;
        }
        return false;
    }
    /**
     * 取出ext里面的关键用户属性
     * ext字段说明见：http://wiki.baidu.com/pages/viewpage.action?pageId=********#id-作业帮APP数据DB存储-12）tblAppUcloud客户端用户表
     *
     * @param array        $arrOutput
     * @return array
     */
    private function buildData($arrOutput) {
        $curUser = Saf_SmartMain::getUserInfo();
        $self    = intval($curUser['uid']) === intval($arrOutput['uid']) ? true : false;

        if (false === $self && isset($arrOutput['ext']['avatarChecking']) && 1 === intval($arrOutput['ext']['avatarChecking'])) {
            $arrOutput['avatar'] = '';
        }
        if (false === $self && isset($arrOutput['ext']['unameChecking']) && 1 === intval($arrOutput['ext']['unameChecking'])) {
            $arrOutput['uname']  = !empty($arrOutput['ext']['oldUname']) ?  $arrOutput['ext']['oldUname'] : Hk_Util_Tools::getRandomUname();
        }
        $extFieldsMap = Hk_Dao_User_Ucloud::getExtFieldsMap();
        foreach ($extFieldsMap as $key => $value) {
            if (isset($arrOutput['ext'][$value]) && $arrOutput['ext'][$value] !== null) {
                $arrOutput[$key] = $arrOutput['ext'][$value];
            }
        }
        //历史旧封禁状态
        if ($arrOutput['baned']) {
            $arrOutput['baned']  = $this->isBannedUser($arrOutput['baned']);
        }
        //新封禁状态
        if(isset($arrOutput['ext']['accountStatus']) && $arrOutput['ext']['accountStatus'] !== null){
            $arrOutput['newbaned'] = $this->isBannedAccount($arrOutput['ext']['accountStatus']);
        }

        $arrOutput['ulevel']     = Hk_Util_Level::getUserLevel($arrOutput['experience'], $arrOutput['goodCount']);
        return $arrOutput;
    }

    /**
     * 批量获取cache数据
     *
     * @param array        $uids
     * @return array
     */
    private function getUserCacheByArray($uids) {
        $data = array();
        foreach ($uids as $uid) {
            if (isset(self::$userData[$uid])) {         # 先从static取
                $data[$uid] = self::$userData[$uid];
            } else {
                $leftUids[] = (int)$uid;
            }
        }
        if (empty($leftUids)) {
            return $data;
        }
        //if (mt_rand(1,100) <= 90) {
        if (true) {
            $ret = $this->rpcUcloud->getUserInfoFromRedis($leftUids);
        } else {
            $keys = array();
            foreach ($leftUids as $leftUid) {
                $keys[] = $this->cacheKey($leftUid);
            }
            $ret  = $this->redis->mGet($keys);
        }
        if (false === $ret || empty($ret) || !is_array($ret)) {
            Bd_Log::warning('ucloud cache mget failed, uids: ' . json_encode($uids));
            return $data;
        }
        foreach ($ret as $seq => $dt) {
            $uid = $leftUids[$seq];
            if (false !== $dt) {
                $data[$uid] = @json_decode($dt, true);
            } else {
                $data[$uid] = array();
            }
        }
        return $data;
    }

    /**
     * 删除用户cache
     *
     * @param int        $uid
     * @return boolean
     */
    public function delUserCache($uid) {
        unset(self::$userData[$uid]);
        $ret = $this->delCacheRedis($uid);
        if (false === $ret) {
            Bd_Log::warning("ucloud cache del failed, uid: $uid");
            return false;
        }
        Bd_Log::addNotice('ucloud_mem_del', $uid);
        return $ret;
    }

    /**
     * 设置用户cache
     *
     * @param int        $uid
     * @param array      $user
     */
    private function setUserCache($uid, $user) {
        if (empty($user)) {
            return false;
        }
        $user['exist'] = 1;
        if (count(self::$userData) < 1000) {
            self::$userData[$uid] = $user;
        }

        $ret = $this->setCacheRedis($uid, $user);
        if (false === $ret) {
            Bd_Log::warning("ucloud cache set failed, uid: $uid");
            return false;
        }
        Bd_Log::addNotice('ucloud_mem_set', $uid);
        return $ret;
    }

    /**
     * 设置redis缓存
     *
     * @param int         $uid
     * @param array       $user
     * @return boolean
     */
    private function setCacheRedis($uid, $user) {
        //不再需要调用接口将数据set到cache中
        //因为查询db的接口已经包含了set cache特性
        //保留此打点 业务方可能会采集此打点
        Hk_Util_Log::setLog('ucloud_redis_set_ok', $uid);
        return true;
    }

    /**
     * 删除redis缓存
     *
     * @param int         $uid
     * @return boolean
     */
    private function delCacheRedis($uid) {
        //$key = $this->cacheKey($uid);
        //$ret = $this->redis->delete($key);
        $ret = $this->rpcUcloud->delUserInfoFromRedis($uid);
        if ($ret >= 0) {
            Hk_Util_Log::setLog('ucloud_redis_del_ok', $uid);
            return true;
        }
        Hk_Util_Log::setLog('ucloud_redis_del_fail', $uid);
        return false;
    }

    private function cacheKey($uid) {
        return "userinfo:{$uid}";
    }

    /**
     * 从数据库获取用户数据
     *
     * @param array       $uids
     * @return array
     */
    private function getDaoUserInfoByArray($uids, $useWDb = false) {
        $arrFields = array (
            'uid',
            'sex',
            'grade',
            'edu_system',
            'entry_school',
            'phone',
            'mobile',
            'experience',
            'uname',
            'wealth',
            'avatar',
            'ext',
            'province',
            'city',
            'region',
        );

        //$objDaoUcloud = new Hk_Dao_User_Ucloud();
        $arrOutput    = array();
        foreach($uids as $uid) {
            //$arrConds    = array(
            //    'uid' => $uid,
            //);
            //$arrUserInfo = $objDaoUcloud->getRecordByConds($uid, $arrConds, $arrFields);
            $arrUserInfo = $this->rpcUcloud->getUserInfoWithFields($uid, $arrFields, 0, $useWDb);
            if (!empty($arrUserInfo)) {
                $ext = json_decode($arrUserInfo['ext'],true);
                $arrUserInfo['ext'] = is_array($ext) ? $ext : [];
                unset($arrUserInfo['mobile']);
                $arrOutput[] = $arrUserInfo;
            }
        }
        return $arrOutput;
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
