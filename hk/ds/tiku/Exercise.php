<?php


/**
 * @file Exercise.php
 * <AUTHOR>
 * @date 2015-03-06
 * @brief 练习题
 */
class Hk_Ds_Tiku_Exercise {

    private $_objDaoExercise;

    public function __construct() {
        $this->_objDaoExercise = new Hk_Dao_Tiku_Exercise();
    }

    /**
     * 根据题库id得到
     * @param array $tids 题库id
     *
     */
    public function getAnswersByTids($tids) {
        if(empty($tids) || !is_array($tids)) {
            return array();
        }

        $answers = array();
        foreach($tids as $tid) {
            $answer = $this->getAnserByTid($tid);
            $answers[$tid] = $answer;
        }
        return $answers;
    }

    public function getAnserByTid($tid) {
        $tid = intval($tid);
        if(empty($tid)) {
            return false;
        }

        static $objDSHomework;
        if(empty($objDSHomework)) {
            $objDSHomework = Hk_Ds_Tiku_Homework::getInstance();
        }

        $info = $objDSHomework->getHomeworkCacheById($tid);
        if(empty($info)) {
            return false;
        }

        $arrInfo = @json_decode($info['question'],true);
        if(empty($arrInfo)) {
            return false;
        }

        return $arrInfo['choose'];

    }

    /**
     * 得到某个问题下一的联系题qid
     * @param int $intQid 问题qid
     * @return array
     */
    public function getExerciseIdsByQid($intQid) {
        $arrFields = array('eqid');
        $arrConds = array(
            'qid'=>$intQid
        );

        $ret = $this->_objDaoExercise->getList($arrFields, $arrConds);

        if(empty($ret)) {
            return false;
        }

        $strQids = $ret[0]['eqid'];
        $arrQids = explode(',', $strQids);

        if(empty($arrQids)) {
            return false;
        }

        return $arrQids;
    }


    /**
     * 得到某个问题下的练习题qid及类型
     * @param int $intQid 问题qid
     * @return array
     */
    public function getExerciseIdsWithTypeByQid($intQid) {
        $arrFields = array('eqid','type');
        $arrConds = array(
            'qid'=>$intQid,
        );

        $ret = $this->_objDaoExercise->getList($arrFields, $arrConds);

        if(empty($ret)) {
            return false;
        }

        $strQids = $ret[0]['eqid'];
        $arrQids = explode(',', $strQids);

        if(empty($arrQids)) {
            return false;
        }

        return array(
            'eqids' => $arrQids,
            'type'  => intval($ret[0]['type']),
        );
    }


    /**
     * 是否有练习题
     * @param int $intQid
     * @return bool 是否有练习题  ture 有 ， false 没有
     */
    public function isExist($intQid) {
        $arrFields = array('eqid');
        $arrConds = array(
                'qid'=>$intQid
        );

        $ret = $this->_objDaoExercise->getList($arrFields, $arrConds);

        if(empty($ret)) {
            return false;
        }

        return true;
    }

    public function getExerciseIdByTidUid($tid, $uid = 0, $cuid = '', $limit = 5) {

        // 得到数据库中的题,过滤掉已经做过的题
        $arrTidsInDbWithType = $this->getExerciseIdsWithTypeByQid($tid);
        $arrTidsInDb = $arrTidsInDbWithType['eqids'];
        $intCntTidInDb = count($arrTidsInDb);

        $tids = array();
        if(is_array($arrTidsInDb) && $arrTidsInDbWithType['type'] == 1) {
            $arrTids = $arrTidsInDb;
            $tmp = array();
            $arrUserChoices = $this->getUserAndRightAnswerByTids($uid, $cuid, $arrTids);
            foreach($arrUserChoices as $utid => $choice) {
                if(!empty($choice['userChoose']) && !empty($choice['rightAnswer']) && $choice['userChoose'] == $choice['rightAnswer']) {
                    continue;
                }
                $tmp[] = $utid;
            }
            $arrTids = $tmp;
            $tids = array_unique(array_merge($tids,$arrTids));
        }

        if(count($tids) >= $intCntTidInDb) {
            $tids = array_slice($tids, 0,$intCntTidInDb);
            return $tids;
        }

        $province = '';
        if($uid > 0) {
            //通过用户的学校信息得到省份
            static $objUcloud;
            if(empty($objUcloud)) {
                $objUcloud = new Hk_Ds_User_Ucloud();
            }

            $userInfo = $objUcloud->getUserInfo($uid);
            if(!empty($userInfo)) {
                $schoolName = $userInfo['schoName'];
                if(!empty($schoolName)) {
                    $arrSchoolInfo = explode('_', $schoolName);
                    $province = $arrSchoolInfo[0];
                }
            }

            /*
            // 通过gps信息到省份
            if(empty($province) && $userInfo['lbsId'] > 0) {
                $lbsId = $userInfo['lbsId'];
                //如果通过GPS得到用户的省份需要迁移GPS的DS和Dao代码及gps及ral的配置文件。
                static $objGps;
                if(empty($objGps)) {
                    $objGps = new Hk_Ds_User_Gps();
                }
                $lbsInfo = $objGps->detailUser($lbsId);
                if(!empty($lbsInfo)) {
                    $province = strval($lbsInfo['poi']['province']);
                }
            }*/
        }

        //对省份信息进行格式化。 除内蒙古和黑龙江外其他地区都是两个字
        if(strpos($province,'内蒙古') !== false || strpos($province,'黑龙江')!== false) {
            $province = mb_substr($province, 0,3,'UTF-8');
        } else {
            $province = mb_substr($province, 0,2,'UTF-8');
        }

        //找到tid的知识点
        $pointTitles = array();
        $arrPointIds = array();
        static $objPointDetail;
        if(empty($objPointDetail)) {
            $objPoint = new Hk_Ds_Tiku_Point();
            $arrPointTitles= $objPoint->getPointTitleByTid($tid, false);
            $arrPointIds = array_keys($arrPointTitles);
            if(!empty($arrPointTitles)) {
                foreach($arrPointTitles as $row) {
                    $pointTitles[] = $row['title'];
                }
            }
        }

        //通过 省份 和 知识点得到练习题id， 过滤已做过的练习
        $pn = 0;
        $rn = 10;
        static $objUserPractice;
        if(empty($objUserPractice)) {
            $objUserPractice = new Hk_Ds_Practice_UserPractice();
        }
        while(true) {
            //$ret = $this->_objDaoExercise->getExerIdByPointAndArea($pointTitles, $province, '选择题',$pn,$rn);
            $ret = $this->_objDaoExercise->getExerIdByPointAndArea($arrPointIds, $province, '选择题',$pn,$rn);
            if(empty($ret) || empty($ret['list'])) {
                break;
            }

            $arrTids = $ret['list'];
            $tmp = array();
            $arrUserChoices = $this->getUserAndRightAnswerByTids($uid, $cuid, $arrTids);
            foreach($arrUserChoices as $utid => $choice) {
                if(!empty($choice['userChoose']) && !empty($choice['rightAnswer']) && $choice['userChoose'] == $choice['rightAnswer']) {
                    continue;
                }
                $tmp[] = $utid;
            }
            $arrTids = $tmp;
            $tids = array_unique(array_merge($tids,$arrTids));
            // 如果大于 等于 limit 或 没有更多的数据 中止循环
            if(count($tids) >= $limit || empty($ret['hasMore'])) {
                break;
            }
            $pn = $pn + $rn;
        }
        // 如果没有合适的eid从数据取出备用的eid


        $tids = array_slice($tids, 0,$intCntTidInDb);
        if(empty($tids)) {
            $tids = $arrTidsInDb;
        } else if (count($tids) < $intCntTidInDb) {
            $tids = array_unique(array_merge($tids,$arrTidsInDb));
            $tids = array_slice($tids, 0,$intCntTidInDb);
        }

        return $tids;
    }

    /**
     * 根据pointID在tblExercise中添加对应关系
     * @param int $tid 题库id
     * @param pids array 知识点id
     * @return bool
     */
    public function syncExerTblByTidPid($tid,$pids) {
        // 得到知识点标题
        static $objPoint;
        if(empty($objPoint)) {
            $objPoint = new Hk_Ds_Tiku_Point();
        }
        $strPointIds = implode(',', $pids);
        $pointInfos = $objPoint->getPointTitleByPointIds($pids);
        if(empty($pointInfos)) {
            Bd_Log::warning('pointInfos is empty,pointIds:'.$strPointIds.' tid:'.$tid);
            return false;
        }
        $pointTitles = array();

        foreach($pointInfos as $pInfo) {
            $pointTitles[] = $pInfo['title'];
        }

        $ret = $this->_objDaoExercise->getExerIdByPointAndArea($pointTitles, '', '选择题',0,5);
        if(empty($ret)) {
            Bd_Log::warning('Fail to getExerIdByPointAndArea, pointIds:'.$strPointIds.' tid:'.$tid);
            return false;
        }

        if(empty($ret['list'])) {
            Bd_Log::warning('The list from zybpractice is empty, pointIds:'.$strPointIds.' tid:'.$tid);
            return false;
        }

        $eqids = implode(',', $ret['list']);

        $sql = "insert into tblExercise (`qid`,`eqid`) values ('{$tid}','{$eqids}') ON DUPLICATE KEY UPDATE eqid = '{$eqids}';";
        $ret = $this->_objDaoExercise->query($sql);
        if(empty($ret)) {
            Bd_Log::warning('Fail to syncExerTblByTidPid, pointIds:'.$strPointIds.' tid:'.$tid);
            return false;
        }
        return true;
    }

    // 根据tid和uid得到练习题
    public function getExerciseByTidUid($intTid,$intUid = 0,$cuid = '',$limit = 5) {
        $arrTids = $this->getExerciseIdByTidUid($intTid,$intUid,$cuid,$limit);
        if(empty($arrTids)) {
            return array();
        }

        static $objDSHomework;
        if(empty($objDSHomework)) {
            $objDSHomework = Hk_Ds_Tiku_Homework::getInstance();
        }



        $data = array();
        $outeids = array();
        $tblType = 0;
        foreach($arrTids as $eqid) {
            $homeworkInfo = $objDSHomework->getQuestionAndAnswer($eqid,1,true);
            if( empty($homeworkInfo) )
            {
                Bd_Log::warning("qid info empty,Qid:".$eqid);
                continue;
            }
            $homeworkInfo['tid'] = $eqid;
            $data[] = $homeworkInfo;
            $outeids[] =  $eqid;
            if(!empty($limit) && count($data) >= $limit) {
                break;
            }
        }
        Bd_Log::addNotice('exerciseIds', implode(',', $outeids));
        return $data;
    }

    // 通过练习题id得到练习题信息
    public function getExerciseByEids($arrEids) {
        if(!is_array($arrEids) || empty($arrEids)) {
            return array();
        }

        static $objDSHomework;
        if(empty($objDSHomework)) {
            $objDSHomework = Hk_Ds_Tiku_Homework::getInstance();
        }

        $data = array();
        foreach($arrEids as $eqid) {
            $homeworkInfo = $objDSHomework->getQuestionAndAnswer($eqid,1,true);
            if( empty($homeworkInfo) )
            {
                Bd_Log::warning("qid info empty,Qid:".$eqid);
                continue;
            }
            $homeworkInfo['tid'] = $eqid;
            $data[] = $homeworkInfo;
            $outeids[] =  $eqid;
        }

        return $data;
    }


    /**
     *
     * @param int $intQid 问题id
     * @param int $intNet
     * @param int $limit  数量
     * @param int $offset 偏移量
     * @param boolen $separate 是否分开选项
     * @return array 返回结果
     */
    public function getExerciseByQid($intQid,$intNet = 0,$limit=null,$offset = 0,$separate = false){
        static $objDSHomework;
        if(empty($objDSHomework)) {
            $objDSHomework = Hk_Ds_Tiku_Homework::getInstance();
        }

        $arrOutput = array(
            'tplType'=>0,
            'data' => array()
        );

        $eqids = $this->getExerciseIdsByQid($intQid);
        if(empty($eqids)) {
            return $arrOutput;
        }

        //$eqids = array(39032329,39032677,39032938,39033274,39034741);
        if(!empty($offset)) {
            $eqids = array_slice($eqids, $offset);
        }

        if(empty($eqids)) {
            return $arrOutput;
        }
        $data = array();
        $outeids = array();
        $tblType = 0;
        foreach($eqids as $eqid) {
            $homeworkInfo = $objDSHomework->getQuestionAndAnswer($eqid,$intNet,$separate);
            if( empty($homeworkInfo) )
            {
                Bd_Log::warning("qid info empty,Qid:".$eqid);
                continue;
            }

            if($homeworkInfo['tplType'] > 0) {
                $tblType = $homeworkInfo['tplType'];
            }
            $homeworkInfo['tid'] = $eqid;
            $data[] = $homeworkInfo;
            $outeids[] =  $eqid;
            if(!empty($limit) && count($data) >= $limit) {
                break;
            }
        }
        Bd_Log::addNotice('exerciseIds', implode(',', $outeids));

        return array(
            'tplType'=> $tblType,
            'data'   => $data
        );
        //return $data;
    }

    public function getUserAndRightAnswerByTids($uid,$cuid,$tids){
        if(!is_array($tids) || empty($tids)) {
            return array();
        }
        static $objUserPractice;
        if(empty($objUserPractice)) {
            $objUserPractice = new Hk_Ds_Practice_UserPractice();
        }
        $correctAnswers = $this->getAnswersByTids($tids);
        $userAnswers = $objUserPractice->getPracticeByTids($uid, $cuid, $tids);
        empty($userAnswers) && $userAnswers = [];
        $formatUserAnswer = array();
        $userJudge = array();
        foreach($userAnswers as $info) {
            $formatUserAnswer[$info['tid']] = $this->getChoice($info['choice']);
            $userJudge[$info['tid']] = $info['judge'];
        }
        $data = array();
        foreach($tids as $tid) {
            $data[$tid] = array(
                'userChoose'  => $formatUserAnswer[$tid],
                'rightAnswer' => $correctAnswers[$tid],
                'judge'       => $userJudge[$tid],
            );
        }
        return $data;
    }

    private function getChoice($bitPack) {
        $choice = array('A','B','C','D');
        //$bit = 4;
        $ret = '';
        foreach($choice as $k => $c) {
            if(($bitPack >> $k) & 0x01) {
                //echo $c,PHP_EOL;
                $ret = $c;
                break;
            }
        }
        return $ret;
    }

    public static function getPointsByString($strPoint) {
        $strPoint =  str_replace('．','',$strPoint);
        $arrPs = explode('；',$strPoint);
        return $arrPs;
    }


    /**
     * 小流量
     *
     * @param  int    $tid
     * @param  string $vcname
     * @param  string $cuid
     * @param  int    $uid
     * @return bool true/false
     */
    public static function isSmallFollow($tid = 0,$vcname = '',$cuid = '', $uid = 0) {
        if(self::isSkipByCuidUid($cuid,$uid)) {
            return true;
        }

        if($vcname == '4.9.26') {
            return true;
        }

        if(crc32($cuid) % 10 == 0) {
            return true;
        }

        static $objPoint;
        if(empty($objPoint)) {
            $objPoint = new Hk_Ds_Tiku_Point();
        }

        $arrPointGradeIdCourseId = $objPoint->getPointGradeIdCourseIdByTid($tid);

        if(!is_array($arrPointGradeIdCourseId) || empty($arrPointGradeIdCourseId)) {
            return false;
        }
        $gradeIds = array();
        $couseIds = array();


        $follow = true;
        foreach($arrPointGradeIdCourseId as $pid => $val) {
            $gradeIds[$val['gradeId']] = $val['gradeId'];
            $couseIds[$val['courseId']] = $val['courseId'];
            if($val['gradeId'] == 2 && $val['courseId']== 6) { // 初中生物不开放
                $follow = false;
            }
        }
        Bd_log::addNotice('tidPointGradeIds',implode(',', $gradeIds));
        Bd_log::addNotice('tidPointCourseIds',implode(',', $couseIds));

        $arrIntersect = array_intersect(array(2), $couseIds); // 学科开全量
        if(!empty($arrIntersect)) {
            return true;
        }

        if(!$follow) { // 初中生物不开放
            return false;
        }

        $arrIntersect = array_intersect(array(2), $gradeIds); // 出初中的练习题
        if(!empty($arrIntersect)) {
            return true;
        }
        return false;
    }

    public static function isSkipByCuidUid($cuid = '', $uid = 0) {
        $skipUids = array(23788366,805735371,212919351);
        $skipCuid = array(
            'D523E03F620F3DF2595375CE853D9423|231472210330868',
            '2D098454C035FC230FB2393F0F6312FF|837599220985568',
            'cc2bdf0bbc3d71f06e4154f1bdc4793887c72262',
            '43a4c7bbbe2d9431e8c003a6d43bd86b4cc8c3c3',
            '8bb596d90bdded1d59f131186d3617d10329dd93',
            'F9A24BA26A2639735B5616976CEE90A4|749953050547153',
            '547250720D81EDEBF7A681BED4F4C2D5|845173420571568',
            'a9a56e53cfe16639dcf92dcc06b27bccb768f20d',
            '0BB0073A4DC108CF995253EE31FC5D4C|877871050686553',
            'B581F1A12516878AA5AC1C8F04FBBFCE|736170120289568',
        );
        $isSkip = false;
        if(in_array($uid,$skipUids) || in_array($cuid, $skipCuid)) {
            $isSkip = true;
        }
        return $isSkip;
    }
}
