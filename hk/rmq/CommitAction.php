<?php
/**
 * rmq命令回调入口
 */
class Hk_Rmq_CommitAction extends Ap_Action_Abstract {

	private $_intCmdNo;

    public function execute() {

		//获取命令数据
		$strContent = file_get_contents("php://input");
		if(empty($strContent)) {
			return $this->renderOut(-100, "command empty");
		}
		$arrCommand = mc_pack_pack2array($strContent);
		if (empty($arrCommand)) {
			return $this->renderOut(-100, "command format invalid");
		}

		if (!isset($arrCommand['command_no']) && isset($arrCommand['_cmd'])) {
			$arrCommand['command_no'] = $arrCommand['_cmd'];
		}
		$this->_intCmdNo = intval($arrCommand['command_no']);
		Bd_Log::addNotice("cmdNo", $this->_intCmdNo);
		if($this->_intCmdNo === 0) {
			return $this->renderOut(-100, "command number invalid");
		}
        if(isset($arrCommand['skip'])){
			Hk_Util_Tools::$nmqskip = $arrCommand['skip'];
		}

	    //获取命令配置: 
		//1. 没有channel参数的时候使用默认配置
		//2. 有channel参数时使用channel自有配置
		$appName = Bd_AppEnv::getCurrApp();
		if (!empty($_GET['channel'])) {
			$channelName = strval($_GET['channel']);
			Bd_Log::addNotice("channel", $channelName);
			$cmdConf = "/app/{$appName}/channel/{$channelName}";
		} else {
			$cmdConf = "/app/{$appName}/{$appName}cmd";
		}

		$arrConf = Bd_Conf::getConf($cmdConf);
		if ($arrConf == false) {
			return $this->renderOut(-100, "getConf:{$cmdConf} error");
		}

		//获取配置中命令部分的配置
		$arrCmdConf = $arrConf['command'][$this->_intCmdNo];
		if (empty($arrCmdConf) || empty($arrCmdConf['service'])) {
			return $this->renderOut(-100, "get cmd:{$this->_intCmdNo} conf failed");
		}

		$strServiceName = $arrCmdConf['service'];
		$strModuleName  = isset($arrCmdConf['module']) ? $arrCmdConf['module'] : $arrConf['defaultMod']['module'];
		$strModuleName  = $strModuleName . $arrCommand['_topic'];
		$strAutoTrans   = empty($arrCmdConf['autoTrans']) ? 'on' : (($arrCmdConf['autoTrans'] !== 'off') ? 'on' : 'off');
		$strDbName		= isset($arrCmdConf['dbname']) ? $arrCmdConf['dbname'] : $arrConf['appdb']['dbname'];
		if (empty($strServiceName) || empty($strModuleName) || empty($strDbName)) {
			return $this->renderOut(-100, "cmd conf invalid, pls check if service, module or db name exists");
		}

		//生成对象实例并初始化
		$pageService = new $strServiceName();
		if(!$pageService->initCommand($this->_intCmdNo, $arrCommand, $strModuleName, $strDbName, $strAutoTrans)) {
			return $this->renderOut(-100, "process command init failed");
		}

		//执行命令
		try {
			Hk_Util_Log::start('ts_execute');
			$out = $pageService->execute();
			Hk_Util_Log::stop('ts_execute');
		} catch(Exception $e) {
            $errMsg = "Caught exception: "."file: ".$e->getFile()." Line".$e->getLine()." Message:".$e->getMessage();
            Bd_Log::warning($errMsg);
			return $this->renderOut($e->getCode(), $errMsg);
		}

		if(!isset($out['errno']) || $out['errno'] === 0) {
			return $this->renderOut();
		} else {
			return $this->renderOut($out['errno'], "Errno:{$out['errno']}");
		}
	}

    private function renderOut($errNo = 0, $errMsg = "success") {
        Bd_Log::addNotice("ProcessResult", $errMsg);

        //记录所有请求中缓存的notice
        Saf_Base_Log::notice();

        $this->_tplData = [
            "errNo"  => $errNo,
            "errMsg" => $errMsg,
            "data"   => [],
        ];

        $json = json_encode($this->_tplData, JSON_FORCE_OBJECT);
        if($json === false) {
            Bd_Log::warning("json_encode failed");
        }

        header('Content-type:application/json; charset=UTF-8');

        // upsHeader 设定
        $upsErrNo = Hk_Util_UpsErrorHeader::getUpsErrNo();
        if($upsErrNo != -1) {
            header('X_BD_UPS_ERR_NO: '.$upsErrNo);
        }
        $upsErrMsg = Hk_Util_UpsErrorHeader::getUpsErrMsg();
        if($upsErrMsg != "") {
            header('X_BD_UPS_ERR_MSG: '.$upsErrMsg);
        }

        echo $json;
        return true;
    }
}
