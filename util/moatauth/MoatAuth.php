<?php
/**
 * Created by phplib.
 * Auther: <EMAIL>
 * Date: 2021/10/9 5:22 下午
 */

class Zb_Util_Moatauth_MoatAuth
{
    /**
     * @link http://ssv.zuoyebang.cc/static/open-sell/index.html#/detail?releaseId=1626697296
     *
     * @param $strAppkey
     * @param $strAppSecret
     * @param $arrParams
     * @param $query
     *
     * @return bool
     */
    protected static function moatSign($strAppkey, $strAppSecret, &$arrParams, $query)
    {
        $arrParams["appkey"] = $strAppkey;

        // $allParam = array_merge($arrParams, $query);
        $allParam = $arrParams;
        ksort($allParam);
        $arrSignParams = array();

        foreach ($allParam as $strKey => $mixValue) {
            switch (true) {
                case is_string($mixValue):
                    $arrSignParams[] = $strKey . $mixValue;
                    break;
                case is_array($mixValue):
                    $arrSignParams[] = $strKey . json_encode(
                            $mixValue, JSON_UNESCAPED_SLASHES |
                                       JSON_UNESCAPED_UNICODE
                        );
                    break;
                default:
                    $arrSignParams[] = $strKey . (string)$mixValue;
            }
        }

        $strEnc            = $strAppkey . implode('', $arrSignParams) . $strAppSecret;
        $strSign           = strtolower(md5($strEnc));
        $arrParams["sign"] = $strSign;
        return true;
    }
}