<?php
/**
 * 二维码生成工具、合并等
 * @author: <PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @create: 2020/12/10 11:50
 */

include_once __DIR__ . '/phpqrcode/phpqrcode.php';

class Oplib_Util_Qrcode_Tools
{
    /**
     * 生成二维码图片并上传到云端
     * @param $qrContent 二维码内容，如网址
     * @param int $width 二维码宽度px
     * @param int $height 二维码图片高度px
     * @return mixed
     * @throws Hk_Util_Exception
     */
    public static function generateQrCodeImg($qrContent, $width = 100, $height = 100)
    {
        // 本地临时文件目录
        $qrTempFile = '/tmp/' . md5($qrContent) . time() . '.jpg';

        // 二维码生成
        QRcode::png($qrContent, $qrTempFile, QR_ECLEVEL_H);

        // 压缩图片尺寸
        Hkzb_Util_FuDao::mkThumbnail($qrTempFile, $width, $height, $qrTempFile);

        // 上传云端
        $ret = Hk_Util_Image::uploadImage(@file_get_contents($qrTempFile));
        unlink($qrTempFile);
        if (false == $ret || '' == $ret['pid']) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::ERROR_UPLOAD_PIC_FAILED);
        }

        return Hk_Util_Image::getImgUrlBySrc($ret['pid']);
    }

    /**
     * 生成二维码图片
     * @param $qrContent 二维码内容
     * @param $outfile 输出文件
     * @param int $width 二维码宽度px
     * @param int $height 二维码图片高度px
     */
    public static function png($qrContent, $outfile, $width = 100, $height = 100)
    {
        // 二维码生成
        QRcode::png($qrContent, $outfile, QR_ECLEVEL_H);

        // 缩略图
        Hkzb_Util_FuDao::mkThumbnail($outfile, $width, $height, $outfile);
    }

    /**
     * 获取图片文件扩展类型
     * @param $filename
     * @return bool|string
     */
    public static function getImageType($filename)
    {
        $fileInfo = finfo_open(FILEINFO_MIME_TYPE);
        $mime = finfo_file($fileInfo, $filename);
        $type = false;

        switch ($mime) {
            case 'image/jpeg':
            case 'image/jpg':
                $type = 'jpeg';
                break;
            case 'image/png':
                $type = 'png';
                break;
        }
        finfo_close($fileInfo);

        return $type;
    }

    /**
     * 两张图片合并成一张图片，将$srcPath合并到$dstPath上，新生成图片到$outfile
     * @param $dstPath
     * @param $srcPath
     * @param $outfile
     * @param int $dstX
     * @param int $dstY
     * @return bool
     */
    public static function merge($dstPath, $srcPath, $outfile, $dstX = 0, $dstY = 0)
    {
        $type1 = self::getImageType($dstPath);
        $type2 = self::getImageType($srcPath);
        if (false === $type1 || false === $type2) {
            return false;
        }
        $func1 = "imagecreatefrom{$type1}";
        $dest = $func1($dstPath);
        $func2 = "imagecreatefrom{$type2}";
        $src = $func2($srcPath);

        $dstWidth = imagesx($dest);
        $dstHeight = imagesy($dest);
        $srcWidth = imagesx($src);
        $srcHeight = imagesy($src);

        if ($dstX == 0) {
            $dstX = ($dstWidth - $srcWidth) / 2;
        }
        if ($dstY == 0) {
            $dstY = ($dstHeight - $srcWidth) / 2;
        }
        imagecopymerge($dest, $src, $dstX, $dstY, 0, 0, $srcWidth, $srcHeight, 100);
        imagejpeg($dest, $outfile);
        imagedestroy($dest);
        imagedestroy($src);

        return true;
    }
}
