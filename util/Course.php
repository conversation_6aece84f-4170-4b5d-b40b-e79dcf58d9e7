<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   Course.php
 * <AUTHOR>
 * @date   2019/3/5 15:45
 * @brief
 **/
class Oplib_Util_Course
{
    /** 根据courseType获取课程类型 如果不存在，则返回$defaultValue 值
     * @param        $intCourseType
     * @param string $defaultValue
     * @return string
     */
    public static function getCourseType($intCourseType, $defaultValue = '')
    {
        if (array_key_exists($intCourseType, Zb_Const_Course::$arrTypeMap)) {
            return Zb_Const_Course::$arrTypeMap[$intCourseType];
        }
        return $defaultValue;
    }

    /** 根据courseStatus获取课程状态 如果不存在，则返回$defaultValue 值
     * @param        $intCourseStatus
     * @param string $defaultValue
     * @return string
     */
    public static function getCourseStatus($intCourseStatus, $defaultValue = '')
    {
        if (array_key_exists($intCourseStatus, Zb_Const_Course::$arrStatusMap)) {
            return Zb_Const_Course::$arrStatusMap[$intCourseStatus];
        }
        return $defaultValue;
    }
}