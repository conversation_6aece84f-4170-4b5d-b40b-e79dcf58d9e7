<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   Cache.php
 * <AUTHOR>
 * @date   2019/3/30 15:40
 * @brief  cache操作
 **/
class Oplib_Util_BuildToolCache
{
    const CACHE_KEY_PREFIX = 'BuildTool:15532:';
    const PUBLISHED_INFO_KEY = 'PUBLISHED_INFO_KEY_';

    const PUBLISHED_ACT_KEY = 'PUBLISHED_ACT_KEY_';

    private static $objRedis;
    private static $objMemcached;

    public static function getCacheInstance($name = 'redis')
    {
        if ($name != 'redis') {
            Oplib_Util_Log::warning(APP, __CLASS__, __FUNCTION, 'Cache instance name error', compact('name'));
            return false;
        }

        if (!self::$objRedis) {
            self::$objRedis = Oplib_Util_Redis_Instance::getInstance();
        }

        if (!self::$objRedis) {
            Bd_Log::warning('Error:[Hk_Service_RedisClient::getInstance("zbsell") fail] ');
        }

        return self::$objRedis;
    }

    public static function getCacheKey($prefix, $uniqueKey)
    {
        return self::CACHE_KEY_PREFIX . md5(md5($prefix) . md5($uniqueKey));
    }
}