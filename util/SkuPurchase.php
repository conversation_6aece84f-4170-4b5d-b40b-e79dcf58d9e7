<?php

/**
 * Class FengNiao_Lottery_SkuPurchase
 * <AUTHOR> <<EMAIL>>
 * @version    2019/12/24 下午8:13
 * @copyright  zybang.com
 */
class FengNiao_Lottery_SkuPurchase
{
    const ACT_CLUSTER = 'actplat';
    const ACT_REWARD_API_SEND = '/actreward/api/sendactreward';
    const APPKEY = 'rnrasiK7gcZZeXSwZVjCmxbLf4I4La2L';
    const APPSECRET = 'MkoGUTj49fjaUPiq5DRMzgrbzitEAcAF';

    private static $lastfrom = '';
    private static $flowPond = '';
    public static function getBizInfo($uid, $skuId)
    {
        //策略信息
        $input       = [
                'uid'           => $uid,
                'sourceId'      => 1,
                'saleChannelId' => 99,
                'skuIdList'     => [$skuId],
                'limitLevel'    => $uid == 0 ? 2 : 1,
        ];
        $skuInfoList = Oplib_Util_RalClient::ralPost('zbbiz', $input, '/zbbiz/skubizapi/getsinglebizlist');
        if (empty($skuInfoList['data']['skuList'][$skuId])) {
            return 0;//获取策略失败
        }
        $bizInfo = $skuInfoList['data']['skuList'][$skuId];
        return $bizInfo;
    }

    /**
     * Notes:
     * @param $lotteryId
     * @param $uid
     * @param $skuId
     * @param $type
     * @param $addressInfo
     * @return array
     * @throws \FengNiao_Exception
     */
    public static function handle($flowId, $uid, $skuId, $type, $addressInfo = '', $lastfrom = '', $flowPond = '')
    {
        self::$flowPond = $flowPond;
        self::$lastfrom = $lastfrom;
        //调用服务进行发货
        if ($type == Service_Data_Lottery_Prize::PRIZE_TYPE_COUPON_BUSINESS_4) {
            $orderId = self::sendCourse($flowId, $uid, $skuId);
        } else if ($type == Service_Data_Lottery_Prize::PRIZE_TYPE_COUPON_BUSINESS_5) {
            $orderId = self::sendMaterial($flowId, $uid, $skuId, $addressInfo);
        } else {
            throw new FengNiao_Exception(FengNiao_ExceptionCodes::STATUS_ERROR, '');
        }
        return ['orderId' => $orderId, 'reason' => ''];
    }

    //课程
    public static function sendCourse($flowId, $uid, $skuId)
    {
        //根据type调用不同的参数
        $params = [
                'clientOrderId' => $flowId, //业务id，为了保证幂等，如果中了相应的课程就不能中第二次
                'appKey'        => self::APPKEY,
                'appSecret'     => self::APPSECRET,
                'uid'           => $uid,
                'rewardDetail'  => json_encode(['3' => ['skuId' => $skuId]]),
                'lastfrom'      => self::$lastfrom,
                'flowPond'      => self::$flowPond
        ];
        $res    = Oplib_Util_RalClient::ralPost(self::ACT_CLUSTER, $params, self::ACT_REWARD_API_SEND);
        if (empty($res['data']['orderId'])) {
            Oplib_Util_Log::warning(
                    'fengniao_lottery',
                    'FengNiao_Lottery_SkuPurchase',
                    'sendMaterial',
                    '领取课程失败！',
                    json_encode(['request' => $params, 'response' => $res])
            );
            throw new FengNiao_Exception(FengNiao_ExceptionCodes::NEWBGIFTBAG_GET_FAIL, '');
        }
        return $res['data']['orderId'];
    }

    public static function sendMaterial($flowId, $uid, $skuId, $addressInfo)
    {
        //根据type调用不同的参数
        $params = [
                'clientOrderId' => $flowId, //业务id，为了保证幂等，防止多发奖品
                'appKey'        => self::APPKEY,
                'appSecret'     => self::APPSECRET,
                'uid'           => $uid,
                'rewardDetail'  => json_encode(['4' => ['skuId' => $skuId, 'addressInfo' => $addressInfo]]),
                'lastfrom'      => self::$lastfrom,
                'flowPond'      => self::$flowPond
        ];
        $res    = Oplib_Util_RalClient::ralPost(self::ACT_CLUSTER, $params, self::ACT_REWARD_API_SEND);
        if (empty($res['data']['orderId'])) {
            Oplib_Util_Log::warning(
                    'fengniao_lottery',
                    'FengNiao_Lottery_SkuPurchase',
                    'sendMaterial',
                    '领取实物失败！',
                    json_encode(['request' => $params, 'response' => $res])
            );
            throw new FengNiao_Exception(FengNiao_ExceptionCodes::NEWBGIFTBAG_GET_FAIL, '');
        }
        return $res['data']['orderId'];
    }


}
