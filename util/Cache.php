<?php

class Qdlib_Util_Cache
{
    const REDIS_CLUSTER_ZHIBO = 'toufang';
    const REDIS_CLUSTER_RTA = 'toufangrta';
    const REDIS_CLUSTER_DATA = 'toufangdata';
    const REDIS_CLUSTER_CUBE = 'cube';
    const REDIS_CLUSTER_DEFAULT = self::REDIS_CLUSTER_ZHIBO;

    const MEMCACHED_CLUSTER_ZHIBOKE = 'zhiboke';
    const MEMCACHED_CLUSTER_DEFAULT = self::MEMCACHED_CLUSTER_ZHIBOKE;

    /**
     * redis client
     * @param string $clusterName
     * @return false|\Redis
     */
    public static function getQudaoRedis($clusterName = self::REDIS_CLUSTER_DEFAULT)
    {
        return Hk_Service_RedisClient::getInstance($clusterName);
    }

    /**
     * @deprecated
     * memcached client
     * @param string $clusterName
     * @return false|Hk_Service_Memcached
     */
    public static function getQudaoMemcached($clusterName = self::MEMCACHED_CLUSTER_DEFAULT)
    {
        return Hk_Service_Memcached::getInstance($clusterName);
    }
}