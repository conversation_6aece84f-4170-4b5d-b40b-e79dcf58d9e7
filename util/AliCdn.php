<?php
/**
 * @file   AliCdn.php
 * <AUTHOR>
 * @date:  2019/2/16
 * @brief  阿里cdn
 */
class Hkzb_Util_AliCdn {
    const PUSH_KEY           = '2GcWEtgxat';
    const PULL_KEY           = 'j4T3zdSd9u';
    const ALI_RTMP_PUSH_ADDR = 'rtmp://alipush.zuoyebang.com/zybang/';
    const ALI_RTMP_PULL_ADDR = 'rtmp://aliplay.zuoyebang.com/zybang/';
    const ALI_FLV_PULL_ADDR  = 'http://aliplay.zuoyebang.com/zybang/';

    /**
     * @param string $streamId 推流id
     * @param bool   $expireTime 过期时间（秒级时间戳）
     * @param string $pullType 拉流类型（rtmp\flv\m3u8）
     * @return array
     * @throws Hk_Util_Exception
     * @desc   获取阿里云的推拉流地址
     */
    public static function getPushAndPullAddress($streamId, $expireTime = false, $pullType = "rtmp") {
        if (!$streamId) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR);
        }
        if ($expireTime === false) {
            $expireTime = strtotime(date("Y-m-d H:i:s", strtotime("+1 day")));
        }
        $pushHashValue = md5('/zybang/' . $streamId . '-' . $expireTime . '-0-0-' . self::PUSH_KEY);
        $pushAuthKey   = "$expireTime-0-0-$pushHashValue";
        $pushAddress   = self::ALI_RTMP_PUSH_ADDR . $streamId . "?auth_key=$pushAuthKey";
        //获取对应类型的地址
        switch ($pullType) {
            case "rtmp":
                $pullHashValue = md5('/zybang/' . $streamId . "-" . $expireTime . '-0-0-' . self::PULL_KEY);
                $pullAuthKey   = "$expireTime-0-0-$pullHashValue";
                $pullAddress   = self::ALI_RTMP_PULL_ADDR . $streamId . "?auth_key=$pullAuthKey";
                break;
            case "flv":
                $pullHashValue = md5('/zybang/' . $streamId . ".flv-" . $expireTime . '-0-0-' . self::PULL_KEY);
                $pullAuthKey   = "$expireTime-0-0-$pullHashValue";
                $pullAddress = self::ALI_FLV_PULL_ADDR . $streamId . ".flv?auth_key=$pullAuthKey";
                break;
            case "m3u8":
                $pullHashValue = md5('/zybang/' . $streamId . ".m3u8-" . $expireTime . '-0-0-' . self::PULL_KEY);
                $pullAuthKey   = "$expireTime-0-0-$pullHashValue";
                $pullAddress = self::ALI_FLV_PULL_ADDR . $streamId . ".m3u8?auth_key=$pullAuthKey";
                break;
            default :
                $pullAddress = '';
                break;
        }
        return array(
            'pushAddress' => $pushAddress,
            'pullAddress' => $pullAddress,
        );
    }
}