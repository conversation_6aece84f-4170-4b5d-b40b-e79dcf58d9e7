<?php
/**************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   Oplib_Util_Helper.php
 * <AUTHOR>
 * @date   2018/8/22 14:39
 * @brief  常用函数
 **/
class Oplib_Util_Helper
{

    /**
     *  功能看php-Manual
     * @param array $input
     * @param mixed $columnKey
     * @param mixed $indexKey
     * @return array
     */
    public static function array_column($input, $columnKey, $indexKey = null)
    {
        if (function_exists('array_column')) {
            return array_column($input, $columnKey, $indexKey);
        }

        $arrOutput = [];
        foreach ($input as $values) {
            if (is_null($indexKey) && is_null($columnKey)) {
                $arrOutput[] = $values;
            } elseif (!is_null($indexKey) && !is_null($columnKey)) {
                if (is_array($values) && array_key_exists($columnKey, $values)) {
                    if (is_array($values) && isset($values[$indexKey])) {
                        $arrOutput[$values[$indexKey]] = $values[$columnKey];
                    } else {
                        $arrOutput[] = $values[$columnKey];
                    }
                }
            } elseif (is_null($indexKey)) {
                if (is_array($values) && array_key_exists($columnKey, $values)) {
                    $arrOutput[] = $values[$columnKey];
                }
            } elseif (is_null($columnKey)) {
                if (is_array($values) && isset($values[$indexKey])) {
                    $arrOutput[$values[$indexKey]] = $values;
                } else {
                    $arrOutput[] = $values;
                }
            }
        }

        return $arrOutput;
    }

    /** 将一个二维数组按照指定列进行排序
     * @param     $input      是个二维数组
     * @param     $columnKey  二维数组要排序的列
     * @param int $sortOrder  升序还是降序排列，SORT_ASC 按照上升顺序排序， SORT_DESC 按照下降顺序排序
     * @return mixed
     */
    public static function array_sort_column($input, $columnKey, $sortOrder = SORT_ASC)
    {
        $arrSort = self::array_column($input, $columnKey);
        array_multisort($arrSort, $sortOrder, $input);

        return $input;
    }

    /** 生成一个累计数字
     * @param int   $beginTime 开始时间
     * @param float $step      步长
     * @param int   $initCount 初始值
     * @return float|int
     */
    public static function genCountNum($beginTime, $step, $initCount = 0)
    {
        $beginTime = intval($beginTime);
        $step      = floatval($step);
        $initCount = intval($initCount);
        $initCount = $initCount + ((time() - $beginTime) / 60) * $step;
        $initCount = intval($initCount);

        return $initCount;
    }

    /**获取从当天0点算起days天后+offset秒的时间戳
     * @param     $days     第几天后
     * @param int $offset   偏移秒数
     * @return false|float|int
     */
    public static function getDayOfTime($days, $offset = 0)
    {
        $days   = intval($days);
        $offset = intval($offset);
        $time   = strtotime(date('Y-m-d 00:00:00')) + 86400 * $days + $offset;
        Bd_Log::addNotice('getDayOfTime_' . $days . '_' . $offset, $time);

        return $time;
    }

    /** 读取excel文件返回一个数组,如果$sheetIndex为null,则返回所有的sheet内容否则返回一个sheet内容
     * @param        $inputFileName   excel文件路径
     * @param null   $sheetIndex      sheet页索引
     * @param string $starColumn      开始列
     * @param int    $starRow         开始行
     * @param null   $highestColumn   结束行
     * @param null   $highestRow      结束列
     * @return array|mixed
     */
    public static function readExcelToArray($inputFileName, $sheetIndex = null, $starColumn = 'A', $starRow = 1, $highestColumn = null, $highestRow = null)
    {
        static $isLoadExcelLib = false;
        if (!$isLoadExcelLib) {
            // 载入excel库
            new Zhibo_Service_Excel();
            $isLoadExcelLib = true;
        }
        date_default_timezone_set('PRC');
        // 读取excel文件
        try {
            $inputFileType = PHPExcel_IOFactory::identify($inputFileName);
            $objReader     = PHPExcel_IOFactory::createReader($inputFileType);
            $objPHPExcel   = $objReader->load($inputFileName);
        } catch (Exception $e) {
            die('加载文件发生错误：”' . pathinfo($inputFileName, PATHINFO_BASENAME) . '”: ' . $e->getMessage());
        }

        if (is_null($sheetIndex)) {
            $allSheets = $objPHPExcel->getAllSheets();
        } elseif (is_int($sheetIndex)) {
            if ($objPHPExcel->getSheetCount() <= $sheetIndex) {
                die("sheetIndex value only 0 -> " . ($objPHPExcel->getSheetCount() - 1) . PHP_EOL);
            }
            $allSheets = [$objPHPExcel->getSheet($sheetIndex)];
        } else {
            die("sheetIndex must be int" . PHP_EOL);
        }

        $allSheetsData = [];
        foreach ($allSheets as $key => $sheet) {
            if (is_null($highestColumn)) {
                $endColumn = $sheet->getHighestColumn();
            } else {
                $endColumn = $highestColumn;
            }
            if (is_null($highestRow)) {
                $endRow = $sheet->getHighestRow();
            } else {
                $endRow = $highestRow;
            }
            $allSheetsData[$key] = $sheet->rangeToArray($starColumn . $starRow . ':' . $endColumn . $endRow, null, true, false);
        }

        if (is_null($sheetIndex)) {
            return $allSheetsData;
        } else {
            return $allSheetsData[0];
        }
    }

    /**
     * @brief  数组转Excel2007
     * <AUTHOR>
     * @date   2020/4/29 16:44
     * @param array  $dataList      要写入的数据
     * @param string $saveFileName  保存文件名:如果文件名为 ('php://output' 或者是 'php://stdout')[不区分大小写] 直接输出到标准输出中
     * @param string $sheetName     sheet名字
     * @param int    $starColumn    数据写入的开始列
     * @param int    $starRow       数据写入的开始行
     * @throws Hk_Util_Exception
     */
    public static function arrayToExcel2007($dataList, $saveFileName, $sheetName = '', $starColumn = 0, $starRow = 1)
    {
        static $isLoadExcelLib = false;
        if (!$isLoadExcelLib) {
            // 载入excel库
            new Zhibo_Service_Excel();
            $isLoadExcelLib = true;
        }
        date_default_timezone_set('PRC');

        // 实例化PHPExcel
        try {
            $phpExcel = new PHPExcel();
        } catch (Exception $e) {
            throw  new Hk_Util_Exception(Hk_Util_ExceptionCodes::OTHER_ERROR, '实例化PHPExcel失败！', ['errMsg' => $e->getMessage()]);
        }

        // 设置第一个为活动的sheet
        $phpExcel->setActiveSheetIndex(0);
        // 设置sheetName
        if(0 >= strlen($sheetName)){
            $phpExcel->getActiveSheet()->setTitle(strval($sheetName));
        }
        // 设置单元格值
        $j =  $starRow; //当前行
        foreach ($dataList as $values) {
            $i = $starColumn; // 当前列
            foreach ($values as $k => $v) {
                $phpExcel->getActiveSheet()->setCellValueByColumnAndRow($i, $j, $v);
                $i++;
            }
            $j++;
        }

        // 保存文件
        $excelWriter = PHPExcel_IOFactory::createWriter($phpExcel, 'Excel2007');
        $excelWriter->save($saveFileName);
    }


    /** 递归array_map
     * @param       $filter     方法名
     * @param array $data       数据
     * @return array
     */
    public static function array_map_recursive($filter, $data)
    {
        $result = array();
        foreach ($data as $key => $val) {
            $result[$key] = is_array($val)
                ? self::array_map_recursive($filter, $val)
                : call_user_func($filter, $val);
        }
        return $result;
    }

    /** 返回一个数组的指定keys
     * @param $values
     * @param $fileds
     * @return array
     */
    public static function array_get_fields_values(&$values, $fileds)
    {
        $ret = [];
        foreach ($fileds as $k) {
            if (array_key_exists($k, $values)) {
                $ret[$k] = $values[$k];
            }
        }
        return $ret;
    }

    /** 构建一个树形结构
     *  example
     *  $dataList = [
     *      ['id'=> 1,'pid'=> 0],
     *      ['id'=> 2,'pid'=> 0],
     *      ['id'=> 3,'pid'=> 1],
     *      ['id'=> 4,'pid'=> 3],
     *      ['id'=> 5,'pid'=> 2],
     *      ['id'=> 6,'pid'=> 2],
     *  ];
     *  build_tree($dataList, 0,'_children','id','pid');
     *  返回
     *  [
     *      0 => [
     *          'id' => 1,
     *          'pid' => 0,
     *          '_children' => [
     *              0 => [
     *                  'id' => 3,
     *                  'pid' => 1,
     *                  '_children' => [
     *                      0 => [
     *                          'id' => 4,
     *                          'pid' => 3,
     *                      ],
     *                  ],
     *              ],
     *          ],
     *      ],
     *      1 => [
     *          'id' => 2,
     *          'pid' => 0,
     *          '_children' => [
     *              0 => [
     *                  'id' => 5,
     *                  'pid' => 2,
     *              ],
     *              1 => [
     *                  'id' => 6,
     *                  'pid' => 2,
     *              ],
     *          ],
     *      ],
     *  ];
     * @param        $dataList      数据列表
     * @param int    $parentId      父ID为该值的为根节点的数据
     * @param string $childrenField 保存孩子节点的数据字段
     * @param string $idField       节点ID字段名
     * @param string $pidField      父节点ID字段名
     * @return array
     */
    public static function build_tree($dataList, $parentId = 0, $childrenField = '_children', $idField = 'id', $pidField = 'parentId')
    {
        $result = [];

        $dataList = self::array_column($dataList, null, $idField);
        foreach ($dataList as $key => &$value) {
            if ($value[$pidField] == $parentId) {
                $result[] = &$value;
            } else {
                if (!isset($dataList[$value[$pidField]][$childrenField]) || !is_array($dataList[$value[$pidField]][$childrenField])) {
                    $dataList[$value[$pidField]][$childrenField] = [];
                }
                $dataList[$value[$pidField]][$childrenField][] = &$value;
            }
            unset($value);
        }

        return $result;
    }

    /**根据url下载数据
     * @param $url
     * @return bool|mixed 下载失败返回false,否则返回下载的数据
     */
    public static function downloadContent($url)
    {
        if ($url == '') {
            Bd_Log::waring("url empty Detail[url:$url]");
            return false;
        }
        $proxy = "proxy.zuoyebang.com:80";
        $curl  = curl_init();
        // 不是测试环境需要设置代理
        if (ral_get_idc() !== 'test') {
            curl_setopt($curl, CURLOPT_PROXY, $proxy);
        }
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_FAILONERROR, true);
        curl_setopt($curl, CURLOPT_TIMEOUT_MS, 4000);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT_MS, 2000);
        curl_setopt($curl, CURLOPT_URL, $url);
        //如果是请求HTTPS接口，添加这一行关掉证书验证
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        $retv    = curl_exec($curl);
        $retInfo = curl_getinfo($curl);

        if (200 != $retInfo['http_code']) {
            $error = curl_error($curl);
            Bd_Log::warning("curl error Detail:" . json_encode($error));
            return false;
        }

        return $retv;
    }

    /**
     * @brief  获取当前请求的app名字，如果获取到的是unknown-app，则返回设置的$default名字
     * <AUTHOR>
     * @date   2019/12/12 11:51
     * @param string $default
     * @return bool|mixed|null|string
     */
    public static function getCurAppName($default = 'unknown-app')
    {
        $curApp = (defined('MAIN_APP') && MAIN_APP != 'unknown-app') ? MAIN_APP : $default;
        return $curApp;
    }

    /**
     * @brief  判断是否命中小流量（满足下面任意一条则命中小流量）
     *         1：$value、$base必须integer类型且大于0,小流量大小命中算法 $value % $base = 0
     *         2：$innerSmallFlow必须integer类型且大于0，并且是内网访问，判断$_REQUEST['innerSmallFlow']
     *            或者 $_COOKIE['innerSmallFlow']是否等于 $innerSmallFlow，如果满足则命中小流量
     * <AUTHOR>
     * @date   2020/1/13 10:03
     * @param int $value 按指定维度分小流量
     * @param int $base  小流量基数
     * @param int $innerSmallFlow 内部小流量开关，大于0并且等于该值则开启小流量
     * @return bool
     */
    public static function isSmallFlow($value, $base = 0, $innerSmallFlow = 0)
    {
        $isSmallFlow = false;
        if (is_integer($value) && is_integer($base) && $base > 0 && $value > 0 && ($value % $base) === 0) {
            $isSmallFlow = true;
        } elseif (is_integer($innerSmallFlow) && $innerSmallFlow > 0 && Hk_Util_Ip::isInnerIp(Hk_Util_Ip::getUserIp())) {
            $innerSmallFlow1 = isset($_REQUEST['innerSmallFlow']) ? strval($_REQUEST['innerSmallFlow']) : '';
            $innerSmallFlow2 = isset($_COOKIE['innerSmallFlow']) ? strval($_COOKIE['innerSmallFlow']) : '';
            $isSmallFlow = $innerSmallFlow1 === strval($innerSmallFlow) || $innerSmallFlow2 === strval($innerSmallFlow);
        }
        Bd_Log::addNotice('isSmallFlow', $isSmallFlow ? 1 : 0);

        return $isSmallFlow;
    }

    /**
     * @brief  判断是否命中小流量（满足下面任意一条则命中小流量）
     *         1：$value、$smallFlowSize、$base必须integer类型且大于0,小流量大小命中算法 $value % $base < $smallFlowSize
     *         2：$innerSmallFlowConfig内部小流量配置,必须是内网访问并且判断$_REQUEST[$innerSmallFlowConfig['k']]
     *            或者 $_COOKIE[$innerSmallFlowConfig['k']]是否等于 $innerSmallFlowConfig['v']，如果满足则命中小流量
     * <AUTHOR>
     * @date   2020/1/13 10:03
     * @param int $value                   按指定维度分小流量
     * @param int $smallFlowSize           小流量大小
     * @param int $base                    小流量基数 默认为100
     * @param array $innerSmallFlowConfig  内部小流量配置['k'=> 标识的key,'v'=> 值]
     * @return bool
     */
    public static function isSmallFlowV2($value, $smallFlowSize = 0, $base = 100, $innerSmallFlowConfig = [])
    {
        $isSmallFlow = false;

        do{
            // 规则1
            Bd_Log::addNotice('smallFlowV2Rule', 1);
            if (is_integer($value) && is_integer($smallFlowSize) && is_integer($base)
                && $value > 0 && $smallFlowSize > 0 && $base > 0
                && ($value % $base) < $smallFlowSize) {
                $isSmallFlow = true;

                break;
            }

            // 规则2:
            Bd_Log::addNotice('smallFlowV2Rule', 2);
            // 内部小流量配置为空，或者不是内网则直接返回
            if (!is_array($innerSmallFlowConfig) || empty($innerSmallFlowConfig)
                || !Hk_Util_Ip::isInnerIp(Hk_Util_Ip::getUserIp())) {
                break;
            }
            $default = ['k' => '', 'v' => ''];
            $config  = array_merge($default, $innerSmallFlowConfig);
            $k = strval($config['k']);
            $v = strval($config['v']);
            if ($k && $v) {
                $isSmallFlow = strval(isset($_REQUEST[$k]) ? $_REQUEST[$k] : '') === $v
                    || strval(isset($_COOKIE[$k]) ? $_COOKIE[$k] : '') === $v;
            }
        }while(false);
        Bd_Log::addNotice('isSmallFlowV2', $isSmallFlow ? 1 : 0);

        return $isSmallFlow;
    }

    // 设置下载header
    public static function setDownloadHeader($filename, $fileSize = 0)
    {
        //设置头信息
        //声明浏览器输出的是字节流
        header('Content-Type: application/octet-stream');
        //声明浏览器返回大小是按字节进行计算
        header('Accept-Ranges:bytes');
        if ($fileSize) {
            //告诉浏览器文件的总大小;注意是'Content-Length:' 非Accept-Length
            header('Content-Length:' . $fileSize);
        }
        $ua = $_SERVER["HTTP_USER_AGENT"] ?? '';
        //声明作为附件处理和下载后文件的名称
        if (preg_match("/MSIE/", $ua)) {
            $encodedFilename = rawurlencode($filename);
            header('Content-Disposition: attachment; filename="' . $encodedFilename . '"');
        } elseif (preg_match("/Firefox/", $ua)) {
            header('Content-Disposition: attachment; filename*="utf8\'\'' . $filename . '"');
        } else {
            header('Content-Disposition: attachment; filename="' . $filename . '"');
        }
    }
}


