<?php
/**
 * Copyright (c) 2016 zuoyebang.com, Inc. All Rights Reserved
 * @file: Format.php
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @date: 2017/3/2
 * @time: 11:31
 * @desc:
 */

class Hkzb_Util_Fudao_NewUserCourse
{
    //课程配置
    public static $NEW_USER_COURSE = array(
        2 => array(//年级
            3 => array(18993),//学科
            2 => array(18984),
        ),
        3 => array(
            3 => array(18991),
            2 => array(18986),
        ),
        15 => array(
            3 => array(18782),
            2 => array(18787),
            1 => array(18784),
        ),
    );
    /**
     * 当前学季配置
     * @var array
     */
    static public $LEARN_SEASON = array ( 
        'CURRENT_SEASON'    => array('秋_1'),
        'SEASON_CODE'       => array('春_2' => 'c2', '暑_1' => 's1', '暑_2' => 's2', '暑_3' => 's3', '暑_4' => 's4', '秋_1' => 'q1','秋_2'=>'q2'),
        'SEASON_START_TIME' => 1483200000, //2017-1-1 00:00:00
        //奖金季度
        'BONUS_SEASON'      => '秋_2',
        //续报卡片当前季度
        'CONTINUE_CARD'     => array('秋_1'),
        //续报季度
        'CONTINUE_SEASONS'  => array("'秋_2'"),
        //课程列表
        'COURSE_LIST'       => array('秋_1','秋_2'),
        'COURSE_LIST_DB'    => array("'秋_1'","'秋_2'"),
        //新学员标识
        'NEW_USER_SEASONS'  => array('秋_2'),
        'NEW_SEASONS'       => array("'秋_2'"),

        // 分层新学员判断学季
        'LAYERED_SEASON_DB' => array("'寒_1'", "'寒_2'", "'寒_3'", "'寒_4'"),
        //PC任务统计
        'PC_TASK'           => array('秋_1'),
    );
    // 根据年级学科获取
    public static function getCourse($grade = 0, $subject = 0){
        if($grade <=0) {
            return [];
        }
        if($subject > 0){
            //单学科
            return isset(self::$NEW_USER_COURSE[$grade][$subject]) ?  self::$NEW_USER_COURSE[$grade][$subject] : [];
        }
        if($subject <= 0) {
            //全部学科
            if(isset(self::$NEW_USER_COURSE[$grade])){
                $result = [];
                foreach(self::$NEW_USER_COURSE[$grade] as $courseList){
                    $result = array_merge($result, $courseList);
                }
                return $result;
            }
            return [];
        }
        return [];
    }
    public static function getAllCourse(){
        $result = [];
        foreach(self::$NEW_USER_COURSE as $gradeList){
            foreach($gradeList as $courseList){
                $result = array_merge($result, $courseList);
            }
        }
        return $result;
    }
    //判断学员是否为新学员
    public function checkNewUser($studentUid)
    {
        if(intval($studentUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid]");
            return false;
        }

        $ret = false;
        //学员首门课程及其学季
        $objMemcached = Hk_Service_Memcached::getInstance("zhiboke");
        $cacheKey     = 'STUDENT_FIRST_COURSE_' . $studentUid;
        $cacheValue   = $objMemcached->get($cacheKey);
        if(empty($cacheValue)){
            return false;
        }
        $objDsCourse  = new Hkzb_Ds_Fudao_Course();
        $courseInfo   = $objDsCourse->getCourseInfo(intval($cacheValue),array('learnSeason','startTime'));
        //当前学季
        $learnSeason  = self::$LEARN_SEASON;
        if (in_array($courseInfo['learnSeason'],$learnSeason['NEW_USER_SEASONS']) 
                && $courseInfo['startTime'] > $learnSeason['SEASON_START_TIME']) {
            $ret = true;
        } else{
            $ret = false;
        }
        
        return $ret;
    }
}
