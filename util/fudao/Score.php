<?php
/**
 * Copyright (c) 2017 zuoyebang.com, Inc. All Rights Reserved
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @file: Score.php
 * @date: 2017/5/16
 * @time: 16:19
 * @desc: 积分相关
 */

class Hkzb_Util_Fudao_Score
{
    //课程可发送红包上限
    const MAX_SCORE_RED_ENVELOPE_NUM = 3;
    //积分红包开关 1 开 0 关
    const SCORE_RED_ENVELOPE_SWITCH = 0;

    // 积分红包开启
    const SCORE_RED_ENVELOPE_OPEN = 1;

    // 积分红包关闭
    const SCORE_RED_ENVELOPE_CLOSE = 0;

    /**
     * 获取用户在某章节下的红包信息
     * @param $intStudentUid
     * @param $intLessonId
     * @return array|bool
     */
    public static function getUserLessonRedEnvelopeInfo($intStudentUid, $intCourseId, $intLessonId)
    {
        $arrData = array(
            'errNo'               => 0,
            'canReceive'          => 0,
            'sendTime'            => 0,//当前可领取的红包的发送时间,
            'receivedRedEnvelope' => array(),//用户已领取的红包信息
            'restRedEnvelope'     => 0,//用户待领取的红包数量
        );
        //参数检查
        if ($intStudentUid <= 0 || $intCourseId <= 0 || $intLessonId <= 0) {
            $arrData['errNo'] = Hk_Util_ExceptionCodes::PARAM_ERROR;
            Bd_Log::warning("studentUid:{$intStudentUid}, courseId:{$intCourseId}, lessonId:{$intLessonId}", Hk_Util_ExceptionCodes::PARAM_ERROR);

            return $arrData;
        }
        //判断课程是否计分
        $objCourse  = new Hkzb_Ds_Fudao_Advanced_Course();
        $courseInfo = $objCourse->getCourseInfo($intCourseId, array('extLesson'), true);
        $arrData['courseType'] = $courseInfo['type'];
       /* if (false === $courseInfo) {
            $arrData['errNo'] = Hk_Util_ExceptionCodes::DB_ERROR;
            Bd_Log::warning("courseId:{$intCourseId}", Hk_Util_ExceptionCodes::DB_ERROR);

            return $arrData;
        }
        if (!Hkzb_Ds_Fudao_Score::courseRankIsOpen($courseInfo)) {
            $arrData['errNo'] = Hk_Util_ExceptionCodes::SCORE_COURSE_CHECK_ERROR;
            //Bd_Log::warning("courseId:{$intCourseId}", Hk_Util_ExceptionCodes::SCORE_COURSE_CHECK_ERROR);

            return $arrData;
        }*/
        //检查学生课程权限
        $objStudentLesson   = new Hkzb_Ds_Fudao_Advanced_StudentLesson();
        $checkStudentLesson = $objStudentLesson->checkStudentLesson($intStudentUid, $intLessonId);
        if (!$checkStudentLesson) {
            $arrData['errNo'] = Hk_Util_ExceptionCodes::STUDENT_CHECK_ERROR;
            Bd_Log::warning("studentUid:{$intStudentUid}, lessonId:{$intLessonId}", Hk_Util_ExceptionCodes::STUDENT_CHECK_ERROR);

            return $arrData;
        }
        //检查课程状态
        $objLesson  = new Hkzb_Ds_Fudao_Lesson();
        $lessonInfo = $objLesson->getLessonInfo($intLessonId, array('status', 'extData'));
        if ($lessonInfo === false) {
            $arrData['errNo'] = Hk_Util_ExceptionCodes::DB_ERROR;
            Bd_Log::warning("lessonId:{$intLessonId}", Hk_Util_ExceptionCodes::DB_ERROR);

            return $arrData;
        }
        if (empty($lessonInfo)) {
            $arrData['errNo'] = Hk_Util_ExceptionCodes::LESSON_NOT_EXIST;
            Bd_Log::warning("lessonId:{$intLessonId}", Hk_Util_ExceptionCodes::LESSON_NOT_EXIST);

            return $arrData;
        }
        //非直播期间
        if ($lessonInfo['status'] != Hkzb_Ds_Fudao_Lesson::STATUS_CLASS_IN) {
            $arrData['errNo'] = Hk_Util_ExceptionCodes::LESSON_HAS_LEAVE;
            Bd_Log::warning("lessonId:{$intLessonId}", Hk_Util_ExceptionCodes::LESSON_HAS_LEAVE);

            return $arrData;
        }
        //检查课程是否有可领取的红包
        //老师发出的红包信息
        $redEnvelopeSendInfo = isset($lessonInfo['extData']['redEnvelopeSendInfo']) ? $lessonInfo['extData']['redEnvelopeSendInfo'] : array();

        //从缓存里获取下红包信息
        $objMemcached = Hk_Service_Memcached::getInstance("zhiboke");
        $cacheKey     = Hkzb_Ds_Fudao_Lesson::REDENVELOPE_CACHE_KEY_PRE . $intLessonId;
        $extData = $objMemcached->get($cacheKey);
        $extData = json_decode(utf8_encode($extData),true);
        if(is_array($extData) && !empty($extData)) {
            $cacheRedEnvelopeSendInfo = isset($extData['redEnvelopeSendInfo']) ? $extData['redEnvelopeSendInfo'] : array();
            if(count($cacheRedEnvelopeSendInfo) > count($redEnvelopeSendInfo)) {
                $redEnvelopeSendInfo = $cacheRedEnvelopeSendInfo;
            }
        }

        if (!empty($redEnvelopeSendInfo)) {
            //获取学生章节信息
            $studentLesson = Hkzb_Ds_Fudao_Advanced_StudentLesson::getStudentLesson($intStudentUid, $intLessonId);
            if ($studentLesson === false) {
                $arrData['errNo'] = Hk_Util_ExceptionCodes::DB_ERROR;

                return $arrData;
            }
            //学生已经领取的红包信息
            $receivedRedEnvelope = isset($studentLesson['extData']['receivedRedEnvelope']) ? $studentLesson['extData']['receivedRedEnvelope'] : array();
            //判断是否有可领取的红包
            foreach ($redEnvelopeSendInfo as $redEnvelope) {
                $isValid  = isset($redEnvelope['isValid']) ? intval($redEnvelope['isValid']) : 0;
                $sendTime = isset($redEnvelope['sendTime']) ? intval($redEnvelope['sendTime']) : 0;
                if (!$isValid || !$sendTime) {
                    continue;
                }
                if (empty($receivedRedEnvelope)) {
                    $arrData['canReceive']          = 1;
                    $arrData['sendTime']            = $sendTime;
                    $arrData['receivedRedEnvelope'] = $receivedRedEnvelope;
                    break;
                } else {
                    foreach ($receivedRedEnvelope as $item) {
                        if ($item['sendTime'] == $sendTime) {
                            continue 2;
                        }
                    }
                    $arrData['canReceive']          = 1;
                    $arrData['sendTime']            = $sendTime;
                    $arrData['receivedRedEnvelope'] = $receivedRedEnvelope;
                    break;
                }
            }
        }
        if (!$arrData['canReceive'] && $arrData['errNo'] == 0) {
            $arrData['errNo'] = Hk_Util_ExceptionCodes::SCORE_NO_VALID_RED_ENVELOPE;
        }

        return $arrData;
    }


    /**
     * 查看章节能否发送红包
     * @param $intLessonId
     * @return array|bool
     */
    public static function getLessonRedEnvelopeInfo($intLessonId)
    {
        $arrData = array(
            'errNo'      => 0,
            'canSend'    => 0,
            'hasSentCnt' => 0,
        );
        if ($intLessonId <= 0) {
            $arrData['errNo'] = Hk_Util_ExceptionCodes::PARAM_ERROR;
            Bd_Log::warning("lessonId:{$intLessonId}", Hk_Util_ExceptionCodes::PARAM_ERROR);

            return $arrData;
        }
        $dbName = Hkzb_Util_FuDao::DBCLUSTER_FUDAO;
        $db     = Hk_Service_Db::getDB($dbName);
        if (empty($db)) {
            $arrData['errNo'] = Hk_Util_ExceptionCodes::DB_ERROR;
            Bd_Log::warning("connect to db fail, db[$dbName]");

            return $arrData;
        }
        $res = $db->startTransaction();
        if (empty($res)) {
            $arrData['errNo'] = Hk_Util_ExceptionCodes::DB_ERROR;
            Bd_Log::warning("start transaction fail, db[$dbName]");

            return $arrData;
        }
        try {
            $objLesson  = new Hkzb_Ds_Fudao_Lesson();
            $lessonInfo = $objLesson->getLessonInfo($intLessonId, array('courseId', 'status', 'extData'));
            if ($lessonInfo === false) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR);
            }
            $objCourse  = new Hkzb_Ds_Fudao_Course();
            $courseInfo = $objCourse->getCourseInfo($lessonInfo['courseId'], array('pack', 'extData'));
            if ($courseInfo === false) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR);
            }
            if ($courseInfo['pack'] <> Hkzb_Ds_Fudao_Advanced_Course::PACK_YESD) {
                $objAdvancedCourse = new Hkzb_Ds_Fudao_Advanced_Course();
                $courseInfo        = $objAdvancedCourse->getCourseInfo($lessonInfo['courseId'], array('extLesson'), false);
            }
            //该课程不可发红包
            /*if (!Hkzb_Ds_Fudao_Score::courseRankIsOpen($courseInfo)) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::SCORE_COURSE_CHECK_ERROR);
            }*/
            //非直播期间不能发红包
            if ($lessonInfo['status'] != Hkzb_Ds_Fudao_Lesson::STATUS_CLASS_IN) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::SCORE_SEND_RED_ENVELOPE_ERROR);
            }
            $redEnvelopeSendInfo = isset($lessonInfo['extData']['redEnvelopeSendInfo']) ? $lessonInfo['extData']['redEnvelopeSendInfo'] : array();
            //前一个红包未关闭，不能发
            foreach ($redEnvelopeSendInfo as $item) {
                if ($item['isValid'] == 1) {
                    // 2017-07-20 需求变更
                    // 上一个红包我诶关闭，下一个红包依然可以正常发放
                    //throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::SCORE_EX_RED_ENVELOPE_NOT_END_ERROR);
                }
            }
            //红包数达到上限，不能发
            if (count($redEnvelopeSendInfo) >= self::MAX_SCORE_RED_ENVELOPE_NUM) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::SCORE_SEND_RED_ENVELOPE_MAX_ERROR);
            }
            $arrData['canSend']    = 1;
            $arrData['hasSentCnt'] = count($redEnvelopeSendInfo);
        } catch (Hk_Util_Exception $e) {
            $arrData['errNo'] = $e->getErrNo();
            $res              = $db->rollback();
            if (empty($res)) {
                $arrData['errNo'] = Hk_Util_ExceptionCodes::DB_ERROR;
                Bd_Log::warning("rollback fail, db[$dbName]");
            }

            $objLesson  = new Hkzb_Ds_Fudao_Lesson();
            $lessonInfo = $objLesson->getLessonInfo($intLessonId, array('courseId', 'status', 'extData'));
            $redEnvelopeSendInfo = isset($lessonInfo['extData']['redEnvelopeSendInfo']) ? $lessonInfo['extData']['redEnvelopeSendInfo'] : array();

            $hasSentCnt = count($redEnvelopeSendInfo);
            $canSent = self::MAX_SCORE_RED_ENVELOPE_NUM - $hasSentCnt;
            if ($canSent<0) {
                $canSent = 0;
            }

            $arrData['hasSentCnt'] = count($redEnvelopeSendInfo);

            return $arrData;
        }
        $res = $db->commit();
        if (empty($res)) {
            $arrData['errNo'] = Hk_Util_ExceptionCodes::DB_ERROR;
            Bd_Log::warning("commit fail, db[$dbName]");

            $objLesson  = new Hkzb_Ds_Fudao_Lesson();
            $lessonInfo = $objLesson->getLessonInfo($intLessonId, array('courseId', 'status', 'extData'));
            $redEnvelopeSendInfo = isset($lessonInfo['extData']['redEnvelopeSendInfo']) ? $lessonInfo['extData']['redEnvelopeSendInfo'] : array();

            $hasSentCnt = count($redEnvelopeSendInfo);
            $canSent = self::MAX_SCORE_RED_ENVELOPE_NUM - $hasSentCnt;
            if ($canSent<0) {
                $canSent = 0;
            }

            $arrData['hasSentCnt'] = count($redEnvelopeSendInfo);

            return $arrData;
        }

        return $arrData;
    }

    /**
     * 获取课中签到状态
     * @param $lessonInfo
     * @param $uid
     * @return bool
     */
    public static function getInClassSignStatus($lessonInfo, $uid)
    {
        $inclassSignStatus = (isset($lessonInfo['extData']['inclassSignStatus']) && intval($lessonInfo['extData']['inclassSignStatus']) == 1);
        if ($inclassSignStatus) {
            //获取用户是否签到过
            $studentLesson = Hkzb_Ds_Fudao_Advanced_StudentLesson::getStudentLesson($uid, $lessonInfo['lessonId'], array('extData'));
            if (empty($studentLesson)) {
                Bd_Log::warning("Error:[param error], Detail:[studentUid:$uid lessonId:{$lessonInfo['lessonId']}]");

                return false;
            }
            $inclassSignStatus = !(isset($studentLesson['extData']['signTime']) && intval($studentLesson['extData']['signTime']) > 0);
        }

        return $inclassSignStatus;
    }
}
