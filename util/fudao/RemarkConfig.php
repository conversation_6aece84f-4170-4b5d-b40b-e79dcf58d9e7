<?php
/***************************************************************************
 * 
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
/**
 * @file         RemarkConfig.php
 * <AUTHOR>
 * @create_date  2017-12-14
 * @brief        助教对学生课后作业评价配置信息
 *  
 **/

class Hkzb_Util_Fudao_RemarkConfig {
    //评级
    static public $remarkMap = array (
        1 => array(             //小学
            1 => array(         //语文
                1 => '作文题：本次作文中运用本讲课上所讲的技巧、紧扣题目，选材新颖、主题突出，语言生动优美、详略得当，字迹工整、错别字较少，将得到S级评分；非作文题：本次作业若作答准确、卷面整洁，将得到S级评分',//A评级标准
                2 => '作文题：本次作文语言通顺、紧扣题目、主题明显、字迹工整，但是本讲技巧没有运用好，将得到A级评分；非作文题：本次作业若作答出现少许失误，将得到A级评分', //B评级标准
                3 => '作文题：本次作文跑题、语言不通顺、字迹潦草、错别字较多、字数不够，会得到B级评分；非作文题：本次作业错误出现较多，会得到B级的评分',//C评级标准
                4 => '亲爱的同学，提交与作业无关的图片的同学，将会得到C级评分',//D评级标准
            ),
            2 => array(         //数学
                1 => '亲爱的同学，本次作业若作答准确、步骤完整，将得到S级评分',             //A评级标准
                2 => '亲爱的同学，本次作业若作答出现少许失误、步骤出现缺失，将得到A级评分', //B评级标准
                3 => '亲爱的同学，本次作业若错误出现较多，会得到B级的评分',                 //C评级标准
                4 => '亲爱的同学，提交与作业无关的图片，将会得到C级评分',                   //D评级标准
            ),
            3 => array(         //英语
                1 => '作文题：本次作文内容符合主题、逻辑清晰、没有出现单词语法错误、字数符合要求、卷面整洁、字迹工整，将得到S级评分；非作文题：本次作业若作答准确、语句通顺、卷面整洁，将得到S级评分',//A评级标准
                2 => '作文题：本次作文内容符合主题、逻辑清晰，字数符合要求，但出现少量单词语法错误，将得到A级评分；非作文题：本次作业若作答出现少许失误，将得到A级评分', //B评级标准
                3 => '作文题：本次作文内容不符合主题、单词语法错误较多、字数不符合要求，会得到B级的评分；非作文题：本次作业若错误出现较多，会得到B级的评分',                 //C评级标准
                4 => '亲爱的同学，提交与作业无关的图片的同学，将会得到C级评分',//D评级标准
            ),
        ),
        2 => array(             //初中
            1 => array(         //语文
                1 => '作文题：本次作文立意合理、内容详尽、结构完整、字迹工整、无错别字、无语病，将得到S级评分；非作文题：本次作业若作答准确、卷面整洁，将得到S级评分',//A评级标准
                2 => '作文题：本次作文立意准确、内容缺失、结构不当、字迹潦草、出现错别字、语病较多，将得到A级评分；非作文题：本次作业若作答出现少许失误，将得到A级评分', //B评级标准
                3 => '作文题：本次作文跑题、语言不通顺、字迹潦草、错别字较多、字数不够，会得到B级评分；非作文题：本次作业错误出现较多，会得到B级的评',//C评级标准
                4 => '亲爱的同学，提交与作业无关的图片的同学，将会得到C级评分',//D评级标准
            ),
            2 => array(         //数学
                1 => '亲爱的同学，本次作业若作答准确、步骤完整，将得到S级评分',             //A评级标准
                2 => '亲爱的同学，本次作业若作答出现少许失误、步骤出现缺失，将得到A级评分', //B评级标准
                3 => '亲爱的同学，本次作业若错误出现较多，会得到B级的评分',                 //C评级标准
                4 => '亲爱的同学，提交与作业无关的图片，将会得到C级评分',                   //D评级标准
            ),
            3 => array(         //英语
                1 => '作文题：本次作文内容符合主题、逻辑清晰、没有出现单词语法错误、字数符合要求、卷面整洁、字迹工整，将得到S级评分；非作文题：本次作业若作答准确、语句通顺、卷面整洁，将得到S级评分',//A评级标准
                2 => '作文题：本次作文内容符合主题、逻辑清晰，字数符合要求，但出现少量单词语法错误，将得到A级评分；非作文题：本次作业若作答出现少许失误，将得到A级评分', //B评级标准
                3 => '作文题：本次作文内容不符合主题、单词语法错误较多、字数不符合要求，会得到B级的评分；非作文题：本次作业若错误出现较多，会得到B级的评分',                 //C评级标准
                4 => '亲爱的同学，提交与作业无关的图片的同学，将会得到C级评分',//D评级标准
            ),
            4 => array(         //物理
                1 => '亲爱的同学，本次作业若作答准确、步骤完整，将得到S级评分',//A评级标准
                2 => '亲爱的同学，本次作业若作答出现少许失误、步骤出现缺失，将得到A级评分', //B评级标准
                3 => '亲爱的同学，本次作业若错误出现较多，会得到B级的评分',                 //C评级标准
                4 => '亲爱的同学，提交与作业无关的图片的同学，将会得到C级评分',//D评级标准
            ),
            5 => array(         //化学
                1 => '亲爱的同学，本次作业若作答准确、步骤完整，将得到S级评分',//A评级标准
                2 => '亲爱的同学，本次作业若作答出现少许失误、步骤出现缺失，将得到A级评分', //B评级标准
                3 => '亲爱的同学，本次作业若错误出现较多，会得到B级的评分',                 //C评级标准
                4 => '亲爱的同学，提交与作业无关的图片的同学，将会得到C级评分',//D评级标准
            ),
        ),
        3 => array(        //高中
            999 => array(  //全科
                1 => '亲爱的同学，2道题都对了，证明你棒棒哒，将得到S级评分',//A评级标准
                2 => '亲爱的同学，2道题，错了一道哦，下次加油呀，你将得到A级评分', //B评级标准
                3 => '亲爱的同学，很遗憾，2道题都错了，应该好好努力了哦，下次期待你的好表现，这次你会得到B级评分',//C评级标准
                4 => '哇，你发现了一个新的级别，快告诉班主任，让他找技术小伙伴查一下哦！',//D评级标准
            ),
        ),
    );
}
