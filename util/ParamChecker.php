<?php

/**
 * Copyright (c) 2018 zuoyebang.com, Inc. All Rights Reserved
 * @author: liu<PERSON><PERSON>@zuoyebang.com
 * @file: phplib/util/ParamChecker.php
 * @date: 2019/02/18
 * @file: 9:16 PM
 * @desc: 提供对公共参数的检查有效性检查方法
 */


/**
 * 提供对公共参数的检查有效性检查
 * Class Qdlib_Util_ParamChecker
 */
class Qdlib_Util_ParamChecker
{
    const RULE_NUMERIC              = "Numeric";
    const RULE_POSITIVE             = "Positive";
    const RULE_NOT_NEGATIVE         = "NotNegative";
    const RULE_RANGE                = "Range";
    const RULE_DATE_VALID           = "DateValid";
    const RULE_ENUM                 = "Enum";
    const RULE_DEFAULT              = "Default";
    const RULE_IS_ARRAY             = "IsArray";
    const RULE_IS_STRING            = "IsString";
    const RULE_NOT_EMPTY            = "NotEmpty";
    const RULE_LENGTH_LIMIT         = "LengthLimit";
    const RULE_MB_LENGTH_LIMIT      = "MBLengthLimit";
    const RULE_MB_LENGTH_FLOOR      = "MBLengthFloor";
    const RULE_CHAR_LENGTH_LIMIT    = 'CharLengthLimit';    // 字符长度限制（1个汉字算做1字符，2个字母或数字算作1字符）
    const RULE_CHAR_LENGTH_FLOOR    = 'CharLengthFloor';    // 字符长度限制（1个汉字算做1字符，2个字母或数字算作1字符）
    const RULE_ALNUM_PLUS = 'Alnumplus';

    const RULE_EMPTY_CASE           = "EmptyCase";
    const RULE_MUST_SET             = "MustSet";
    const RULE_CONFLICT             = "Conflict";
    const RULE_STRUCT               = "Struct";
    const RULE_EXCEPTION_RANGE      = "ExceptionRange";

    /** @var array parameter should be numeric and larger than zero */
    public static $numericPositive = [
        self::RULE_NUMERIC,
        self::RULE_POSITIVE,
    ];

    /** @var array parameter should be numeric and not negative */
    public static $numericNotNegative = [
        self::RULE_NUMERIC,
        self::RULE_NOT_NEGATIVE,
    ];

    /** @var array parameter should be a not empty string */
    public static $notEmptyString = [
        self::RULE_IS_STRING,
        self::RULE_NOT_EMPTY,
    ];

    /** @var array parameter should be numeric and not negative. If not pass, will be init to `0` */
    public static $default0NotNegative = [
        Qdlib_Util_ParamChecker::RULE_DEFAULT => 0,
        Qdlib_Util_ParamChecker::RULE_EMPTY_CASE => 0,
        Qdlib_Util_ParamChecker::RULE_NUMERIC,
        Qdlib_Util_ParamChecker::RULE_NOT_NEGATIVE,
    ];

    /** @var array parameter should be a default empty string */
    public static $defaultEmptyString = [
        self::RULE_DEFAULT => '',
        self::RULE_EMPTY_CASE => '',
        self::RULE_IS_STRING,
    ];

    public static function getLengthLimitRule($maxLength, $allowEmpty = false)
    {
        $rule = [self::RULE_IS_STRING];
        if ($allowEmpty) {
            $rule[self::RULE_DEFAULT] = '';
            $rule[self::RULE_EMPTY_CASE] = '';
        } else {
            $rule[] = self::RULE_NOT_EMPTY;
        }
        $rule[self::RULE_LENGTH_LIMIT] = $maxLength;
        return $rule;
    }

    /**
     * @param $params
     * @param array $ruleArr
     * @param $throwException
     * @return array
     * @throws Hk_Util_Exception
     */
    public static function check($params, array $ruleArr, $throwException = true)
    {
        $ret = [];
        $errInfo = [];
        $isPass = true;
        foreach ($ruleArr as $field => $rules) {
            //如果参数没有设置，则需要校验 是否必传
            if (!isset($params[$field])) {
                //必传，则报错
                if (!empty($rules[self::RULE_MUST_SET])) {
                    $isPass = false;
                    $fieldErrMsg = " 没有传入`{$field}`";
                    $errInfo[$field] = $fieldErrMsg;
                    if ($throwException) {
                        throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, $fieldErrMsg);
                    } else {
                        continue;
                    }
                } else {
                    // 不是必传，则不需要校验此字段
                    continue;
                }
            }
            unset($rules[self::RULE_MUST_SET]);
            if (!isset($params[$field]) && in_array(self::RULE_DEFAULT, array_keys($rules), true)) {
                $ret[$field] = $rules[self::RULE_DEFAULT];
                continue;
            }
            if (in_array(self::RULE_EMPTY_CASE, array_keys($rules), true) &&
                isset($params[$field]) && self::checkIsEmpty($params[$field])) {
                $ret[$field] = $rules[self::RULE_EMPTY_CASE];
                continue;
            }
            unset($rules[self::RULE_DEFAULT]);
            unset($rules[self::RULE_EMPTY_CASE]);
            $value = $params[$field];
            if (!empty($rules)) {
                foreach ($rules as $rule => $rulePara) {
                    if (is_int($rule)) {
                        $rule = $rulePara;
                        $rulePara = null;
                    }
                    $method = "rule" . $rule;
                    if (!method_exists("Qdlib_Util_ParamChecker", $method)) {
                        throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "check rule {$method} not exists");
                    }
                    try {
                        self::$method($field, $value, $rulePara, $params);

                    } catch (Exception $e) {
                        $isPass = false;
                        $errInfo[$field] = $e->getMessage();
                        if ($throwException) {
                            throw $e;
                        } else {
                            continue;
                        }
                    }
                }
            }
            $ret[$field] = $value;
        }
        return [$isPass, $isPass ? $ret : $errInfo];
    }

    private static function rulePositive($field, $value)
    {
        if ($value <= 0) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "参数 `{$field}` 应该为正, 传入错误 '{$value}'");
        }
    }

    private static function ruleNumeric($field, &$value)
    {
        if (!is_numeric($value)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "param `{$field}` 应该是数字, 传入错误 '{$value}'");
        }
        $value = intval(round($value));
    }

    private static function ruleNotNegative($field, $value)
    {
        if ($value < 0) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "param `{$field}` 应该非负, 传入错误 '{$value}'");
        }
    }

    private static function ruleRange($field, $value, $rulePara = null)
    {
        if (empty($rulePara) || !isset($rulePara['min']) || !isset($rulePara['max'])) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "Invalid rule params, function:" . __FUNCTION__);
        }

        $min = intval($rulePara['min']);
        $max = intval($rulePara['max']);
        if ($value < $min || $value > $max) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "param `{$field}` 超出范围 {$min} ~ {$max}, 传入错误 '{$value}'");
        }
    }

    private static function ruleDateValid($field, &$value)
    {
        $time = strtotime($value);
        if (empty($time) || $time < 946656000 || $time > 4102416000) { // 有效时间范围 2000-01-01 00:00:00 ~ 2100-01-01 00:00:00
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "param `{$field}` 不是有效时间, 传入错误 '{$value}'");
        }
        $value = date("Y-m-d H:i:s", $time);
    }

    private static function ruleEnum($field, $value, $rulePara = null)
    {
        if (empty($rulePara) || !is_array($rulePara)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "Invalid rule params, function:" . __FUNCTION__);
        }
        if (!in_array($value, $rulePara)) {
            $enums = implode(",", $rulePara);
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "param `{$field}` 不是限定枚举值, " .
                "应该在 [{$enums}], 传入错误 '{$value}'");
        }
    }

    private static function ruleIsString($field, &$value)
    {
        if (!is_string($value)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "param `{$field}` 应该传入字符串, 传入错误 {$value}");
        }
        $value = trim(strval($value));
    }

    private static function ruleNotEmpty($field, $value)
    {
        if (empty($value)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "param `{$field}` 应该非空, 传入错误 {$value}");
        }
    }

    private static function ruleLengthLimit($field, $value, $rulePara = null)
    {
        $limit = $rulePara;
        if (!is_int($limit) || intval($limit <= 0)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "Invalid rule params, function:"
                . __FUNCTION__ . ", the rule param of " . __FUNCTION__ . " should be positive integer, the given is {$rulePara}");
        }

        if (strlen($value) > $rulePara) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, " `{$field}` 长度应该小于等于 {$rulePara}");
        }
    }

    private static function ruleMBLengthLimit($field, $value, $rulePara = null)
    {
        $limit = $rulePara;
        if (!is_int($limit) || intval($limit <= 0)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "Invalid rule params, function:"
                . __FUNCTION__ . ", the rule param of " . __FUNCTION__ . " should be positive integer, the given is {$rulePara}");
        }
        if (mb_strlen($value, 'utf8') > $rulePara) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, " `{$field}` 宽字符长度应该小于等于 {$rulePara}");
        }
    }

    private static function ruleMBLengthFloor($field, $value, $rulePara = null)
    {
        $limit = $rulePara;
        if (!is_int($limit) || intval($limit <= 0)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "Invalid rule params, function:"
                . __FUNCTION__ . ", the rule param of " . __FUNCTION__ . " should be positive integer, the given is {$rulePara}");
        }
        if (mb_strlen($value, 'utf8') < $rulePara) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, " `{$field}` 字符长度应该大于等于 {$rulePara}");
        }
    }

    private static function ruleCharLengthLimit($field, $value, $rulePara = null)
    {
        $limit = $rulePara;
        if (!is_int($limit) || intval($limit <= 0)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "Invalid rule params, function:"
                . __FUNCTION__ . ", the rule param of " . __FUNCTION__ . " should be positive integer, the given is {$rulePara}");
        }
        $value = preg_replace('/[^\x00-\xff]/u', '11', $value);
        if (strlen($value) > $rulePara * 2) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, " `{$field}` 字符长度应该小于等于 {$rulePara}");
        }
    }

    private static function ruleCharLengthFloor($field, $value, $rulePara = null)
    {
        $limit = $rulePara;
        if (!is_int($limit) || intval($limit <= 0)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "Invalid rule params, function:"
                . __FUNCTION__ . ", the rule param of " . __FUNCTION__ . " should be positive integer, the given is {$rulePara}");
        }
        $value = preg_replace('/[^\x00-\xff]/u', '11', $value);
        if (strlen($value) < $rulePara * 2) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, " `{$field}` 宽字符长度应该大于等于 {$rulePara}");
        }
    }

    /**
     * Alpha-numeric with underscores and dashes
     *
     * @param	string
     * @return	bool
     */
    private static function ruleAlnumplus($field, $value)
    {
        if(!preg_match('/^[a-z0-9_-]+$/i', $value))
        {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "{$field} 输入不是字母、数字和符号");
        }
    }

    private static function ruleIsArray($field, &$value, $rulePara)
    {
        // 针对是json字符串的情况
        if (is_string($value)) {
            $value = json_decode($value, true);
        }
        if (!is_array($value)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "param `{$field}` 应该传入数组或json数组");
        }
        if (empty($rulePara)) return;
        foreach ($value as &$one) {
            foreach ($rulePara as $rule => $ruleParas) {
                if (is_int($rule)) {
                    $rule = $ruleParas;
                    $ruleParas = null;
                }
                $method = "rule" . $rule;
                if (!method_exists("Qdlib_Util_ParamChecker", $method)) {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "check rule {$method} not exists");
                }
                try {
                    self::$method($field, $one, $ruleParas, $value);
                } catch (Hk_Util_Exception $e) {
                    $pieces = explode("--", $e->getMessage());
                    $usefulMsg = $pieces[count($pieces) - 1];
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "数组 `{$field}` 的值 '{$one}' 没有通过 $method 检测. {$usefulMsg}");
                }
            }
        }
    }

    private static function ruleConflict($field, &$value, $rulePara, $checkParams) {
        if (!is_array($rulePara)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "rulePara `{$field}` should be array");
        }
        if (empty($rulePara)) {
            return;
        }
        foreach ( $rulePara as $conflictField) {
            if (isset($checkParams[$conflictField])) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "`{$field}` 和这些字段冲突 ". json_encode($rulePara));
            }
        }
    }

    private static function ruleExceptionRange($field, $value, $rulePara = null) {
        if (empty($rulePara) || !isset($rulePara['min']) || !isset($rulePara['max']) || !isset($rulePara['exception'])) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "Invalid rule params, function:" . __FUNCTION__);
        }

        $min = intval($rulePara['min']);
        $max = intval($rulePara['max']);
        if (
            ($value < $min || $value > $max)
            && (!in_array($value,$rulePara['exception']))
        ) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "param `{$field}` 超出范围 {$min} ~ {$max}, 传入错误 '{$value}'");
        }

    }

    private static function ruleStruct($field, &$value, $rulePara ) {


        foreach ($rulePara as $fieldCur => $arrRuleParas) {


            foreach ($arrRuleParas as $rule => $itemRulePara) {
                if (is_int($rule)) {
                    $rule = $itemRulePara;
                    $ruleParas = null;
                }
                if ($rule == self::RULE_MUST_SET) {
                    if ($arrRuleParas[self::RULE_MUST_SET] && !isset($value[$fieldCur])) {
                        throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "字段 {$fieldCur} 未定义");
                    }
                    continue;
                }
                if (!$value[$fieldCur]) {
                    continue;
                }

                $fetchItem = &$value[$fieldCur];
                $method = "rule" . $rule;
                if (!method_exists("Qdlib_Util_ParamChecker", $method)) {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "check rule {$method} not exists");
                }
                try {
                    self::$method($fieldCur, $fetchItem, $itemRulePara);
                } catch (Hk_Util_Exception $e) {
                    $pieces = explode("--", $e->getMessage());
                    $usefulMsg = $pieces[count($pieces) - 1];
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "结构体 `{$fieldCur}` 的值 '{$fetchItem}' 没有通过 $method 检测. {$usefulMsg}");
                }
            }
        }

    }

    private static function checkIsEmpty($value)
    {
        if (is_int($value)) {
            return false;
        }
        if (is_string($value)) {
            if (strlen($value) == 0) {
                return true;
            }
            $decode = json_decode($value, true);
            if (is_array($decode) && empty($decode)) {
                return true;
            } else {
                return false;
            }
        }
        if (is_array($value)) {
            if (empty($value)) {
                return true;
            } else {
                return false;
            }
        }
        return false;
    }
}


define("ZB_UTIL_PARAM_CHECKER_TEST", false);
if (defined("ZB_UTIL_PARAM_CHECKER_TEST") && ZB_UTIL_PARAM_CHECKER_TEST) {

    if (!class_exists("Hk_Util_Exception_Test")) {
        class Hk_Util_Exception_Test extends Exception
        {
            public function __construct($code = 0, $message = "", Throwable $previous = null)
            {
                parent::__construct($message, $code, $previous);
            }
        }

        class_alias("Hk_Util_Exception_Test", "Hk_Util_Exception");
    }

    if (!class_exists("Hk_Util_ExceptionCodes_Test")) {
        class Hk_Util_ExceptionCodes_Test
        {
            const PARAM_ERROR = 1;
        }

        class_alias("Hk_Util_ExceptionCodes_Test", "Hk_Util_ExceptionCodes");
    }

    $params = [
        "table" => "0",
        "type" => 1,
        "cds" => 12,
        "sdf" => [
            "aa" => 1,
            "bb" => 1,
            "cc" => "hello"
        ],
        "ccc" => 1,
        "lengthLimit" => "sdf",
        "date" => "Mon Mar  4 16:52:29 CST 2009",
        "name" => "ndfai",
        "empty" => [],
        "siteSite" => ["ssss"]
    ];

    $rules = [
        "siteSite" => [
            Qdlib_Util_ParamChecker::RULE_MUST_SET => true,
            Qdlib_Util_ParamChecker::RULE_IS_ARRAY => [
                Qdlib_Util_ParamChecker::RULE_ENUM=>[
                    "ssss","bb"
                ],
            ],
        ],
        "table" => [
            Qdlib_Util_ParamChecker::RULE_IS_STRING,

        ],
        "type" => [
            Qdlib_Util_ParamChecker::RULE_ENUM => [1, 2, 3],
        ],
        "cds" => [
            Qdlib_Util_ParamChecker::RULE_DEFAULT => 131,
            Qdlib_Util_ParamChecker::RULE_POSITIVE,
            Qdlib_Util_ParamChecker::RULE_NUMERIC,
        ],
        "sdf" => [
            Qdlib_Util_ParamChecker::RULE_STRUCT => [
                "aa" => [
                    Qdlib_Util_ParamChecker::RULE_POSITIVE,
                    Qdlib_Util_ParamChecker::RULE_NUMERIC,
                ],
                "bb" => [
                    Qdlib_Util_ParamChecker::RULE_MUST_SET => true,
                    Qdlib_Util_ParamChecker::RULE_NUMERIC,
                ],
                "cc" => [
                    Qdlib_Util_ParamChecker::RULE_MUST_SET => true,
                    Qdlib_Util_ParamChecker::RULE_ENUM => [
                        "hello"
                    ]
                ]
            ],
        ],
        "ccc" => [
            Qdlib_Util_ParamChecker::RULE_NOT_NEGATIVE,
          //  Qdlib_Util_ParamChecker::RULE_CONFLICT => ["name"],
        ],
        "lengthLimit" => [
            Qdlib_Util_ParamChecker::RULE_IS_STRING,
            Qdlib_Util_ParamChecker::RULE_LENGTH_LIMIT => 10,
        ],
        "date" => [
            Qdlib_Util_ParamChecker::RULE_DATE_VALID,
        ],
        "name" => [
            Qdlib_Util_ParamChecker::RULE_MB_LENGTH_FLOOR => 3,
        ],
        "empty" => [
            Qdlib_Util_ParamChecker::RULE_DEFAULT => "sdfa",
            Qdlib_Util_ParamChecker::RULE_EMPTY_CASE => "sdfa",
        ],
        "default" => [
            Qdlib_Util_ParamChecker::RULE_DEFAULT => "DDD"
        ],
    ];
    try {
        var_dump(Qdlib_Util_ParamChecker::check($params, $rules, false));
    } catch (Hk_Util_Exception $e) {
        echo $e->getCode() . "   " . $e->getMessage() . PHP_EOL;
    }
}