<?php
/**
 * Created by PhpStorm.
 * @file SecKill12002.php
 * <AUTHOR>
 * @date 18-7-7
 * @version
 * @brief
 *
 **/


class Oplib_Util_SecKill12002
{

    static public $ZHOU_START_HOUR = array(
        0 => 7,
        1 => 9,
        2 => 9,
        3 => 9,
        4 => 9,
        5 => 9,
        6 => 7,
    );
    static public $ZHOU_END_HOUR = array(
        0 => 23,
        1 => 23,
        2 => 23,
        3 => 23,
        4 => 23,
        5 => 23,
        6 => 23,
    );

    public static function getBankeCardCourse($grade, $subject)
    {
        $oneDollarInfo = [
            'grade' => 0,
            'subject' => 0,
            'content' => '',
            'title' => '',
            'isStart' => 0,
            'timeContent' => '',
            'classStartTime' => '',
            'jumpUrl' => ''

        ];
        //新四年级数学
        if ($grade == 14) {
            $nowTime = Oplib_Util_Tool::getCurrentTimeStamp();
            $price = 1;
            if ($nowTime >= ********** && $nowTime < **********) {
                $price = 19;
            }
            if ($nowTime >= **********) {
                $price = 50;
            }
            $oneDollarInfo['grade'] = $grade;
            $oneDollarInfo['subject'] = 2;
            $oneDollarInfo['isStart'] = 0;
            $oneDollarInfo['title'] = $price . "元秒杀数学暑期长期班";
            $oneDollarInfo['subTitle'] = "原价349 / 7次课 /教材包邮";
            $oneDollarInfo['jumpUrl'] = 'https://www.zybang.com/course/favorable/salecoursedetail?grade=' . $grade . '&subject=2';
            $oneDollarInfo = self::getSecKillTimestamp($oneDollarInfo);


        }
        //新初一数学
        if ($grade == 2) {
            $oneDollarInfo['grade'] = $grade;
            $oneDollarInfo['subject'] = 2;
            $oneDollarInfo['isShow'] = 1;
            $oneDollarInfo['title'] = "50元秒杀数学暑期长期班";
            $oneDollarInfo['subTitle'] = "原价299 / 10次课 /教材包邮";
            $oneDollarInfo['jumpUrl'] = 'https://www.zybang.com/course/favorable/salecoursedetail?grade=' . $grade . '&subject=2';
            $oneDollarInfo = self::getSecKillTimestamp($oneDollarInfo);

        }
        //新初二物理
        if ($grade == 3) {
            $oneDollarInfo['grade'] = $grade;
            $oneDollarInfo['subject'] = 4;
            $oneDollarInfo['isShow'] = 1;
            $oneDollarInfo['title'] = "50元秒杀物理暑期长期班";
            $oneDollarInfo['subTitle'] = "原价299 / 10次课 /教材包邮";
            $oneDollarInfo['jumpUrl'] = 'https://www.zybang.com/course/favorable/salecoursedetail?grade=' . $grade . '&subject=4';
            $oneDollarInfo = self::getSecKillTimestamp($oneDollarInfo);

        }

        //新高一数学
        if ($grade == 5) {
            $oneDollarInfo['grade'] = $grade;
            $oneDollarInfo['subject'] = 2;
            $oneDollarInfo['isShow'] = 1;
            $oneDollarInfo['title'] = "50元秒杀数学暑期长期班";
            $oneDollarInfo['subTitle'] = "原价249 / 7次课 /教材包邮";
            $oneDollarInfo['jumpUrl'] = 'https://www.zybang.com/course/favorable/salecoursedetail?grade=' . $grade . '&subject=2';
            $oneDollarInfo = self::getSecKillTimestamp($oneDollarInfo);

        }

        return $oneDollarInfo;

    }
    private static  function getSecKillTimestamp($oneDollarInfo){
        $week = date('w');
        $startTime = self::getTimestamp(self::$ZHOU_START_HOUR[$week]);
        $endTime = self::getTimestamp(self::$ZHOU_END_HOUR[$week]);
        $now = Oplib_Util_Tool::getCurrentTimeStamp();

        if ($now > $startTime && $now < $endTime) {
            $oneDollarInfo['classStartTime'] = '';
            $oneDollarInfo['timeContent'] = $endTime - $now;
            $oneDollarInfo['isStart'] = 1;


        }
        if ($now > $endTime) {
            $oneDollarInfo['timeContent'] = 0;
            $showTime = date('m月d日 H:i', strtotime('+1 days', $startTime));
            $oneDollarInfo['classStartTime'] = $showTime."准时开抢";//';
            $oneDollarInfo['isStart'] =2;
            $oneDollarInfo['timeContent'] = 0;

        }
        if ($now < $startTime) {
            $oneDollarInfo['timeContent'] = 0;
            $showTime = date('m月d日 H:i', $startTime);
            $oneDollarInfo['classStartTime'] = $showTime."准时开抢";
            $oneDollarInfo['isStart'] = 0;


        }
        return $oneDollarInfo;

    }
    private static function getTimestamp($hour)
    {
        $year = date("Y");
        $month = date("m");
        $day = date("d");
        $time = mktime($hour, 0, 0, $month, $day, $year);//当天时间戳
        return $time;
    }

}