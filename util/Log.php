<?php
/**
 * Created by PhpStorm.
 * User: <PERSON>haifeng
 * Date: 2019-08-06
 * Time: 16:31
 * 参考  Qdlib_Util_Log
 * wiki http://wiki.afpai.com/pages/viewpage.action?pageId=47455400
 * 1、增加日志监控，warnning      无提醒
 *                alarm          1分钟 3次 发提醒
 *                fatal          每次 发提醒
 *                addNotice      无
 */
class Oplib_Util_Log {

    /**
     * @brief 平时日志记录使用 排查问题
     * @param string $system 必须 系统 skyfire/buildtool
     * @param string $serviceName 必须 服务 serviceName
     * @param string $method 必须 方法 tehuike
     * @param string $serviceDetail 必须 想要打印的附加信息 xxxxxx
     * @param string|array $repairParams 可选
     * @param int|null   $errNo 错误号,错误号为null时，不打印warning
     * @param int        $depth 控制warning日志中显示那个地方调用的
     * @return string
     * @example Oplib_Util_Log::warning('skyfire', 'Service_Page_2019_GaoKaoXuanYan_Gkxy', 'execute', '同步失败id=xxxx', json_encode(array(xxx=>xxx)))
     */
    public static function warning ($system, $serviceName, $method, $serviceDetail, $repairParams = '', $errNo = 0, $depth = 1) {
        if(is_array($repairParams)){
            $repairParams = json_encode($repairParams);
        }
        $msg = sprintf("warSystem[%s] warServiceName[%s] warMethod[%s] warServiceDetail[%s] warRepairParams[%s]",
            $system, $serviceName, $method,$serviceDetail, $repairParams
        );

        // 错误号为null时，不打印warning
        is_null($errNo) || Bd_Log::warning($msg, $errNo, null, $depth);

        return $msg;
    }

    /**
     * @brief 错误日志，达到一定的错误次数会自动报警
     * @param string $system 必须 系统 clue/report
     * @param string $serviceName 必须 服务 addclue
     * @param string $method 必须 方法 syncdbfail
     * @param string $serviceDetail 必须 想要打印的附加信息 xxxxxx
     * @param string|array $repairParams 可选
     * @param int|null   $errNo 错误号,错误号为null时，不打印warning
     * @param int        $depth 控制warning日志中显示那个地方调用的
     * @return string
     * @example Oplib_Util_Log::alarm('report', 'addclue', 'syncdbfail', '同步失败id=xxxx', json_encode(array(xxx=>xxx)))
     */
    public static function alarm ($system, $serviceName, $method, $serviceDetail, $repairParams = '', $errNo = 0, $depth = 1) {
        if(is_array($repairParams)){
            $repairParams = json_encode($repairParams);
        }
        $msg = sprintf("alarmSystem[%s] alarmServiceName[%s] alarmMethod[%s] alarmServiceDetail[%s] alarmRepairParams[%s]",
            $system, $serviceName, $method,$serviceDetail, $repairParams
        );

        // 错误号为null时，不打印warning
        is_null($errNo) || Bd_Log::warning($msg, $errNo, null, $depth);

        //3次做报警
        $objRedis = Oplib_Util_Redis_Instance::getInstance();
        $cacheKey =  sprintf('actplat_log_error_%s_%s_%s_%s', date('Y-m-d H:i', time()),$system, $serviceName, $method);
        $keyCount = $objRedis->get($cacheKey);
        $keyCount = intval($keyCount);
        $keyCountNew = $keyCount + 1;
        $objRedis->setex($cacheKey, $expire = 60, $keyCountNew);
        if ($keyCountNew >= 3) {
            $logId = Bd_Log::genLogID();
            $markdown = [
                'title' => 'Alarm报警',
                            // "> key: $cacheKey \n\n" .
                'text'  =>  "#### [**$system**] Alarm报警: $serviceDetail (每分钟{$keyCountNew}次) \n\n " .
                            "$serviceName::$method() \n\n" .
                            "> " .  $repairParams . "\n\n" .
                            "logId [$logId](http://beidou.zuoyebang.cc/".self::getDiagnose()."?logId=$logId)\n\n" .
                            "@所有人"
            ];
            Oplib_Util_DingtalkRobot::sendDingtalkMarkDown($markdown,'',[]);
        }
        return $msg;
    }

    /**
     * @brief 致命的错误这个会直接报警
     * @param string $system 必须 系统 clue/report
     * @param string $serviceName 必须 服务 addclue
     * @param string $method 必须 方法 syncdbfail
     * @param string $serviceDetail 必须 想要打印的附加信息 xxxxxx
     * @param string|array $repairParams 可选
     * @param array $phone 可选 报警 叮叮@手机号
     * @param int|null   $errNo 错误号,错误号为null时，不打印warning
     * @param int        $depth 控制warning日志中显示那个地方调用的
     * @return string
     * @example Oplib_Util_Log::fatal('clue', 'addclue', 'syncdbfail', '同步失败id=xxxx', json_encode(array(xxx=>xxx)), array(13301171016,18611113333))
     */
    public static function fatal($system, $serviceName, $method, $serviceDetail, $repairParams = '', $phone = array(), $errNo = 0, $depth = 1) {
        if(is_array($repairParams)){
            $repairParams = json_encode($repairParams, JSON_UNESCAPED_UNICODE);
        }

        $objRedis = Oplib_Util_Redis_Instance::getInstance();
        $cacheKey =  sprintf('actplat_fatal_log_%s_%s_%s_%s_%s', date('Y-m-d H:i', time()),$system, $serviceName, $method, $repairParams);
        $keyCount = $objRedis->get($cacheKey);
        $keyCountNew = intval($keyCount) + 1;
        $objRedis->setex($cacheKey, $expire = 60, $keyCountNew);
        if( $keyCount > 1 ){
            return;
        }

        $msg = sprintf("fatalSystem[%s] fatalServiceName[%s] fatalMethod[%s] fatalServiceDetail[%s] fatalRepairParams[%s]",
                       $system, $serviceName, $method,$serviceDetail, $repairParams
        );

        // 错误号为null时，不打印warning
        is_null($errNo) || Bd_Log::fatal($msg, $errNo, null, $depth);
        $isAtAll = true;
        $atMobiles = [];
        if ($phone) {
            $isAtAll = false;
            $atMobiles = is_array($phone) ? $phone : array($phone);
        }
        $logId = Bd_Log::genLogID();
        $markdown = [
            'title' => 'Fatal报警',
            'text'  =>  "#### [**$system**] Fatal报警: $serviceDetail\n\n " .
                "$serviceName::$method() \n\n" .
                "> " .  $repairParams . "\n\n" .
                "logId [$logId](http://beidou.zuoyebang.cc/".self::getDiagnose()."?logId=$logId)\n\n" .
                "@" . ($isAtAll ? '所有人' : implode('@', $atMobiles)) . "\n"
        ];
        Oplib_Util_DingtalkRobot::sendDingtalkMarkDown($markdown,'', $atMobiles);

        return $msg;
    }

    /**
     * @brief 封装addNotice ，保证脚本模式下也可以直接记录日志
     * @param $key key
     * @param $value 内容
     * return void
     */
    public static function addNotice($key, $value) {
        if (PHP_SAPI === 'cli') {
            Bd_Log::notice(sprintf('%s[%s]', $key, $value));
        } else {
            Bd_Log::addNotice($key, $value);
        }
    }

    public static function getDiagnose()
    {
        $strIdc = Bd_Conf::getConf('idc/cur');
        $diagnose = 'diagnose';
        if (strtolower($strIdc) == 'test') {
            $diagnose = 'dockerdiagnose';
        }
        return $diagnose;
    }
}