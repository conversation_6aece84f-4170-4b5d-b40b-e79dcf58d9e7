<?php
/**
 * Created by PhpStorm.
 * User: duxiang
 * Date: 2019-04-24
 * Time: 16:31
 */


class Qdlib_Util_Log {

    /**
     * http://wiki.zuoyebang.cc/pages/viewpage.action?pageId=********
     */
    private static $systemPhones = [
        'report' => ['***********'],
        'clue' => ['***********'],
        'account' => ['***********'],
        'kafka' => ['***********'],
        'order' => ['***********'],
        'datacenter' => ['***********'],
        'swift' => ['***********'],
        'syncdata' => ['***********'],
        'activity' => ['***********'],
        'courseorder' => ['***********'],
        'rta' => ['***********', '***********'],
        'qudao' => ['***********'],
        'crontab' => ['***********'],
        'lastfrom' => ['***********'],
    ];

    /**
     * @brief 平时日志记录使用 排查问题
     * @param string $system 必须 系统 clue/report
     * @param string $serviceName 必须 服务 addclue
     * @param string $method 必须 方法 syncdbfail
     * @param string $ServiceDetail 必须 想要打印的附加信息 xxxxxx
     * @param string $repairParams 可选
     * return void
     * @example Qdlib_Util_Log::warning('clue', 'addclue', 'syncdbfail', '同步失败id=xxxx', json_encode(array(xxx=>xxx)))
     */
    public static function warning ($system, $serviceName, $method, $serviceDetail, $repairParams = '') {
        $msg = sprintf("warSystem[%s] warServiceName[%s] warMethod[%s] warServiceDetail[%s] warRepairParams[%s]",
            $system, $serviceName, $method,$serviceDetail, $repairParams
        );
        $msg = Qdlib_Util_Tool::unicodeToUtf8($msg);
        Bd_Log::warning($msg);
        $statsd = new Qdlib_Monitor_Sentinel('qudao');
        $statsd->count('error_log_v2', 1, ['level' => 'warning', 'warSystem' => $system, 'warServiceName' => $serviceName, 'warMethod' => $method]);

    }

    /**
     * @brief 错误日志，达到一定的错误次数会自动报警
     * @param string $system 必须 系统 clue/report
     * @param string $serviceName 必须 服务 addclue
     * @param string $method 必须 方法 syncdbfail
     * @param string $ServiceDetail 必须 想要打印的附加信息 xxxxxx
     * @param string $repairParams 可选
     * return void
     * @example Qdlib_Util_Log::error('clue', 'addclue', 'syncdbfail', '同步失败id=xxxx', json_encode(array(xxx=>xxx)))
     */
    public static function error ($system, $serviceName, $method, $serviceDetail, $repairParams = '') {
        $msg = sprintf("errSystem[%s] errServiceName[%s] errMethod[%s] errServiceDetail[%s] errRepairParams[%s]",
            $system, $serviceName, $method,$serviceDetail, $repairParams
        );
        $statsd = new Qdlib_Monitor_Sentinel('qudao');
        $statsd->count('error_log_v2', 1, ['level' => 'error', 'warSystem' => $system, 'warServiceName' => $serviceName, 'warMethod' => $method]);
        $msg = Qdlib_Util_Tool::unicodeToUtf8($msg);
        Bd_Log::warning($msg);

        //3次做报警
        $objCache = Qdlib_Util_Cache::getQudaoRedis();
        $cacheKey =  sprintf('qudao_tflog_error_%s_%s_%s_%s', date('YmdHi', time()),$system, $serviceName, $method);
        $keyCount = $objCache->get($cacheKey);
        $keyCount = intval($keyCount);
        $keyCountNew = $keyCount + 1;
        $objCache->set($cacheKey, $keyCountNew, 60);
        if ($keyCountNew >= 3) {
            if (isset(self::$systemPhones[$system])) {
                Qdlib_Util_DingtalkRobot::sendDingtalk(sprintf("error报警\n 每分钟达到%s次,key=%s\n detail=%s", $keyCountNew, $cacheKey, $msg), false, self::$systemPhones[$system], Qdlib_Util_DingtalkRobot::QUDAO_ACCESS_TOKEN_ERROR);
            } else {
                Qdlib_Util_DingtalkRobot::sendDingtalk(sprintf("error报警\n 每分钟达到%s次,key=%s\n detail=%s", $keyCountNew, $cacheKey, $msg), true, [], Qdlib_Util_DingtalkRobot::QUDAO_ACCESS_TOKEN_ERROR);
            }
        }

    }

    /**
     * @brief 错误日志，达到一定的错误次数会自动报警
     * @param string $system 必须 系统 clue/report
     * @param string $serviceName 必须 服务 addclue
     * @param string $method 必须 方法 syncdbfail
     * @param string $ServiceDetail 必须 想要打印的附加信息 xxxxxx
     * @param string $repairParams 可选
     * @param string $expire 可选
     * @param string $countLimit 可选
     * @param string $accessToken 可选
     * return void
     * @example Qdlib_Util_Log::error('clue', 'addclue', 'syncdbfail', '同步失败id=xxxx', json_encode(array(xxx=>xxx)))
     */
    public static function errorWithToken($system, $serviceName, $method, $serviceDetail, $repairParams = '', $expire=60, $countLimit=1, $accessToken='') {
        $msg = sprintf("errSystem[%s] errServiceName[%s] errMethod[%s] errServiceDetail[%s] errRepairParams[%s]",
            $system, $serviceName, $method,$serviceDetail, $repairParams
        );
        $statsd = new Qdlib_Monitor_Sentinel('qudao');
        $statsd->count('error_log_v2', 1, ['level' => 'error', 'warSystem' => $system, 'warServiceName' => $serviceName, 'warMethod' => $method]);
        $msg = Qdlib_Util_Tool::unicodeToUtf8($msg);
        Bd_Log::warning($msg);

        //3次做报警
        $objCache = Qdlib_Util_Cache::getQudaoRedis();
        $cacheKey =  sprintf('qudao_tflog_error_token_%s_%s_%s_%s', date('YmdHi', time()),$system, $serviceName, $method);
        $keyCount = $objCache->get($cacheKey);
        $keyCount = intval($keyCount);
        $keyCountNew = $keyCount + 1;
        $objCache->set($cacheKey, $keyCountNew, $expire);
        if ($accessToken==''){
            $accessToken=Qdlib_Util_DingtalkRobot::QUDAO_ACCESS_TOKEN_APP_ERROR;
        }

        if ($keyCountNew >= $countLimit) {
                Qdlib_Util_DingtalkRobot::sendDingtalk(sprintf("error报警\n 每%d分钟达到%s次,key=%s\n detail=%s", $expire/60,$keyCountNew, $cacheKey, $msg), false, [], $accessToken);
        }

    }

    /**
     * @brief 致命的错误这个会直接报警
     * @param string $system 必须 系统 clue/report
     * @param string $serviceName 必须 服务 addclue
     * @param string $method 必须 方法 syncdbfail
     * @param string $ServiceDetail 必须 想要打印的附加信息 xxxxxx
     * @param string $repairParams 可选
     * @param array $phone 可选 报警 叮叮@手机号
     * @param string $accessToken 可选 报警accessToken 指定机器人
     * return void
     * @example Qdlib_Util_Log::fatal('clue', 'addclue', 'syncdbfail', '同步失败id=xxxx', json_encode(array(xxx=>xxx)), array(13301171016,18611113333))
     */
    public static function fatal ($system, $serviceName, $method, $serviceDetail, $repairParams = '', $phone = array(), $accessToken = '') {
        $msg = sprintf("fatalSystem[%s] fatalServiceName[%s] fatalMethod[%s] fatalServiceDetail[%s] fatalRepairParams[%s]",
            $system, $serviceName, $method,$serviceDetail, $repairParams
        );
        $msg = Qdlib_Util_Tool::unicodeToUtf8($msg);
        Bd_Log::fatal($msg);
        if ($phone) {
            $isAtAll = false;
            $atMobiles = $phone;
        } elseif (isset(self::$systemPhones[$system])) {
            $isAtAll = false;
            $atMobiles = self::$systemPhones[$system];
        } else {
            $isAtAll = true;
            $atMobiles = [];
        }
        Qdlib_Util_DingtalkRobot::sendDingtalk("fatal报警\n".$msg, $isAtAll, $atMobiles, $accessToken);
        $statsd = new Qdlib_Monitor_Sentinel('qudao');
        $statsd->count('error_log_v2', 1, ['level' => 'fatal', 'warSystem' => $system, 'warServiceName' => $serviceName, 'warMethod' => $method]);

    }

    /**
     * @brief 封装addNotice ，保证脚本模式下也可以直接记录日志
     * @param $key string key
     * @param $value string 内容
     * return void
     */
    public static function addNotice($key, $value) {
        if (PHP_SAPI === 'cli') {
            Bd_Log::notice(sprintf('%s[%s]', $key, $value));
        } else {
            Bd_Log::addNotice($key, $value);
        }
    }

    public static function noticeFrequency ($system, $serviceDetail,$key, $frequency=10,$slot=0,$token='') {
        if($key==""){
            return false;
        }
        $msg = sprintf("errSystem[%s] errServiceDetail[%s] key[%s] ",
            $system, $serviceDetail,$key
        );
        $msg = Qdlib_Util_Tool::unicodeToUtf8($msg);
        Bd_Log::warning($msg);
        //自定义频次做报警
        if($token==""){
            $token = Qdlib_Util_DingtalkRobot::QUDAO_ACCESS_TOKEN_FREQUENCY;
        }

        $objCache = Qdlib_Util_Cache::getQudaoRedis();
        $cacheKey =  sprintf('qudao_tflog_noticeFrequency_%s_%s', date('YmdHi', time()),$key);
        $keyCount = $objCache->get($cacheKey);
        $keyCount = intval($keyCount);
        $keyCountNew = $keyCount + 1;
        $objCache->set($cacheKey, $keyCountNew, 60);
        $slotCache = true;
        if($slot>0){
            $slotCache = $objCache->set($key.'_slot', 1,  ['NX', 'EX' => $slot]);
        }
        if ($keyCountNew >= $frequency && $slotCache) {
            Qdlib_Util_DingtalkRobot::sendDingtalk(sprintf("【业务报警】%s\n %s\n 分钟频次为：%d", $system, $serviceDetail,$keyCountNew), true, [], $token);
        }

    }


}