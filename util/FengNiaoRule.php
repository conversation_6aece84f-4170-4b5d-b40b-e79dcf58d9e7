<?php

class Oplib_Util_FengNiaoRule{

    /**
     * 路由规则v22v1
     * @param $groupRouter
     * [
     *    [  config : [],pageId: ]
     * ]
     */
    public static function groupRouterV1TOV2($groupRouter){
        if(empty($groupRouter) || !is_array($groupRouter)){
            return  [];
        }
        $groupRouterV2 = [];
        foreach ($groupRouter as $router){
            if($router['pageId'] && !$router['page']){
                $router['page'] = $router['pageId'];
            }
            if($router['page'] && is_array($router['config'])){
                $routerV2 = [
                    'pageId'  => $router['page'],
                    'page'    => $router['page'],
                    'config'  => []
                ];
                foreach ($router['config'] as $key => $rule){
                    if( empty($rule) ) continue;
                    if( $rule[0] === "-1" ) continue;
                    $configV2 = [];
                    switch ($key){
                        case 'time':
                            if($rule[0] > 0){
                                $configV2 = ['id' => 1,'value' => $rule];
                            }
                            break;
                        case 'userGrade':
                            $configV2 = ['id' => 2,'value' => $rule];
                            break;
                        case 'userType':
                            $configV2 = ['id' => 3,'value' => $rule];
                            break;
                        case 'userVipRights':
                            $configV2 = ['id' => 4,'value' => $rule];
                            break;
                        case 'business':
                            switch ($rule['value']){
                                case "1001": // 商品库存为0
                                    $configV2 = ['id' => 5,'value' => $rule['skuId']];
                                    break;
                                case "1002": //用户已购买商品
                                    $configV2 = ['id' => 6,'value' => $rule['skuId']];
                                    break;
                                case "1003": //用户有资格购买商品
                                    $configV2 = ['id' => 7,'value' => $rule['skuId']];
                                    break;
                                case "1004": //库存可用
                                    $configV2 = ['id' => 8,'value' => $rule['skuId']];
                                    break;

                                case "1101":
                                    // 用户未作答试卷
                                    $configV2 = ['id' => 13,'value' => []];
                                    break;
                                case "1102":
                                    // 正确率
                                    $configV2 = ['id' => 12,'value' => [$rule['minRightPercen'], $rule['maxRightPercen']]];
                                    break;

                                case "1201":
                                    $configV2 = ['id' => 9,'value' => [$rule['startValue'], $rule['endValue']]];
                                    break;
                                case "1202":
                                    $configV2 = ['id' => 10,'value' => [$rule['startValue'], $rule['endValue']]];
                                    break;
                                case "1203":
                                    $configV2 = ['id' => 11,'value' => [$rule['startValue'], $rule['endValue']]];
                                    break;
                                default:
                                    $configV2 = [];
                                    break;
                            }
                            break;
                        default:
                            break;
                    }
                    if($configV2){
//                        $configV2['label'] = $rule['label'];
                        $routerV2['config'][] = $configV2;
                    }
                }
                $groupRouterV2[] = $routerV2;
            }
        }
        return $groupRouterV2;
    }
}