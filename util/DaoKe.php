<?php
/**************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   DaoKe.php
 * <AUTHOR>
 * @date   2018/7/15
 * @brief  催到课
 **/
class Oplib_Util_DaoKe
{
    public static function getCourseInfo($uid)
    {
        $arrOutput = [];

        $objCache  = Hk_Service_RedisClient::getInstance("zhibo");

        // 课程信息key
        $courseInfoKey = sprintf(Oplib_Const_Cache::UCEP_UID_DAO_KE_TI_XING_KEY_PRE, $uid);
        $courseInfo    = $objCache->get($courseInfoKey);
        // courseInfo = ['skuId'=>1,'courseId'=>1,'lessonId'=>1,'grade'=>1,'subject'=>1,'onlineStart'=>1,'onlineStop'=>1]
        $courseInfo = json_decode($courseInfo, true);

        if ($courseInfo) {
            $nowTime = time();
            // 课程开始前一天00:00
            $courseBeginYesterdayTime = strtotime(date('Y-m-d 00:00:00',intval($courseInfo['onlineStart']))) - 86400;
            // 取课程为没有结束，并且当前时间大于课程开始前一天00:00
            if (intval($courseInfo['onlineStop']) >= $nowTime
                && $courseBeginYesterdayTime <= $nowTime) {
                /* 暂时下掉，已经不能通过本地调用了，如果要使用需要修改为通过rpc调用
                $courseId = intval($courseInfo['courseId']);


                // 查询用户是否购买指定课程
                $objZbCoreDsDarInterface = new Zb_Core_Ds_Dar_Interface();
                $coursePayInfo           = $objZbCoreDsDarInterface->queryBoughtCourseList($uid, [$courseId]);

                if (isset($coursePayInfo[$courseId]) && Zb_Core_Dao_Dar_SubTrade::TRADE_STATUS_PAID == $coursePayInfo[$courseId]) {
                    $arrOutput = $courseInfo;
                } elseif (Hk_Util_Ip::isInnerIp()) {
                    // 为了方便测试，如果是内网用户，即使没有购买该课程也认为购买了该课程
                    $arrOutput = $courseInfo;
                }*/
                $arrOutput = $courseInfo;
            }
        }

        return $arrOutput;
    }

    /** @判断用户有没有退课
     * @param $uid
     * @param $courseInfo
     * @example $courseInfo = [
     *    'skuId'       => 2006316,
     *    'courseId'    => 56513,
     *    'lessonId'    => 114782,
     *    'courseName'  => "【阶段提升】解一元一次方程进阶",
     *    'grade'       => 7,
     *    'subject'     => 3,
     *    'onlineStart' => 1536411600, // 开课时间的时间戳
     *    'onlineStop'  => 1536419700, // 下课时间的时间戳
     * ];
     * @return array
     */
    public static function judgeCourseInfo($uid, $courseInfo)
    {
        $arrOutput = [];

        $nowTime = time();
        // 取课程为没有结束的数据
        if (is_array($courseInfo) && intval($courseInfo['onlineStop']) >= $nowTime) {
            /* 暂时下掉，已经不能通过本地调用了，如果要使用需要修改为通过rpc调用
            $courseId = intval($courseInfo['courseId']);
            // 查询用户是否购买指定课程
            $objZbCoreDsDarInterface = new Zb_Core_Ds_Dar_Interface();
            $coursePayInfo           = $objZbCoreDsDarInterface->queryBoughtCourseList($uid, [$courseId]);

            if (isset($coursePayInfo[$courseId]) && Zb_Core_Dao_Dar_SubTrade::TRADE_STATUS_PAID == $coursePayInfo[$courseId]) {
                $arrOutput = $courseInfo;
            } elseif (Hk_Util_Ip::isInnerIp()) {
                // 为了方便测试，如果是内网用户，即使没有购买该课程也认为购买了该课程
                $arrOutput = $courseInfo;
            }*/
            $arrOutput = $courseInfo;
        }
        return $arrOutput;
    }

    /** 格式化课程信息
     * @param $courseInfo
     * @example $courseInfo = [
     *    'skuId'       => 2006316,
     *    'courseId'    => 56513,
     *    'lessonId'    => 114782,
     *    'courseName'  => "【阶段提升】解一元一次方程进阶",
     *    'grade'       => 7,
     *    'subject'     => 3,
     *    'onlineStart' => 1536411600, // 开课时间的时间戳
     *    'onlineStop'  => 1536419700, // 下课时间的时间戳
     * ];
     * @return mixed
     */
    public static function formateCourseInfo($courseInfo)
    {
        $dayTime    = strtotime(date('Y-m-d 00:00:00', intval($courseInfo['onlineStart'])));
        $courseTime = date('H:i', intval($courseInfo['onlineStart']));
        $courseDay  = self::formatOnlineTime(intval($courseInfo['onlineStart']));
        $courseName = strval($courseInfo['courseName']);
        $subject    = $courseInfo['subject'];
        if (isset(Zb_Const_GradeSubject::$SUBJECT[$subject])) {
            $subject = strval(Zb_Const_GradeSubject::$SUBJECT[$subject]);
        }
        $needle = '】';
        $pos    = mb_strrpos($courseName, $needle);
        if (false !== $pos && mb_strpos($courseName, '【') === 0) {
            // 去掉课程名中以‘【xxxx】'开头中的内容
            $courseName = mb_substr($courseName, $pos + mb_strlen($needle));
        }
        $courseInfo['dayTime']    = $dayTime;    // 表示开课时间当天零点的时间戳
        $courseInfo['courseTime'] = $courseTime; // 具体的时间比如20:00
        $courseInfo['courseDay']  = $courseDay;  // 上课日期，比如后天，明天
        $courseInfo['courseName'] = $courseName; // 去掉课程名中‘【】'中的课程名
        $courseInfo['subject']    = $subject;    // 学科，例如数学

        return $courseInfo;

    }

    /** 根据时间戳转换为:今天、明天、后天、本周X、下周X
     * @param $onlineTimestamp
     * @return false|string
     */
    public static function formatOnlineTime($onlineTimestamp)
    {
        $onlineTime = '';

        // 首先计算，今天，明天，后天
        $onlineTimestamp = intval($onlineTimestamp);
        $now = time();
        // 今天0点时间戳
        $todayDateTime = strtotime(date('Y-m-d 00:00:00',$now));
        if ($todayDateTime <= $onlineTimestamp && $onlineTimestamp < ($todayDateTime + 86400)) {
            $onlineTime = '今天';
        }elseif(($todayDateTime + 86400) <= $onlineTimestamp  && $onlineTimestamp < ($todayDateTime + 86400*2)){
            $onlineTime = '明天';
        }elseif(($todayDateTime + 86400*2) <= $onlineTimestamp  && $onlineTimestamp < ($todayDateTime + 86400*3)){
            $onlineTime = '后天';
        }
        if($onlineTime){
            return $onlineTime;
        }

        // 下面计算:下周X,本周X
        $dayMap          = [1 => '一', 2 => '二', 3 => '三', 4 => '四', 5 => '五', 6 => '六', 7 => '日'];
        // 计算上课时间是周几，1 是周一 7是周日
        $onlineDayOfTheWeek = date('N', $onlineTimestamp);
        $cnOnlineDay        = $dayMap[$onlineDayOfTheWeek];
        $nowDayOfTheWeek    = date('N');

        // 上周日的时间戳,23:59:59的时间戳
        $lastSunDayTime = (0 - $nowDayOfTheWeek) * 86400 + strtotime(date('Y-m-d 23:59:59', $now));
        // 本周日的时间戳,23:59:59的时间戳
        $sunDayTime = (7 - $nowDayOfTheWeek) * 86400 + strtotime(date('Y-m-d 23:59:59', $now));
        // 下周日的时间戳,23:59:59的时间戳
        $nextSunDayTime = (14 - $nowDayOfTheWeek) * 86400 + strtotime(date('Y-m-d 23:59:59', $now));
        if ($onlineTimestamp > $nextSunDayTime || $onlineTimestamp <= $lastSunDayTime) {
            $onlineTime = date('n月j日', $onlineTimestamp);
        } elseif ($onlineTimestamp > $sunDayTime) {
            // 下周开课
            $onlineTime = '下周' . $cnOnlineDay;
        } elseif ($onlineTimestamp > $lastSunDayTime) {
            // 本周开课
            $onlineTime = '本周' . $cnOnlineDay;
        }

        return $onlineTime;
    }


    /** 判断指定的版本的app是否隐藏到课提醒
     * @param $os
     * @param $zbkvc
     * @return bool
     */
    public static function isHide($os, $zbkvc)
    {
        if (strval(trim($os)) == 'ios' && intval(trim($zbkvc)) == 55) {
            return true;
        }
        return false;
    }
}
