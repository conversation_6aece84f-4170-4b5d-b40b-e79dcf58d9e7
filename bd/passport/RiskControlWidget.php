<?php

/**
 * 风控组件
 * 
 * #需要配置RAL
 * #线上BNS地址 ppui-api.passport.all
 * #线下BNS地址 rd-ubtags.passport.all
 * [CamelConfig]
 * [.ServiceConfig]
 * [..Webfoot]
 * [...@Service]
 * [...@Service]
 * Name : rd-ubtags.passport.all
 * Rename : pass-ppui-api
 * Timeout : 100
 * @version 2014-7-7 11:11:11
 * <AUTHOR>
 * @copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 */
class Bd_Passport_RiskControlWidget {

    /**
     * SAPI
     * @var Bd_Passport_SAPI
     */
    private $sapi;

    /**
     * api 地址
     * 
     * @var string
     */
    private $url_path;

    /**
     * 风控数据
     * 
     * @var array
     */
    private $data;
    
    /**
     * 请求参数
     * 
     * @var array
     */
    private $params;

    /**
     * 请求BNS地址
     *
     * @var string
     */
    private $serverName;

    /**
     * 请求字段名称
     */
    private $operation;
    
    /**
     * 
     * 
     * 配置：
     * passport.conf
     * [sapi]
     * $tpl_$appid : $pass
     * 
     * @param int $appid SAPI appid
     * @param string $tpl 产品线标示
     */
    function __construct($appid = 1, $tpl = null) {
        $this->sapi = new Bd_Passport_SAPI($appid, $tpl);
        $this->url_path = '/ubtags/api/?getriskrate';
        $this->params = array();
    }
	
    /**
     * 请求pass服务
     * 
     * @example
     *	$rc = new Bd_Passport_RiskControlWidget();
     *	$features = $rc->query(array('clientip'=> Bd_Passport_Util::getClientIp(), 'fr_uri' => $_SERVER['REQUEST_URI']))->getFeature();
     * 
     * @param array $input
     * 	string	clientip	用户IP
     * 	string	fr_uri		当前请求URI
     * 	mixed	xxx		其它应用特征
     * 				
	 * @return Bd_Passport_RiskControlWidget
	 * @see http://dev.passport.baidu.com/docs/agg/view?path=pcapi.text&doc=pc/jsapi/device_info.text
     */
    public function query($input = null, $querytype = null) {
    	/*
    	 * 如自行封装该lib，请完整实现header内容
    	 */
    	$header = array(
    		'useragent' => (string)$_SERVER['HTTP_USER_AGENT'],
    		'referer' => (string)$_SERVER['HTTP_REFERER'],
    		'cookie' => array('BAIDUID'  => (string)$_REQUEST['BAIDUID'],  'BDUSS' => (string)$_REQUEST['BDUSS'], 'cuid' =>isset($_REQUEST['cuid'])?(string)$_REQUEST['cuid']:""),
    	);
    	if (is_array($input)) {
    		$this->params['ext'] = $input;
    	}
    	if ($querytype) {
    		$this->params['querytype'] = $querytype;
    	}


        if(!empty($this->operation)){
            $this->params['operation'] = $this->operation;
        }

        $this->data = $this->sapi->sendByRal($this->url_path,'post', $this->params, $header);

    	return $this;
    }


    /**
     * 查询用户信息
     *
     * @example
     *	$rc = new Bd_Passport_RiskControlWidget();
     *	$features = $rc->queryInfoByUserid(123, array('stolen', 'majia'), array('clientip'=> Bd_Passport_Util::getClientIp(), 'fr_uri' => $_SERVER['REQUEST_URI']));
     *
     * @param int $uid 用户userid
     * @param array $fields 查询字段
     * @param array $input
     * 	string	clientip	用户IP
     * 	string	fr_uri		当前请求URI
     * 	mixed	xxx		其它应用特征
     *
     * @return array
     */
    public function queryInfoByUserid($uid, $fields, $input = array()) {
    	
    	$this->params['uid'] = $uid;
    	$this->params['fields'] = $fields;
    	$this->query($input, "queryInfoByUserid");
    	return $this->getFeature();
    }


    /**
     * 获取是否是Majia
     */
    public function setIsMajia(){
        $this->operation[] = "majia";
        return $this;
    }

    /**
     * 获取是否高价值账号
     */
    public function setIsVip(){
        $this->operation[] = "vip";
        return $this;
    }


    /**
     * 获取近期是否存在异常登录
     */
    public function setIsNormal(){
        $this->operation[] = "isnormal";
        return $this;
    }

    /**
     * 获取是否为常用地登录
     */
    public function setCommonPlace(){
        $this->operation[] = 'commonplace';
        return $this;
    }

    /**
     * 获取是否作弊IP 代理IP
     */
    public function setIpTags(){
        $this->operation[] = "ip_tag";
        return $this;
    }

    /**
     * 获取CUID
     */
    public function setCuid(){
        $this->operation[] = "cuid";
        return $this;
    }

    /**
     * 获取是否被盗
     */
    public function setIsStolen(){
       $this->operation[] = "stolen";
        return $this;
    }

    /**
     * 获取用户历史绑定信息
     */
    public function setHistory(){
        $this->url_path = "/ubtags/api/?getchangelog";
        return $this;
    }


    /**
     * 获取特征
     * 
     * @example
     * 		$rc = new Bd_Passport_RiskControlWidget();
     * 		$features = $rc->query(array('clientip'=> getUserIp(), 'fr_uri' => $_SERVER['REQUEST_URI']))->getFeature();
     *  	$info = $rc->getInfo();	//info数据只可用于打日志
     * @return array
     * 	boolean	safelogin	当前会话状态是否安全
     * 	boolean	robot		是否是机器行为
     * 		   null 查询失败
     */
    public function getFeature() {
    	if ($this->getErrno() === 0) {
    		return $this->data['info'];
        } else {
    		return null;
    	}
    }
    
    /**
     * 获取其他信息
     * 
     * @return array 该方法可提供扩展信息，数据内容存在变动可能，请勿依赖该数据
     */
    public function getInfo() {
    	return $this->data['detail'];
    }
    
    /**
     * 
     * @return int
     * 			0	成功
     * 			69001	系统/未知错误
     * 			69002	参数错误
     * 			69003	未授权
     * 			69004	用户数据不存在
     * 			其他	失败
     */
    public function getErrno() {
    	return $this->data['errno'];
    }
    
    /**
     * 
     * @return string 错误信息
     */
    public function getErrmsg() {
    	return $this->data['errmsg'];
    }
}
