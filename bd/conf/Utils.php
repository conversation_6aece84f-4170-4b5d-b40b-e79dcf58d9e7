<?php
/***************************************************************************
 * 
 * Copyright (c) 2012 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
/**
 * @file Utils.php
 * <AUTHOR>
 * @date 2012/01/06 16:14:16
 * @brief 
 *  
 **/
class Bd_Conf_Utils {
    /**
     * @param mixed $arr
     * @return mixed
     */
    private function array_is_list($arr) {
        $keys = array_keys($arr);
        sort($keys);
        $i = 0;
        foreach ($keys as $k) {
            if ($k !== $i) {
                return false;
            }
            $i++;
        }
        return true;
    }
    /**
     * @param mixed $arr
     * @return mixed
     */
    private function array_item_all_array($arr) {
        foreach ($arr as $v) {
            if (!is_array($v)) {
                return false;
            }
        }
        return true;
    }
    /**
     * @param mixed $arr
     * @return mixed
     */
    private function array_item_all_not_array($arr) {
        foreach ($arr as $v) {
            if (is_array($v)) {
                return false;
            }
        }
        return true;
    }
    /**
     * @param mixed $key
     * @param mixed $value
     * @return mixed
     */
    private function conf_get_line($key, $value) {
        $str = "/[\\x00-\\x1F\\x80-\\xFF\"#]/";
        if (preg_match($str, $value) == 0) {
            return "{$key}: {$value}";
        }
        $s = '';
        $len = strlen($value);
        for ($i = 0; $i < $len; $i++) {
            $v = $value[$i];
            $c = ord($v);
            if ($c <= 32 || $v == '\\' || $v == "\"" || $c >= 128) {
                $val = dechex($c);
                if (strlen($val) < 2) {
                    $val = '0' . $val;
                } else {
                    $val = substr($val, -2, 2);
                }
                $s .= '\\x' . $val;
            } else {
                $s .= $v;
            }
        }
        return "{$key}: \"{$s}\"";
    }
    /**
     * @param mixed $arr
     * @param mixed $level
     * @return mixed
     */
    public function array2configure($arr, $level = 0) {
        $str = '';
        $arraykeys = array();
        
        // first output all KV keys
        foreach ($arr as $k => $v) {
            if (is_array($v)) {
                $arraykeys[] = $k;
                continue;
            }
            $str .= self::conf_get_line($k, $v) . "\n";
        }
        
        // next output simple arrays, @a:b kind
        $dictkeys = array();
        foreach ($arraykeys as $k) {
            $v = $arr[$k];
            if (self::array_is_list($v) && self::array_item_all_not_array($v)) {
                foreach ($v as $ck => $cv) {
                    $str .= '@' . self::conf_get_line($k, $cv) . "\n";
                }
            } else {
                $dictkeys[] = $k;
            }
        }
        
        // finally output all dicts and complicated arrays
        foreach ($dictkeys as $k) {
            $v = $arr[$k];
            if (self::array_is_list($v) && self::array_item_all_array($v)) {
                foreach ($v as $ck => $cv) {
                    $str .= '[' . str_repeat('.', $level) . '@' . $k . "]\n";
                    $str .= self::array2configure($cv, $level + 1);
                }
            } else {
                $str .= '[' . str_repeat('.', $level) . $k . "]\n";
                $str .= self::array2configure($v, $level + 1);
            }
        }
        return $str;
    }
}

/* vim: set ft=php expandtab ts=4 sw=4 sts=4 tw=0: */
