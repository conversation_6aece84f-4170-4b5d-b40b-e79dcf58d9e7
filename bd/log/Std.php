<?php


/**
 *
 * it can extend SplFileObject
 *
 */
class Bd_Log_Std extends \SplFileObject implements Bd_Log_Writer{

    /**
     * todo https://github.com/docker-library/php/issues/207 && https://bugs.php.net/bug.php?id=71880
     * @param string $f
     * @return Bd_Log_Std
     */
    public static function getInstance($f = "php://stdout") {
        $env_log_path = getenv("LOG_STREAM");
        if ($env_log_path) {
            $f = $env_log_path;
        }
        return new static($f, "a");
    }


    public function write($str) {
        return $this->fwrite($str . PHP_EOL);
    }
}