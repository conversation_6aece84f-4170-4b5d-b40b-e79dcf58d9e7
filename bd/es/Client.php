<?php


/**
 * es客户端实例
 *
 * @filesource bd/es/Client.php
 * <AUTHOR>
 * @version 1.0
 * @date    2018-08-03
 */
class Bd_Es_Client {

    private static $inst = array();

    private function __construct() {}

    /**
     * 获取指定配置实例
     *
     * @param string      $name
     * @return mixed:Bd_Es_Rpc|boolean
     */
    public static function getInstance($name) {
        if (isset(self::$inst[$name])) {
            return self::$inst[$name];
        }

        # 获取client服务配置
        $conf = Bd_Conf::getConf("/esclient/services/{$name}");
        if (false === $conf) {
            Bd_Log::warning("es error, {$name}, no configure exist");
            return false;
        }

        $ralName = $conf["ral"];
        $auth    = isset($conf["auth"]) ? strval($conf["auth"]) : "";
        $client  = new Bd_Es_Rpc($ralName, $auth);
        self::$inst[$name] = $client;
        return $client;
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */