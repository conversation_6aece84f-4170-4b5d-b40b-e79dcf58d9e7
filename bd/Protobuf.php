<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Protobuf.php
 * <AUTHOR>
 * @date 2015/11/06 18:03:23
 * @brief 
 *  
 **/

class Bd_Protobuf {

    private static $proto_dir = null;// = ROOT_PATH . '/proto'; 
    private static $cache_dir = null;//ROOT_DATA . "/proto"; 
    private static $depth = 2;
    private static $use_apc = false; 

	const TIME_EXPIRE_DFT = 86400;
    
    /**
     * @param null
     * @return object
     *
     **/
    public function __construct(){
        $extensions = get_loaded_extensions();
        if (!in_array("bdprotobuf", $extensions)){
            trigger_error("tbprotobuf no loaded!", E_USER_ERROR);
        }

        if(self::$proto_dir == null){
            self::$proto_dir = ROOT_PATH . '/php/phplib/protobuf/proto';
        }
        
        if(self::$cache_dir == null){
            self::$cache_dir = ROOT_PATH . '/data/proto';
        }

        if(in_array("apc", $extensions)){
            self::$use_apc = true;
        }
    }


   /**
    * @param string proto file name
    * @param string protobuf strings
    * @param string message type
    * @return array array
    *
    **/ 
    public function protobufToArray($proto, $input, $message_type){
        
        $desc = self::genDesc($proto); 
        //调用protobuf2arraybydesc（描述，$input,$message_type）
        if(!is_array($desc)){
            return false;
        }
        $ret_value = protobuf2arraybydesc($desc, $input, $message_type);
        
        //返回结果
        return $ret_value;
    }


   /**
    * @param string proto file name
    * @param array protobuf array
    * @param string message type
    * @return string protobuf string 
    *
    **/ 
    public function arrayToProtobuf($proto, $array, $message_type){
        
        $desc = self::genDesc($proto); 
        //调用array2protobufbydesc（描述，$array,$message_type）
        if(!is_array($desc)){
            return false;
        }
        $ret_value = array2protobufbydesc($desc, $array, $message_type);
        
        //返回结果
        return $ret_value; 
    }
    
    /**
     * @desc get proto filepath by protoName
     * @param unknown $proto
     * @return string|boolean
     */
    public function getProtoFile($proto , $appName = "")
    {
    	$filePath = self::$proto_dir . '/' . strval($appName) . '/' . strval($proto) . '.proto';
    	
    	if( (is_callable('bd_lazy_file_exists')
    			&& bd_lazy_file_exists($filePath)
    		) 
    		|| file_exists($filePath)
		){
    		return $filePath;
    	}
    	
    	return false ;
    }
    
    /**
     * @desc check if proto file change
     * @param unknown $proto
     * @return boolean
     */
    public function checkProtoNeedReload($proto , $appName = "")
    {
    	$filePath = $this->getProtoFile($proto , $appName);
    	if( false === $filePath ){
    		return false;
    	}
    	
    	if( false !== $this->getDesc($filePath)){
    		return false;
    	}
    	
    	$this->storeDesc($filePath, '', self::TIME_EXPIRE_DFT );
    	return true;
    }

   /**
    * @param string proto file name
    * @return string/bool protobuf desc or false
    *
    **/ 
    private static function genDesc($proto){
        
        //获取proto文件全路径
        $dir = self::$proto_dir;
        $file = $dir . "/" . $proto;

        if(!file_exists($file)){
            Bd_Log::warning("$file not exists!, please check it.");
            return false;
        
        }
        
        $desc = self::getDesc($file);
        if ($desc == false) {
            $desc = proto2desc($dir, $proto);
            if(!is_array($desc)) {
                Bd_Log::warning("gen desc from proto failed, please check $file,desc:".serialize($desc));
                return false;
            }
            
            $ret = self::storeDesc($file, $desc); 
            if(!$ret) {
                Bd_Log::warning("store desc failed");
            }
        }

        return $desc;
    }

   /**
    * @param string proto file name
    * @param array proto desc
    * @return bool true/false
    *
    **/ 
    private function storeDesc($key, $value , $expire = null){
        $file = self::_genKey($key);

        $stats = stat($key);
        $last_modify_time = $stats["mtime"];

        //$expire = $this->ttl;
        $expire = isset($expire) ? intval($expire) : Bd_Conf::getAppConf("/protobuf/ttl");
        if ($expire > 0) {
            $refresh_time = gettimeofday(true) + $expire;
        } else {
            $refresh_time = null;
            $expire = 0;
        }

        $value = array(
            'refresh_time' => $refresh_time,
            'last_modify_time' => $last_modify_time,
            'data' => $value,
        );

        if(self::$use_apc){
            $new_value = serialize($value);
            return apc_store(md5($file), $new_value, $expire);
        }

        $content = "<?php \n//key:$key\n return " . var_export($value,true) . ";\n \x3F>";
        $temp_file = $file . "_" . getmypid();
        $ret = file_put_contents($temp_file, $content);
        $ret = $ret && rename($temp_file, $file);
        return $ret;
    
    }

   /**
    * @param string proto file name
    * @return string desc or false
    *
    **/ 
    private function getDesc($key){
        $file = self::_genKey($key);

        if (!file_exists($file)) {
            return false;
        }

        //获取文件更新时间
        //获取本地缓存时间 和 依赖的proto 文件时间
        //对比2个时间戳，有更新重新调用proto2desc(路径，文件名), 并写入本地文件
        $stats = stat($key);
        $last_modify_time = $stats["mtime"];
        
        if(self::$use_apc){
            $value = apc_fetch(md5($file));
        }else{
            $value= include($file);
        }

        if ( is_array($value) && isset($value['data']) ) {
            if ( 
                //直接文件有更新
                ((isset($value['last_modify_time'])) && ( $value['last_modify_time'] != $last_modify_time))
                //cache超出有效期
                || (($value['refresh_time'] > 0) && ($value['refresh_time'] < gettimeofday(true)))
            ) {
                return false;
            }
            return $value['data'];
        }
        return false;
    }

    /**
     *
     * @param string file
     * @return string file path of local cache
     *
     **/
    private static function _genKey($file){
        $dir = self::$cache_dir;
        
        $md5 = md5($file);
        for($i = 0; $i < self::$depth; $i++) {
            $dir .= "/" . $md5[$i];     
        } 
    
        if (!file_exists($dir)) {
            $ret = mkdir($dir, 0755, true);
            if ($ret != true) {
                Bd_Log::warning("make dir[$dir] failed");
                return false;
            }
        }

        return $dir . '/' . $md5 . ".php";
    }
}




/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
