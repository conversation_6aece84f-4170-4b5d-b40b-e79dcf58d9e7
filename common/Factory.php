<?php
/**
 * @file Factory.php
 * <AUTHOR>
 * @date 2018-06-04
 * @brief 实例运营位
 **/
class Oplib_Common_Factory {
    
    public function instancePos($posId) {
        try {
            if (0 >= $posId) {
                throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::OP_POS_NOT_EXIST);
            }
            $opClass = "Oplib_Pos_{$posId}";
            if (!class_exists($opClass)) {
                throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::OP_POS_CLASS_NOT_EXIST);
            }
            $obj = new $opClass($posId);
            if ($obj instanceof Oplib_Common_BasePos) {
                return $obj;
            } else {
                throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::INSTANCE_FAILED);
            }
        } catch (Oplib_Common_Exception $e) {
            // 输出的地方做统一格式吧
            return false;
        }
    }
    //获取单条详情
    public function instancePosDetail($posId) {
        try {
            if (0 >= $posId) {
                throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::OP_POS_NOT_EXIST);
            }
            $opClass = "Oplib_Pos_{$posId}Detail";
            if (!class_exists($opClass)) {
                throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::OP_POS_CLASS_NOT_EXIST);
            }
            $obj = new $opClass($posId);
            if ($obj instanceof Oplib_Common_BasePos) {
                return $obj;
            } else {
                throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::INSTANCE_FAILED);
            }
        } catch (Oplib_Common_Exception $e) {
            // 输出的地方做统一格式吧
            return false;
        }
    }


    //获取批量详情
    public function instancePosDetailBatch($posId) {
        try {
            if (0 >= $posId) {
                throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::OP_POS_NOT_EXIST);
            }
            $opClass = "Oplib_Pos_{$posId}DetailBatch";
            if (!class_exists($opClass)) {
                throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::OP_POS_CLASS_NOT_EXIST);
            }
            $obj = new $opClass($posId);
            if ($obj instanceof Oplib_Common_BasePos) {
                return $obj;
            } else {
                throw new Oplib_Common_Exception(Oplib_Common_ExceptionCodes::INSTANCE_FAILED);
            }
        } catch (Oplib_Common_Exception $e) {
            // 输出的地方做统一格式吧
            return false;
        }
    }
}
