<?php
/**
 * Created by PhpStorm.
 * @file LeadsErrorCode.php
 * <AUTHOR>
 * @date 19-4-27
 * @version
 * @brief
 *
 **/


class Qdlib_Common_LeadsErrorCodes
{


    //clue(200000-209999)
    //线索成为例子参数过滤>100
    const  CLUE_OPERATION_PHONE_ERROR                      =  200101;  //手机号不正确  为空或格式不正确
    const  CLUE_OPERATION_GRADE_ERROR                      =  200102;  //年级参数错误
    const  CLUE_OPERATION_SUBJECT_ERROR                    =  200103;  //科目参数错误
    const  CLUE_OPERATION_APPLYTIME_ERROR                  =  200104;  //申请时间错误
    const  CLUE_OPERATION_LIMITOVER_ERROR                  =  200105;  //调用次数超出
    const  CLUE_OPERATION_NOUID_ERROR                      =  200106;  //学生ID为空
    const  CLUE_OPERATION_GRADERANG_ERROR                  =  200107;  //年级范围检查失败  过滤高中
    const  CLUE_OPERATION_HANCHUN_ERROR                    =  200108;  //寒春检查失败
    const  CLUE_OPERATION_NOCOURSEID_ERROR                 =  200109;  //没有课程ID
    const  CLUE_OPERATION_NOIMPORT_ERROR                   =  200110;  //非导入
    const  CLUE_OPERATION_NOSCUID_ERROR                    =  200111;  //没有传来SCUID
    const  CLUE_OPERATION_REDISLOCK_ERROR                  =  200112;  //没有获取到redis并发锁          //兼容  以后要删除
    const  CLUE_OPERATION_NOGRADEBATCH_ERROR               =  200113;  //参数没有年级也没有批次
    //经过查库验证  >200
    const  CLUE_OPERATION_BLACKLUSERS_ERROR                =  200201;  //系统黑名单用户过滤
    const  CLUE_OPERATION_BLACKCOURSE_ERROR                =  200202;  //课程黑名单过滤
    const  CLUE_OPERATION_BATCH_ERROR                      =  200203;  //批次不正确或未启动
    const  CLUE_OPERATION_NOCOURSE_ERROR                   =  200204;  //无课程信息
    const  CLUE_OPERATION_TRANSLEADS_ERROR                 =  200205;  //已转化
    const  CLUE_OPERATION_SCBUY_ERROR                      =  200206;  //SC购买
    const  CLUE_OPERATION_LONGCOURSE_ERROR                 =  200207;  //已购班课
    const  CLUE_OPERATION_NOLONGCOURSE_ERROR               =  200208;  //没有购买过购班课
    const  CLUE_OPERATION_NOPRICEBUY_ERROR                 =  200209;  //0元购买专题课过滤
    const  CLUE_OPERATION_NOEXPERIENCE_ERROR               =  200210;  //非体验课
    const  CLUE_OPERATION_DAYI_ERROR                       =  200211;  //答疑检查失败
    const  CLUE_OPERATION_TEACHERFOLLOW_ERROR              =  200212;  //不符合拓科范围：用户首节班课开课超过30天
    const  CLUE_OPERATION_USERNOENTER_ERROR                =  200213;  //此用户没有进入销售系统
    const  CLUE_OPERATION_OTHERSCFOLLOW_ERROR              =  200214;  //此用户目前在其他SC名下有未过期的例子
    const  CLUE_OPERATION_SCFOLLOW_ERROR                   =  200215;  //此用户在该SC名下有未过期的例子
    const  CLUE_OPERATION_LAXINMARKET_ERROR                =  200216;  //拉新运营
    const  CLUE_OPERATION_NOCOURSETOUPDATE_ERROR           =  200217;  //无课程信息不更新leads信息
    const  CLUE_OPERATION_ZHUANJIESHAOSCEXITS_ERROR        =  200218;  //用户是sc已有的例子，不符合转介绍规则
    const  CLUE_OPERATION_ZHUANJIESHAOTMKEXITS_ERROR       =  200219;  //用户存在tmk有效例子，不符合转介绍规则
    const  CLUE_OPERATION_ZHUANTIKE_NOPRICEBUY_ERROR       =  200220;  //0元购买体验课过滤
    const  CLUE_OPERATION_NOTIYANKE_ERROR                  =  200221;  //没有购买过体验课
    const  CLUE_OPERATION_BUYBLACKCOURSE_ERROR             =  200222;  //用户购课记录中存在被屏蔽的课程
    const  CLUE_OPERATION_TUOKEISLEADS_ERROR               =  200223;  //该用户存在例子有效期内例子，有效期结束后可进行激活
    const  CLUE_OPERATION_ISINNER_COURSE_ERROR             =  200224;  //课程为内部课程
    const  CLUE_AIED_ERROR                                 =  200225;  //先知系统导入返回不成功

    //ral或数据库异常问题>300
    const  CLUE_OPERATION_GETPHONE_DB_ERROR                =  200301;  //获取手机号数据库异常
    const  CLUE_OPERATION_COURSE_DB_ERROR                  =  200302;  //获取课程详情数据库异常
    const  CLUE_OPERATION_USER_DB_ERROR                    =  200303;  //获取用户UID数据库异常
    const CLUE_OPERATION_BATCH_DB_ERROR         = 200304; //获取批次数据库异常
    const CLUE_OPERATION_STRATEGY_DB_ERROR      = 200305; //获取策略数据库异常
    const CLUE_OPERATION_LEADSINFO_DB_ERROR     = 200306; //查询leads信息数据库异常
    const CLUE_OPERATION_UPDATELEADS_DB_ERROR   = 200307; //更新leads信息失败
    const CLUE_OPERATION_CREATELEADS_ERROR      = 200308; //创建例子失败
    const CLUE_OPERATION_GETCUSTOM_DB_ERROR     = 200309; //获取CUSTOM表异常
    const CLUE_OPERATION_TRADE_DB_ERROR         = 200310; //获取订单表异常
    const CLUE_OPERATION_REDISLOCK_DB_ERROR     = 200311; //没有获取到redis并发锁
    const CLUE_OPERATION_LONGCOURSE_DB_ERROR    = 200312; //获取长班课数据库异常
    const CLUE_OPERATION_LESSONINFO_DB_ERROR    = 200313; //获取lessonName数据库异常
    const CLUE_OPERATION_UNFINISHED_DB_ERROR    = 200314; //获取未完成课程数据库异常
    //成功 >400
    const CLUE_OPERATION_UPDATELEADS_SUCESS     = 200401; //更新例子有效期成功
    const CLUE_OPERATION_CREATELEADS_SUCESS     = 200402; //创建例子成功
    //线索成为例子参数过滤>100
    const LOG_OPERATION_PHONE_ERROR = 101; //手机号不正确 为空或格式不正确
    const LOG_OPERATION_GRADE_ERROR = 102; //年级参数错误
    const LOG_OPERATION_SUBJECT_ERROR = 103; //科目参数错误
    const LOG_OPERATION_APPLYTIME_ERROR = 104; //申请时间错误
    const LOG_OPERATION_LIMITOVER_ERROR = 105; //调用次数超出
    const LOG_OPERATION_NOUID_ERROR = 106; //学生ID为空
    const LOG_OPERATION_GRADERANG_ERROR = 107; //年级范围检查失败 过滤高中
    const LOG_OPERATION_HANCHUN_ERROR = 108; //寒春检查失败
    const LOG_OPERATION_NOCOURSEID_ERROR = 109; //没有课程ID
    const LOG_OPERATION_NOIMPORT_ERROR = 110; //非导入
    const LOG_OPERATION_NOSCUID_ERROR = 111; //没有传来SCUID
    const LOG_OPERATION_REDISLOCK_ERROR = 112; //没有获取到redis并发锁     //兼容 以后要删除
    const LOG_OPERATION_NOGRADEBATCH_ERROR = 113; //参数没有年级也没有批次

    //经过查库验证 >200
    const LOG_OPERATION_BLACKLUSERS_ERROR = 201; //系统黑名单用户过滤
    const LOG_OPERATION_BLACKCOURSE_ERROR = 202; //课程黑名单过滤
    const LOG_OPERATION_BATCH_ERROR = 203; //批次不正确或未启动
    const LOG_OPERATION_NOCOURSE_ERROR = 204; //无课程信息
    const LOG_OPERATION_TRANSLEADS_ERROR = 205; //已转化
    const LOG_OPERATION_SCBUY_ERROR = 206; //SC购买
    const LOG_OPERATION_LONGCOURSE_ERROR = 207; //已购班课
    const LOG_OPERATION_NOLONGCOURSE_ERROR = 208; //没有购买过购班课
    const LOG_OPERATION_NOPRICEBUY_ERROR = 209; //0元购买专题课过滤
    const LOG_OPERATION_NOEXPERIENCE_ERROR = 210; //非体验课
    const LOG_OPERATION_DAYI_ERROR = 211; //答疑检查失败
    const LOG_OPERATION_TEACHERFOLLOW_ERROR = 212; //不符合拓科范围：用户首节班课开课超过30天
    const LOG_OPERATION_USERNOENTER_ERROR = 213; //此用户没有进入销售系统
    const LOG_OPERATION_OTHERSCFOLLOW_ERROR = 214; //此用户目前在其他SC名下有未过期的例子
    const LOG_OPERATION_SCFOLLOW_ERROR = 215; //此用户在该SC名下有未过期的例子
    const LOG_OPERATION_LAXINMARKET_ERROR = 216; //拉新运营
    const LOG_OPERATION_NOCOURSETOUPDATE_ERROR = 217; //无课程信息不更新leads信息
    const LOG_OPERATION_ZHUANJIESHAOSCEXITS_ERROR = 218; //用户是sc已有的例子，不符合转介绍规则
    const LOG_OPERATION_ZHUANJIESHAOTMKEXITS_ERROR = 219; //用户存在tmk有效例子，不符合转介绍规则
    const LOG_OPERATION_ZHUANTIKE_NOPRICEBUY_ERROR = 220; //0元购买体验课过滤
    const LOG_OPERATION_NOTIYANKE_ERROR = 221; //没有购买过体验课
    const LOG_OPERATION_BUYBLACKCOURSE_ERROR = 222; //用户购课记录中存在被屏蔽的课程
    const LOG_OPERATION_TUOKEISLEADS_ERROR = 223; //该用户存在例子有效期内例子，有效期结束后可进行激活
    const LOG_QUDAO_UP_PRIVATE_LONG    = 297;//历史班课用户
    const LOG_SHORT_TRAIN_IS_BUY_TODAY_ERROR = 298; //当天购买后短训班
    const LOG_SHORT_TRAIN_IS_BUY_NO_REQUEST_DX_ERROR = 299; //用户60天内购买过短训班 不导给电销

    //ral或数据库异常问题>300
    const LOG_OPERATION_GETPHONE_DB_ERROR = 301; //获取手机号数据库异常
    const LOG_OPERATION_COURSE_DB_ERROR = 302; //获取课程详情数据库异常
    const LOG_OPERATION_USER_DB_ERROR = 303; //获取用户UID数据库异常
    const LOG_OPERATION_BATCH_DB_ERROR = 304; //获取批次数据库异常
    const LOG_OPERATION_STRATEGY_DB_ERROR = 305; //获取策略数据库异常
    const LOG_OPERATION_LEADSINFO_DB_ERROR = 306; //查询leads信息数据库异常
    const LOG_OPERATION_UPDATELEADS_DB_ERROR = 307; //更新leads信息失败
    const LOG_OPERATION_CREATELEADS_ERROR = 308; //创建例子失败
    const LOG_OPERATION_GETCUSTOM_DB_ERROR = 309; //获取CUSTOM表异常
    const LOG_OPERATION_TRADE_DB_ERROR = 310; //获取订单表异常
    const LOG_OPERATION_REDISLOCK_DB_ERROR = 311; //没有购买过体验课
    const LOG_OPERATION_LONGCOURSE_DB_ERROR = 312; //获取长班课数据库异常
    const LOG_OPERATION_LESSONINFO_DB_ERROR = 313; //获取lessonName数据库异常
    const LOG_OPERATION_UNFINISHED_DB_ERROR = 314; //获取未完成课程数据库异常

    const LOG_SHORT_TRAIN_IS_BUY_REQUEST_ERROR = 399; //通过uid判断用户是否购买过短训班异常

    //成功 >400
    const LOG_OPERATION_UPDATELEADS_SUCESS = 401; //更新例子有效期成功
    const LOG_OPERATION_CREATELEADS_SUCESS = 402; //创建例子成功


    //原因映射
    static $LEADS_ERROR_MAP = [
        self::LOG_OPERATION_PHONE_ERROR => '手机号为空或格式不正确',
        self::LOG_OPERATION_GRADE_ERROR => '年级填写错误',
        self::LOG_OPERATION_SUBJECT_ERROR => '科目填写错误',
        self::LOG_OPERATION_APPLYTIME_ERROR => '申请时间格式错误',
        self::LOG_OPERATION_LIMITOVER_ERROR => '调用次数超出',
        self::LOG_OPERATION_NOUID_ERROR => '学生UID为空',
        self::LOG_OPERATION_GRADERANG_ERROR => '不符合系统已有年级限制',
        self::LOG_OPERATION_HANCHUN_ERROR => '不符合系统年级学科限制',
        self::LOG_OPERATION_NOCOURSEID_ERROR => '缺少课程ID',
        self::LOG_OPERATION_NOIMPORT_ERROR => '非导入批次',
        self::LOG_OPERATION_NOSCUID_ERROR => '缺少SCuid',
        self::LOG_OPERATION_REDISLOCK_ERROR => '没有获取到redis并发锁', //兼容 以后要删除
        self::LOG_OPERATION_NOGRADEBATCH_ERROR => '缺少年级及批次',
        self::LOG_OPERATION_BLACKLUSERS_ERROR => '系统黑名单用户过滤',
        self::LOG_OPERATION_BLACKCOURSE_ERROR => '课程黑名单过滤',
        self::LOG_OPERATION_BATCH_ERROR => '批次不正确或未启动',
        self::LOG_OPERATION_NOCOURSE_ERROR => '无此课程信息',
        self::LOG_OPERATION_TRANSLEADS_ERROR => '该用户已转化',
        self::LOG_OPERATION_SCBUY_ERROR => '此购买为SC购买',
        self::LOG_OPERATION_LONGCOURSE_ERROR => '该用户已购班课',
        self::LOG_OPERATION_NOLONGCOURSE_ERROR => '该用户没有购买过购班课',
        self::LOG_OPERATION_NOTIYANKE_ERROR => '该用户没有购买过体验课',
        self::LOG_OPERATION_NOPRICEBUY_ERROR => '0元购买专题课过滤',
        self::LOG_OPERATION_ZHUANTIKE_NOPRICEBUY_ERROR => '0元购买体验课过滤',
        self::LOG_OPERATION_NOEXPERIENCE_ERROR => '此课程类型非体验课',
        self::LOG_OPERATION_DAYI_ERROR => '该用户已购答疑',
        self::LOG_OPERATION_TEACHERFOLLOW_ERROR => '不符合拓科范围：用户首节班课开课超过30天',
        self::LOG_OPERATION_TUOKEISLEADS_ERROR => '该用户存在例子有效期内例子，有效期结束后可进行激活',
        self::LOG_OPERATION_USERNOENTER_ERROR => '该用户没有进入销售系统',
        self::LOG_OPERATION_OTHERSCFOLLOW_ERROR => '该用户目前在其他SC名下有未过期的例子',
        self::LOG_OPERATION_SCFOLLOW_ERROR => '该用户在此SC名下有未过期的例子',
        self::LOG_OPERATION_LAXINMARKET_ERROR => '用户为拉新运营,不进入系统',
        self::LOG_OPERATION_NOCOURSETOUPDATE_ERROR => '存在有效期Leads且不更新leads有效期',
        self::LOG_OPERATION_ZHUANJIESHAOSCEXITS_ERROR => '用户是sc已有的例子，不符合转介绍规则',
        self::LOG_OPERATION_ZHUANJIESHAOTMKEXITS_ERROR => '用户存在tmk有效例子，不符合转介绍规则',
        self::LOG_OPERATION_GETPHONE_DB_ERROR => '获取手机号数据库异常',
        self::LOG_OPERATION_COURSE_DB_ERROR => '获取课程详情数据库异常',
        self::LOG_OPERATION_TRADE_DB_ERROR => '获取订单详情数据库异常',
        self::LOG_OPERATION_REDISLOCK_DB_ERROR => '没有获取到redis并发锁',
        self::LOG_OPERATION_USER_DB_ERROR => '获取用户UID数据库异常',
        self::LOG_OPERATION_BATCH_DB_ERROR => '获取批次数据库异常',
        self::LOG_OPERATION_STRATEGY_DB_ERROR => '获取策略数据库异常',
        self::LOG_OPERATION_LEADSINFO_DB_ERROR => '查询leads信息数据库异常',
        self::LOG_OPERATION_UPDATELEADS_DB_ERROR => '更新leads信息失败',
        self::LOG_OPERATION_CREATELEADS_ERROR => '创建例子失败',
        self::LOG_OPERATION_GETCUSTOM_DB_ERROR => '获取CUSTOM表异常',
        self::LOG_OPERATION_LONGCOURSE_DB_ERROR => '获取长班课数据库异常',
        self::LOG_OPERATION_LESSONINFO_DB_ERROR => '获取lessonName数据库异常',
        self::LOG_OPERATION_UNFINISHED_DB_ERROR => '获取未完成课程数据库异常',
        self::LOG_OPERATION_UPDATELEADS_SUCESS => '更新例子有效期成功',
        self::LOG_OPERATION_CREATELEADS_SUCESS => '创建例子成功',
        self::LOG_OPERATION_BUYBLACKCOURSE_ERROR => '用户购课记录中存在被屏蔽的课程',
        //新电销错误码
        self::CLUE_OPERATION_PHONE_ERROR => '手机号为空或格式不正确',
        self::CLUE_OPERATION_GRADE_ERROR => '年级填写错误',
        self::CLUE_OPERATION_SUBJECT_ERROR => '科目填写错误',
        self::CLUE_OPERATION_APPLYTIME_ERROR => '申请时间格式错误',
        self::CLUE_OPERATION_LIMITOVER_ERROR => '调用次数超出',
        self::CLUE_OPERATION_NOUID_ERROR => '学生UID为空',
        self::CLUE_OPERATION_GRADERANG_ERROR => '不符合系统已有年级限制',
        self::CLUE_OPERATION_HANCHUN_ERROR => '不符合系统年级学科限制',
        self::CLUE_OPERATION_NOCOURSEID_ERROR => '缺少课程ID',
        self::CLUE_OPERATION_NOIMPORT_ERROR => '非导入批次',
        self::CLUE_OPERATION_NOSCUID_ERROR => '缺少SCuid',
        self::CLUE_OPERATION_REDISLOCK_ERROR => '没有获取到redis并发锁', //兼容 以后要删除
        self::CLUE_OPERATION_NOGRADEBATCH_ERROR => '缺少年级及批次',
        self::CLUE_OPERATION_BLACKLUSERS_ERROR => '系统黑名单用户过滤',
        self::CLUE_OPERATION_BLACKCOURSE_ERROR => '课程黑名单过滤',
        self::CLUE_OPERATION_BATCH_ERROR => '批次不正确或未启动',
        self::CLUE_OPERATION_NOCOURSE_ERROR => '无此课程信息',
        self::CLUE_OPERATION_TRANSLEADS_ERROR => '该用户已转化',
        self::CLUE_OPERATION_SCBUY_ERROR => '此购买为SC购买',
        self::CLUE_OPERATION_LONGCOURSE_ERROR => '该用户已购班课',
        self::CLUE_OPERATION_NOLONGCOURSE_ERROR => '该用户没有购买过购班课',
        self::CLUE_OPERATION_NOTIYANKE_ERROR => '该用户没有购买过体验课',
        self::CLUE_OPERATION_NOPRICEBUY_ERROR => '0元购买专题课过滤',
        self::CLUE_OPERATION_ZHUANTIKE_NOPRICEBUY_ERROR => '0元购买体验课过滤',
        self::CLUE_OPERATION_NOEXPERIENCE_ERROR => '此课程类型非体验课',
        self::CLUE_OPERATION_DAYI_ERROR => '该用户已购答疑',
        self::CLUE_OPERATION_TEACHERFOLLOW_ERROR => '不符合拓科范围：用户首节班课开课超过30天',
        self::CLUE_OPERATION_TUOKEISLEADS_ERROR => '该用户存在例子有效期内例子，有效期结束后可进行激活',
        self::CLUE_OPERATION_USERNOENTER_ERROR => '该用户没有进入销售系统',
        self::CLUE_OPERATION_OTHERSCFOLLOW_ERROR => '该用户目前在其他SC名下有未过期的例子',
        self::CLUE_OPERATION_SCFOLLOW_ERROR => '该用户在此SC名下有未过期的例子',
        self::CLUE_OPERATION_LAXINMARKET_ERROR => '用户为拉新运营,不进入系统',
        self::CLUE_OPERATION_NOCOURSETOUPDATE_ERROR => '存在有效期Leads且不更新leads有效期',
        self::CLUE_OPERATION_ZHUANJIESHAOSCEXITS_ERROR => '用户是sc已有的例子，不符合转介绍规则',
        self::CLUE_OPERATION_ZHUANJIESHAOTMKEXITS_ERROR => '用户存在tmk有效例子，不符合转介绍规则',
        self::CLUE_OPERATION_GETPHONE_DB_ERROR => '获取手机号数据库异常',
        self::CLUE_OPERATION_COURSE_DB_ERROR => '获取课程详情数据库异常',
        self::CLUE_OPERATION_TRADE_DB_ERROR => '获取订单详情数据库异常',
        self::CLUE_OPERATION_REDISLOCK_DB_ERROR => '没有获取到redis并发锁',
        self::CLUE_OPERATION_USER_DB_ERROR => '获取用户UID数据库异常',
        self::CLUE_OPERATION_BATCH_DB_ERROR => '获取批次数据库异常',
        self::CLUE_OPERATION_STRATEGY_DB_ERROR => '获取策略数据库异常',
        self::CLUE_OPERATION_LEADSINFO_DB_ERROR => '查询leads信息数据库异常',
        self::CLUE_OPERATION_UPDATELEADS_DB_ERROR => '更新leads信息失败',
        self::CLUE_OPERATION_CREATELEADS_ERROR => '创建例子失败',
        self::CLUE_OPERATION_GETCUSTOM_DB_ERROR => '获取CUSTOM表异常',
        self::CLUE_OPERATION_LONGCOURSE_DB_ERROR => '获取长班课数据库异常',
        self::CLUE_OPERATION_LESSONINFO_DB_ERROR => '获取lessonName数据库异常',
        self::CLUE_OPERATION_UNFINISHED_DB_ERROR => '获取未完成课程数据库异常',
        self::CLUE_OPERATION_UPDATELEADS_SUCESS => '更新例子有效期成功',
        self::CLUE_OPERATION_CREATELEADS_SUCESS => '创建例子成功',
        self::CLUE_OPERATION_BUYBLACKCOURSE_ERROR => '用户购课记录中存在被屏蔽的课程',

        //历史班课用户
        self::LOG_QUDAO_UP_PRIVATE_LONG  => '判断用户是否是班课用户',
        //短训班
        self::LOG_SHORT_TRAIN_IS_BUY_TODAY_ERROR => '用户当日购买短训班',
        self::LOG_SHORT_TRAIN_IS_BUY_NO_REQUEST_DX_ERROR => '用户60天内购买过短训班',
        self::LOG_SHORT_TRAIN_IS_BUY_REQUEST_ERROR => '判断用户是否购买短训班异常',
    ];
}