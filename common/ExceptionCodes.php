<?php
/**
 * @file ExceptionCode.php
 * <AUTHOR>
 * @date 2018-06-04
 * @brief 异常错误号
 **/
class Oplib_Common_ExceptionCodes {

    //===0-999 通用错误===
    const SUCC                        = 0; //成功
    const PARAM_ERROR                 = 1; //参数错误
    const DB_ERROR                    = 2; //数据操作错误
    const INSTANCE_FAILED             = 3; //实例化失败
    const USER_NOT_LOGIN              = 4; //用户未登陆
    const PIC_FILE_NOT_EXIST          = 5; //图片-上传文件不存在
    const PIC_UPLOAD_INVALID_SIZE     = 6; //图片-大小超限
    const PIC_UPLOAD_INVALID_TYPE     = 7; //图片-不支持该格式
    const PIC_UPLOAD_FAIL             = 8; //图片-上传失败
    const PIC_FILE_NOT_OK             = 9; //图片-上传文件未就绪
    const STATUS_ERROR                = 10; //状态异常
    const TIME_ERROR                  = 11; //时间异常
    const CACHE_ERROR                 = 12; //缓存错误
    const NETWORK_ERROR               = 13; //网络错误
    const QPS_MAX                     = 14; //限流
    const RPC_CALL_ERROR              = 15; //RPC调用出错
    const FILE_EXCEEDS_MAX_VALUE      = 16; //上传文件大小超过最大值
    const FILE_ONLY_PORTION           = 17; //文件只有部分被上传
    const FILE_NOT_UPLOADED           = 18; //没有文件被上传
    const FILE_TEMP_DIR_NOT_FOUND     = 19; //找不到临时文件夹
    const FILE_WRITE_ERROR            = 20; //文件写入失败
    const FILE_UNKNOWN_UPLOAD_ERROR   = 21; //未知的上传错误
    const FILE_EXCEEDS_SPECIFIED_SIZE = 22; //文件超过指定大小
    const FILE_EXTENSION_NOT_ALLOWED  = 23; //未允许的文件扩展名
    const FILE_ILLEGAL_UPLOADING      = 24; //非法上传文件
    const DATA_MATCHING_ERROR         = 25; //数据匹配错误

    //===1000-1999 运营位错误===
    const OP_POS_NOT_EXIST          = 1000; // 运营位不存在
    const OP_POS_CLASS_NOT_EXIST    = 1001; // 运营位文件不存在
    const OP_POS_CONFLICT           = 1002; // 运营位冲突
    const OP_POS_AD_NOT_EXIST       = 1003; // 推荐位不存在

    //===2000-2999 蜂鸟错误===
    const BIRD_NO_PRIVILEGE         = 2003; //没有权限才做

    const BIRD_RESUBMIT_USER        = 2502; //该表单已经提交
    const BIRD_RESUBMIT_PHONE       = 2503; //手机号已经提交过了

    //===3000-3999 sku错误===
    const SKU_NOT_EXIST           = 3001; //sku不存在
    const SKU_NOT_ONLINE          = 3002; //sku未上线
    const SKU_EXCEED_CNT          = 3003; //超过最大报名人数
    const STUDENT_CHECK_ERROR     = 3004; //学生检查失败
    const SKU_STRATEGY_FAILED     = 3005; //sku策略获取失败
    const SKU_NOT_CAN_BUY         = 3006; //用户无法购买
    const SKU_BUY_FAILD           = 3007; //用户购买失败
    const COURSE_NOT_EXIST        = 3008; //课程不存在
    const LESSON_NOT_EXIST        = 3009; //章节不存在
    const QID_MAX_ERROR           = 3010; //超过手动输入最大报名人数
    const SKU_MAX_ERROR           = 3011; //超过sku最大输入数
    const SKU_SEPARATE_ERROR      = 3012; //sku输入或分隔符错误

    //== 4000-4999 qudao错误
    const SAME_SIGN                 = 4001; //该投放计划中数量重复请确认相同投放计划不可重复使用一个数字
    const SAME_ACCOUNT              = 4002; //投放计划已有其他账号使用，请确认投放计划后重试
    const INVALID_IS                = 4003; //未知的is_valid
    const GET_AUTH_ERROR            = 4004; //授权失败
    const SAME_PHONE                = 4005; //输入相同phone
    const CUID_EXIST                = 4006; //该设备已领取

    //== 5000-5999 转介绍
    const INTRO_BILL_ILLEGAL          = 5000; // 非法海报
    const INTRO_TO_PHONE_EMPTY        = 5001; // 被介绍人电话空
    const INTRO_TO_PHONE_EXIST        = 5002; // 此号码已被推荐
    const INTRO_TO_PHONE_ILLEGAL      = 5003; // 此号码非新用户
    const INTRO_PHONE_FORMAT_ERROR    = 5004; //请填写正确的手机号
    const INTRO_SEND_SMS_TOKEN_ERROR  = 5005; //验证码发送失败
    const INTRO_SMS_TOKEN_CHECK_ERROR = 5006; //验证码校验失败
    const INTRO_NAME_EMPTY            = 5007; //请输入姓名
    const INTRO_GRADE_ID_ERROR        = 5008; //请选择年级
    const INTRO_GET_UID_ERROR         = 5009; //获取uid失败，请刷新页面重试
    const INTRO_SHARE_UID_ERROR       = 5010; //邀请人信息不存在
    const INTRO_BANKE_USER_ERROR      = 5011; //不符合活动规则，已购买过班课
    const INTRO_ENTRANCE_DX_ERROR     = 5012; //进入电销系统失败
    const INTRO_PHONE_HAS_INVITED     = 5013; //不符合活动规则，已被邀请过
    const INTRO_FREQUENCY_LOCK        = 5014; //操作太频繁
    const INTRO_ACTIVITY_NO_EFFECT    = 5015; //活动已失效
    const INTRO_ACTIVITY_TYPE_ERROR   = 5016; //活动类型错误
    const INTRO_NO_PRIVILEGE          = 5017; //没有权限
    const INTRO_GET_SEND_MONEY_ERROR  = 5018; //获取返现金额错误
    const INTRO_AUDIT_TYEP_ERROR      = 5019; //审核类型错误
    const INTRO_AUDIT_REFUSE_REASON_EMPTY = 5020; //请填写拒绝原因
    const INTRO_AUDIT_ERROR               = 5021; //审核失败
    const INTRO_SEND_MONEY_LIST_EMPTY     = 5022; //请选择返现用户
    const INTRO_SEND_CNT_LIMIT            = 5023; //一次性返现人数限制
    const INTRO_OVER_BUDGET               = 5024; //超过活动总预算
    const INTRO_SEARCH_SEND_MONEY_ERROR   = 5025; //查询返现明细失败
    const INTRO_SEND_TOTAL_MONEY_ERROR    = 5026; //活动发放总金额校验失败

    //== 6000-6999 运营平台 start

    const SKYFIRE_DISPATCH_FAILD   = 6001; // 分发参数错误
    const SKYFIRE_DISPATCH_OBJNAME = 6002; // 分发类名不存在
    const SKYFIRE_DISPATCH_METHOD  = 6003; // 分发方法不存在
    //== 6000-6999 运营平台 end

	//== 7000-7999 coupon start
	const UNVALID_CODENAME                      = 7000;
	const UNVALID_UID                           = 7002;
	const UNVALID_ITEMID                        = 7003;
	const HAS_EXCHANGE                          = 7004;
	const COUPON_STOCK_INSUFFICIENT             = 7005;
	const COUPON_CODE_INSUFFICIENT              = 7006;
	const HAS_SEND_COUPON                       = 7007;
	const OVER_REQUEST                          = 7008;
    const COUPON_GET_FAILURE                    = 7009; // 优惠券领取失败
    const COUPON_MATCHING_FAILURE               = 7010; // 优惠券匹配失败
	//== 7000-7999 coupon end

    // 特惠课 8100 -8200
    const TEHUIKE_STATUS_ERROR                  = 8100;
    const TEHUIKE_FLOW_LIMIT                    = 8110;

    // QQ加群  8200
    const QQGROUP_EMPTY_SMS                     = 8210;
    const QQGROUP_EMPTY_GROUPNUM                = 8211;
    const QQGROUP_EMPTY_UPLIMIT                 = 8212;
    const QQGROUP_EMPTY_IOSKEY                  = 8213;
    const QQGROUP_EMPTY_ANDROIDKEY              = 8214;
    const QQGROUP_EMPTY_GROUPNAME               = 8215;
    const QQGROUP_EMPTY_GROUPLINK               = 8216;
    const QQGROUP_BEYOND_MAXNUM                 = 8217;

    //新手礼包
    const NEWBGIFTBAG_JOIN_ONE        = 8310;//活动仅限参与一次
    const NEWBGIFTBAG_JOIN_NEW        = 8311;//您不是新用户，不能参与本次活动哦
    const NEWBGIFTBAG_GET_FAIL        = 8312;//领取失败，请稍后再试
    const NEWBGIFTBAG_HAD_GOT         = 8313;//您已领取过该课程
    const NEWBGIFTBAG_NO_VALID        = 8314;//获取有效课程失败
    const NEWBGIFTBAG_NOT_PRE_LONG    = 8315;//课程信息错误

    // 分销 8400
    const FENXIAO_NOT_ZHAOMU         = 8401;    // 来晚了,已无法加入
    const FENXIAO_ACT_OUTDATE        = 8402;    // 来晚了,活动已结束

    //== 8000-8999 课程相关
    const PLAYBACK_NOT_GENERATE     = 8000; // 视频回放未生成
    const GPUID_NOT_EXIST           = 8001; // gpuid不存在
    const COURSE_INFO_NOT_EXIST     = 8002; // 课程信息不存在
    const NOT_AUTH_TO_SEE           = 8003; // 没有权限观看视频
    const LESSON_ALREADY_EXIST      = 8004; // 章节信息已经存在

    //==9000-9999 ol相关
    const USER_NO_THIS_COURSE       = 9000; //用户未购买该课程

    //==10000-19999 蜂鸟相关
    const HB_ACT_NOSTART       = 10009; //活动还没有开始
    const HB_ACT_OFFLINE       = 10010; //活动已经下线

    //==12000-12999 老师说相关
    const  REACH_STAN            = 12000;  //老师发布动态上线

    // 兑换码相关
    // 发放时，兑换码系统校验逻辑 9020101 - 9020200
    const GET_CDKEY_STOCK_LACK = 9020101; // 兑换码数量不足，发放失败

    // 兑换码核销校验逻辑 9020201 - 9020300
    const EXCHANGE_CDKEY_NOT_EXIST  = 9020201; // 兑换码不存在，请输入正确的兑换码
    const EXCHANGE_CDKEY_DOING      = 9020202; // 兑换码正在兑换中，请等待
    const EXCHANGE_CDKEY_DONE       = 9020203; // 兑换码已被兑换，不可重复使用
    const EXCHANGE_CDKEY_REFUND     = 9020204; // 兑换码已被退款，不可兑换
    const EXCHANGE_CDKEY_CANCEL     = 9020205; // 兑换码已被作废，不可兑换
    const EXCHANGE_CDKEY_UNSOLD     = 9020206; // 兑换码未出售，不可兑换
    const EXCHANGE_CDKEY_INVALID    = 9020207; // 兑换码已失效，请联系第三方平台退款
    const EXCHANGE_CDKEY_UNENFORCED = 9020208; // 未到达生效时间，兑换码还未生效，不可兑换
    const EXCHANGE_CDKEY_SUCC       = 9020209; // 兑换码兑换成功
    const EXCHANGE_CDKEY_FAIL       = 9020210; // 兑换码兑换失败

    // 兑换码退款校验逻辑 9020301 - 9020400
    const REFUND_CDKEY_UNSOLD     = 9020301; // 兑换码未出售，不可退款
    const REFUND_CDKEY_EXCHANGING = 9020302; // 正在兑换中，不可退款，请联系作业帮
    const REFUND_CDKEY_EXCHANGED  = 9020303; // 兑换码已被兑换，不可退款
    const REFUND_CDKEY_REFUND     = 9020304; // 兑换码已退款，不可重复操作
    const REFUND_CDKEY_CANCEL     = 9020305; // 兑换码已作废，不可退款

    // 兑换码服务异常 9020401 - 9020500
    const CDKEYSERVE_CDKEY_NOT_EXIST         = 9020401; // 兑换码不存在
    const CDKEYSERVE_CDKEYSERIES_NOT_EFFECT  = 9020402; // 兑换码批次无效
    const CDKEYSERVE_CDKEY_SKU_ERROR         = 9020403; // 兑换码兑换商品ID错误
    const CDKEYSERVE_CDKEY_APPLY_DUP         = 9020404; // 重复申请兑换码
    const CDKEYSERVE_CDKEY_APPLY_ERROR       = 9020405; // 申请兑换码异常
    const CDKEYSERVE_CDKEY_CHANGE_ERROR      = 9020406; // 修改兑换码状态异常
    const CDKEY_THIRD_PARTY_EXCEPTION        = 9020407; // 第三方调用异常
    const CDKEY_STOCK_UNLOCK_FAIL            = 9020408; // 库存解锁失败
    const CDKEY_CHANNEL_MATCH_FAIL           = 9020409; // 渠道匹配失败
    const CDKEY_EXCHANGE_STATUS_ERROR        = 9020410; // 兑换码核销状态错误
    const CDKEY_COUNT_LACK                   = 9020411; // 兑换码数量不足
    const SERIES_CDKEY_UNENFORCED            = 9020412; // 该批次兑换码未生效
    const SERIES_CDKEY_CANCEL                = 9020413; // 该批次兑换码已停用
    const SERIES_CDKEY_INVALID               = 9020414; // 该批次兑换码已过期
    const SERIES_CDKEY_ERROR                 = 9020415; // 该批次兑换码异常
    const CDKEY_STOCK_LOCK_FAIL              = 9020416; // 库存锁定失败


    public static $errMsg = array(
        //===0-999 通用错误===
        self::SUCC                        => '成功',
        self::PARAM_ERROR                 => '参数错误',
        self::DB_ERROR                    => '数据操作错误',
        self::INSTANCE_FAILED             => '实例化失败',
        self::USER_NOT_LOGIN              => '用户未登陆',
        self::PIC_FILE_NOT_EXIST          => '图片-上传文件不存在',
        self::PIC_UPLOAD_INVALID_SIZE     => '图片-大小超限',
        self::PIC_UPLOAD_INVALID_TYPE     => '图片-不支持该格式',
        self::PIC_UPLOAD_FAIL             => '图片-上传失败',
        self::PIC_FILE_NOT_OK             => '图片-上传文件未就绪',
        self::STATUS_ERROR                => '状态异常',
        self::TIME_ERROR                  => '时间异常',
        self::CACHE_ERROR                 => '缓存错误',
        self::NETWORK_ERROR               => '网络错误',
        self::QPS_MAX                     => '活动太火爆，请稍候重试',
        self::RPC_CALL_ERROR              => 'RPC调用出错',
        self::FILE_EXCEEDS_MAX_VALUE      => '上传文件大小超过最大值',
        self::FILE_ONLY_PORTION           => '文件只有部分被上传',
        self::FILE_NOT_UPLOADED           => '没有文件被上传',
        self::FILE_TEMP_DIR_NOT_FOUND     => '找不到临时文件夹',
        self::FILE_WRITE_ERROR            => '文件写入失败',
        self::FILE_UNKNOWN_UPLOAD_ERROR   => '未知的上传错误',
        self::FILE_EXCEEDS_SPECIFIED_SIZE => '文件超过指定大小',
        self::FILE_EXTENSION_NOT_ALLOWED  => '未允许的文件扩展名',
        self::FILE_ILLEGAL_UPLOADING      => '非法上传文件',
        self::DATA_MATCHING_ERROR         => '数据匹配错误',

        //===1000-1999 运营位错误===
        self::OP_POS_NOT_EXIST          => '运营位不存在',
        self::OP_POS_CLASS_NOT_EXIST    => '运营位文件不存在',
        self::OP_POS_CONFLICT           => '运营位冲突',
        self::OP_POS_AD_NOT_EXIST       => '推荐位不存在',

        //===2000-2999  蜂鸟权限错误
        self::BIRD_NO_PRIVILEGE         => '没有权限',
        self::BIRD_RESUBMIT_USER        => '表单已经提交过了',
        self::BIRD_RESUBMIT_PHONE       => '手机号已经提交过了',

        //===3000-3999 sku错误===
        self::SKU_NOT_EXIST           => 'sku不存在',
        self::SKU_NOT_ONLINE          => 'sku未上线',
        self::SKU_EXCEED_CNT          => '超过最大报名人数',
        self::STUDENT_CHECK_ERROR     => '学生检查失败',
        self::SKU_STRATEGY_FAILED     => 'sku策略获取失败',
        self::SKU_NOT_CAN_BUY         => '用户无法购买',
        self::SKU_BUY_FAILD           => '用户购买失败',
        self::COURSE_NOT_EXIST        => '课程不存在',
        self::LESSON_NOT_EXIST        => '章节不存在',
        self::QID_MAX_ERROR           => '最多不超过100个',
        self::SKU_MAX_ERROR           => '最多只能输入50个skuid',
        self::SKU_SEPARATE_ERROR      => '分隔符必须为英文分号',

        //===4000-4999 qudao错误
        self::SAME_SIGN                 => '该投放计划中数量重复请确认相同投放计划不可重复使用一个数字',
        self::SAME_ACCOUNT              => '投放计划已有其他账号使用，请确认投放计划后重试',
        self::INVALID_IS                => '未知is—valid',
        self::GET_AUTH_ERROR            => '获取授权信息失败',
        self::SAME_PHONE                => '输入相同phone',
        self::CUID_EXIST                => '该设备已领取',

        //== 5000-5999 转介绍
        self::INTRO_BILL_ILLEGAL          => '非法海报',
        self::INTRO_TO_PHONE_EMPTY        => '被介绍人电话空',
        self::INTRO_TO_PHONE_EXIST        => '此号码已被推荐',
        self::INTRO_TO_PHONE_ILLEGAL      => '此号码非新用户',
        self::INTRO_PHONE_FORMAT_ERROR    => '请填写正确的手机号',
        self::INTRO_SEND_SMS_TOKEN_ERROR  => '验证码发送失败',
        self::INTRO_SMS_TOKEN_CHECK_ERROR => '验证码校验失败',
        self::INTRO_NAME_EMPTY            => '请输入姓名',
        self::INTRO_GRADE_ID_ERROR        => '请选择年级',
        self::INTRO_GET_UID_ERROR         => '获取uid失败，请刷新页面重试',
        self::INTRO_SHARE_UID_ERROR       => '邀请人信息不存在',
        self::INTRO_BANKE_USER_ERROR      => '不符合活动规则，已购买过班课',
        self::INTRO_ENTRANCE_DX_ERROR     => '不满足活动规则',
        self::INTRO_PHONE_HAS_INVITED     => '不符合活动规则，您已被邀请过',
        self::INTRO_FREQUENCY_LOCK        => '操作太频繁',
        self::INTRO_ACTIVITY_NO_EFFECT    => '活动已失效',
        self::INTRO_ACTIVITY_TYPE_ERROR   => '活动类型错误',
        self::INTRO_NO_PRIVILEGE          => '没有权限',
        self::INTRO_GET_SEND_MONEY_ERROR  => '获取返现金额错误',
        self::INTRO_AUDIT_TYEP_ERROR      => '审核类型错误',
        self::INTRO_AUDIT_REFUSE_REASON_EMPTY => '请填写拒绝原因',
        self::INTRO_AUDIT_ERROR               => '审核失败',
        self::INTRO_SEND_MONEY_LIST_EMPTY     => '请选择返现用户',
        self::INTRO_SEND_CNT_LIMIT            => '一次性返现人数太多，请勾选不超过20个用户',
        self::INTRO_OVER_BUDGET               => '超过活动总预算',
        self::INTRO_SEARCH_SEND_MONEY_ERROR   => '查询返现明细失败',
        self::INTRO_SEND_TOTAL_MONEY_ERROR    => '活动发放总金额校验失败',

        //== 6000-6999 运营平台 start

        self::SKYFIRE_DISPATCH_FAILD   => '分发参数错误',
        self::SKYFIRE_DISPATCH_OBJNAME => '分发类名不存在',
        self::SKYFIRE_DISPATCH_METHOD  => '分发方法不存在',
        //== 6000-6999 运营平台 end

        //== 特惠课start
        self::TEHUIKE_STATUS_ERROR     => '来晚啦,该活动已下线',
        self::TEHUIKE_FLOW_LIMIT       => '报名异常火爆，请稍后再试哦',
        //== 特惠课end

        //ol 相关
        self::USER_NO_THIS_COURSE     => '您未购买该活动对应的课程',

        // QQ加群
        self::QQGROUP_EMPTY_SMS         => '自定义短信文案不能为空',
        self::QQGROUP_EMPTY_GROUPNUM    => 'QQ群号必须大于0',
		self::QQGROUP_EMPTY_UPLIMIT     => '满群上线必须大于0',
		self::QQGROUP_EMPTY_ANDROIDKEY  => '安卓密钥错误',
		self::QQGROUP_EMPTY_IOSKEY      => 'ios密钥错误',
		self::QQGROUP_EMPTY_GROUPNAME   => '群名称不能为0或空',
		self::QQGROUP_EMPTY_GROUPLINK   => '进群链接不能为0或空',
        self::QQGROUP_BEYOND_MAXNUM     => '自定义短信文案超出上限',

		//新手礼包
        self::NEWBGIFTBAG_JOIN_ONE        => '活动仅限参与一次',
        self::NEWBGIFTBAG_JOIN_NEW        => '您不是新用户，不能参与本次活动哦',
        self::NEWBGIFTBAG_GET_FAIL        => '领取失败，请稍后再试',
        self::NEWBGIFTBAG_HAD_GOT         => '您已领取过该课程',
        self::NEWBGIFTBAG_NO_VALID        => '获取有效课程失败',
        self::NEWBGIFTBAG_NOT_PRE_LONG    => '课程信息错误',

        // 分销
		self::FENXIAO_NOT_ZHAOMU          => '来晚了,已无法再加入',
		self::FENXIAO_ACT_OUTDATE         => '来晚了,活动已结束',
		//== 100001-999999 skyfire

		//== 100001-299999 coupon
		self::UNVALID_CODENAME                  => '无效的优惠码',
		self::UNVALID_UID                       => '无效的用户uid',
		self::UNVALID_ITEMID                    => '无效的优惠券id',
		self::HAS_EXCHANGE                      => '已经兑换过啦',
		self::HAS_SEND_COUPON                   => '已经领取过优惠券啦',
		self::COUPON_STOCK_INSUFFICIENT         => '优惠券库存不足',
		self::COUPON_CODE_INSUFFICIENT          => '优惠码库存不足',
		self::OVER_REQUEST                      => '请求次数太多啦',
        self::COUPON_GET_FAILURE                => '优惠券领取失败',
        self::COUPON_MATCHING_FAILURE           => '优惠券匹配失败',

        // 课程相关
        self::PLAYBACK_NOT_GENERATE     => '视频回放未生成',
        self::GPUID_NOT_EXIST           => 'gpuid不存在',
        self::COURSE_INFO_NOT_EXIST     => '课程信息不存在',
        self::NOT_AUTH_TO_SEE           => '没有权限观看该视频',
        self::LESSON_ALREADY_EXIST      => '章节信息已经存在',

        // 蜂鸟相关
        self::HB_ACT_OFFLINE      => '活动已经下线',
        self::HB_ACT_NOSTART      => '活动还没有开始',
        //==10000-10999 老师说相关
        self::REACH_STAN                => '请下周再发哦~',

        // 兑换码相关
        self::GET_CDKEY_STOCK_LACK               => '兑换码数量不足，发放失败',
        self::EXCHANGE_CDKEY_NOT_EXIST           => '兑换码不存在，请输入正确的兑换码',
        self::EXCHANGE_CDKEY_DOING               => '兑换码正在兑换中，请等待',
        self::EXCHANGE_CDKEY_DONE                => '兑换码已被兑换，不可重复使用',
        self::EXCHANGE_CDKEY_REFUND              => '兑换码已被退款，不可兑换',
        self::EXCHANGE_CDKEY_CANCEL              => '兑换码已被作废，不可兑换',
        self::EXCHANGE_CDKEY_UNSOLD              => '兑换码未出售，不可兑换',
        self::EXCHANGE_CDKEY_INVALID             => '兑换码已失效，请联系第三方平台退款',
        self::EXCHANGE_CDKEY_UNENFORCED          => '未到达生效时间，兑换码还未生效，不可兑换',
        self::REFUND_CDKEY_UNSOLD                => '兑换码未出售，不可退款',
        self::REFUND_CDKEY_EXCHANGING            => '正在兑换中，不可退款，请联系作业帮',
        self::REFUND_CDKEY_EXCHANGED             => '兑换码已被兑换，不可退款',
        self::REFUND_CDKEY_REFUND                => '兑换码已退款，不可重复操作',
        self::REFUND_CDKEY_CANCEL                => '兑换码已作废，不可退款',
        self::CDKEYSERVE_CDKEY_NOT_EXIST         => '兑换码不存在',
        self::CDKEYSERVE_CDKEYSERIES_NOT_EFFECT  => '兑换码批次无效',
        self::CDKEYSERVE_CDKEY_SKU_ERROR         => '兑换码兑换商品ID错误',
        self::CDKEYSERVE_CDKEY_APPLY_DUP         => '重复申请兑换码',
        self::CDKEYSERVE_CDKEY_APPLY_ERROR       => '申请兑换码异常',
        self::CDKEYSERVE_CDKEY_CHANGE_ERROR      => '修改兑换码状态异常',
        self::CDKEY_THIRD_PARTY_EXCEPTION        => '第三方调用异常',
        self::CDKEY_STOCK_UNLOCK_FAIL            => '库存解锁失败',
        self::EXCHANGE_CDKEY_SUCC                => '兑换码兑换成功',
        self::CDKEY_CHANNEL_MATCH_FAIL           => '渠道匹配失败',
        self::CDKEY_EXCHANGE_STATUS_ERROR        => '兑换码核销状态错误',
        self::EXCHANGE_CDKEY_FAIL                => '兑换码兑换失败',
        self::CDKEY_COUNT_LACK                   => '兑换码数量不足',
        self::SERIES_CDKEY_UNENFORCED            => '该批次兑换码未生效',
        self::SERIES_CDKEY_CANCEL                => '该批次兑换码已停用',
        self::SERIES_CDKEY_INVALID               => '该批次兑换码已过期',
        self::SERIES_CDKEY_ERROR                 => '该批次兑换码异常',
        self::CDKEY_STOCK_LOCK_FAIL              => '库存锁定失败',
    );


    /**
     * 获取错误信息 utf8编码
     * @param $errno
     * @return string
     */
    public static function getErrMsg($errno)
    {
        if (isset(self::$errMsg[$errno])) {
            return self::$errMsg[$errno];
        }

        return '未知错误';
    }
}
