<?php
class Oplib_Biz_Const_Grade{
    //业务线
    const BRAND_ID_HUANXIONG = 2;
    const BRAND_ID_BANGBANG = 3;

    //学部
    const GRADE_STAGE_PRESCHOOL = 60; //学前
    const GRADE_STAGE_PRIMARY   = 1;  //小学
    const GRADE_STAGE_JUNIOR    = 20; //初中
    const GRADE_STAGE_SENIOR    = 30; //高中

    const YINIANJI = 1;
    const ERNINAJI = 2;
    const SANNIANJI = 4;
    const SINIANJI = 8;
    const WUNIANJI = 16;
    const LIUNIANJI = 32;
    const XIAOXUE = 64;
    const CHUYI   = 128;
    const CHUER = 256;
    const CHUSAN = 512;
    const CHUZHONG = 1024;
    const GAOYI = 2048;
    const GAOER = 4096;
    const GAOSAN = 8192;
    const GAOZHONG = 16384;
    const XUEQIAN = 32768;//学前
    const XUEQIANBAN = 65536;//学前班


    //年级常量
    const GRADE_ID_0_4 = 64; //小班班
    const GRADE_ID_0_3 = 63; //中班
    const GRADE_ID_0_2 = 62; //大班
    const ONEGRADE = 11;//一年级
    const TWOGRADE = 12;//二年级
    const THREEGRADE = 13;//三年级
    const FOURGRADE = 14;//四年级
    const FIVEGRADE = 15;//五年级
    const SIXGRADE = 16;//六年级
    const SEVENGRADE = 2;//初一
    const EIGHTGRADE = 3;//初二
    const NINEGRADE = 4;//初三
    const TENGRADE = 5;//高一
    const ELEVENGRADE = 6;//高二
    const TWELVEGRADE = 7;//高三
    const THIRTEENGRADE = 61;//学前班
    const ONEMOREGRADE = 1;//小学
    const TWOMOREGRADE = 20;//初中
    const THREEMOREGRADE = 30;//高中
    const FOURMOREGRADE = 60;//学前
    const OTHEROREGRADE = 255;//高中

    const PREJUNIOR = 21; // 预初

    // 63制升年级的映射
    public static $gradeUpMap = array(
        self::THIRTEENGRADE     => self::ONEGRADE,
        self::ONEGRADE          => self::TWOGRADE,
        self::TWOGRADE          => self::THREEGRADE,
        self::THREEGRADE        => self::FOURGRADE,
        self::FOURGRADE         => self::FIVEGRADE,
        self::FIVEGRADE         => self::SIXGRADE,
        self::SIXGRADE          => self::SEVENGRADE,
        self::SEVENGRADE        => self::EIGHTGRADE,
        self::EIGHTGRADE        => self::NINEGRADE,
        self::NINEGRADE         => self::TENGRADE,
        self::TENGRADE          => self::ELEVENGRADE,
        self::ELEVENGRADE       => self::TWELVEGRADE,
        self::TWELVEGRADE       => self::TWELVEGRADE,
    );

    // 54制升年级的映射
    public static $gradeUpMap54 = array(
        self::THIRTEENGRADE     => self::ONEGRADE,
        self::ONEGRADE          => self::TWOGRADE,
        self::TWOGRADE          => self::THREEGRADE,
        self::THREEGRADE        => self::FOURGRADE,
        self::FOURGRADE         => self::FIVEGRADE,
        self::FIVEGRADE         => self::PREJUNIOR,
        self::PREJUNIOR         => self::SEVENGRADE,
        self::SEVENGRADE        => self::EIGHTGRADE,
        self::EIGHTGRADE        => self::NINEGRADE,
        self::NINEGRADE         => self::TENGRADE,
        self::TENGRADE          => self::ELEVENGRADE,
        self::ELEVENGRADE       => self::TWELVEGRADE,
        self::TWELVEGRADE       => self::TWELVEGRADE,
    );


    //年级映射表
    public static $gradeTextMap = array(
        1  => '小学',
        11  => '一年级',
        12  => '二年级',
        13  => '三年级',
        14  => '四年级',
        15  => '五年级',
        16  => '六年级',

        20   => '初中',
        self::PREJUNIOR => '预初',
        2   => '初一',
        3   => '初二',
        4   => '初三',

        30   => '高中',
        5   => '高一',
        6   => '高二',
        7   => '高三',


        60  => '学前',
        61  => '学前班',
        62  => '大班',
        63  => '中班',
        64  => '小班',
        50  => '高中',
    );

    //学科映射表
    public static $subjectTextMap = array(
        1 => '语文',
        2 => '数学',
        3 => '英语',
        4 => '物理',
        5 => '化学',
        6 => '生物',
        7 => '政治',
        8 => '历史',
        9 => '地理',
        10=> '兴趣课',//直播课使用
        11=> '思想品德',//直播课使用
        12=> '讲座',//直播课使用
        13=> '理综',//试卷用
        14=> '文综',//试卷用
        15=> '奥数',
        16=> '科学',
    );

    //年级学部映射
    public static $gradeXueBuMap = array(
        11  => 1,
        12  => 1,
        13  => 1,
        14  => 1,
        15  => 1,
        16  => 1,
        1  => 1,
        2   => 20,
        3   => 20,
        4   => 20,
        self::PREJUNIOR  => 20, // 预初年级算初中学部
        20   => 20,
        5   => 30,
        6   => 30,
        7   => 30,
        30   => 30,
        50   => 30,
        60  => 60, // 学前学部
        62  => 60,
        63  => 60,
        64  => 60,
    );

    //学历
    public static $degreeTextMap = array(
        1  =>  '小学',
        2  =>  '初中',
        3  =>  '高中',
        4  =>  '中专',
        5  =>  '大专',
        6  =>  '本科',
        7  =>  '研究生',
        8  =>  '博士',
        9  =>  '博士后',
        10 => '其他'
    );

    //课程类型标签
    static public $courseTypeTag = array(
        0=>'专题课',
        2=>'长期班',
        4=>'体验课',
    );

    static public $xuebuList = [
        self::ONEMOREGRADE,
        self::TWOMOREGRADE,
        self::THREEMOREGRADE,
        self::FOURMOREGRADE,
        self::OTHEROREGRADE,
    ];
}