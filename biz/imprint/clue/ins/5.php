<?php

class Oplib_Biz_Imprint_Clue_Ins_5 extends Oplib_Biz_Imprint_Clue_Base {

    /**
     * 格式化数据
     * @param $param
     * @return array
     */
    public static function format4imprintClue($param) {
        $data = array();

        $mustKeys = array(
            'uuid',
            'path',
        );
        foreach ($mustKeys as $key) {
            if (!isset($param[$key])) {
                return [];
            }
            $data[$key] = $param[$key];
        }

        $key2default = array(
            'clientIp' => '',
            'studentUid' => 0,
            'cuid' => '',
            'appid' => '',
            'vc' => 0,
            'vcname' => '',
            'zbkvc' => 0,
            'ykvc' => 0,
            'os' => '',
            'os_version' => '',
            'nt' => '',
            'brand' => '',
            'device' => '',
            'conn' => 0,
            'networkid' => '',
        );
        foreach ($key2default as $key => $default) {
            $data[$key] = $param[$key] ?? $default;
        }

        return $data;
    }

}
