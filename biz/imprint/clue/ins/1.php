<?php

class Oplib_Biz_Imprint_Clue_Ins_1 extends Oplib_Biz_Imprint_Clue_Base {

    /**
     * 格式化数据
     * @param $param
     * @return array
     */
    public static function format4imprintClue($param) {
        $data = array();

        $mustKeys = array(
            'cuid',
            'bucketId',
            'isGray',
            'zb_main3',
        );
        foreach ($mustKeys as $key) {
            if (!isset($param[$key])) {
                return [];
            }
            $data[$key] = $param[$key];
        }

        $key2default = array(
            'studentUid' => 0,
            'grade' => 0,
            'appId' => '',
            'os' => '',
            'vc' => 0,
            'vcname' => '',
            'zbkvc' => 0,
            'ykvc' => 0,
            'path' => '',
        );
        foreach ($key2default as $key => $default) {
            $data[$key] = $param[$key] ?? $default;
        }

        return $data;
    }

}
