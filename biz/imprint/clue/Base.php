<?php

/**
 * brief: 基类-imprintclue
 * @author: <EMAIL>
 */
abstract class Oplib_Biz_Imprint_Clue_Base implements Oplib_Biz_Imprint_Clue_Able {

    private static $Inses;

    /**
     * 获取type
     * @return int
     */
    public static function getType2imprintClue() {
        $className = static::class;
        $typeStr = substr($className, strpos(__CLASS__, 'Base'));
        $type = intval($typeStr);
        return $type;
    }

}
