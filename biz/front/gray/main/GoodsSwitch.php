<?php

/**
 * brief: 灰度控制-goods switch 下面的配置
 * @author: <EMAIL>
 */
class Oplib_Biz_Front_Gray_Main_GoodsSwitch {

    private static $Ins;
    private $videoWhiteList;
    private $isVideoWhiteListInstalled = false;
    private $key2value;

    protected function __construct() {
        $this->key2value = array();
    }

    public static function getIns() {
        if (!self::$Ins) {
            self::$Ins = new self();
        }

        return self::$Ins;
    }

    /*
     * 某key是否打开
     */
    public function isKeyOpen($key) {
        if (!isset($this->key2value[$key])) {
            $value = false; // 默认为不打开
            $ret = Zb_Service_NCM::get(1, 'goods', 'switch', $key);
            if ($ret) {
                $value = true;
            }
            $this->key2value[$key] = $value;
        }
        return $this->key2value[$key];
    }
}
