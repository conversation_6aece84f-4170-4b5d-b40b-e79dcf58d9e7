<?php

/**
 * brief: 将cuid进行分桶
 * @author: <EMAIL>
 */
class Oplib_Biz_Front_Common_Bucket_CuidBucket {

    private static $Ins;
    private $cuid2bucketId;

    protected function __construct() {
        $this->cuid2bucketId = [];
    }

    public static function getInstance() {
        if (!self::$Ins) {
            self::$Ins = new self();
        }

        return self::$Ins;
    }

    public static function getCuidBucketId($cuid) {
        // imp提供的哈希算法没有php语言实现，走系统调用性能较差，改用crc
        $num = crc32(intval($cuid));
        $bucketId = (abs($num) % 100) + 1;
        return $bucketId;

        $bucketId = 0;
        do {
            $path = dirname(__FILE__);
            $command = "{$path}/example '$cuid'";
            $str = exec($command, $output, $ret);
            if (0 !== $ret) {
                Bd_Log::warning("hash cuid failed, command[$command] ret[$ret]");
                break;
            }

            $num = intval(str_replace('x86_32: ', '', $str));
            $bucketId = (abs($num) % 100) + 1;
        } while (false);

        return $bucketId;
    }

    public function getBucketId($cuid) {
        if (!isset($this->cuid2bucketId[$cuid])) {
            $this->cuid2bucketId[$cuid] = self::getCuidBucketId($cuid);
        }

        return $this->cuid2bucketId[$cuid];
    }

}

