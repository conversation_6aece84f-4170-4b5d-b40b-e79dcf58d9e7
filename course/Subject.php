<?php
/**
 * @file: Subject.php
 *        学科 类文件
 * @author: dds(<EMAIL>)
 * @date: 2021-10-12 17:00:07
 * @brief: 学科
 *         使用中
 */


class Sp_Dict_Course_Subject
{
   //*Const
   const CHINESE = 1;//语文 - 使用中
   const MATHEMATICS = 2;//数学 - 使用中
   const ENGLISH = 3;//英语 - 使用中
   const PHYSICS = 4;//物理 - 使用中
   const CHEMISTRY = 5;//化学 - 使用中
   const BIOLOGY = 6;//生物 - 使用中
   const POLITICS = 7;//政治 - 使用中
   const HISTORY = 8;//历史 - 使用中
   const GEOGRAPHY = 9;//地理 - 使用中
   const INTEREST = 10;//兴趣课 - 使用中
   const MORALEDUCATION = 11;//思想品德  - 使用中
   const LECTURE = 12;//讲座 - 使用中
   const LIZONG = 13;//理综  - 使用中
   const WENZONG = 14;//文综 - 使用中
   const OLYMPIAD = 15;//奥数 - 使用中
   const SCIENCE = 16;//科学 - 使用中
   const KOUYU = 17;//口语 - 使用中
   const XIEZUO = 18;//写作 - 使用中
   const YUEDU = 19;//阅读 - 使用中
   const CIHUI = 20;//词汇 - 使用中
   const YUFA = 21;//语法 - 使用中
   const TINGLI = 22;//听力 - 使用中
   const ZONGHE = 23;//综合 - 使用中
   const CPA = 24;//中级财会 - 使用中
   const CJCK = 25;//初级财会 - 使用中
   const BISHI = 26;//笔试 - 使用中
   const MIANSHI = 27;//面试 - 使用中
   const SHENGKAO = 28;//省考 - 使用中
   const GUOKAO = 29;//国考 - 使用中
   const PUTONGHUA = 30;//普通话 - 使用中
   const ZHAOSHIBILU = 31;//招录笔试 - 使用中
   const CPA_N = 32;//MBA - 使用中
   const SIWEI = 33;//思维 - 使用中
   const XIEZI = 34;//写字 - 使用中
   const MEISHU = 35;//美术 - 使用中
   const SHIYE = 36;//事业单位考试 - 使用中
   const CMA = 37;//CMA - 使用中
   const SWS = 38;//税务师 - 使用中
   const ZY = 39;//专业 - 使用中
   const SCRATCH = 40;//Scratch - 使用中
   const PYTHON = 41;//Python - 使用中
   const JISHU = 42;//技术 - 使用中
   const CPA_R = 43;//CPA - 使用中
   const FUYE = 44;//副业 - 使用中
   const XINGQU = 45;//兴趣 - 使用中
   const ZHICHANG = 46;//职场 - 使用中
   const KOUCAI = 47;//口才 - 使用中

   //*Map
   public static $map = [
       self::CHINESE => "语文",
       self::MATHEMATICS => "数学",
       self::ENGLISH => "英语",
       self::PHYSICS => "物理",
       self::CHEMISTRY => "化学",
       self::BIOLOGY => "生物",
       self::POLITICS => "政治",
       self::HISTORY => "历史",
       self::GEOGRAPHY => "地理",
       self::INTEREST => "兴趣课 直播课使用",
       self::MORALEDUCATION => "思想品德 直播课使用",
       self::LECTURE => "讲座 直播课使用",
       self::LIZONG => "理综 试卷用",
       self::WENZONG => "文综 试卷用",
       self::OLYMPIAD => "奥数",
       self::SCIENCE => "科学",
       self::KOUYU => "成人-实用英语-口语",
       self::XIEZUO => "成人-实用英语-写作",
       self::YUEDU => "成人-实用英语-阅读",
       self::CIHUI => "成人-实用英语-词汇",
       self::YUFA => "成人-实用英语-语法",
       self::TINGLI => "成人-实用英语-听力",
       self::ZONGHE => "成人-实用英语-综合",
       self::CPA => "成人-财会-中级财会",
       self::CJCK => "成人-财会-初级财会",
       self::BISHI => "成人-教师资格证-笔试",
       self::MIANSHI => "成人-教师资格证-面试",
       self::SHENGKAO => "省考",
       self::GUOKAO => "国考",
       self::PUTONGHUA => "普通话",
       self::ZHAOSHIBILU => "招录笔试",
       self::CPA_N => "MBA",
       self::SIWEI => "思维",
       self::XIEZI => "写字",
       self::MEISHU => "美术",
       self::SHIYE => "事业单位考试",
       self::CMA => "CMA",
       self::SWS => "税务师",
       self::ZY => "专业",
       self::SCRATCH => "Scratch",
       self::PYTHON => "Python",
       self::JISHU => "技术",
       self::CPA_R => "CPA",
       self::FUYE => "副业",
       self::XINGQU => "兴趣",
       self::ZHICHANG => "职场",
       self::KOUCAI => "口才",
   ];

   //*KeyMap - 字符串map
   public static $keyMap = [
       self::CHINESE => "chinese",
       self::MATHEMATICS => "mathematics",
       self::ENGLISH => "english",
       self::PHYSICS => "physics",
       self::CHEMISTRY => "chemistry",
       self::BIOLOGY => "biology",
       self::POLITICS => "politics",
       self::HISTORY => "history",
       self::GEOGRAPHY => "geography",
       self::INTEREST => "interest",
       self::MORALEDUCATION => "moraleducation",
       self::LECTURE => "lecture",
       self::LIZONG => "lizong",
       self::WENZONG => "wenzong",
       self::OLYMPIAD => "olympiad",
       self::SCIENCE => "science",
       self::KOUYU => "kouyu",
       self::XIEZUO => "xiezuo",
       self::YUEDU => "yuedu",
       self::CIHUI => "cihui",
       self::YUFA => "yufa",
       self::TINGLI => "tingli",
       self::ZONGHE => "zonghe",
       self::CPA => "cpa",
       self::CJCK => "cjck",
       self::BISHI => "bishi",
       self::MIANSHI => "mianshi",
       self::SHENGKAO => "shengkao",
       self::GUOKAO => "guokao",
       self::PUTONGHUA => "putonghua",
       self::ZHAOSHIBILU => "zhaoshibilu",
       self::CPA_N => "cpa_n",
       self::SIWEI => "siwei",
       self::XIEZI => "xiezi",
       self::MEISHU => "meishu",
       self::SHIYE => "shiye",
       self::CMA => "cma",
       self::SWS => "sws",
       self::ZY => "zy",
       self::SCRATCH => "scratch",
       self::PYTHON => "python",
       self::JISHU => "jishu",
       self::CPA_R => "cpa_r",
       self::FUYE => "fuye",
       self::XINGQU => "xingqu",
       self::ZHICHANG => "zhichang",
       self::KOUCAI => "koucai",
   ];
}
