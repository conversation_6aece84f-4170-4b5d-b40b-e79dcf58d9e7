<?php
/**
 * @file: ShelfTimeType.php
 *        上架策略时间模版 类文件
 * @author: dds(<EMAIL>)
 * @date: 2021-10-12 17:00:07
 * @brief: 上架策略时间模版
 *         使用中
 */


class Sp_Dict_Course_ShelfTimeType
{
   //*Const
   const SHELF_TIME_TYPE_FIRST_LESSON_DATE_ZERO = 2;//首个章节上课日零点 - 使用中
   const SHELF_TIME_TYPE_BEFORE_FIRST_LESSON_TWO_HOUR = 3;//首个章节开课前2小时 - 使用中
   const OFF_SHELF_TIME_TYPE_FIVE_DAYS_BEFORE_START = 5;//开课前五天零点 - 使用中
   const OFF_SHELF_TIME_TYPE_FIRST_LESSON_DATE_ZERO = 6;//首个章节上课日零点 - 使用中
   const OFF_SHELF_TIME_TYPE_FIRST_LESSON_START = 7;//第一个章节开课时 - 使用中
   const OFF_SHELF_TIME_TYPE_SECOND_LESSON_START = 8;//第二个章节开课时 - 使用中
   const OFF_SHELF_TIME_TYPE_THIRD_LESSON_START = 9;//第三个章节开课时 - 使用中
   const OFF_SHELF_TIME_TYPE_FOURTH_LESSON_START = 10;//第四个章节开课时 - 使用中
   const OFF_SHELF_TIME_TYPE_LAST_LESSON_START = 11;//最后一个章节开课时 - 使用中
   const OFF_SHELF_TIME_TYPE_LAST_LESSON_STOP = 12;//最后一个章节结束时 - 使用中
   const OFF_SHELF_TIME_TYPE_FIRST_LESSON_DATE_BEFORE_ZERO = 14;//首个章节上课前1日0点 - 使用中
   const OFF_SHELF_TIME_TYPE_FIRST_LESSON_DATE_TWO_BEFORE_Z = 15;//首个章节上课前2日14点 - 使用中
   const SHELF_TIME_WITHOUT_DELAY = 1;//立即上架 - 使用中

   //*Map
   public static $map = [
       self::SHELF_TIME_TYPE_FIRST_LESSON_DATE_ZERO => "首个章节上课日零点",
       self::SHELF_TIME_TYPE_BEFORE_FIRST_LESSON_TWO_HOUR => "首个章节开课前2小时",
       self::OFF_SHELF_TIME_TYPE_FIVE_DAYS_BEFORE_START => "开课前五天零点",
       self::OFF_SHELF_TIME_TYPE_FIRST_LESSON_DATE_ZERO => "首个章节上课日零点",
       self::OFF_SHELF_TIME_TYPE_FIRST_LESSON_START => "第一个章节开课时",
       self::OFF_SHELF_TIME_TYPE_SECOND_LESSON_START => "第二个章节开课时",
       self::OFF_SHELF_TIME_TYPE_THIRD_LESSON_START => "第三个章节开课时",
       self::OFF_SHELF_TIME_TYPE_FOURTH_LESSON_START => "第四个章节开课时",
       self::OFF_SHELF_TIME_TYPE_LAST_LESSON_START => "最后一个章节开课时",
       self::OFF_SHELF_TIME_TYPE_LAST_LESSON_STOP => "最后一个章节结束时",
       self::OFF_SHELF_TIME_TYPE_FIRST_LESSON_DATE_BEFORE_ZERO => "首个章节上课前1日0点",
       self::OFF_SHELF_TIME_TYPE_FIRST_LESSON_DATE_TWO_BEFORE_Z => "首个章节上课前2日14点",
       self::SHELF_TIME_WITHOUT_DELAY => "立即上架",
   ];

   //*KeyMap - 字符串map
   public static $keyMap = [
       self::SHELF_TIME_TYPE_FIRST_LESSON_DATE_ZERO => "shelf_time_type_first_lesson_date_zero",
       self::SHELF_TIME_TYPE_BEFORE_FIRST_LESSON_TWO_HOUR => "shelf_time_type_before_first_lesson_two_hour",
       self::OFF_SHELF_TIME_TYPE_FIVE_DAYS_BEFORE_START => "off_shelf_time_type_five_days_before_start",
       self::OFF_SHELF_TIME_TYPE_FIRST_LESSON_DATE_ZERO => "off_shelf_time_type_first_lesson_date_zero",
       self::OFF_SHELF_TIME_TYPE_FIRST_LESSON_START => "off_shelf_time_type_first_lesson_start",
       self::OFF_SHELF_TIME_TYPE_SECOND_LESSON_START => "off_shelf_time_type_second_lesson_start",
       self::OFF_SHELF_TIME_TYPE_THIRD_LESSON_START => "off_shelf_time_type_third_lesson_start",
       self::OFF_SHELF_TIME_TYPE_FOURTH_LESSON_START => "off_shelf_time_type_fourth_lesson_start",
       self::OFF_SHELF_TIME_TYPE_LAST_LESSON_START => "off_shelf_time_type_last_lesson_start",
       self::OFF_SHELF_TIME_TYPE_LAST_LESSON_STOP => "off_shelf_time_type_last_lesson_stop",
       self::OFF_SHELF_TIME_TYPE_FIRST_LESSON_DATE_BEFORE_ZERO => "off_shelf_time_type_first_lesson_date_before_zero",
       self::OFF_SHELF_TIME_TYPE_FIRST_LESSON_DATE_TWO_BEFORE_Z => "off_shelf_time_type_first_lesson_date_two_before_z",
       self::SHELF_TIME_WITHOUT_DELAY => "shelf_time_without_delay",
   ];
}
