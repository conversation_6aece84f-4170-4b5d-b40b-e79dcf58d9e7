<?php
/***************************************************************************
 *
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   LessonState.php
 * <AUTHOR>
 * @date   2015/11/17 14:13:18
 * @brief  课节状态
 *
 **/
class Hkzb_Ds_Fudao_LessonState
{
    //出勤状态
    const ATTEND_NO  = 0; //未出勤
    const ATTEND_YES = 1; //出勤
    //学习报告状态
    const REPORT_NO  = 0;  //发送报告
    const REPORT_YES = 1; //未发送

    const ALL_FIELDS = 'id,studentUid,courseId,lessonId,teacherUid,assistantUid,classId,attend,rightNum,totalNum,homework,report,createTime,updateTime,extData,hasCourse,attendLong,submitTime';

    //课后作业批改评价级别
    static $EXERCISE_EVALUATION_LEVEL_ARRAY = array(
        array(
            'levelCode'     => 1,
            'levelDescribe' => '满意',
        ),
        array(
            'levelCode'     => 3,
            'levelDescribe' => '不满意',
        ),
    );
    
    //课后作业批改不满意对应的选项
    static $EXERCISE_EVALUATION_NOT_SATISFIED_ARRAY = array(
        array(
            'optionCode'     => 1,
            'optionDescribe' => '其它',
            'sort'           => 10000,//排序在最后
        ),
        array(
            'optionCode'     => 2,
            'optionDescribe' => '讲解思路不清晰',
            'sort'           => 1,
        ),
        array(
            'optionCode'     => 3,
            'optionDescribe' => '语音有杂音',
            'sort'           => 2,
        ),
        array(
            'optionCode'     => 4,
            'optionDescribe' => '作业评级不合理',
            'sort'           => 3,
        ),
        array(
            'optionCode'     => 5,
            'optionDescribe' => '作业批改错误',
            'sort'           => 4,
        ),
        array(
            'optionCode'     => 6,
            'optionDescribe' => '语音讲解错误',
            'sort'           => 5,
        ),
    );
    private $objDaoLessonState;

    /**
     * 构造函数
     *
     */
    public function __construct()
    {
        $this->objDaoLessonState = new Hkzb_Dao_Fudao_LessonState();
    }
    
       /**
     * 更新学生信息（新添加的方法）
     *
     * @param  int  $courseId    课程id
     * @param  array$arrParams   课程属性
     * @return bool true/false
     */
    public function updateLessonstateByclassId($courseId, $classId = 0, $arrStuList, $arrParams) {
        if(intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");
            return false;
        }
     
        $arrConds = array(
            'classId'        => intval($classId),
            'courseId'   => intval($courseId),
        );
        if(count($arrStuList) > 0){
            $strStudentUid = implode(",",$arrStuList);
            $strStudentUid = 'student_uid in ('.$strStudentUid.')';
            $arrConds[] = $strStudentUid;
        }
        
        $arrFields = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach($arrParams as $key => $value) {
            if(!in_array($key, $arrAllFields)) {
                continue;
            }

            $arrFields[$key] = $value;
        }

        //ext由ps补全，json处理在ds
        if(isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }

        //补全更新时间
        $arrFields['updateTime'] = time();
        
        $objDaoLessonstate = new Hkzb_Dao_Fudao_Lessonstate();
        $ret = $objDaoLessonstate->updateByConds($arrConds, $arrFields);

        return $ret;
    }
    

    /**
     * 新增学生课节状态
     *
     * @param  array $arrParams 状态属性
     * @return bool true/false
     */
    public function addLessonState($arrParams)
    {
        if (intval($arrParams['studentUid']) <= 0 || intval($arrParams['courseId']) <= 0 || intval($arrParams['lessonId']) <= 0 || intval($arrParams['teacherUid']) <= 0 || intval($arrParams['assistantUid']) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[param: " . json_encode($arrParams) . "]");

            return false;
        }

        $arrFields = array(
            'studentUid'   => intval($arrParams['studentUid']),
            'courseId'     => intval($arrParams['courseId']),
            'lessonId'     => intval($arrParams['lessonId']),
            'classId'      => intval($arrParams['classId']),
            'teacherUid'   => intval($arrParams['teacherUid']),
            'assistantUid' => intval($arrParams['assistantUid']),
            'createTime'   => time(),
            'updateTime'   => time(),
            'extData'      => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
        );

        $ret = $this->objDaoLessonState->insertRecords($arrFields);

        return $ret;
    }

    /**
     * 学生上课
     *
     * @param  int $studentUid 学生uid
     * @param  int $courseId   课程id
     * @param  int $lessonId   课节id
     * @return mix
     */
    public function attendClass($studentUid, $courseId, $lessonId)
    {
        if (intval($studentUid) <= 0 || intval($courseId) <= 0 || intval($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId lessonId:$lessonId]");

            return false;
        }

        //进来则显示出勤
        $arrConds = array(
            'studentUid' => intval($studentUid),
            'courseId'   => intval($courseId),
            'lessonId'   => intval($lessonId),
            'attend'     => self::ATTEND_NO,
        );
        $arrFields = array(
            'attend'     => self::ATTEND_YES,
            'updateTime' => time(),
        );
        $ret = $this->objDaoLessonState->updateByConds($arrConds, $arrFields);

        return $ret;
    }

    /**
     * 批量学生上课
     *
     * @param  array $arrStudentUid 学生uids
     * @param  int   $courseId   课程id
     * @param  int   $lessonId   课节id
     * @return mix
     */
    public function attendClassForStudentUids($arrStudentUids, $courseId, $lessonId)
    {
        if (empty($arrStudentUids) || intval($courseId) <= 0 || intval($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUids:".json_encode($arrStudentUids)." courseId:$courseId lessonId:$lessonId]");

            return false;
        }

        //进来则显示出勤
        $arrConds = array(
            'courseId'   => intval($courseId),
            'lessonId'   => intval($lessonId),
            'attend'     => self::ATTEND_NO,
        );
        $arrConds[] = 'student_uid in (' . implode(',',$arrStudentUids) . ')';
        $arrFields = array(
            'attend'     => self::ATTEND_YES,
            'updateTime' => time(),
        );
        $ret = $this->objDaoLessonState->updateByConds($arrConds, $arrFields);

        return $ret;
    }

    /**
     * 学生到课30分钟字段添加
     *
     * @param  array $arrStudentUid 学生uids
     * @param  int   $courseId   课程id
     * @param  int   $lessonId   课节id
     * @return mix
     */
    public function attendLongClassForStudentUids($arrStudentUids, $courseId, $lessonId)
    {
        if (empty($arrStudentUids) || intval($courseId) <= 0 || intval($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUids:".json_encode($arrStudentUids)." courseId:$courseId lessonId:$lessonId]");

            return false;
        }

        //进来则显示出勤
        $arrConds = array(
            'courseId'   => intval($courseId),
            'lessonId'   => intval($lessonId),
            'attendLong' => self::ATTEND_NO,
        );
        $arrConds[] = 'student_uid in (' . implode(',',$arrStudentUids) . ')';
        $arrFields = array(
            'attendLong' => self::ATTEND_YES,
            'updateTime' => time(),
        );
        $ret = $this->objDaoLessonState->updateByConds($arrConds, $arrFields);

        return $ret;
    }

    /**
     * 批量学生完课
     *
     * @param  array $arrStudentUid 学生uids
     * @param  int   $courseId   课程id
     * @param  int   $lessonId   课节id
     * @return mix
     */
    public function hasCourseClassForStudentUids($arrStudentUids, $courseId, $lessonId)
    {
        if (empty($arrStudentUids) || intval($courseId) <= 0 || intval($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUids:".json_encode($arrStudentUids)." courseId:$courseId lessonId:$lessonId]");

            return false;
        }

        //进来则显示完课
        $arrConds = array(
            'courseId'   => intval($courseId),
            'lessonId'   => intval($lessonId),
            'hasCourse'  => self::ATTEND_NO,
        );
        $arrConds[] = 'student_uid in (' . implode(',',$arrStudentUids) . ')';
        $arrFields = array(
            'hasCourse'     => self::ATTEND_YES,
            'updateTime' => time(),
        );
        $ret = $this->objDaoLessonState->updateByConds($arrConds, $arrFields);

        return $ret;
    }

    /**
     * 设置习题总数
     *
     * @param  int   $courseId      课程id
     * @param  int   $lessonId      课节id
     * @param  array $exerciseList  练习ids
     * @param  array $studentList   学生id
     * @return mix
     */
    public function setTotalNum($courseId,$lessonId,$exerciseList,$studentList)
    {
        $count = count($exerciseList);
        $sql = 'update tblLessonState set total_num = total_num + '. $count .' where course_id = ' . $courseId . ' and lesson_id = ' . $lessonId . ' and student_uid in (' . implode(',',$studentList) . ')';
        $res = $this->objDaoLessonState->query($sql);
        if($res === false){
            Bd_Log::warning("Error:[set total_num error],Detail:[courseId:$courseId lessonId:$lessonId studentUid:$studentUid]");
            return false;
        }
        return true;
    }
    private function _setTotalNum($courseId,$lessonId,$studentUid,$idlist){
        if (intval($courseId) <= 0 || intval($lessonId) <= 0 || intval($studentUid) <= 0) {
            Bd_Log::warning("Error:[param error],Detail:[courseId:$courseId lessonId:$lessonId studentUid:$studentUid]");
            return false;
        }
        //获取当节信息
        $lessonState = $this->getLessonState($studentUid,$courseId,$lessonId);
        if(!$lessonState){
            Bd_Log::warning("Error:[param error],Detail:[courseId:$courseId lessonId:$lessonId studentUid:$studentUid]");
            return false;
        
        }
        $extData = $lessonState['extData']; 
        $exerciseIds = (isset($extData['exercise'])) ? $extData['exercise'] : array();
        foreach($idlist as $exerciseId){
            if(!in_array($exerciseId,$exerciseIds)){
                $exerciseIds[] = $exerciseId;
            }
        }
        $extData['exercise'] = $exerciseIds;      
        $extData = json_encode($extData); 
        $arrFields = array(
            'totalNum'   => count($exerciseIds),
            'updateTime' => time(),
            'extData'    => $extData,
        );
        $arrConds = array(
            'courseId'   => intval($courseId),
            'lessonId'   => intval($lessonId),
            'studentUid' => intval($studentUid),
        );  

        $ret = $this->objDaoLessonState->updateByConds($arrConds, $arrFields);

        return $ret;   

    }
    /**
     * 设置课后作业状态
     *
     * @param  int $courseId   课程id
     * @param  int $lessonId   课节id
     * @return bool true/false
     */
    public function setHomeworkStatus($courseId,$lessonId,$status)
    {
        if (intval($courseId) <= 0 || intval($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId lessonId:$lessonId]");
            return false;
        }

        $arrConds = array(
            'courseId'   => intval($courseId),
            'lessonId'   => intval($lessonId),
        );

        $arrFields = array(
            'homework'   => $status,
            'updateTime' => time(),
        );

        $ret = $this->objDaoLessonState->updateByConds($arrConds, $arrFields);

        return $ret;
    }   


    /**
     * 答对题目加法
     *
     * @param  int $studentUid 学生uid
     * @param  int $courseId   课程id
     * @param  int $lessonId   课节id
     * @param  int $rightNum   答对数
     * @return bool true/false
     */
    public function addRightNum($studentUid,$courseId,$lessonId,$rightNum)
    {
        if (intval($courseId) <= 0 || intval($lessonId) <= 0 || intval($studentUid) <= 0 || intval($rightNum) <=0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId lessonId:$lessonId studentUid:$studentUid rightNum:$rightNum]");
            return false;
        }

        $arrConds = array(
            'courseId'   => intval($courseId),
            'lessonId'   => intval($lessonId),
            'studentUid' => intval($studentUid),
        );

        $arrFields = array(
            'rightNum'   => $rightNum,
            'updateTime' => time(),
        );

        $ret = $this->objDaoLessonState->updateByConds($arrConds, $arrFields);

        return $ret;
    }

    /**
     * 提交课后作业
     *
     * @param  int $studentUid 学生uid
     * @param  int $courseId   课程id
     * @param  int $lessonId   课节id
     * @return mix
     */
    public function submitHomework($studentUid, $courseId, $lessonId,$status)
    {
        if (intval($studentUid) <= 0 || intval($courseId) <= 0 || intval($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId lessonId:$lessonId]");

            return false;
        }

        //将未提交作业的状态置为已提交
        $arrConds = array(
            'studentUid' => intval($studentUid),
            'courseId'   => intval($courseId),
            'lessonId'   => intval($lessonId),
        );
        $arrFields = array(
            'homework'   => intval($status),
            'updateTime' => time(),
            'submitTime' => time(),
        );
        $ret = $this->objDaoLessonState->updateByConds($arrConds, $arrFields);

        return $ret;
    }


    /**
     * 学生发送报告
     *
     * @param  int $studentUid 学生uid
     * @param  int $courseId   课程id
     * @param  int $lessonId   课节id
     * @return mix
     */
    public function sendReport($studentUid, $courseId, $lessonId)
    {
        if (intval($studentUid) <= 0 || intval($courseId) <= 0 || intval($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId lessonId:$lessonId]");

            return false;
        }

        //获取未发送学习报告
        $arrConds = array(
            'studentUid' => intval($studentUid),
            'courseId'   => intval($courseId),
            'lessonId'   => intval($lessonId),
            'report'     => self::REPORT_NO,
        );
        $arrFields = array(
            'report'     => self::REPORT_YES,
            'updateTime' => time(),
        );
        $ret = $this->objDaoLessonState->updateByConds($arrConds, $arrFields);

        return $ret;
    }
    /**
     * 设置扩展字段
     *
     * @param  int $studentUid 学生uid
     * @param  int $courseId   课程id
     * @param  int $lessonId   课节id
     * @return mix
     */
    public function setExtData($studentUid, $courseId, $lessonId, $arrNewExtData)
    {
        if (intval($studentUid) <= 0 || intval($courseId) <= 0 || intval($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId lessonId:$lessonId]");

            return false;
        }

        $ret = $this->getLessonState($studentUid,$courseId, $lessonId, array('extData'));
        if (empty($ret)) {
            Bd_Log::warning("Error:[getLessonDetail error], Detail:[studentUid:$studentUid courseId:$courseId lessonId:$lessonId]");

            return false;
        }

        $arrExtData = $ret['extData'];
        foreach($arrNewExtData as $key=>$value){
             $arrExtData[$key] = $value;
        }

        $arrFields = array(
            'extData'    => json_encode($arrExtData),
            'updateTime' => time(),
        );


        $arrConds = array(
            'studentUid' => intval($studentUid),
            'courseId'   => intval($courseId),
            'lessonId'   => intval($lessonId),
        );

        $ret = $this->objDaoLessonState->updateByConds($arrConds, $arrFields);

        return $ret;
    }

    /**
     * 获取学生课堂信息
     *
     * @param  int $studentUid 学生uid
     * @param  int $courseId   课程id
     * @param  int $lessonId   课节id
     * @return mix
     */
    public function getLessonState($studentUid, $courseId, $lessonId, $arrFields = array())
    {
        if (intval($studentUid) <= 0 || intval($courseId) <= 0 || intval($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId lessonId:$lessonId]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
            //'courseId'   => intval($courseId),
            'lessonId'   => intval($lessonId),
        );

        $ret = $this->objDaoLessonState->getRecordByConds($arrConds, $arrFields);

        return $ret;
    }
    /** 
     * @param $arrConds
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getLessonStateListByConds($arrConds, $arrFields = array(),$offset = 0, $limit = 20) {
        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }   

        $arrAppends = array(
            "order by id asc",
            "limit $offset, $limit",
        );  

        $ret = $this->objDaoLessonState->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    public function getLessonStateInfoArr($courseId,$lessonId, $classId,$arrFields = array(),$offset = 0, $limit = 0) {
        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds['courseId']       = $courseId;
        $arrConds['lessonId']       = $lessonId;
        $arrConds['classId']        = $classId;

        if($limit > 0){
            $arrAppends = array(
                "limit $offset, $limit",
            );
        }

        $ret = $this->objDaoLessonState->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }
    /** 
     * 获取数量
     * @param $arrConds
     * @return false|int
     */
    public function getLessonStateCntByConds($arrConds) {
        $ret = $this->objDaoLessonState->getCntByConds($arrConds);
        return $ret;
    }
    /**获取出勤人数 
     * @param int $courseId
     * @param int $studentUid
     * @return array
     */
    public function getAttendNumByCourseId($courseId,$studentUid=0,$classId=0,$lessonId = 0){
        if (intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");
            return false;
        }
        //获取学生的课程出勤次数时，不能传入classId和lessonId
        if (intval($studentUid) > 0 && (intval($classId) > 0 || intval($lessonId) > 0)) {
            Bd_Log::warning("Error:[param error], Detail:[classId:$classId lessonId:$lessonId]");
            return false;
        }

        $arrConds = array(
            'courseId'   => intval($courseId),
            'attend'     => self::ATTEND_YES,
        );
        if ($studentUid > 0) {
            $arrConds['studentUid']  = $studentUid;
        }
        elseif ($classId > 0 && $lessonId > 0) {
            $arrConds['classId']  = $classId;
            $arrConds['lessonId'] = $lessonId;
        } else{
            return 0;
        }

        $ret = $this->objDaoLessonState->getCntByConds($arrConds);;
        return $ret;   
    }
    /**获取提交作业数 
     * @param int $courseId
     * @param int $studentUid
     * @return array
     */
    public function getSubmitHomeworkNumByCourseId($courseId,$studentUid=0,$classId=0,$lessonId=0){
        if (intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");
            return false;
        }
        if (intval($studentUid) <= 0 && intval($classId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid classId:$classId]");
            return false;
        }

        $arrConds = array(
            'courseId'   => intval($courseId),
        );
        if (intval($studentUid) > 0) {
            $arrConds['studentUid'] = $studentUid;
        }
        if (intval($classId) > 0) {
            $arrConds['classId'] = $classId;
        }
        if(intval($lessonId) > 0) {
            $arrConds['lessonId'] = $lessonId;
        }
        $arrConds[] = 'homework in ('.Hkzb_Ds_Fudao_ExerciseDetail::STATUS_TOREVIEW.','.Hkzb_Ds_Fudao_ExerciseDetail::STATUS_REVIEWED.','.Hkzb_Ds_Fudao_ExerciseDetail::STATUS_REANSWER.')';
        $ret = $this->objDaoLessonState->getCntByConds($arrConds);
        return $ret;
    }
    /**获取课堂答题正确数
     * @param int $courseId
     * @param int $studentUid
     * @return array
     */
    public function getRightRateByCourseId($courseId,$studentUid){
        if (intval($studentUid) <= 0 || intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId]");
            return false;
        }

        // 修改点：由于tblLessonState表没有 studentUid 和 courseId 的索引
        // 再加上数据量达五千万，导致查询时候卡死，所以改成根据 studentUid 和 lessonId 查询
        $objDsLesson = new Hkzb_Ds_Fudao_Lesson();
        
        $arrLessonConds = array(
            'courseId'=>$courseId,
        );
        $arrLessonFields = array(
            'lessonId'
        );
        // 没必要再查一次cnt，直接查询100条，线上课程不可能一个子课下面有大于100个章节的情况
        $arrLessonList = $objDsLesson->getLessonListByConds($arrLessonConds,$arrLessonFields,0,100);

        // 加一个0，为了防止sql出错
        $arrLessonIds = array(0);
        foreach ($arrLessonList as $lesson) {
            $arrLessonIds[] = intval($lesson['lessonId']);
        }

        $arrFields = array('rightNum','totalNum');
        $arrConds = array(
//            'courseId'   => intval($courseId),
            'lesson_id in ('.implode(',',$arrLessonIds).')',
            'studentUid' => intval($studentUid),
        );
        $ret = $this->objDaoLessonState->getListByConds($arrConds,$arrFields);
        if($ret){
            $right = 0;
            $total = 0;
            $rate  = 0;
            foreach($ret as $list){
		        $right += $list['rightNum'];
                $total += $list['totalNum'];
            }
            $rate = ($total > 0) ? (intval($right)*100)/intval($total) : 0; 
            $ret  = $rate;
        }
        return $ret;
    }

    //新版接口,根据业务而走
    /**
     * 提交课后作业
     *
     * @param  int $studentUid 学生uid
     * @param  int $courseId   课程id
     * @param  int $lessonId   课节id
     * @param  int $status     状态
     * @param  array $extData  扩展
     * @return mix
     */
    public function reviewExercise($studentUid, $courseId, $lessonId,$status,$extData = '')
    {
        if (intval($studentUid) <= 0 || intval($courseId) <= 0 || intval($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId lessonId:$lessonId]");
            return false;
        }

        //将未提交作业的状态置状态
        $arrConds = array(
            'studentUid' => intval($studentUid),
            'courseId'   => intval($courseId),
            'lessonId'   => intval($lessonId),
        );
        $arrFields = array(
            'homework'   => intval($status),
            'extData'    => json_encode($extData),
            'updateTime' => time(),
        );

        $ret = $this->objDaoLessonState->updateByConds($arrConds, $arrFields);

        return $ret;
    }    
     /**
     * 获取学生课堂信息
     *
     * @param  int $studentUid 学生uid
     * @param  int $courseId   课程id
     * @return mix
     */
    public function getLessonStateList($studentUid, $courseId,  $arrFields = array())
    {
        if (intval($studentUid) <= 0 || intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId lessonId:$lessonId]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
            'courseId'   => intval($courseId),
        );

        $ret = $this->objDaoLessonState->getListByConds($arrConds, $arrFields);

        return $ret;
    }
    
    
    
     /**
     * 获取学生课堂信息
     *
     * @param  int $studentUid 学生uid
     * @param  int $courseId   课程id
     * @param  int $lessonId   课节id
     * @return mix
     */
    public function getLessonList($arrConds, $arrFields = array())
    {
       

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $ret = $this->objDaoLessonState->getListByConds($arrConds, $arrFields);

        return $ret;
    }

    /**
     * (新)采用新课后作业系统学生提交作业时调用修改学生课后作业状态使用
     *
     * @param  int $studentUid 学生uid
     * @param  int $lessonId   课节id
     * @return bool
     * note : 不了解慎用
     */
    public function addStudentLessonState($lessonId, $studentUid)
    {
        //参数检查
        if ($lessonId <= 0 || $studentUid <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$lessonId studentUid:$studentUid]");
            return false;
        }
        //获取子课
        $objDsLesson = new Hkzb_Ds_Fudao_Lesson();
        $lessonInfo  = $objDsLesson->getLessonInfo($lessonId, array('courseId'));
        if (empty($lessonInfo)) {
            Bd_Log::warning("Error:[getLessonInfo error], Detail:[lessonId:$lessonId]");
            return false;
        }
        $subCourseId = intval($lessonInfo['courseId']);

        $ret = $this->submitHomework($studentUid, $subCourseId, $lessonId, Hkzb_Ds_Fudao_ExerciseDetail::STATUS_TOREVIEW);
        $retUpdate = $this->setExtData($studentUid, $subCourseId, $lessonId, array('submitTime' => time()));
        if($ret === false || $retUpdate === false){
            Bd_Log::warning("Error:[add homework error], Detail:[studentUid:$studentUid lessonId:$lessonId]");
        }
        else{
            $num= $this->getSubmitHomeworkNumByCourseId($subCourseId, $studentUid);
            if($num > 0) {
                $objLessonAllState = new Hkzb_Ds_Fudao_LessonAllState();
                $ret = $objLessonAllState->addHomeworkNum($studentUid, $subCourseId, $num);
                if($ret === false){
                    Bd_Log::warning("Error:[add homeworkall error], Detail:[studentUid:$studentUid lessonId:$lessonId]");
                    return false;
                } 
            }
        }
        return true;
    }

    /**
     * 更新学生信息（新添加的方法）
     *
     * @param  array$arrConds    
     * @param  array$arrParams   课程属性
     * @return bool true/false
     */
    public function updateLessonstateByConds($arrConds, $arrParams) {
        if(empty($arrConds)) {
            Bd_Log::warning("Error:[param error], Detail:[" . json_encode($arrConds) . "]");
            return false;
        }
        $arrFields = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach($arrParams as $key => $value) {
            if(!in_array($key, $arrAllFields)) {
                continue;
            }
            $arrFields[$key] = $value;
        }
        //ext由ps补全，json处理在ds
        if(isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }
        //补全更新时间
        $arrFields['updateTime'] = time();
        $objDaoLessonstate = new Hkzb_Dao_Fudao_Lessonstate();
        $ret = $objDaoLessonstate->updateByConds($arrConds, $arrFields);

        return $ret;
    }
}
