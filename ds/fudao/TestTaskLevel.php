<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Date: 2018/12/10
 * Time: 16:02
 * 激励奖励查询接口
 */
class Hkzb_Ds_Fudao_TestTaskLevel
{

    private $_objDaoTestTaskLevel;

    const ALL_FIELDS = 'id,uid,courseId,level,extData,createTime,updateTime';

    public function __construct()
    {
        $this->_objDaoTestTaskLevel = new Hkzb_Dao_Fudao_TestTaskLevel();
    }

    public function getStuLevel($intUid = 0, $intCourseId = 0, $arrField = array()) {

        if ($intUid == 0 || $intCourseId == 0) {
            return array();
        }

        if(empty($arrField)) {
            $arrField = explode(',', self::ALL_FIELDS);
        }
        $arrConds = array(
            'uid'      => $intUid,
            'courseId' => $intCourseId,
        );
        $arrAppends = array(
            'order by create_time asc'
        );

        $ret = $this->_objDaoTestTaskLevel->getListByConds($arrConds, $arrField,NULL, $arrAppends);

        if(empty($ret)) {
            return array();
        }

        return end($ret);
    }

    public function addStuLevel($intUid = 0, $intCourseId, $intLevel) {

        if($intUid <=0 || $intCourseId <= 0 || $intLevel <= 0) {
            return false;
        }

        $arrFiels = array(
            'uid'         => $intUid,
            'courseId'    => $intCourseId,
            'level'       => $intLevel,
            'createTime'  => time(),
            'updateTime'  => time(),
        );

        $ret = $this->_objDaoTestTaskLevel->insertRecords($arrFiels);

        if(false === $ret) {
            return false;
        }

        return array('level' => $intLevel);
    }
}