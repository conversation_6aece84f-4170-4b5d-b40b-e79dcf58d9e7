<?php

/**
 * @file   CourseCoupon.php
 * <AUTHOR>
 * @date   2017-06-28
 * @brief  
 *
 **/

class Hkzb_Ds_Fudao_CourseCoupon {
    const ALL_FIELDS = 'id,studentUid,phone,courseId,courseName,teacherName,couponId,couponStatus,price,endTime,mark,createTime,updateTime,opUid,opUname,extData';

    private $_objDaoCourseCoupon;

    public function __construct() {
        $this->_objDaoCourseCoupon = new Hkzb_Dao_Fudao_CourseCoupon();
    }

    public function addCourseCoupon($arrParams) {
        $studentUid = $arrParams['studentUid'];
        $courseId = $arrParams['courseId'];
        if($studentUid <= 0 || $courseId <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId]");
            return false;
        }

        $arrFields = array(
            'studentUid'   => $studentUid,
            'phone'        => isset($arrParams['phone']) ? intval($arrParams['phone']) : 0,
            'courseId'     => $courseId,
            'courseName'   => isset($arrParams['courseName']) ? strval($arrParams['courseName']) : '',
            'teacherName'  => isset($arrParams['teacherName']) ? strval($arrParams['teacherName']) : '',
            'couponId' => isset($arrParams['couponId']) ? intval($arrParams['couponId']) : 0,
            'couponStatus' => 1,
            'price'        => isset($arrParams['price']) ? intval($arrParams['price']) : 0,
            'endTime'      => isset($arrParams['endTime']) ? intval($arrParams['endTime']) : 0,
            'mark'         => isset($arrParams['mark']) ? strval($arrParams['mark']) : '',
            'createTime'   => time(),
            'updateTime'   => time(),
            'opUid'        => isset($arrParams['opUid']) ? intval($arrParams['opUid']) : 0,
            'opUname'      => isset($arrParams['opUname']) ? strval($arrParams['opUname']) : '',
            'extData'      => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
        );

        $ret = $this->_objDaoCourseCoupon->insertRecords($arrFields);
        if(false === $ret) {
            Bd_Log::warning("Error:[insertRecords error], Detail:[" . json_encode($arrFields) . "]");
            return false;
        }

        return true;
    }

    public function useCoupon($couponId) {
        if($couponId <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[couponId:$couponId]");
            return false;
        }

        $arrFields = array(
            'couponStatus' => 2,
        );
        $arrConds = array(
            'couponId' => $couponId,
        );
        $ret = $this->_objDaoCourseCoupon->updateByConds($arrConds, $arrFields);
        if(false === $ret) {
            Bd_Log::warning("Error:[updateByConds error], Detail:[couponId:$couponId]");
            return false;
        }
    }

    public function getCourseCoupon($studentUid, $courseId, $arrFields = array()) {
        if($studentUid <= 0 || $courseId <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId]");
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrConds = array(
            'studentUid' => $studentUid,
            'courseId'   => $courseId,
        );
        $courseCoupon = $this->_objDaoCourseCoupon->getRecordByConds($arrConds, $arrFields);
        if(false === $courseCoupon) {
            Bd_Log::warning("Error:[getRecordByConds error], Detail:[studentUid:$studentUid courseId:$courseId]");
            return false;
        }

        return $courseCoupon;
    }

    public function getCourseCouponCnt($beginTime, $endTime, $opUname = '', $couponStatus = 0) {
        if($beginTime <= 0 || $endTime <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId]");
            return false;
        }

        $arrFields = self::ALL_FIELDS;
        $arrConds = array(
            'createTime' => array($beginTime, '>=', $endTime, '<'),
        );
        if(strlen($opUname) > 0) {
            $arrConds['opUname'] = $opUname;
        }
        if($couponStatus == 1) {
            $arrConds['couponStatus'] = 1;
            $arrConds['endTime'] = array(time(), '>');
        }
        if($couponStatus == 2) {
            $arrConds['couponStatus'] = 2;
        }
        if($couponStatus == 3) {
            $arrConds['couponStatus'] = 1;
            $arrConds['endTime'] = array(time(), '<=');
        }

        $courseCouponCnt = $this->_objDaoCourseCoupon->getCntByConds($arrConds, $arrFields);
        if(false === $courseCouponCnt) {
            Bd_Log::warning("Error:[getCntByConds error], Detail:[studentUid:$studentUid courseId:$courseId]");
            return false;
        }

        return $courseCouponCnt;
    }

    public function getCourseCouponList($beginTime, $endTime, $opUname = '', $couponStatus = 0, $offset = 0, $limit = 0) {
        if($beginTime <= 0 || $endTime <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId]");
            return false;
        }

        $arrFields = explode(',', self::ALL_FIELDS);
        $arrConds = array(
            'createTime' => array($beginTime, '>=', $endTime, '<'),
        );
        if(strlen($opUname) > 0) {
            $arrConds['opUname'] = $opUname;
        }
        if($couponStatus == 1) {
            $arrConds['couponStatus'] = 1;
            $arrConds['endTime'] = array(time(), '>');
        }
        if($couponStatus == 2) {
            $arrConds['couponStatus'] = 2;
        }
        if($couponStatus == 3) {
            $arrConds['couponStatus'] = 1;
            $arrConds['endTime'] = array(time(), '<=');
        }

        $arrAppends = array(
            'order by create_time desc',
        );
        if($limit > 0) {
            $arrAppends[] = "limit $offset, $limit";
        }

        $courseCouponList = $this->_objDaoCourseCoupon->getListByConds($arrConds, $arrFields, null, $arrAppends);
        if(false === $courseCouponList) {
            Bd_Log::warning("Error:[getListByConds error], Detail:[beginTime:$beginTime endTime:$endTime]");
            return false;
        }

        return $courseCouponList;
    }
}
