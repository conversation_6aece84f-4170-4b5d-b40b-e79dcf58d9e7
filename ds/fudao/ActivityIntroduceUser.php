<?php
/**************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 *
 * @file        ActivityIntroduceUser.php
 * <AUTHOR>
 * @create_date 2017-11-22
 * @bref        转介绍用户表ds层
 **/

class Hkzb_Ds_Fudao_ActivityIntroduceUser
{
    //删除状态
    const DELETE_STATUS_OK      = 0;
    const DELETE_STATUS_DELETED = 1;

    //成绩映射数组
    static public $deltedMap = array(
        self::DELETE_STATUS_OK      => '未删',
        self::DELETE_STATUS_DELETED => '已删',
    );

    //成绩类型
    const ACHIEVEMENT_TYPE_A  = 1;
    const ACHIEVEMENT_TYPE_B  = 2;
    const ACHIEVEMENT_TYPE_C  = 3;

    //成绩映射数组
    static public $achievementMap = array(
        self::ACHIEVEMENT_TYPE_A => '名列前茅',
        self::ACHIEVEMENT_TYPE_B => '中等水平',
        self::ACHIEVEMENT_TYPE_C => '偏差',
    );

    //年级数组
    static public $grade = array(
        1 => '一年级',
        2 => '二年级',
        3 => '三年级',
        4 => '四年级',
        5 => '五年级',
        6 => '六年级',
        7 => '初一',
        8 => '初二',
        9 => '初三',
        10 => '高一',
        11 => '高二',
        12 => '高三',
    );

    //将自己的年级映射为作业帮年级
    static public $gradeMap = array(
        1 => 11,
        2 => 12,
        3 => 13,
        4 => 14,
        5 => 15,
        6 => 16,
        7 => 2,
        8 => 3,
        9 => 4,
        10 => 5,
        11 => 6,
        12 => 7,
    );

    //学科
    static public $subject = array(
        1 => '语文',
        2 => '数学',
        3 => '英语',
        4 => '物理',
        5 => '化学',
        6 => '生物',
        7 => '政治',
        8 => '历史',
        9 => '地理',
    );

    const ALL_FIELDS = 'id,introducerUid,introducerdPhone,grade,subject,achievement,deleted,createTime,updateTime,extData';

    private $objDaoActivityIntroduceUser;

    /**
     * 构造函数
     *
     */
    public function __construct()
    {
        $this->objDaoActivityIntroduceUser = new Hkzb_Dao_Fudao_ActivityIntroduceUser();
    }

    /**
     * 新增记录
     *
     * @param  array $arrParams 属性
     * @return bool true/false
     */
    public function addActivityIntroduceUser($arrParams)
    {
        if (intval($arrParams['introducerUid']) <= 0 || strlen($arrParams['introducerdPhone']) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[param: " . json_encode($arrParams) . "]");
            return false;
        }

        $arrFields = array(
            'introducerUid'    => isset($arrParams['introducerUid']) ? intval($arrParams['introducerUid']) : 0,
            'introducerdPhone' => isset($arrParams['introducerdPhone']) ? strval($arrParams['introducerdPhone']) : '',
            'grade'            => isset($arrParams['grade']) ? intval($arrParams['grade']) : 0,
            'subject'          => isset($arrParams['subject']) ? intval($arrParams['subject']) : 0,
            'achievement'      => isset($arrParams['achievement']) ? intval($arrParams['achievement']) : 0,
            'deleted'          => isset($arrParams['deleted']) ? intval($arrParams['deleted']) : self::DELETE_STATUS_OK,
            'createTime'       => time(),
            'updateTime'       => time(),
            'extData'          => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
        );

        $ret = $this->objDaoActivityIntroduceUser->insertRecords($arrFields);

        return $ret;
    }

    /**
     * 获取数量
     * @param $arrConds
     * @return false|int
     */
    public function getActivityIntroduceUserCntByConds($arrConds)
    {
        $ret = $this->objDaoActivityIntroduceUser->getCntByConds($arrConds);

        return $ret;
    }

    /**
     * @param       $arrConds
     * @param array $arrFields
     * @param int   $offset
     * @param int   $limit
     * @return array|false
     */
    public function getActivityIntroduceUserListByConds($arrConds, $arrFields = array(), $offset = 0, $limit = 20)
    {
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrAppends = array(
            "order by create_time desc",
            "limit $offset, $limit",
        );

        $ret = $this->objDaoActivityIntroduceUser->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }
}