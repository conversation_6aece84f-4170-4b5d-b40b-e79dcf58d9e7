<?php
/***************************************************************************
 *
 * Copyright (c) 2017 zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   LessonState.php
 * <AUTHOR>
 * @date   2017/3/13 14:13:18
 * @brief  学生-习题
 *
 **/
class Hkzb_Ds_Fudao_Advanced_LessonState
{
    /**
     * 提交课后作业
     * @param $studentUid
     * @param $lessonId
     * @param $status
     * @return bool|mix
     */
    public function submitHomework($studentUid, $lessonId, $status)
    {
        //参数检查
        if ($lessonId <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$lessonId]");
            return false;
        }

        //获取子课
        $objDsLesson = new Hkzb_Ds_Fudao_Lesson();
        $lessonInfo  = $objDsLesson->getLessonInfo($lessonId, array('courseId'));
        if (empty($lessonInfo)) {
            Bd_Log::warning("Error:[getLessonInfo error], Detail:[lessonId:$lessonId]");
            return false;
        }

        $subCourseId = intval($lessonInfo['courseId']);

        //代理接口
        $objLessonState = new Hkzb_Ds_Fudao_LessonState();
        $ret            = $objLessonState->submitHomework($studentUid, $subCourseId, $lessonId, $status);

        return $ret;
    }

    /**
     * 设置扩展字段
     * @param $studentUid
     * @param $lessonId
     * @param $arrNewExtData
     * @return bool|mix
     */
    public function setExtData($studentUid, $lessonId, $arrNewExtData)
    {
        //参数检查
        if ($lessonId <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$lessonId]");
            return false;
        }

        //获取子课
        $objDsLesson = new Hkzb_Ds_Fudao_Lesson();
        $lessonInfo  = $objDsLesson->getLessonInfo($lessonId, array('courseId'));
        if (empty($lessonInfo)) {
            Bd_Log::warning("Error:[getLessonInfo error], Detail:[lessonId:$lessonId]");
            return false;
        }

        $subCourseId = intval($lessonInfo['courseId']);

        //代理接口
        $objLessonState = new Hkzb_Ds_Fudao_LessonState();
        $ret            = $objLessonState->setExtData($studentUid, $subCourseId, $lessonId, $arrNewExtData);

        return $ret;
    }

    /**
     * 获取提交作业数
     * @param $lessonId
     * @param $studentUid
     * @return array|bool
     */
    public function getSubmitHomeworkNumByCourseId($lessonId, $studentUid)
    {
        //参数检查
        if ($lessonId <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$lessonId]");
            return false;
        }

        //获取子课
        $objDsLesson = new Hkzb_Ds_Fudao_Lesson();
        $lessonInfo  = $objDsLesson->getLessonInfo($lessonId, array('courseId'));
        if (empty($lessonInfo)) {
            Bd_Log::warning("Error:[getLessonInfo error], Detail:[lessonId:$lessonId]");
            return false;
        }

        $subCourseId = intval($lessonInfo['courseId']);

        //代理接口
        $objLessonState = new Hkzb_Ds_Fudao_LessonState();
        $ret            = $objLessonState->getSubmitHomeworkNumByCourseId($subCourseId, $studentUid);

        return $ret;
    }

    /**
     * 答对题目加法
     *
     * @param  int $studentUid 学生uid
     * @param  int $lessonId 课节id
     * @param  int $rightNum 答对数
     * @return bool true/false
     */
    public function addRightNum($studentUid, $lessonId, $rightNum)
    {
        //参数检查
        if ($lessonId <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$lessonId]");
            return false;
        }

        //获取子课
        $objDsLesson = new Hkzb_Ds_Fudao_Lesson();
        $lessonInfo  = $objDsLesson->getLessonInfo($lessonId, array('courseId'));
        if (empty($lessonInfo)) {
            Bd_Log::warning("Error:[getLessonInfo error], Detail:[lessonId:$lessonId]");
            return false;
        }

        $subCourseId = intval($lessonInfo['courseId']);

        //代理接口
        $objLessonState = new Hkzb_Ds_Fudao_LessonState();
        $ret            = $objLessonState->addRightNum($studentUid, $subCourseId, $lessonId, $rightNum);

        return $ret;
    }

    /**
     * 获取课堂答题正确数
     * @param $lessonId
     * @param $studentUid
     * @return array|bool
     */
    public function getRightRateByCourseId($lessonId, $studentUid)
    {
        //参数检查
        if ($lessonId <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$lessonId]");
            return false;
        }

        //获取子课
        $objDsLesson = new Hkzb_Ds_Fudao_Lesson();
        $lessonInfo  = $objDsLesson->getLessonInfo($lessonId, array('courseId'));
        if (empty($lessonInfo)) {
            Bd_Log::warning("Error:[getLessonInfo error], Detail:[lessonId:$lessonId]");
            return false;
        }

        $subCourseId = intval($lessonInfo['courseId']);

        //代理接口
        $objLessonState = new Hkzb_Ds_Fudao_LessonState();
        $ret            = $objLessonState->getRightRateByCourseId($subCourseId, $studentUid);

        return $ret;
    }
    
    /**
     * 获取学生课堂信息
     *
     * @param  int $studentUid 学生uid
     * @param  int $lessonId   课节id
     * @return mix
     */
    public function getLessonState($studentUid, $lessonId, $arrFields = array())
    {
        //参数检查
        if ($lessonId <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$lessonId]");
            return false;
        }

        //获取子课
        $objDsLesson = new Hkzb_Ds_Fudao_Lesson();
        $lessonInfo  = $objDsLesson->getLessonInfo($lessonId, array('courseId'));
        if (empty($lessonInfo)) {
            Bd_Log::warning("Error:[getLessonInfo error], Detail:[lessonId:$lessonId]");
            return false;
        }

        $subCourseId = intval($lessonInfo['courseId']);

        //代理接口
        $objLessonState = new Hkzb_Ds_Fudao_LessonState();
        $ret = $objLessonState->getLessonState($studentUid, $subCourseId, $lessonId, $arrFields);

        return $ret;
    }
}
