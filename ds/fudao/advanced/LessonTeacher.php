<?php
/**
* @file LessonTeacher.php
* <AUTHOR>
* @date 2016/9/18 17:13:18
* @brief 学生的课程
*
**/
class Hkzb_Ds_Fudao_Advanced_LessonTeacher{
    const ALL_FIELDS = 'lessonId,lessonName,courseId,abstract,startTime,stopTime,status,createTime,updateTime,operatorUid,operator,extData,classEssential';
    const ADVANCE_LESSONTEACHER_C_T_KEY_ = 'ADVANCE_LESSONTEACHER_C_T_KEY_';
    const ADVANCE_LESSONTEACHER_L_TI_KEY_ = 'ADVANCE_LESSONTEACHER_L_TI_KEY_';
    /***
     * @param int $lessonId
     * @return bool|mix|mixed|string
     */
    public function getLessonTeacher($lessonId = 0){
        if($lessonId <= 0){
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$lessonId]");
            return false;
        }
        $objMemcached = Hk_Service_Memcached::getInstance('zhiboke');
        $cacheKey = self::ADVANCE_LESSONTEACHER_L_TI_KEY_ . $lessonId;
        $cacheValue = $tmpValue = $objMemcached->get($cacheKey);
        if(!empty($cacheValue)) {
            $cacheValue = json_decode(utf8_encode($cacheValue), true);
            if(json_last_error() !== JSON_ERROR_NONE){
                Bd_Log::warning("Error:[jsondecode err], Detail:[json:$tmpValue],ErrorNum:".json_last_error());
            }else{
                return $cacheValue;
            }
        }
        $arrConds = array(
            'lessonId' => $lessonId,
        );
        //访问Dao
        $objDaoLesson = new Hkzb_Dao_Fudao_Lesson();
        $courseInfo = $objDaoLesson->getRecordByConds($arrConds, array('courseId'));
        $courseId = $courseInfo['courseId'];
        //获取课程teacher信息
        $teacherUid = $this->getTeacherUid($courseId);
        $objDsTeacher = new Hkzb_Ds_Fudao_Teacher();
        $teacherInfo = $objDsTeacher->getTeacherInfo($teacherUid, [], true);
        //缓存写入
        $cacheValue = json_encode($teacherInfo);
        $objMemcached->set($cacheKey, $cacheValue, 60);
        return $teacherInfo;
    }

    /**
     * @param int $courseId
     * @return bool|mixed
     */
    private function getTeacherUid($courseId = 0){
        if($courseId <= 0){
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");
            return false;
        }
        $objMemcached = Hk_Service_Memcached::getInstance('zhiboke');
        $cacheKey = self::ADVANCE_LESSONTEACHER_C_T_KEY_ . $courseId;
        $cacheValue = $objMemcached->get($cacheKey);
        if(intval($cacheValue) > 0) {
            return $cacheValue;
        }
        $arrConds = array(
            'courseId' => $courseId,
        );
        //访问dao
        $objDaoCourse = new Hkzb_Dao_Fudao_Course();
        $courseInfo = $objDaoCourse->getRecordByConds($arrConds, ['teacherUid']);
        $teacherUid = $courseInfo['teacherUid'];
        $objMemcached->set($cacheKey, $teacherUid, 60);
        return $teacherUid;
    }
}