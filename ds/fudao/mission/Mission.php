<?php
/**
 * Created by PhpStorm.
 * User: l<PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * Time: 2017/12/12 17:08
 */

class Hkzb_Ds_Fudao_Mission_Mission
{
    protected $_objDsExamPaper;
    protected $_objDsMissionInfo;
    protected $_objDsMissionDetails;
    protected $_objDsMissionMsgSend;

    protected $_objDsExamPaperStudent;
    protected $_ObjDaoExamPaperStudent;

    protected $_objDsHomework;

    protected $_objDsStudent;
    protected $_objDsCourse;
    protected $_objDsStudentCourse;
    private $_objMemCached;

    // redis缓存KEY
    const REDIS_STUDENT_MISSION_INFO = 'COURSE_STUDENT_MISSION_INFO_UID_%s_MISSIONID_%s'; //学生试卷答题内容
    public function __construct() {

        $this->_objDsLesson            = new Hkzb_Ds_Fudao_Lesson();
        $this->_objDsExamPaper         = new Hkzb_Ds_Fudao_Exam_ExamPaper();
        $this->_objDsMissionInfo       = new Hkzb_Ds_Fudao_Mission_MissionInfo();
        $this->_objDsMissionDetails    = new Hkzb_Ds_Fudao_Mission_MissionDetails();
        $this->_ObjDaoMissionDetails   = new Hkzb_Dao_Fudao_Mission_MissionDetails();
        $this->_objDsExamPaperStudent  = new Hkzb_Ds_Fudao_Exam_ExamPaperStudent();
        $this->_ObjDaoExamPaperStudent = new Hkzb_Dao_Fudao_Exam_ExamPaperStudent();
        $this->_objDsStudent           = new Hkzb_Ds_Fudao_Student();
        $this->_objDsCourse            = new Hkzb_Ds_Fudao_Course();
        $this->_objDsStudentCourse     = new Hkzb_Ds_Fudao_StudentCourse();
        $this->_objDsAdvCourse         = new Hkzb_Ds_Fudao_Advanced_Course();
        $this->_objDsMissionMsgSend    = new Hkzb_Ds_Fudao_Mission_MissionMsgSend();

        $this->_objDsHomework          = new Hkzb_Ds_Homework_Homework();

        $this->_objMemCached                = Hk_Service_Memcached::getInstance("zhiboke");
    }

    // 根据课程ID  获取用户日历列表
    public function getMissionList($courseId,$studentId) {

        if (empty($courseId) || empty($studentId)) {
            return false;
        }

        $ret = array();
        $arrConds    = array(
            'course_id' => $courseId
        );
        //获取课程信息
        $courseInfo = $this->_objDsAdvCourse->getCourseInfo($courseId,array('extLesson'));
        $lessonList = isset($courseInfo['extInfo']['extLesson']['allLesson']) ? $courseInfo['extInfo']['extLesson']['allLesson'] : array();
        $tmpLessonMission = array();
        foreach ($lessonList as $lesson){
            if(!isset($lesson['extData']['stopReason'])){
                $tmpKey[] = date("Y-m-d ",$lesson['startTime']);
                $tmpLessonMission['missionId']       = '';
                $tmpLessonMission['missionType']     = Hkzb_Ds_Fudao_Mission_MissionInfo::MISSION_TYPE_LESSON;
                $tmpLessonMission['missionStart']    = $lesson['startTime'];
                $tmpLessonMission['missionEnd']      = $lesson['stopTime'];
                $tmpLessonMission['lessonId']        = $lesson['lessonId'];
                $ret[]= $tmpLessonMission;
            }

        }

        $missionList = $this->_objDsMissionInfo->getMissionInfoByCourseId($courseId);
        if (empty($missionList)) {
            return $ret;
        }
        $tmpMission = array();
        foreach ($missionList as $mission){
            $missionStart = date("Y-m-d ",$mission['missionStart']);
           // if(!in_array($missionStart,$tmpKey) && $mission['relationStatus'] == Hkzb_Ds_Fudao_Mission_MissionInfo::MISSION_RELATIONSTATUS_BINDING){
            if($mission['relationStatus'] == Hkzb_Ds_Fudao_Mission_MissionInfo::MISSION_RELATIONSTATUS_BINDING){ //去掉当日有课查询不到任务的限制
                $tmpMission['missionId']       = $mission['missionId'];
                $tmpMission['missionType']     = $mission['missionType'];
                $tmpMission['missionStart']    = $mission['missionStart'];
                $tmpMission['missionEnd']      = $mission['missionEnd'];
                $tmpMission['lessonId']        = '';
                $arrConds         =  array(
                    'missionId'   => $mission['missionId'],
                    'studentUid'   => $studentId,
                    'deleted'     => Hkzb_Ds_Fudao_Mission_MissionDetails::RELATION_STATUS_OK
                );
                $studentMission[$mission['missionId']]  =  $this->_objDsMissionDetails->getMissionDetailsRecordByConds($arrConds);
                if(!empty($studentMission[$mission['missionId']])){

                    $tmpMission['studentMission']['isFinish']   =  $studentMission[$mission['missionId']]['isFinish'];
                    $tmpMission['studentMission']['finishTime'] =  $studentMission[$mission['missionId']]['finishTime'];
                }
                $ret[]= $tmpMission;
                unset($tmpMission);
            }else{
                $arrParams = array(
                    'deleted' => Hkzb_Ds_Fudao_Mission_MissionInfo::DELETE

                );
                $this->_objDsMissionInfo->updateMissionInfo($mission['missionId'],$mission['optionId'],$arrParams);
            }

        }


        // 取得列的列表
        foreach ($ret as $key => $value){
            $volume[$key] = $value['missionStart'];
        }

        if(is_array($ret) && empty($ret)){
            array_multisort($volume, SORT_ASC, $ret);
        }


        return $ret;
    }


    /**
     * <AUTHOR>
     * 根据任务id获取题目信息
     * @param  missionId 任务ID
     * @return array|false
     */
    public function getQuestionListByMissionId($missionId,$studentId)
    {
        if (intval($missionId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[missionId:$missionId]");
            return false;
        }


        $arrConds=array(
            'missionType' => Hkzb_Ds_Fudao_Mission_MissionInfo::MISSION_TYPE_PRACTICE,
            'missionId'   => $missionId
        );
        $relatioInfo = $this->_objDsMissionInfo->getRecordByConds($arrConds,array('relationId'));
        if($relatioInfo['relationId'] == 0 ){
            Bd_Log::warning("Error:[param error], Detail:[relationId: null ]");
            return false;
        }
        $questions = array();
        $questions['examId']= $relatioInfo['relationId'];
        $examIds[] = $relatioInfo['relationId'];
        //获取试卷信息
        $examList = $this->_objDsExamPaper->getExamListByExamIds($examIds, array('tidList'));
        // 题库DS层
        $objDsHomework = new Hkzb_Ds_Homework_Homework();


        foreach ($examList as $exam) {
            foreach ($exam['tidList'] as $tid => $list) {//获取试卷下有多少题目
                $tid = isset($list['tid']) ? $list['tid'] : $tid;
                $arrHomeworkInfo = $objDsHomework->getHomeworkDetailByTid($tid);
                $arrQuestion = $arrHomeworkInfo['question'];
                $arrAnswer = $arrHomeworkInfo['answer'];

                //单题
                $tmp = array();
                $tmp['tid'] = $tid;
                $tmp['type'] = $list['tp'];
                $tmp['title'] = $arrQuestion['title'];
                //单选  多选
                if ($list['tp'] == Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_SINGLE || $list['tp'] == Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_MULTIPLE) {
                    $tmp['options'] = $arrQuestion['options'];
                    $tmp['choose'] = $arrQuestion['choose'];
                    $tmp['analysis'] = $arrHomeworkInfo['extContent']['subjectAnalysis'];
                    $tmp['answer'] = $arrAnswer['title'];//增加解答
                }
                //课中 课下
                if ($list['tp'] ==  Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_IN_LESSON_READ  || $list['tp'] == Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_AFTER_LESSON_READ) {
                    $tmp['translation'] = $arrQuestion['translation'];
                    $tmp['image'] = $arrQuestion['image']['name'];
                    $tmp['audioFile'] = $arrQuestion['audioFile']['name'];
                }
                $questions['list'][] = $tmp;
            }
        }
        if(!empty($questions)){
            $arrConds         =  array(
                'missionId'   => $missionId,
                'studentUid'   => $studentId,
                'deleted'     => Hkzb_Ds_Fudao_Mission_MissionDetails::RELATION_STATUS_OK
            );
            $studentMission  =  $this->_objDsMissionDetails->getMissionDetailsRecordByConds($arrConds);
            if(empty($studentMission)){
                $arrInsertFields  = array(
                    'missionId'   => $missionId,
                    'studentUid'  => $studentId,
                    'createTime'   => time(),
                    'updateTime'   => time(),

                );

                $ret = $this->_ObjDaoMissionDetails->insertRecords($arrInsertFields);
                if($ret === false){
                    $lastSql = $this->_ObjDaoMissionDetails->getLastSQL();
                    Bd_Log::warning("Error[Db insert _ObjDaoMissionDetails error] Detail[lastSql: $lastSql]");
                    return false;
                }

            }
        }
        return $questions;
    }


    /**
     * <AUTHOR>
     * 根据任务id获取任务下各题型的数量
     * @param  missionId 任务ID
     * @return array|false
     */
    public function getQuestionCntByMissionId($missionId)
    {
        if (intval($missionId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[missionId:$missionId]");
            return false;
        }
        $arrConds=array(
            'missionType' => Hkzb_Ds_Fudao_Mission_MissionInfo::MISSION_TYPE_PRACTICE,
            'missionId'   => $missionId
        );
        $relatioInfo = $this->_objDsMissionInfo->getRecordByConds($arrConds,array('relationId'));
        if($relatioInfo['relationId'] == 0 ){
            Bd_Log::warning("Error:[param error], Detail:[relationId: null ]");
            return false;
        }
        $exam = $this->_objDsExamPaper->getExamInfoById($relatioInfo['relationId'], array('content'));
        //获取试卷信息
        $num = array();
        if(empty($exam['content']['seqs'])){
            return $num;
        }
        $num['1'] = 0;
        $num['2'] = 0;
        foreach ($exam['content']['seqs'] as  $content) {
            //单选  多选
            if ($content['tp'] == Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_SINGLE || $content['tp'] == Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_MULTIPLE) {
                $num['1'] += $content['cnt'];
            }
            //课中 课下
            if ($content['tp'] ==  Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_IN_LESSON_READ  || $content['tp'] == Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_AFTER_LESSON_READ) {
                $num['2'] += $content['cnt'];
            }



        }
        return $num;
    }



    /**
     * <AUTHOR>
     * 根据任务id获取该任务下学生的完成情况
     * @param  missionId 任务ID
     * @return array|false
     */
    public function getStudentMissonDetailByMissionId($missionId,$limit)
    {

        if (intval($missionId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[missionId:$missionId]");
            return false;
        }

        $arrConds = array(
            'missionId' => $missionId,
        );
        $cont = $this->_objDsMissionDetails->getMissionDetailsCntByConds($arrConds);
        $limit = $limit>0?intval($limit):$cont;
        $missionDetailData = $this->_objDsMissionDetails->getMissionDetailsListByConds($missionId, array('student_uid'),0,$limit);
        $studentList = array();
        foreach ($missionDetailData as $student){
            $studentList['student_uid'][] = $student['student_uid'];
        }
        $studentList['total'] = $cont;
        return $studentList;
    }


    /**
     * <AUTHOR>
     * 根据题目id数组获取题目答案
     * @param  $tIds 题目Ids
     * @return array|false
     */
    public function getQuestionAnswerBytIds($tIds)
    {
        if (intval($tIds) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[tIds:$tIds]");
            return false;
        }

        // 题库DS层
        $objDsHomework = new Hkzb_Ds_Homework_Homework();

        $answer = array();
        foreach ($tIds as $tid) {//获取试卷下有多少题目
            $arrHomeworkInfo = $objDsHomework->getHomeworkDetailByTid($tid);
            $arrQuestion = $arrHomeworkInfo['question'];
            $tmp = array();
            $tmp['tid'] = $tid;
            //单选  多选
            $tmp['choose'] = str_split($arrQuestion['choose']);//$arrQuestion['choose'];
            $answer[] = $tmp;
        }

        return $answer;
    }

    /**
     *<AUTHOR>
     * 保存学生答题信息
     * @param $studentUid
     * @param $taskId
     * @param array $missionDetails
     * @return array
     */
    public function saveStudentAnswerInfo($studentUid,$taskId,$missionDetails = array()) {
        $arrOutput = array(
            'errNo' => 0,
        );
        $studentUid = intval($studentUid);
        $taskId     = intval($taskId);

        $arrConds=array(
            'missionType' => Hkzb_Ds_Fudao_Mission_MissionInfo::MISSION_TYPE_PRACTICE,
            'missionId'   => $taskId
        );
        $relatioInfo = $this->_objDsMissionInfo->getRecordByConds($arrConds,array('relationId','optionId','relationStatus'));
        if($relatioInfo['relationId'] == 0 ){
            $arrOutput['errNo'] = 1;//没有绑定试卷
            Bd_Log::warning("Error:[param error], Detail:[taskId: $taskId]");
            return $arrOutput;
        }

        if($relatioInfo['relationStatus'] == Hkzb_Ds_Fudao_Mission_MissionInfo::MISSION_RELATIONSTATUS_UNBUNDLING){
            $arrOutput['errNo'] = 2;//已解绑
            Bd_Log::warning("Error:[param error], Detail:[taskId: $taskId]");
            return $arrOutput;

        }

        $cacheKey = sprintf(self::REDIS_STUDENT_MISSION_INFO, $studentUid, $taskId);
        $oldCacheValue = $this->_objMemCached->get($cacheKey);
        $missionDetailsData = array();
        if(strlen($oldCacheValue) > 0){
            $missionDetailsData = json_decode($oldCacheValue, true);
        }

        $missionDetailsData[$missionDetails['tid']] = $missionDetails;
        $exTime = 3600 * 24 * 7;
        $cacheValue = json_encode($missionDetailsData);
        $this->_objMemCached->set($cacheKey, $cacheValue, time() + $exTime);

        return $arrOutput;
    }

    /**
     * <AUTHOR>
     * 获取学生答题信息
     * @param  $studentUid $taskId
     * @return array
     */

    public function getStudentAnswerInfo($studentUid,$taskId) {
        $arrOutput  = array(
            'errNo' => 0,
            'data'  => array()
        );
        $cacheKey = sprintf(self::REDIS_STUDENT_MISSION_INFO, $studentUid, $taskId);
        $cacheValue = $this->_objMemCached->get($cacheKey);
        $studentAnswerInfo = json_decode($cacheValue, true);
        if(empty($studentAnswerInfo)){
            $studentUid = intval($studentUid);
            $taskId     = intval($taskId);
            $arrConds=array(
                'missionType' => Hkzb_Ds_Fudao_Mission_MissionInfo::MISSION_TYPE_PRACTICE,
                'missionId'   => $taskId
            );
            $relatioInfo = $this->_objDsMissionInfo->getRecordByConds($arrConds,array('relationId','optionId','relationStatus'));
            if($relatioInfo['relationId'] == 0 ){
                $arrOutput['errNo'] = 1;//没有绑定试卷
                Bd_Log::warning("Error:[param error], Detail:[relationId: null ]");
                return $arrOutput;
            }

            if($relatioInfo['relationStatus'] == Hkzb_Ds_Fudao_Mission_MissionInfo::MISSION_RELATIONSTATUS_UNBUNDLING){
                $arrOutput['errNo'] = 2;//已解绑
                Bd_Log::warning("Error:[param error], Detail:[relationId: null ]");
                return $arrOutput;
            }


            $arrConds = array(
                'examId'     => $relatioInfo['relationId'],
                'studentUid' => $studentUid
            );

            $arrFields = array(
                 'answerInfo'
            );
            $arrStdExamInfo = $this->_ObjDaoExamPaperStudent->getRecordByConds($relatioInfo['relationId'], $arrConds, $arrFields);

        }
        $answerInfo = !empty($studentAnswerInfo)?$studentAnswerInfo:$arrStdExamInfo['answerInfo'];

        $arrOutput['data'] = $answerInfo;
        return $arrOutput;
    }

    /**
     * <AUTHOR>
     * 提交任务
     * @param  $studentUid $taskId
     * @return array
     */
    public function submitMission($studentUid,$taskId) {
        $arrOutput = array(
            'errNo' => 0,
        );
        $studentUid = intval($studentUid);
        $taskId     = intval($taskId);

        $arrConds=array(
            'missionType' => Hkzb_Ds_Fudao_Mission_MissionInfo::MISSION_TYPE_PRACTICE,
            'missionId'   => $taskId
        );
        $relatioInfo = $this->_objDsMissionInfo->getRecordByConds($arrConds,array('relationId','optionId','relationStatus'));
        if($relatioInfo['relationId'] == 0 ){
            $arrOutput['errNo'] = 1;//没有绑定试卷
            Bd_Log::warning("Error:[param error], Detail:[relationId: null ]");
            return $arrOutput;
        }

        if($relatioInfo['relationStatus'] == Hkzb_Ds_Fudao_Mission_MissionInfo::MISSION_RELATIONSTATUS_UNBUNDLING){
            $arrOutput['errNo'] = 2;//已解绑
            Bd_Log::warning("Error:[param error], Detail:[relationId: null ]");
            return $arrOutput;
        }


        $arrConds = array(
            'examId'     => $relatioInfo['relationId'],
            'studentUid' => $studentUid
        );

        $arrFields = array(
            'id', 'examId', 'courseId', 'lessonId', 'studentUid',
            'relationType', 'status', 'answerInfo',
            'extData', 'createTime', 'updateTime'
        );

        $arrStdExamInfo = $this->_ObjDaoExamPaperStudent->getRecordByConds($relatioInfo['relationId'], $arrConds, $arrFields);

        if (!empty($arrStdExamInfo)) {
            // 已存在
            $arrOutput['errNo'] = 4;
            Bd_Log::warning("Error[is  exist record] Detail[examId:{$relatioInfo['relationId']},studentUid:{$studentUid}]");
            return $arrOutput;
        }
        // 学生答题记录
        $arrAnswerInfo = $this->getStudentAnswerInfo($studentUid, $taskId);
        $arrInsertFields  = array(
            'examId'       => $relatioInfo['relationId'],
            'courseId'     => $relatioInfo['relationId'],
            'studentUid'   => $studentUid,
            'relationType' => Hkzb_Ds_Fudao_Mission_MissionInfo::MISSION_TYPE_PRACTICE,
            'answerInfo'   => json_encode($arrAnswerInfo['data']),
            'createTime'   => time(),
            'updateTime'   => time(),

        );

        $ret = $this->_ObjDaoExamPaperStudent->insertRecords($relatioInfo['relationId'], $arrInsertFields);

        if($ret === false){
            $lastSql = $this->_ObjDaoMissionDetails->getLastSQL();
            Bd_Log::warning("Error[Db insert _ObjDaoMissionDetails error] Detail[lastSql: $lastSql]");
            $arrOutput['errNo'] = 3;
            return $arrOutput;
        }


        $arrUpdateConds = array(
            'missionId' => intval($taskId),
            'studentUid' => intval($studentUid)
        );
        $arrUpdate = array(
            'isFinish' => 1,
            'finishTime' => time()
        );
        $ret = $this->_ObjDaoMissionDetails->updateByConds( $arrUpdateConds, $arrUpdate);
        if($ret === false){
            $arrOutput['errNo'] = 3; //更新数据库失败
            $lastSql = $this->_ObjDaoMissionDetails->getLastSQL();
            Bd_Log::warning("Error[Db insert _ObjDaoMissionDetails error] Detail[lastSql: $lastSql]");
        }

        return $arrOutput;
    }

    /**
     * <AUTHOR>
     * 根据任务id获取任务基本信息
     * @param  missionId 任务ID
     * @return array|false
     */
    public function getMissionInfoByMissionId($missionId){
        $arrOutput = array();
        $missionId = intval($missionId);
        if ($missionId <= 0){
            Bd_log::warning("Error[param error'] Detail[missionId: $missionId]");
            return false;
        }

        $missionInfo = $this->_objDsMissionInfo->getMissionInfoByMissionId($missionId);
        if (empty($missionInfo) || $missionInfo['relationStatus'] == Hkzb_Ds_Fudao_Mission_MissionInfo::MISSION_RELATIONSTATUS_UNBUNDLING){
            Bd_log::warning("Error[param error'] Detail[missionId: $missionId  missionStatus: ". $missionInfo['relationStatus'] ."]");
            return false;
        }

        $arrOutput['missionId']      = $missionInfo['missionId'];
        $arrOutput['missionType']    = $missionInfo['missionType'];
        $arrOutput['optionId']       = $missionInfo['optionId'];
        $arrOutput['missionStart']   = $missionInfo['missionStart'];
        $arrOutput['missionEnd']     = $missionInfo['missionEnd'];
        $arrOutput['relationStatus'] = $missionInfo['relationStatus'];

        return $arrOutput;
    }

    /**
     * <AUTHOR>
     * 根据试题ids获取试题信息
     * @param $arrTids
     * @return array|bool
     */
    public function getTidInfoListByTids($arrTids){
        if (empty($arrTids)){
            Bd_log::warning("Error[param error'] Detail[tids: ".json_encode($arrTids)."]");
            return false;
        }

        $arrOutput = array();
        foreach ($arrTids as $tid){
            $tid = intval($tid);
            if ($tid <= 0){
                continue;
            }

            $arrOutput [$tid] = array();
            $tidInfo = $this->_objDsHomework->getHomeworkDetailByTid($tid);
            if (empty($tidInfo)){
                continue;
            }
            $arrQuestion   = $tidInfo['question'];
            $arrAnswer     = $tidInfo['answer'];
            $arrExtContent = $tidInfo['extContent'];

            //format 试题信息
            $tmpQ        = array();
            $tmpQ['tid'] = $tid;

            if (empty($arrQuestion['questionList'])){ //普通题
                $tmpQ['title'] = $arrQuestion['title'];  //试题标题
                if (isset($arrQuestion['translation'])){   //汉语翻译，目前 课下跟读、课中跟读题会有
                    $tmpQ['translation'] = $arrQuestion['translation'];
                }
                if (isset($arrQuestion['options'])){       //选项，针对普通选择题（单选、多选、判断）
                    $tmpQ['options'] = $arrQuestion['options'];
                }
                if (isset($arrQuestion['choose'])){        //选项答案，针对普通选择题（单选、多选、判断）
                    $tmpQ['choose'] = str_split($arrQuestion['choose']);
                }
                if (isset($arrAnswer['title'])){           //解答
                    $tmpQ['answer'] = $arrAnswer['title'];
                }
                if (isset($arrExtContent['subjectAnalysis'])){ //试题解析
                    $tmpQ['analysis'] = $arrExtContent['subjectAnalysis'];
                }
                if ( !empty($arrQuestion['image']) ){         //课下跟读题，上传的图片
                    $tmpQ['image'] = $arrQuestion['image']['name'];
                }
                if ( !empty($arrQuestion['audioFile']) ){     //课下跟读题，音频文件
                    $tmpQ['audioFile'] = $arrQuestion['audioFile']['name'];
                }
            }

            $arrOutput[$tid] = $tmpQ;
        }

        return $arrOutput;
    }

    /**
     * <AUTHOR>
     * 更新学生答案 更新数据库和缓存（db已存在更新db和redis, db不存在只更新redis）
     * @param $arrParams
     * @return array
     */
    public function updateStudentAnswer($arrParams){
        $arrOutput = array(
            'errNo' => 0,
        );

        $studentUid  = intval($arrParams['studentUid']);
        $taskId      = intval($arrParams['taskId']);
        $relationId  = intval($arrParams['relationId']);
        $tid         = intval($arrParams['tid']);


        if ($studentUid <= 0 || $taskId <= 0 || $relationId <= 0 || $tid <= 0 || empty($arrParams['answer'])){
            $arrOutput['errNo'] = -1;//参数错误
            Bd_Log::warning("Error:[param error], Detail:[params:". json_encode($arrParams). "]");
            return $arrOutput;
        }

        $arrConds=array(
            'missionType' => Hkzb_Ds_Fudao_Mission_MissionInfo::MISSION_TYPE_PRACTICE,
            'missionId'   => $taskId
        );
        $relationInfo = $this->_objDsMissionInfo->getRecordByConds($arrConds,array('relationId','optionId','relationStatus'));
        if($relationInfo['relationId'] == 0 ){
            $arrOutput['errNo'] = 1;//没有绑定试卷
            Bd_Log::warning("Error:[param error], Detail:[taskId: $taskId tid: $tid]");
            return $arrOutput;
        }

        if($relationInfo['relationStatus'] == Hkzb_Ds_Fudao_Mission_MissionInfo::MISSION_RELATIONSTATUS_UNBUNDLING
           || $relationId != $relationInfo['relationId']
        ){
            $arrOutput['errNo'] = 2;//已解绑
            Bd_Log::warning("Error:[param error], Detail:[taskId: $taskId, relationId: $relationId]");
            return $arrOutput;
        }

        //更新数据库
        $arrConds = array(
            'examId'     => $relationId,
            'studentUid' => $studentUid
        );
        $arrFields = array(
            'id', 'examId', 'courseId', 'lessonId', 'studentUid',
            'relationType', 'status', 'answerInfo',
            'extData', 'createTime', 'updateTime'
        );

        $stdExamInfo = $this->_ObjDaoExamPaperStudent->getRecordByConds($relationId, $arrConds, $arrFields);
        $answerInfo  = $stdExamInfo['answerInfo'];

        if (!empty($stdExamInfo) && !empty($answerInfo[$tid])) { //已存在 更新数据库
            $newAnswerInfo = $answerInfo;
            $newAnswerInfo[$tid]['answer'] = $arrParams['answer'];
            $strAnswerInfo  = json_encode($newAnswerInfo);
            $arrUpdateConds = array(
                'id' => intval($stdExamInfo['id']),
            );
            $arrUpdate = array(
                'answerInfo' => $strAnswerInfo,
                'updateTime' => time(),
            );
            $ret = $this->_ObjDaoExamPaperStudent->updateByConds($relationId, $arrUpdateConds, $arrUpdate);
            if($ret === false){
                $arrOutput['errNo'] = 3; //更新数据库失败
                Bd_Log::warning("Error[update answer error] Detail[id: {$stdExamInfo['id']} taskId: $taskId examId: $relationId studentUid: $studentUid] tid: $tid");
                return $arrOutput;
            }
        }

        //更新缓存
        $cacheKey      = sprintf(self::REDIS_STUDENT_MISSION_INFO, $studentUid, $taskId);
        $oldCacheValue = $this->_objMemCached->get($cacheKey);
        $missionDetailsData = array();
        if(strlen($oldCacheValue) > 0){
            $missionDetailsData = json_decode($oldCacheValue, true);
        }
        $missionDetailsData[$tid]['answer'] = $arrParams['answer'];

        $exTime = 3600 * 24 * 7;
        $cacheValue = json_encode($missionDetailsData);
        $this->_objMemCached->set($cacheKey, $cacheValue, time() + $exTime);

        return $arrOutput;
    }

    /**
     * <AUTHOR>
     * 根据任务id获取任务状态
     * @param  missionId 任务ID
     * @return int 0 正常 1 未找到 2 已解绑
     */
    public function getMissionStatusByMissionId($missionId){
        $missionStatus = 0;
        $missionId = intval($missionId);
        if ($missionId <= 0){
            Bd_log::warning("Error[param error'] Detail[missionId: $missionId]");
            return false;
        }

        $missionInfo = $this->_objDsMissionInfo->getMissionInfoByMissionId($missionId);
        if(empty($missionInfo) || $missionInfo['deleted'] == Hkzb_Ds_Fudao_Mission_MissionInfo::DELETE){
            $missionStatus = 1;
        }
        if ( $missionInfo['relationStatus'] == Hkzb_Ds_Fudao_Mission_MissionInfo::MISSION_RELATIONSTATUS_UNBUNDLING){
            $missionStatus = 2;
        }



        return $missionStatus;
    }


    /**
     * <AUTHOR>
     * 根据任务id 学生id获取任务状态
     * @param  missionId 任务ID $studentId 学生ID
     * @return array
     */
    public function getStudentMissionInfoByMissionId($missionId, $studentId)
    {

        if (empty($missionId) || empty($studentId)) {
            return false;
        }

        $ret = array();
        $arrConds = array(
            'missionType' => Hkzb_Ds_Fudao_Mission_MissionInfo::MISSION_TYPE_PRACTICE,
            'missionId'   => $missionId,
            'deleted'     => Hkzb_Ds_Fudao_Mission_MissionInfo::DELETE_OK
        );
        $relatioInfo = $this->_objDsMissionInfo->getRecordByConds($arrConds, array());
        if (empty($relatioInfo)) {
            Bd_Log::warning("Error:[param error], Detail:[relationId: null ]");
            return $ret;
        }

        $missionStart        = date("Y-m-d ", $relatioInfo['missionStart']);
        $ret['missionId']    = $relatioInfo['missionId'];
        $ret['missionType']  = $relatioInfo['missionType'];
        $ret['missionStart'] = $relatioInfo['missionStart'];
        $ret['missionEnd']   = $relatioInfo['missionEnd'];
        $arrConds = array(
            'missionId'  => $relatioInfo['missionId'],
            'studentUid' => $studentId,
            'deleted'    => Hkzb_Ds_Fudao_Mission_MissionDetails::RELATION_STATUS_OK
        );
        $studentMission = $this->_objDsMissionDetails->getMissionDetailsRecordByConds($arrConds);
        if (!empty($studentMission)) {

            $ret['studentMission']['isFinish']   = $studentMission['isFinish'];
            $ret['studentMission']['finishTime'] = $studentMission['finishTime'];
        }


        return $ret;
    }

    //根据课程ID、日期（按天）、批量学生Uid，获取学生当日当个课程下的任务完成与推送情况
    public function getStudentMissonDetailByCourseIdDateStudentUids($courseId, $date, $arrStudentUids) {
        if (intval($courseId) <= 0 || intval($date) <= 0 || empty($arrStudentUids)) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId, date:$date]");
            return array();
        }

        $startTime = strtotime($date);
        $stopTime = $startTime + 86400;
        if(empty($startTime) || empty($stopTime)){
            return array();
        }

        $missionList = $this->_objDsMissionInfo->getMissionInfoByCourseId($courseId);
        if($missionList === false || empty($missionList)){
            return array();
        }

        $missionId = 0;
        foreach ($missionList as $missionData) {
            if($missionData['missionStart'] >= $startTime && $missionData['missionStart'] < $stopTime){
                $missionId = intval($missionData['missionId']);
            }
        }
        if(empty($missionId)){
            return array();
        }

        $arrConds = array(
            'missionId' => $missionId,
        );
        $count = $this->_objDsMissionDetails->getMissionDetailsCntByConds($arrConds);
        $missionDetailList = $this->_objDsMissionDetails->getMissionDetailsListByConds($arrConds, array('studentUid', 'isFinish','finishTime'), 0, intval($count));

        $arrConds = array(
            'missionId'     => $missionId,
            'msgSendStatus' => Hkzb_Ds_Fudao_Mission_MissionMsgSend::MSG_SEND_STATUS_DONE,
        );
        $count = $this->_objDsMissionMsgSend->getMsgSendCntByConds($arrConds);
        $missionSendList = $this->_objDsMissionMsgSend->getMsgSendListByConds($arrConds, array('studentUid', 'missionId', 'msgSendStatus'), 0, intval($count));

        $arrOutput = array();
        foreach ($missionDetailList as $missionDetail) {
            if(isset($arrOutput[$missionDetail['studentUid']])){
                $arrOutput[$missionDetail['studentUid']]['isFinish'] = $missionDetail['isFinish'];
                $arrOutput[$missionDetail['studentUid']]['finishTime'] = $missionDetail['finishTime'];
            } else{
                $arrOutput[$missionDetail['studentUid']] = array(
                    'isFinish'   => $missionDetail['isFinish'],
                    'finishTime' => $missionDetail['finishTime'],
                    'sendStatus' => 0,
                );
            }
        }
        foreach ($missionSendList as $sendDetail) {
            if(isset($arrOutput[$sendDetail['studentUid']])){
                $arrOutput[$sendDetail['studentUid']]['sendStatus'] = $sendDetail['msgSendStatus'];
            } else{
                $arrOutput[$sendDetail['studentUid']] = array(
                    'isFinish'   => 0,
                    'finishTime' => 0,
                    'sendStatus' => $sendDetail['msgSendStatus'],
                );
            }
        }

        return $arrOutput;
    }

    //单个学员、单个课程，获取当前学生当个课程所有任务的完成情况
    public function getStudentMissonFinishStatusByCourseIdStudentUid($courseId, $studentUid) {
        if (intval($courseId) <= 0 || intval($studentUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId, studentUid:$studentUid]");
            return array();
        }

        $missionList = $this->_objDsMissionInfo->getMissionInfoByCourseId($courseId);
        if($missionList === false || empty($missionList)){
            return array();
        }

        $arrMissionIds = array();
        $arrMissionData = array();
        foreach ($missionList as $missionData) {
            if(intval($missionData['missionId']) > 0){
                $arrMissionIds[] = intval($missionData['missionId']);
                $arrMissionData[$missionData['missionId']] = $missionData;
            }
        }
        if(empty($arrMissionIds)){
            return array();
        }

        $arrConds = array(
            'studentUid' => $studentUid,
            'mission_id in ('.implode(',', $arrMissionIds).')',
        );
        $count = $this->_objDsMissionDetails->getMissionDetailsCntByConds($arrConds);
        $missionDetailList = $this->_objDsMissionDetails->getMissionDetailsListByConds($arrConds, array('missionId', 'isFinish'), 0, intval($count));

        $arrOutput = array();
        foreach ($missionDetailList as $missionDetail) {
            $arrOutput[] = array(
                'missionId' => $missionDetail['missionId'],
                'missionStart' => $arrMissionData[$missionDetail['missionId']]['missionStart'],
                'isFinish' => $missionDetail['isFinish'],
            );
        }

        return $arrOutput;
    }

    //单个学员、单个课程、单个日期，学员任务对应试卷的完成详情
    public function getMissonExamDetailByCourseIdDateStudentUid($courseId, $date, $arrStudentUids) {
        if (intval($courseId) <= 0 || intval($date) <= 0 || empty($arrStudentUids)) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId, date:$date]");
            return array();
        }

        $startTime = strtotime($date);
        $stopTime = $startTime + 86400;
        if(empty($startTime) || empty($stopTime)){
            return array();
        }

        $missionList = $this->_objDsMissionInfo->getMissionInfoByCourseId($courseId);
        if($missionList === false || empty($missionList)){
            return array();
        }

        $missionId = 0;
        $examId = 0;
        foreach ($missionList as $missionData) {
            if($missionData['missionStart'] >= $startTime && $missionData['missionStart'] < $stopTime){
                $missionId = intval($missionData['missionId']);
                $examId = intval($missionData['relationId']);
            }
        }
        if(empty($missionId) || empty($examId)){
            return array();
        }

        $examPaperInfo = $this->_objDsExamPaper->getExamPaperInfoByExamId($examId);
        if(empty($examPaperInfo)){
            return array();
        }

        $studentAnswerInfo = $this->_objDsExamPaperStudent->getMultiStudentsAnswerInfoById($examId, $arrStudentUids);
        if(!empty($studentAnswerInfo) || is_array($studentAnswerInfo)){
            $examPaperInfo['answerInfo'] = $studentAnswerInfo;
        } else{
            $examPaperInfo['answerInfo'] = array();
        }

        return $examPaperInfo;
    }

    //多学员、单个课程、单个日期，向前n次的试卷完成情况（不包括所填日期）
    public function getMissonExamFinishTimesByCourseIdDateTimesStudentUid($courseId, $date, $times, $arrStudentUids) {
        if (intval($courseId) <= 0 || intval($date) <= 0 || intval($times) <= 0 || empty($arrStudentUids)) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId, date:$date, times:$times]");
            return array();
        }

        $startTime = strtotime($date);
        $arrConds = array(
            'optionId' => $courseId,
            'relationStatus'    =>  1,
            'mission_start<'.$startTime,
        );
        $missionList = $this->_objDsMissionInfo->getMissionInfoListByCondsOrderByStartDesc($arrConds, array(), 0, intval($times));
        if($missionList === false || empty($missionList)){
            return array();
        }
        $realTimes = count($missionList);

        $origFinishList = array();
        foreach ($missionList as $missionData) {
            $examId = intval($missionData['relationId']);
            if(empty($examId)){
                continue;
            }
            $studentAnswerInfo = $this->_objDsExamPaperStudent->getMultiStudentsAnswerInfoById($examId, $arrStudentUids);
            foreach ($studentAnswerInfo as $answerInfo) {
                if($answerInfo['studentUid'] > 0){
                    $origFinishList[$answerInfo['studentUid']] += 1;
                }
            }
        }

        $finishList = array();
        foreach ($arrStudentUids as $studentUid) {
            $finishList[$studentUid] = array(
                'studentUid' => $studentUid,
                'examTimes' => intval($origFinishList[$studentUid]),
                'totalTimes' => intval($realTimes),
            );
        }

        return $finishList;
    }
}