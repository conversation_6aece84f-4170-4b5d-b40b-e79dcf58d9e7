<?php

/**
 * @file   CourseExamPaper.php
 * <AUTHOR>
 * @date   2017/4/13
 * @brief  课程，章节与试卷的绑定关系
 *
 **/
class Hkzb_Ds_Fudao_CourseExamPaper {
    //状态
    const STATUS_OK      = 0; //未删除
    const STATUS_DELETED = 1; //已删除
    static $STATUS_ARRAY = array(
        self::STATUS_OK      => '未删除',
        self::STATUS_DELETED => '已删除',
    );

    const CURL_URL = 'http://platmis.zuoyebang.cc/tikumis/examapi/';//题库线上url

    const ALL_FIELDS = 'examPaperId,examPaperName,grade,subject,deleted,lessonId,createTime,updateTime,operatorUid,operator,extData,courseId,examTimeType,passScore,maxTryNum';

    private $objDaoCourseExamPaper;

    /**
     * 构造函数
     *
     */
    public function __construct() {
        $this->objDaoCourseExamPaper = new Hkzb_Dao_Fudao_CourseExamPaper();
    }

    /**
     * 新增课程
     *
     * @param  array  $arrParams 课程属性
     * @return bool true/false
     */
    public function addCourseExamPaper($arrParams) {
        if(intval($arrParams['examPaperId']) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[param: ". json_encode($arrParams). "]");
            return false;
        }

        $arrFields = array(
            'examPaperId'   => intval($arrParams['examPaperId']),
            'examPaperName' => isset($arrParams['examPaperName']) ? strval($arrParams['examPaperName']) : '',
            'grade'         => isset($arrParams['grade']) ? intval($arrParams['grade']) : 0,
            'subject'       => isset($arrParams['subject']) ? intval($arrParams['subject']) : 0,
            'deleted'       => self::STATUS_OK, //初始状态为未删除
            'lessonId'      => 0, 
            'createTime'    => time(),
            'updateTime'    => time(),
            'operatorUid'   => isset($arrParams['operatorUid']) ? intval($arrParams['operatorUid']) : 0,
            'operator'      => isset($arrParams['operator']) ? strval($arrParams['operator']) : '',
            'extData'       => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
        );
        $ret = $this->objDaoCourseExamPaper->insertRecords($arrFields);
        return $ret;
    }

    /**
     * 更新试卷
     *
     * @param  int $examPaperId  试卷id
     * @param  array $arrParams 试卷属性
     * @return bool true/false
     */
    public function updateCourseExamPaper($examPaperId, $arrParams){
        if (intval($examPaperId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[examPaperId:$examPaperId]");

            return false;
        }

        $arrConds = array(
            'examPaperId' => intval($examPaperId),
        );

        $arrFields    = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach ($arrParams as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }

            $arrFields[$key] = $value;
        }

        //ext由ps补全，json处理在ds
        if (isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }

        //补全更新时间
        $arrFields['updateTime'] = time();

        $ret = $this->objDaoCourseExamPaper->updateByConds($arrConds, $arrFields);

        return $ret;
    }
 
    /**
     * 删除试卷
     *
     * @param  int  $examPaperId  试卷id
     * @param  int  $operatorUid  操作人id
     * @param  int  $operator  操作人
     * @return bool true/false
     */
    public function deleteCourseExamPaper($examPaperId, $operatorUid = 0, $operator = '') {
        if(intval($examPaperId) <= 0 || intval($operatorUid) <= 0 || strlen($operator) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[examPaperId:$examPaperId operatorUid:$operatorUid operator:$operator]");
            return false;
        }

        $arrConds = array(
            'examPaperId' => intval($examPaperId),
        );

        $arrFields = array(
            'deleted'     => self::STATUS_DELETED,
            'updateTime'  => time(),
            'operatorUid' => intval($operatorUid),
            'operator'    => strval($operator),
        );

        $ret = $this->objDaoCourseExamPaper->updateByConds($arrConds, $arrFields);

        return $ret;
    }

    /**
     * 获取试卷详情
     *
     * @param  int  $examPaperId  试卷id
     * @param  mix  $arrFields 指定属性
     * @return mix
     */
    public function getCourseExamPaperInfo($examPaperId, $arrFields = array()) {
        if(intval($examPaperId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[examPaperId:$examPaperId]");
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'examPaperId' => intval($examPaperId),
            'deleted'      => self::STATUS_OK,
        );

        $ret = $this->objDaoCourseExamPaper->getRecordByConds($arrConds, $arrFields);
        return $ret;

    }

    /**
     * @param $arrConds
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getCourseExamPaperListByConds($arrConds, $arrFields = array(), $offset = 0, $limit = 20) {
        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrAppends = array(
            "order by create_time desc",
            "limit $offset, $limit",
        );

        $ret = $this->objDaoCourseExamPaper->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /**
     * 获取数量
     * @param $arrConds
     * @return false|int
     */
    public function getCourseExamPaperCntByConds($arrConds) {
        $ret = $this->objDaoCourseExamPaper->getCntByConds($arrConds);
        return $ret;
    }

}