<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file StudentMedal.php
 * <AUTHOR>
 * @date 2015/11/17 14:13:18
 * @brief 学生勋章
 *  
 **/

class Hkzb_Ds_Fudao_StudentMedal {
   
    //学生获取勋章规则
    //  答对题目数 =》勋章ID,
    public static $getMedalRule = array(
        '1' => '1',    
        '2' => '2',    
        '3' => '3',    
        '4' => '4',    
        '5' => '5',    
    );

    //答对说明
    //勋章ID=》勋章说明
    public static $introduce = array(
        '1' => "在本节课中，累计答对1题可获得",    
        '2' => "在本节课中，累计答对2题可获得",
        '3' => "在本节课中，累计答对3题可获得",
        '4' => "在本节课中，累计答对4题可获得",
        '5' => "在本节课中，累计答对5题可获得",
    );

    const ALL_FIELDS = 'id,medalId,studentUid,courseId,lessonId,createTime,updateTime,extData';

    private $objDaoStudentMedal;

    /**
     * 构造函数
     *
     */
    public function __construct() {
        $this->objDaoStudentMedal = new Hkzb_Dao_Fudao_StudentMedal();
    }

    /**
     * 新增学生获得勋章
     *
     * @param  array  $arrParams 课程属性
     * @return bool true/false
     */
    public function addStudentMedal($arrParams) {
        if(intval($arrParams['medalId']) <= 0 || intval($arrParams['studentUid']) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[param: ". json_encode($arrParams). "]");
            return false;
        }   
        
        $arrFields = array(
            'medalId'           => isset($arrParams['medalId']) ? intval($arrParams['medalId']) : 0,
            'studentUid'        => isset($arrParams['studentUid']) ? intval($arrParams['studentUid']) : 0,
            'courseId'          => isset($arrParams['courseId']) ? intval($arrParams['courseId']) : 0,
            'lessonId'          => isset($arrParams['lessonId']) ? intval($arrParams['lessonId']) : 0,
            'createTime'        => time(),
            'updateTime'        => time(),
            'extData'           => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
        );  
        
        $ret = $this->objDaoStudentMedal->insertRecords($arrFields);
        
        return $ret;
    } 




    /**获取某个学生某节课获得勋章列表
     * @param int $studentUid
     * @param int $courseId
     * @param int $lessonId
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return list
     */
    public function getMedalListByStudentUid($studentUid,$courseId,$lessonId,$medalId=0,$arrFields = array(), $offset = 0, $limit = 20) {

        if(intval($courseId) <= 0 || intval($studentUid) <= 0 || strlen($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error],Detail:[courseId:$courseId studentUid:$studentUid lessonId:$lessonId]");
            return false;
        }


        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrConds = array(
            'studentUid' => $studentUid,
            'courseId'   => $courseId,
            'lessonId'   => $lessonId,
        );

        if(intval($medalId) > 0){
            $arrConds['medalId'] = intval($medalId);
        }

        $arrAppends = array(
            "order by createTime desc",
            "limit $offset, $limit",
        );

        $ret = $this->objDaoStudentMedal->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /**获取某个学生某节课获得勋章列表总数
     * @param int $studentUid
     * @param int $courseId
     * @param int $lessonId
     * @return count
     */
    public function getMedalCntByStudentUid($studentUid,$courseId,$lessonId) {
        if(intval($courseId) <= 0 || intval($studentUid) <= 0 || strlen($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error],Detail:[courseId:$courseId studentUid:$studentUid lessonId:$lessonId]");
            return false;
        } 
        $arrConds = array(
            'studentUid' => $studentUid,
            'courseId'   => $courseId,
            'lessonId'   => $lessonId,
        );    

        $ret = $this->objDaoStudentMedal->getCntByConds($arrConds);

        return $ret;
    }
}
