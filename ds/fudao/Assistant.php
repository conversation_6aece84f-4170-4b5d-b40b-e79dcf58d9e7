<?php

/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Date: 2017/11/15
 * Time: 上午11:41
 */

class Hkzb_Ds_Fudao_Assistant
{

    /*
     * 转换私人id到业务id
     * @param $personUid
     * @return int|bool
     */

    const NOW_SEASON_YEAR = 2019;   // 默认当前学年
    const NOW_LEARN_SEASON_ID = 4;     // 默认当前学季

    public static function getAssistantUidByPersonUid($personUid = 0, $courseId = 0)
    {

        $personUid = intval($personUid);
        $courseId  = intval($courseId);
        if (empty($personUid)) {
            Bd_Log::warning("[getAssistantUidByPersonUid] Param false! personUid:{$personUid},courseId:{$courseId}");

            return false;
        }

        // if (!Hk_Util_Ip::isInnerIp()) {

        //     Bd_Log::warning("[getAssistantUidByPersonUid] not inNetWork");

        //     return false;
        // }

        $db = Hk_Service_Db::getDB('bzr/bzr_fudao');
        //处理报警
        if (empty($db)) {
            Bd_Log::warning("[getAssistantUidByPersonUid] db error, Db init false!");

            return false;
        }

        // 通过courseId获取学季
        $seasonMap = array(
            '春' => 1,
            '暑' => 2,
            '秋' => 3,
            '寒' => 4,
        );

        if ($courseId) {
            $courseSql
                        = "select season_year, season, season_num from tblAssistantNewCourse where course_id = $courseId and deleted = 0";
            $courseInfo = $db->query($courseSql);
            if ($courseInfo === false || empty($courseInfo[0])) {
                return false;
            }
            $courseInfo = $courseInfo[0];
        }


        $seasonYear    = $courseInfo['season_year'] ? intval($courseInfo['season_year']) : self::NOW_SEASON_YEAR;
        $learnSeasonId = $seasonMap[$courseInfo['season']] ? $seasonMap[$courseInfo['season']]
            : self::NOW_LEARN_SEASON_ID;
        if (in_array($learnSeasonId, array(1, 3))) {
            $learnSeasonId = intval($learnSeasonId.$courseInfo['season_num']);
        }

        //        $sql = 'select teacher_uid from tblAssistantP2BRelation where deleted = 0 and person_uid='.$personUid.' and season_year='.self::NOW_SEASON_YEAR.' and learn_season_id='.self::NOW_LEARN_SEASON_ID;
        $sql = 'select teacher_uid from tblAssistantP2BRelation where deleted = 0 and person_uid='.$personUid
            .' and season_year='.$seasonYear.' and learn_season_id='.$learnSeasonId.' and bind_type=1';


        $ret = $db->query($sql);

        if ($ret === false || empty($ret[0]['teacher_uid'])) {
            Bd_Log::warning("[getAssistantUidByPersonUid] data empty,".json_encode($ret));

            return false;
        }

        return intval($ret[0]['teacher_uid']);
    }
}
