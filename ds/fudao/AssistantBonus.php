<?php

/**
 * @file   AssistantBonus.php
 * <AUTHOR>
 * @date   2015-05-08
 * @brief  老师奖金
 *
 **/
class Hkzb_Ds_Fudao_AssistantBonus{
    const BONUS_ATTEND      = 1;
    const BONUS_HOMEWORK    = 2;
    const BONUS_CONTINUE    = 3;
    
    const BONUS_LONG_PRIVATE = 1;
    const BONUS_LONG_PRIVATE_MANAGER = 2;
    public static $ARR_BONUS_TYPE = array(
        self::BONUS_LONG_PRIVATE         => '班课一线人员绩效',
        self::BONUS_LONG_PRIVATE_MANAGER => '班课管理组绩效',
    );
    const ALL_FIELDS = 'id,assistantUid,packCourseId,courseId,lessonId,classId,groupId,type,learnSeason,attendTotal,attendCnt,attendRate,attendBonus,homeworkTotal,homeworkCnt,homeworkRate,homeworkBonus,continueTotal,continueCnt,continueRate,continueBonus,createTime,updateTime,extData,personUid';

    private $_objDaoAssistantBonus;

    public function __construct() {
        $this->_objDaoAssistantBonus = new Hkzb_Dao_Fudao_AssistantBonus();
    }

    public function addAssistantBonus($arrParams) {
        if (empty($arrParams['assistantUid'])) {
            Bd_Log::warning("Error:[param error], Detail:[param: " . json_encode($arrParams) . "]");
            return false;
        }

        $arrFields = array(
            'assistantUid'  => intval($arrParams['assistantUid']),
            'packCourseId'  => intval($arrParams['packCourseId']),
            'courseId'      => intval($arrParams['courseId']),
            'lessonId'      => intval($arrParams['lessonId']),
            'classId'       => intval($arrParams['classId']),
            'groupId'       => intval($arrParams['groupId']),
            'type'          => intval($arrParams['type']),
            'learnSeason'   => strval($arrParams['learnSeason']),
            'attendTotal'   => intval($arrParams['attendTotal']),
            'attendCnt'     => intval($arrParams['attendCnt']),
            'attendRate'    => intval($arrParams['attendRate']),
            'attendBonus'   => intval($arrParams['attendBonus']),
            'homeworkTotal' => intval($arrParams['homeworkTotal']),
            'homeworkCnt'   => intval($arrParams['homeworkCnt']),
            'homeworkRate'  => intval($arrParams['homeworkRate']),
            'homeworkBonus' => intval($arrParams['homeworkBonus']),
            'continueTotal' => intval($arrParams['continueTotal']),
            'continueCnt'   => intval($arrParams['continueCnt']),
            'continueRate'  => intval($arrParams['continueRate']),
            'continueBonus' => intval($arrParams['continueBonus']),
            'createTime'    => time(),
            'updateTime'    => 0,
            'extData'       => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '[]',
            'personUid'     => intval($arrParams['personUid']),
        );

        $ret = $this->_objDaoAssistantBonus->insertRecords($arrFields);
        if ($ret === false) {
            return false;
        }

        return $this->_objDaoAssistantBonus->getInsertId();
    }

    public function updateAssistantBonus($arrConds, $arrParams) {
        if (empty($arrConds) || empty($arrParams)) {
            Bd_Log::warning("Error:[param error], Detail:[conds:$arrConds arrParams:$arrParams]");
            return false;
        }

        $data = $this->_objDaoAssistantBonus->getRecordByConds($arrConds, array('extData'));
        if(!$data) {
            Bd_Log::warning("Error:[param error], Detail:[" . json_encode($arrConds) . "]");
            return false;
        }
        $arrFields = array();

        $extData = $data['extData'];
        if (isset($arrParams['extData']) && is_array($arrParams['extData'])) {
            foreach ($arrParams['extData'] as $key => $value) {
                $extData[$key] = $value;
            }
            $arrFields['extData'] = json_encode($extData);
            unset($arrParams['extData']);
        }

        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach ($arrParams as $key => $value) {
            if (!$value && !in_array($key, $arrAllFields)) {
                continue;
            }
            $arrFields[$key] = $value;
        }
        $arrFields['updateTime'] = time();
        $ret = $this->_objDaoAssistantBonus->updateByConds($arrConds, $arrFields);

        if ($ret === false) {
            Bd_Log::warning("Error:[update error], Detail:[" . json_encode($arrConds) . "]");
            return false;
        }

        return true;
    }

    public function getAssistantBonusInfo($bonusId, $arrFields = array()) {
        if (intval($bonusId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[bonusId:$bonusId]");
            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrConds = array(
            'id' => intval($bonusId),
        );

        $ret = $this->_objDaoAssistantBonus->getRecordByConds($arrConds, $arrFields);
        return $ret;
    }

    public function getAssistantBonusListByConds($arrConds, $arrFields = array(), $offset = 0, $limit = 20) {
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrAppends = array(
            //"order by create_time desc",
            "limit $offset, $limit",
        );
        $ret = $this->_objDaoAssistantBonus->getListByConds($arrConds, $arrFields, NULL, $arrAppends);
        return $ret;
    }

    /**
     * 获取总记录数
     *
     * @param       $arrConds
     * @return array|false
     */
    public function getAssistantBonusCnt($arrConds) {
        $ret = $this->_objDaoAssistantBonus->getCntByConds($arrConds);
        return $ret;
    }

    /**
     * 获取辅导教师分组
     *
     * @param       $assistantUid
     * @return array|false
     */
    public function getAssistantGroup($assistantUid, $learnSeason = '', $type = self::BONUS_LONG_PRIVATE) {
         if (intval($assistantUid) <= 0 || intval($type) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[assistantUid:$assistantUid type:$type]");
            return false;
        }

        $arrFields = array('groupId');
        $arrConds = array(
            'assistantUid'  => strval($assistantUid),
            'type'          => $type,
        );
        if(!empty($learnSeason)){
            $arrConds['learnSeason'] = strval($learnSeason);
        }
        $arrAppends = array(
            "order by create_time desc ",
            "limit 0, 1",
        );

        $ret = $this->_objDaoAssistantBonus->getListByConds($arrConds, $arrFields, NULL, $arrAppends);
        return $ret[0]['groupId'];
    }
    
    /**
     * 获取当季续报奖金计算范围内课程
     *
     * @param   $learnSeason
     * @return array|false
     */
    public function getSeasonCourseList($learnSeason, $type = self::BONUS_LONG_PRIVATE) {
         if (empty($learnSeason)  || intval($type) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[learnSeason:$learnSeason type:$type]");
            return false;
        }

        $arrFields = array('assistantUid','packCourseId','courseId','classId','groupId','type','learnSeason','extData');
        $arrConds = array(
            'learnSeason'   => strval($learnSeason),
            'type'          => $type,
        );
        $arrAppends = array(
			"group by pack_course_id,assistant_uid",
            "order by create_time desc",
        );

        $ret = $this->_objDaoAssistantBonus->getListByConds($arrConds, $arrFields, NULL, $arrAppends);
        return $ret;
    }
    
    /**
     * 获取多个季度需要计算续报奖金的课程
     *
     * @param   $learnSeasonSql
     * @return array|false
     */
    public function getCourseListBySeasons($learnSeasonSql, $type = self::BONUS_LONG_PRIVATE) {
         if (empty($learnSeasonSql) || intval($type) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[learnSeasonSql:$learnSeasonSql type:$type]");
            return false;
        }

        $arrFields = array('assistantUid','packCourseId','courseId','classId','groupId','type','learnSeason','extData');
        $arrConds = array(
            'learn_season in ('.$learnSeasonSql.')',
            'type'  => $type,
        );
        $arrAppends = array(
			"group by pack_course_id,assistant_uid",
            "order by create_time desc",
        );

        $ret = $this->_objDaoAssistantBonus->getListByConds($arrConds, $arrFields, NULL, $arrAppends);
        return $ret;
    }
}
