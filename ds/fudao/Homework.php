<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Homework.php
 * <AUTHOR>
 * @date 2015/11/17 14:13:18
 * @brief 习题
 *  
 **/

class Hkzb_Ds_Fudao_Homework {
    const ALL_FIELDS = 'tid,content,gradeId,subjectId,channel,deleted,createTime,extData';

    private $objDaoHomework;

    /**
     * 构造函数
     *
     */
    public function __construct() {
        $this->objDaoHomework = new Hkzb_Dao_Fudao_Homework();
    }

    /**
     * 获取习题详情
     *
     * @param  int $tid     习题id
     * @return mix
     */
    public function getHomeworkById($tid, $arrFields = array()) {
        if(intval($tid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[tid:$tid]");
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'tid' => $tid,    
        );

        $ret = $this->objDaoHomework->getRecordByConds($arrConds, $arrFields);
        
        return $ret;
    }
}
