<?php
/**
 * Copyright (c) 2017 zuoyebang.com, Inc. All Rights Reserved
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @file: UserScoreLog.php
 * @date: 2017/5/25
 * @time: 14:36
 * @desc:
 */

class Hkzb_Ds_Fudao_UserScoreLog
{
    const TYPE_TUTOR_ADD                 = 0; //后台补加
    const TYPE_ATTEND_CLASS              = 1; //进入教室
    const TYPE_EXIT_CLASS                = 2; //离开教室
    const TYPE_PRAISE                    = 3; //表扬
    const TYPE_SUBMIT_EXERCISE_INCLASS   = 4; //回答课堂互动题
    const TYPE_SUBMIT_EXERCISE_SUFCLASS  = 5; //提交课后作业
    const TYPE_CORRECT_EXERCISE          = 6; //作业被批改
    const TYPE_RECEIVE_RED_ENVELOPE      = 7;//领取学分红包
    const TYPE_ADD_ONLINE_SCORE          = 8;//在线时长加分
    const TYPE_SUBMIT_INCLASSSIGN        = 9; //签到
    const TYPE_CONSUME_SCORE             = 10;//消耗学分
    const TYPE_RANK_AWARD_SCORE          = 11;//排行榜奖励学分

    const TYPE_FINISH_LESSON             = 12;//领取完课星
    const TYPE_FINISH_EXERCISE           = 13;//领取全部答题星
    const TYPE_FINISH_EXAM               = 14;//领取答题85分以上星
    const TYPE_ENGLISH_PRECLASS_EXERCISE = 15;//领取英语预习跟读
    const TYPE_ENGLISH_POSCLASS_EXERCISE = 16;//领取英语课后复习
    const TYPE_ENGLISH_INCLASS_EXERCISE = 17;//领取英语课后复习
    const TYPE_SURVEY_INCLASS_SUBMIT     = 18;//下课评价
    const TYPE_DAILY_PRACTICE_SHARE      = 19;//每日一练打卡分享加3学分
    const TYPE_DAILY_PRACTICE_GIFT       = 20;//每日一练7天奖励21学分
    const TYPE_ACTIVITY                    = 21; //活动加分
    const TYPE_STAGE_TEST                 = 22; //阶段测试加学分
    const TYPE_CLASS_TEST                 = 23; // 堂堂测加分
    const TYPE_INCLASS_SIGN_LITTLE_MATH   = 24; // 小学数学签到
    const TYPE_XX_MATH_PRETEST_CLASS      = 25; // 小数课前预习测加分
    const TYPE_XX_MATH_SYNTEST            = 26; // 小数同步联系加分
    const TYPE_INCLASS_SIGN_LITTLE_MVP   = 27; // MVP课程签到加学分
    const TYPE_SUBMIT_EXERCISE_INCLASS_MVP = 28; //  MVP课程课中H5互动题加学分
    const TYPE_CLASS_TEST_MVP                = 29; // MVP出门测加分
    const TYPE_CLASS_MVP_WORD_PRACTICE       = 30;//MVP单词练习
    const TYPE_CLASS_MVP_POINT_BOX           = 31;//MVP知识点开宝箱
    const TYPE_CLASS_MVP_LYL                 = 32;//MVP练一练
    const TYPE_CLASS_MVP_UNIT              = 36; // MVP单元测
    const TYPE_FINISH_CLASS               = 33; // 课程任务奖励加分
    const TYPE_CLASS_TEST_MVP_INDOOR      = 34; // 帮帮英语入门测加分
    const TYPE_PRE_MVP_GRAMMAR_PRACTICE    = 35; // MVP语法练习
    const TYPE_COURSE_TASK_REWARD           = 37; // 任务系统奖励加分
    const TYPE_CLASS_MVP_PRE_SHARE         = 38; // MVP预习分享
    const TYPE_CLASS_MVP_FINISH_SHARE      = 39; // MVP完成直播分享
    const TYPE_CLASS_MVP_LYL_SHARE         = 40; // MVP练一练分享
    const TYPE_CLASS_MVP_POINT_SHARE       = 41; // MVP知识点提升完成分享
    const TYPE_INCLASS_SIGN_IMG            = 42; // 形象签到加学分
    const TYPE_ILAB_ABTEST                 = 48; // ilab版本巩固练习加学分
    const TYPE_INCLASS_XS_ENCOURAGE        = 46; // 小数激励加学分
    const TYPE_INCLASS_XS_ENCOURAGE_STU        = 47; // 小数激励加学分
    const TYPE_INCLASS_XS_QUICK_RED        = 45; //小数红包加分

    const TYPE_SUBMIT_EXERCISE_SUFCLASS_HOME = 50;//巩固练习有大作文
    const TYPE_RECEIVE_RED_ENVELOPE_HAND     = 51; //手速红包


    public static $TYPE_NAME = array(
        self::TYPE_TUTOR_ADD                 => '补加学分',
        self::TYPE_ATTEND_CLASS              => '进入教室加分',
        self::TYPE_EXIT_CLASS                => '离开教室加分',
        self::TYPE_PRAISE                    => '老师表扬加分',
        self::TYPE_SUBMIT_EXERCISE_INCLASS   => '课中答题加分',
        self::TYPE_SUBMIT_EXERCISE_SUFCLASS  => '完成巩固练习加分',
        self::TYPE_CORRECT_EXERCISE          => '练习被批改加分',
        self::TYPE_RECEIVE_RED_ENVELOPE      => '奖励学分加分',
        self::TYPE_ADD_ONLINE_SCORE          => '在线时长加分',
        self::TYPE_SUBMIT_INCLASSSIGN        => '签到加分',
        self::TYPE_CONSUME_SCORE             => '消耗学分',
        self::TYPE_RANK_AWARD_SCORE          => '学分排行榜奖励',
        self::TYPE_FINISH_LESSON             => '听课星加分',
        self::TYPE_FINISH_EXERCISE           => '答题星加分',
        self::TYPE_FINISH_EXAM               => '测试星加分',
        self::TYPE_ENGLISH_PRECLASS_EXERCISE => '完成预习任务',
        self::TYPE_ENGLISH_POSCLASS_EXERCISE => '完成复习任务',
        self::TYPE_ENGLISH_INCLASS_EXERCISE  => '课中答题加分',
        self::TYPE_SURVEY_INCLASS_SUBMIT     => '完成课堂评价',
        self::TYPE_DAILY_PRACTICE_SHARE      => '天天加油站打卡加分',
        self::TYPE_DAILY_PRACTICE_GIFT       => '天天加油站打卡7天奖励',
        self::TYPE_ACTIVITY                  => '活动奖励加分',
        self::TYPE_STAGE_TEST                => '完成阶段测',
        self::TYPE_CLASS_TEST                => '完成课上堂堂测',
        self::TYPE_INCLASS_SIGN_LITTLE_MATH  => '直播课签到加分',
        self::TYPE_XX_MATH_PRETEST_CLASS     => '课前预习',
        self::TYPE_XX_MATH_SYNTEST           => '同步练习',
        self::TYPE_FINISH_CLASS              => '课程任务奖励',
        self::TYPE_INCLASS_SIGN_LITTLE_MVP  => '帮帮英语课程签到加分',
        self::TYPE_SUBMIT_EXERCISE_INCLASS_MVP  => '帮帮英语互动题加分',
        self::TYPE_CLASS_TEST_MVP                 =>'帮帮英语出门测加分',
        self::TYPE_CLASS_MVP_WORD_PRACTICE       =>'帮帮英语单词练习加分',
        self::TYPE_CLASS_MVP_POINT_BOX            =>'帮帮英语知识点开宝箱加分',
        self::TYPE_CLASS_MVP_LYL                   =>'帮帮英语练一练加分',
        self::TYPE_CLASS_TEST_MVP_INDOOR           =>'帮帮英语入门测加分',
        self::TYPE_PRE_MVP_GRAMMAR_PRACTICE        =>'帮帮英语语法练习加分',
        self::TYPE_CLASS_MVP_UNIT                  => '帮帮英语单元测加分',
        self::TYPE_COURSE_TASK_REWARD              => '课程任务奖励',
        self:: TYPE_CLASS_MVP_PRE_SHARE         => '帮帮英语完成预习分享加分',
        self:: TYPE_CLASS_MVP_FINISH_SHARE      => '帮帮英语完成直播分享加分',
        self:: TYPE_CLASS_MVP_LYL_SHARE         => '帮帮英语完成练一练分享加分',
        self:: TYPE_CLASS_MVP_POINT_SHARE       => '帮帮英语完成知识点提升分享加分',
        self::TYPE_INCLASS_SIGN_IMG              => '形象签到加分',
        self::TYPE_ILAB_ABTEST                   => '巩固练习加学分',
        self::TYPE_INCLASS_XS_ENCOURAGE => '小数激励加学分',
        self::TYPE_INCLASS_XS_ENCOURAGE_STU => '小数激励个人加学分',
        self::TYPE_INCLASS_XS_QUICK_RED => '红包加分',
        self::TYPE_RECEIVE_RED_ENVELOPE_HAND => '领取手速红包',
        self::TYPE_SUBMIT_EXERCISE_SUFCLASS_HOME => '巩固练习有大作文',

 );

    public function __construct()
    {
        $this->_objDaoUserScoreLog = new Hkzb_Dao_Fudao_UserScoreLog();
    }

    /**
     * 获取学分记录列表
     * @param $intStudentUid
     * @param int $intLessonId
     * @param int $intCourseId
     * @param array $arrFields
     * @param int $pn
     * @param int $rn
     * @return array|bool|false
     */
    public function getUserScoreLogList($intStudentUid, $intLessonId = 0, $intCourseId = 0, $arrFields = array(), $pn = 0, $rn = 20)
    {
        if ($intStudentUid <= 0) {
            return false;
        }
        $arrConds = array(
            'uid' => $intStudentUid,
        );
        if ($intLessonId > 0) {
            $arrConds['lessonId'] = $intLessonId;
        }
        if ($intCourseId > 0) {
            $arrConds['courseId'] = $intCourseId;
        }
        if (empty($arrFields)) {
            $arrFields = Hkzb_Dao_Fudao_UserScoreLog::$arrFields;
        }
        if ($rn > 0) {
            $arrAppends = array(
                "order by create_time desc limit $pn, $rn",
            );
        } else {
            $arrAppends = array(
                "order by create_time desc",
            );
        }
        $res = $this->_objDaoUserScoreLog->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $res;
    }

    /**
     * 添加记录
     * @param $arrFields
     * @return bool
     */
    public function addUserScoreLog($arrFields)
    {
        $arrData = array(
            'uid'        => isset($arrFields['uid']) ? intval($arrFields['uid']) : 0,
            'courseId'   => isset($arrFields['courseId']) ? intval($arrFields['courseId']) : 0,
            'lessonId'   => isset($arrFields['lessonId']) ? intval($arrFields['lessonId']) : 0,
            'type'       => isset($arrFields['type']) ? intval($arrFields['type']) : 0,
            'score'      => isset($arrFields['score']) ? intval($arrFields['score']) : 0,
            'sign'       => isset($arrFields['sign']) ? intval($arrFields['sign']) : 0,
            'notes'      => isset($arrFields['notes']) ? trim($arrFields['notes']) : '',
            'createTime' => time(),
            'extData'    => isset($arrFields['extData']) ? json_encode($arrFields['extData']) : '',
        );
        if ($arrData['uid'] <= 0 || $arrData['type'] < 0) {
            return false;
        }
        $res = $this->_objDaoUserScoreLog->insertRecords($arrData);

        return $res;
    }

    /**
     * 获取学分记录详情
     * @param $intUid
     * @param $intCourseId
     * @param $intLessonId
     * @param $intType
     * @param $intSign
     * @param array $arrFields
     * @return array|bool|false
     */
    public function getUserScoreLogItem($intUid, $intCourseId, $intLessonId, $intType, $intSign, $arrFields = array())
    {
        if ($intUid <= 0 || $intCourseId <= 0 || $intLessonId <= 0 || $intType <= 0) {
            return false;
        }
        $arrConds = array(
            'uid'      => $intUid,
            'courseId' => $intCourseId,
            'lessonId' => $intLessonId,
            'type'     => $intType,
            'sign'     => $intSign,

        );
        if (empty($arrFields)) {
            $arrFields = Hkzb_Dao_Fudao_UserScoreLog::$arrFields;
        }
        $ret = $this->_objDaoUserScoreLog->getRecordByConds($arrConds, $arrFields);

        return $ret;
    }

    /**
     * getUserScoreLogItems
     *
     * 获取学分记录不使用签名sign
     * @param mixed $intUid
     * @param mixed $intCourseId
     * @param mixed $intLessonId
     * @param mixed $intType
     * @param mixed $arrFields
     * @access public
     * @return array|bool
     */
    public function getUserScoreLogItems($intUid, $intCourseId, $intLessonId, $intType, $arrFields = [], $arrAppends = array())
    {
        if ($intUid <= 0 || $intCourseId <= 0 || $intLessonId <= 0 || $intType <= 0) {
            return false;
        }
        $arrConds = [
            'uid'      => $intUid,
            'lessonId' => $intLessonId,
            'courseId' => $intCourseId,
            'type'     => $intType,

        ];
        if (empty($arrFields)) {
            $arrFields = Hkzb_Dao_Fudao_UserScoreLog::$arrFields;
        }
        $arrAppends = array(
            'limit 1',
        );
        $res = $this->_objDaoUserScoreLog->getListByConds($arrConds, $arrFields, null, $arrAppends);

        return $res;
    }

    /**
     * 通过时间范围获取学分记录列表,
     * @param $intStudentUid
     * @param int $startTime
     * @param int $endTime
     * @param array $arrFields
     * @param int $pn
     * @param int $rn
     * @return array|bool|false
     */
    public function getUserScoreLogListByTime($intStudentUid, $startTime = 0, $endTime = 0, $arrFields = array(), $pn = 0, $rn = 20)
    {
        if ($intStudentUid <= 0) {
            return false;
        }
        $arrConds = array(
            'uid' => $intStudentUid,
        );

        if ($startTime > 0) {
            $arrConds['createTime'] = array($startTime, '>=');
        }
        if ($endTime > 0) {
            $arrConds['createTime'] = array($endTime, '<');
        }
        if (empty($arrFields)) {
            $arrFields = Hkzb_Dao_Fudao_UserScoreLog::$arrFields;
        }
        if ($rn > 0) {
            $arrAppends = array(
                "order by create_time desc limit $pn, $rn",
            );
        } else {
            $arrAppends = array(
                "order by create_time desc",
            );
        }
        $res = $this->_objDaoUserScoreLog->getListByConds($arrConds, $arrFields, NULL, $arrAppends);
        if (false === $res) {
            Bd_Log::warning("Error:[getUserScoreLogListByTime], Detail:" . json_encode($arrConds));

            return false;
        }

        return $res;
    }

    /*
     * 重构入库之前查询学生有没有在此之前入过库
     * */
    public function getUserScoreLog($intUid , $intType , $intSign ,$arrFields = array()) {

        $arrConds = array(
            'uid'      => $intUid,
            'type'     => $intType,
            'sign'     => $intSign,
        );


        if (empty($arrFields)) {
            $arrFields = Hkzb_Dao_Fudao_UserScoreLog::$arrFields;
        }
        $ret = $this->_objDaoUserScoreLog->getRecordByConds($arrConds, $arrFields);


        return $ret;
    }

    /*
     * 获取学生上课章节数据的总分
     * */
    public static function getUserLessonScore($intUid = 0, $intLessonId = 0, $arrFields = array('score')) {
        if($intUid == 0 || $intLessonId == 0) {
            Bd_Log::warning("pamar error:" . json_encode("lessonId = " . $intLessonId . "uid = ". $intUid  ));
            return false;
        }

        $arrConds = array(
            'uid' => $intUid,
            'lessonId' => $intLessonId,
        );

        if(empty($arrFields)) {
            $arrFields = Hkzb_Dao_Fudao_UserScoreLog::$arrFields;
        }

        $objDaoUserScore = new Hkzb_Dao_Fudao_UserScoreLog();
        $data = $objDaoUserScore->getListByConds($arrConds, $arrFields);

        $intScore = 0;
        if(!empty($data)) {
            foreach($data as $key => $val) {
                $intScore += $val['score'];
            }
        }

        return intval($intScore);
    }

    public function batchUserScore($arrUids, $intLessonId, $intCourseId, $intType, $intSign, $arrFields = array())
    {
        if ($intCourseId <= 0 || $intLessonId <= 0 || $intType <= 0) {
            return false;
        }
        $strUids = implode(',', $arrUids);
        $arrConds = array(
            'uid in('.$strUids.')',
            'lessonId' => $intLessonId,
            'courseId' => $intCourseId,
            'type'     => $intType,
            'sign'     => $intSign,
        );
        if (empty($arrFields)) {
            $arrFields = Hkzb_Dao_Fudao_UserScoreLog::$arrFields;
        }

        $ret = $this->_objDaoUserScoreLog->getListByConds($arrConds, $arrFields);
        return $ret;
    }
}
