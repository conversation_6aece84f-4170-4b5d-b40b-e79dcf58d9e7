<?php
/**
 * @file    IMUserEmotion.php
 * <AUTHOR>
 * @date    2017-12-08
 * @brief   用户表情包关系
 */
class Hkzb_Ds_Fudao_IMUserEmotion {
    //删除状态
    const DELETED_NO  = 0;
    const DELETED_YES = 1;

    const ALL_FIELDS = 'id,uid,emotionId,createTime,updateTime,deleted,extData';

    private $_objDaoUserEmotion;

    public function __construct()
    {
        $this->_objDaoUserEmotion = new Hkzb_Dao_Fudao_IMUserEmotion();
    }

    /**
     * 增加用户关系记录
     * @param $arrParams
     * @return bool|int
     */
    public function addUserEmotion($arrParams) {
        if (intval($arrParams['uid']) <= 0 || intval($arrParams['emotionId']) <= 0) {
            Bd_Log::warning("Error[param error] Detail[input:". json_encode($arrParams) ."]");
            return false;
        }
        $curTime = time();
        $arrFields = array(
            'uid' => intval($arrParams['uid']),
            'emotionId' => intval($arrParams['emotionId']),
            'deleted' => self::DELETED_NO,
            'createTime' => $curTime,
            'updateTime' => $curTime,
            'extData' => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
        );
        $ret = $this->_objDaoUserEmotion->insertRecords($arrFields);
        if (false === $ret) {
            return false;
        }
        return $this->_objDaoUserEmotion->getInsertId();
    }

    /**
     * 获取用户的表情包
     * @param $uid
     * @param int $offset
     * @param int $limit
     * @param array $arrFields
     * @return array|bool|false
     */
    public function getUserEmotionListByUid($uid, $offset = 0, $limit = 20, $arrFields = array()) {
        if (intval($uid) <= 0) {
            Bd_Log::warning("Error[param error] Detail[uid:$uid]");
            return false;
        }
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrAppends = array(
            'order by update_time desc',
            "limit $offset,$limit",
        );
        $ret = $this->_objDaoUserEmotion->getListByConds(array('uid' => $uid, 'deleted' => self::DELETED_NO), $arrFields, NULL, $arrAppends);
        return $ret;
    }

    /**
     * 根据uid和表情id获取记录
     * @param $uid
     * @param $emotionId
     * @param $isIncludeDeleted
     * @param array $arrFields
     * @return array|bool|false
     */
    public function getUserEmotionByUidAndEmotionId($uid, $emotionId, $arrFields = array(), $isIncludeDeleted = 0) {
        if (intval($uid) <= 0 || intval($emotionId) <= 0) {
            Bd_Log::warning("Error[param error] Detail[uid:$uid;emotionId:$emotionId]");
            return false;
        }
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrConds = array(
            'uid' => $uid,
            'emotionId' => $emotionId,
            'deleted' => self::DELETED_NO,
        );
        if ($isIncludeDeleted) {
            unset($arrConds['deleted']);
        }

        $ret = $this->_objDaoUserEmotion->getRecordByConds($arrConds, $arrFields);
        return $ret;

    }

    /**
     * 更新用户表情包关系表
     * ps:extData上层处理好，json串传过来
     * @param $id
     * @param $arrParam
     * @return bool
     */
    public function updateUserEmotion($id, $arrParam) {
        $arrFields = explode(',', self::ALL_FIELDS);
        $arrUpdateFields = array();
        foreach ($arrParam as $key=>$value) {
            if (in_array($key, $arrFields)) {
                $arrUpdateFields[$key] = $value;
            }
        }
        $arrUpdateFields['updateTime'] = time();
        $ret = $this->_objDaoUserEmotion->updateByConds(array('id'=>$id), $arrUpdateFields);
        return $ret;
    }
}