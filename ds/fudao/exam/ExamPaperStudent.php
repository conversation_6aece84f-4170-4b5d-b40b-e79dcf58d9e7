<?php

/**
 * 试卷模块
 *
 * 试卷模块分为三部分：试卷资源，试卷绑定关系，考试系统
 * 试卷资源     ：查询试卷信息，对外提供查询试卷信息接口，与题库系统衔接
 * 试卷绑定关系  ：试卷与课程或者章节的绑定关系，与课程系统衔接
 * 考试系统     ：用户考试行为与考试情况，与用户系统，课程系统衔接
 *
 */

/**
 * @file    ExamInfo.php
 * <AUTHOR>
 * @data    2017-09-06
 * @desc    考试系统
 */
class Hkzb_Ds_Fudao_Exam_ExamPaperStudent {
    /* 状态 begin */

    const STATUS_OK = 0;


    //课后作业已批改
    const STATUS_HAS_COMMENT = 1;
    //课后作业需重做
    const STATUS_REDO = 2;
    /* 状态 end */
    const HOMEWORK_STATUS_UNASSIGN = 0;
    const HOMEWORK_STATUS_ASSIGN = 1;
    const HOMEWORK_STATUS_SUBMIT = 2;
    const HOMEWORK_STATUS_COMMENT = 3;

    protected $HOMEWORK_STATUS_ARRAY = array(
        self::HOMEWORK_STATUS_UNASSIGN => "未布置",
        self::HOMEWORK_STATUS_ASSIGN => "未提交",
        self::HOMEWORK_STATUS_SUBMIT => "已提交待批改",
        self::HOMEWORK_STATUS_COMMENT => "已批改",
    );
    protected $_ObjDaoExamPaperStudent;
    protected $_objDsExamPaper;
    protected $_objDsRelation;
    protected $_objDaoExamPaper;
    protected $_objDaoRelation;

    public function __construct() {
        $this->_objDsExamPaper = new Hkzb_Ds_Fudao_Exam_ExamPaper();
        $this->_objDsRelation = new Hkzb_Ds_Fudao_Exam_ExamPaperRelation();
        $this->_objDaoRelation = new Hkzb_Dao_Fudao_Exam_ExamPaperRelation();
        $this->_objDaoExamPaper = new Hkzb_Dao_Fudao_Exam_ExamPaper();
        $this->_ObjDaoExamPaperStudent = new Hkzb_Dao_Fudao_Exam_ExamPaperStudent();
    }

    // 提交题目
    public function submitSubject($arrInput) {

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

        /**
         * 提交题目，默认已经进入试卷了才可以提交，也就是说
         * 在数据库里面已经存在该条答题记录
         */
        $intExamId = $arrInput['examId'];
        $intStudentUid = $arrInput['uid'];
        $arrAnswerRecord = $arrInput['answerInfo'];

        $intTid = intval($arrAnswerRecord['tid']);
        $arrAnswer = $arrAnswerRecord['answer'];
        $intAnswerSheet = $arrAnswerRecord['inAnswerSheet'];
        $intEntryTime = $arrAnswerRecord['entryTime'];
        $intSubmitTime = $arrAnswerRecord['submitTime'];

        $arrConds = array(
            'examId' => $intExamId,
            'studentUid' => $intStudentUid,
        );

        $arrFields = array(
            'id', 'examId', 'courseId', 'lessonId', 'studentUid',
            'relationType', 'status', 'answerInfo',
            'extData', 'createTime', 'updateTime',
        );

        $arrStdExamInfo = $this->_ObjDaoExamPaperStudent->getRecordByConds($intExamId, $arrConds, $arrFields);

        if (empty($arrStdExamInfo)) {
            // 没有找到记录
            Bd_Log::warning("Error[not find record] Detail[examId:{$intExamId},studentUid:{$intStudentUid}]");
            return false;
        }

        // 题目详情
        $arrExamInfo = $this->getExamPaperInfo($intExamId);
        $arrSubject = $arrExamInfo['subject'];

        $intScore = 0;
        // 判断是否正确
        $intCorrect = 0;

        $subjectMap = array();
        foreach ($arrSubject as $value) {
            $subjectMap[$value['tid']] = $value;
        }

        if (!isset($subjectMap[$intTid])) {
            // 说明这个tid不在本试卷里面
            Bd_Log::warning("Error[tid not in exam] Detail[examId:{$intExamId},studentUid:{$intStudentUid},tid:{$intTid}]");
            return false;
        }

        $arrCorrectAnswer = $subjectMap[$intTid]['question']['choose'];

        if (count($arrCorrectAnswer) == count($arrAnswer)) {

            $intCorrect = 1;
            foreach ($arrCorrectAnswer as $answer) {
                if (!in_array($answer, $arrAnswer)) {
                    $intCorrect = 0;
                }
            }
            if ($intCorrect) {
                $score = $subjectMap[$intTid]['score'];
                $intScore = intval($score * 10);
            }
        }

        // 一次答题信息(包含对错与得分)
        $arrNowAnswer = array(
            'tid' => $intTid,
            'answer' => $arrAnswer,
            'entryTime' => $intEntryTime,
            'submitTime' => $intSubmitTime,
            'correct' => $intCorrect,
            'score' => $intScore,
        );

        // 从 examInfo 字段中获取考试相关信息，进行校验
        //$maxTryNum = $arrExamInfo['maxTryNum'];
        $maxDuration = $arrExamInfo['duration'];

        // 获取最后一次提交试卷的记录
        $arrAnswerRecord = $arrStdExamInfo['answerInfo'];

        $intRound = count($arrAnswerRecord) - 1;
        if ($intRound < 0) {
            $intRound = 0;
        }

        if (1 == $arrAnswerRecord[$intRound]['isFinish']) {
            // 本次试卷已经提交，请开始新一次答题
            Bd_Log::warning("Error[exam is end] Detail[examId:{$intExamId},studentUid:{$intStudentUid},round:{$intRound}]");

            $arrOutput = array(
                'ret' => true,
                'examInfo' => $arrExamInfo,
                'answerInfo' => $arrAnswerRecord,
            );

            return $arrOutput;
            //return false;
        }

        // 提交接口里面不判断考试次数，因为提交接口不会增加考试次数
        // 用户答题已经用时
        $nowDuration = $arrAnswerRecord[$intRound]['duration'];

        if (($nowDuration + ($intSubmitTime - $intEntryTime) > $maxDuration)) {
            // 已经超时 本次答题置为已结束
            $arrAnswerRecord[$intRound]['isFinish'] = 1;
        }
        $arrAnswerRecord[$intRound]['duration'] = $nowDuration + ($intSubmitTime - $intEntryTime);

        /**
         * 计算本次考试的最后得分
         * 考试得分只可能在两个地方计算。一个是这里，另一个是提交试卷接口
         *
         * 这个得分是乘以10的
         */
        $examlist = $arrAnswerRecord[$intRound]['examlist'];

        // 如果答案为空，不写入数据库
        // 暂时放在这里判断
        $examlist[$intTid] = $arrNowAnswer;

        $totalScore = 0;
        // 以最后一次答题记录为准
        $scoreTidMap = array();
        // 获取每一道题目的最后一次答题记录
        foreach ($examlist as $subject) {
            $scoreTidMap[$subject['tid']] = $subject;
        }
        foreach ($scoreTidMap as $subject) {
            if (1 == $subject['correct']) {
                $totalScore += intval($subject['score']);
            }
        }
        $arrAnswerRecord[$intRound]['score'] = $totalScore;
        $arrAnswerRecord[$intRound]['inAnswerSheet'] = $intAnswerSheet;
        $arrAnswerRecord[$intRound]['examlist'] = $examlist;


        $id = $arrStdExamInfo['id'];

        $arrUpdateConds = array(
            'id' => $id,
        );

        $arrUpdateFields = array(
            'answerInfo' => json_encode($arrAnswerRecord),
            'updateTime' => time(),
        );

        $this->_ObjDaoExamPaperStudent->updateByConds($intExamId, $arrUpdateConds, $arrUpdateFields);

        $arrOutput = array(
            'ret' => true,
            'examInfo' => $arrExamInfo,
            'answerInfo' => $arrAnswerRecord,
        );

        return $arrOutput;
    }

    // 提交试卷
    public function submitExamPaper($arrInput) {

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

        $intExamId = intval($arrInput['examId']);
        $intStudentUid = intval($arrInput['uid']);
        $intSubmitTime = intval($arrInput['submitTime']);
        $intEntryTime = intval($arrInput['entryTime']);

        $arrConds = array(
            'examId' => $intExamId,
            'studentUid' => $intStudentUid,
        );

        $arrFields = array(
            'id', 'examId', 'courseId', 'lessonId', 'studentUid',
            'relationType', 'status', 'answerInfo',
            'extData', 'createTime', 'updateTime',
        );

        $arrStdExamInfo = $this->_ObjDaoExamPaperStudent->getRecordByConds($intExamId, $arrConds, $arrFields);

        if (empty($arrStdExamInfo)) {
            // 没有找到记录
            Bd_Log::warning("Error[not find record] Detail[examId:{$intExamId},studentUid:{$intStudentUid}]");
            return false;
        }

        // 学生答题记录
        $arrAnswerInfo = $arrStdExamInfo['answerInfo'];

        $intRound = count($arrAnswerInfo) - 1;
        if ($intRound <= 0) {
            $intRound = 0;
        }

        if (0 == $arrAnswerInfo[$intRound]['isFinish']) {
            // 用户答题已经用时
            $nowDuration = $arrAnswerInfo[$intRound]['duration'];

            $arrAnswerInfo[$intRound]['duration'] = $nowDuration + ($intSubmitTime - $intEntryTime);
        }
        $arrAnswerInfo[$intRound]['isFinish'] = 1;

        $id = $arrStdExamInfo['id'];

        $arrUpdateConds = array(
            'id' => $id,
        );

        $maxScore = 0;
        $index = 0;
        foreach ($arrAnswerInfo as $key => $value) {
            if ($value['score'] > $maxScore) {
                $index = $key;
            }
        }

        $arrUpdateFields = array(
            'answerInfo' => json_encode($arrAnswerInfo),
            'updateTime' => time(),
            'bestScore' => $arrAnswerInfo[$index]['score'],
            'bestDuration' => $arrAnswerInfo[$index]['duration'],
            'bestEnterTime' => $arrAnswerInfo[$index]['beginTime']
        );

        $this->_ObjDaoExamPaperStudent->updateByConds($intExamId, $arrUpdateConds, $arrUpdateFields);

        $intCourseId = $arrStdExamInfo['courseId'];
        $intRelationType = $arrStdExamInfo['relationType'];

        $arrOutput = array(
            'courseId' => $intCourseId,
            'relationType' => $intRelationType,
        );

        return $arrOutput;
    }

    // 获取某一个学生的考试情况
    public function getStudentExamInfoById($intExamId, $intStudentUid) {

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

        /**
         * 其中
         * answerInfo [
         *  {
         *      'isFinish':整数 1：完成 0：未完成,
         *      'beginTime':整数 开始时间
         *      'leftTime':剩余时间;
         *      'score'
         *      'examlist':[ 答题记录列表
         *          {
         *              'tid': 整数 题目id
         *              'answer': 回答答案 数组形式 例如 单选a 就是 ['a']
         *              'entryTime': 开始答题时间
         *              'submitTime': 结束答题时间
         *          }
         *      ],
         *  }
         * ]
         */
        // 获取试卷信息
        $arrExamPaperInfo = $this->_objDsExamPaper->getExamPaperInfoByExamId($intExamId);

        if (empty($arrExamPaperInfo)) {

            return false;
        }

        $arrRelation = $this->_objDsRelation->getRelationListByExamId($intExamId);

        if (empty($arrRelation) || !isset($arrRelation['courseId'])) {

            return false;
        }

        $intRelationType = $arrRelation['relationType'];
        $intCourseId = $arrRelation['courseId'];

        $arrExamInfo = $arrRelation['examInfo'];

        $examInfo = array(
            'examId' => $intExamId,
            'title' => $arrExamPaperInfo['title'],
            'sumscore' => $arrExamPaperInfo['sumscore'],
            'sumnumber' => $arrExamPaperInfo['sumnumber'],
            'description' => $arrExamPaperInfo['description'],
            'testType' => $intRelationType,
            'passScore' => $arrExamInfo['passScore'],
            'maxTryNum' => $arrExamInfo['maxTryNum'],
            'duration' => $arrExamInfo['duration'],
            'subject' => $arrExamPaperInfo['subject'],
        );

        $arrConds = array(
            'examId' => $intExamId,
            'studentUid' => $intStudentUid,
                //'status'     => self::STATUS_OK,
        );

        $arrFields = array(
            'id',
            'examId',
            'bestScore',
            'courseId',
            'lessonId',
            'studentUid',
            'relationType',
            'status',
            'answerInfo',
            'extData',
            'createTime',
            'updateTime',
        );

        $arrExamInfo = $this->_ObjDaoExamPaperStudent->getRecordByConds($intExamId, $arrConds, $arrFields);

        if (empty($arrExamInfo)) {
            return $arrExamInfo;
        }

        $arrOutput = array(
            'examInfo' => $examInfo,
            'id' => intval($arrExamInfo['id']),
            'examId' => intval($arrExamInfo['examId']),
            'courseId' => intval($arrExamInfo['courseId']),
            'lessonId' => intval($arrExamInfo['lessonId']),
            'studentUid' => intval($arrExamInfo['studentUid']),
            'relationType' => intval($arrExamInfo['relationType']),
            'status' => intval($arrExamInfo['status']),
            'answerInfo' => $arrExamInfo['answerInfo'],
            'extData' => $arrExamInfo['extData'],
            'createTime' => intval($arrExamInfo['createTime']),
            'updateTime' => intval($arrExamInfo['updateTime']),
            'bestScore' => intval($arrExamInfo['bestScore']),
        );

        return $arrOutput;
    }

    // 获取多个学生的考试情况
    public function getMultiStudentsAnswerInfoById($intExamId, $arrStudentUids) {

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

        /**
         * 其中
         * output [
         *  {
         *        'studentUid':整数 用户uid
         *      'isFinish':整数 1：完成 0：未完成,
         *      'beginTime':整数 开始时间
         *      'leftTime':剩余时间;
         *      'score'
         *      'examlist':[ 答题记录列表
         *          {
         *              'tid': 整数 题目id
         *              'answer': 回答答案 数组形式 例如 单选a 就是 ['a']
         *              'entryTime': 开始答题时间
         *              'submitTime': 结束答题时间
         *          }
         *      ],
         *  }
         * ]
         */
        if(intval($intExamId) <= 0 || empty($arrStudentUids)){
            return array();
        }
        // 获取试卷信息
        $arrExamPaperInfo = $this->_objDsExamPaper->getExamPaperInfoByExamId($intExamId);
        if (empty($arrExamPaperInfo)) {
            return array();
        }

        $arrConds = array(
            'examId' => $intExamId,
            'student_uid in ('.implode(',', $arrStudentUids).')',
            //'status'     => self::STATUS_OK,
        );

        $arrFields = array(
            'studentUid',
            'answerInfo',
        );

        $arrExamInfo = $this->_ObjDaoExamPaperStudent->getListByConds($intExamId, $arrConds, $arrFields);
        if (empty($arrExamInfo)) {
            return array();
        }

        $arrOutput = array();
        foreach ($arrExamInfo as $value) {
            $value['answerInfo']['studentUid'] = $value['studentUid'];
            $arrOutput[] = $value['answerInfo'];
        }

        return $arrOutput;
    }

    // 开始一次新的考试
    public function beginNewExam($arrInput) {

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

        $intExamId = intval($arrInput['examId']);
        $intStudentUid = intval($arrInput['uid']);


        // 事务 确保一致性 注意事务的隔离级别
        $this->_ObjDaoExamPaperStudent->startTransaction();

        // 获取试卷信息
        $arrExamPaperInfo = $this->_objDsExamPaper->getExamPaperInfoByExamId($intExamId);

        if (empty($arrExamPaperInfo)) {

            return false;
        }

        $arrRelation = $this->_objDsRelation->getRelationListByExamId($intExamId);

        if (empty($arrRelation) || !isset($arrRelation['courseId'])) {

            return false;
        }

        $intRelationType = $arrRelation['relationType'];
        $intCourseId = $arrRelation['courseId'];

        $arrExamInfo = $arrRelation['examInfo'];

        $examInfo = array(
            'examId' => $intExamId,
            'title' => $arrExamPaperInfo['title'],
            'sumscore' => $arrExamPaperInfo['sumscore'],
            'sumnumber' => $arrExamPaperInfo['sumnumber'],
            'description' => $arrExamPaperInfo['description'],
            'testType' => $intRelationType,
            'passScore' => $arrExamInfo['passScore'],
            'maxTryNum' => $arrExamInfo['maxTryNum'],
            'duration' => $arrExamInfo['duration'],
            'subject' => $arrExamPaperInfo['subject'],
        );

        $arrConds = array(
            'examId' => $intExamId,
            'studentUid' => $intStudentUid,
        );

        $arrFields = array(
            'id', 'examId', 'courseId', 'lessonId', 'studentUid',
            'relationType', 'status', 'answerInfo',
            'extData', 'createTime', 'updateTime',
        );

        $record = $this->_ObjDaoExamPaperStudent->getRecordByConds($intExamId, $arrConds, $arrFields);

        //
        if (empty($record)) {


            $answerInfo = array();

            $answerInfo[] = array(
                'isFinish' => 0,
                'beginTime' => time(),
                'duration' => 0,
                'score' => 0,
                'examlist' => array(),
            );

            $arrInsert = array(
                'examId' => $intExamId,
                'courseId' => $intCourseId,
                'lessonId' => 0,
                'studentUid' => $intStudentUid,
                'relationType' => $intRelationType,
                'status' => 0,
                'answerInfo' => json_encode($answerInfo),
                'extData' => '[]',
                'createTime' => time(),
                'updateTime' => time(),
            );


            $insertRet = $this->_ObjDaoExamPaperStudent->insertRecords($intExamId, $arrInsert);

            if (!$insertRet) {

                return false;
            }
        }

        if ($record) {

            $intMaxTryNum = $examInfo['maxTryNum'];
            $answerInfo = $record['answerInfo'];

            $intRound = count($answerInfo) - 1;
            if ($intRound < 0) {
                $intRound = 0;
            }

            if (count($answerInfo) >= $intMaxTryNum) {

                $this->_ObjDaoExamPaperStudent->rollback();

                $arrOutput = array(
                    'examInfo' => $examInfo,
                    'answerInfo' => $answerInfo,
                );

                return $arrOutput;
                //return false;
            }

            // 如果上一次考试没有结束 接着考试
            if (0 == $answerInfo[$intRound]['isFinish']) {
                //$answerInfo[$intRound]['isFinish'] = 1;
            }

            if (1 == $answerInfo[$intRound]['isFinish']) {

                $answerInfo[] = array(
                    'isFinish' => 0,
                    'beginTime' => time(),
                    'duration' => 0,
                    'score' => 0,
                    'examlist' => array(),
                );

                $arrUpdateConds = array(
                    'id' => intval($record['id']),
                );

                $arrUpdate = array(
                    'answerInfo' => json_encode($answerInfo),
                    'updateTime' => time(),
                );

                $this->_ObjDaoExamPaperStudent->updateByConds($intExamId, $arrUpdateConds, $arrUpdate);
            }
        }

        $this->_ObjDaoExamPaperStudent->commit();

        $arrOutput = array(
            'examInfo' => $examInfo,
            'answerInfo' => $answerInfo,
        );

        return $arrOutput;
    }

    /**
     * 获取学生某个课程，某个类型试卷的考试结
     * @param $intUid
     * @param $intCourseId
     * @param int $intRelationType
     * @return array|bool|false
     */
    public function getStudentCourseExamResult($intUid, $intCourseId, $intRelationType = 3) {

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

        if (empty($intUid) || empty($intCourseId)) {
            Bd_Log::warning("param error uid:{$intUid} courseId:{$intCourseId}");

            return false;
        }

        $objDsRelation = new Hkzb_Dao_Fudao_Exam_ExamPaperRelation();

        $arrConds = array(
            'courseId' => intval($intCourseId),
            'relationType' => intval($intRelationType),
            'status' => Hkzb_Ds_Fudao_Exam_ExamPaperRelation::RELATION_STATUS_OK
        );

        $arrFields = array(
            'examId'
        );

        $ret = $objDsRelation->getRecordByConds($arrConds, $arrFields);

        if ($ret === false) {
            Bd_Log::warning("db error courseId :{$intCourseId}");

            return false;
        }

        if (empty($ret)) {
            return array();
        }
        $examId = $ret['examId'];

        $studentExamInfo = $this->getStudentExamInfoById($examId, $intUid);

        return $studentExamInfo;
    }

    public function getStudentAfterCourseExamResult($intUid, $intCourseId, $intRelationType = 3) {

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

        if (empty($intUid) || empty($intCourseId)) {
            Bd_Log::warning("param error uid:{$intUid} courseId:{$intCourseId}");

            return false;
        }

        $objDaoRelation = new Hkzb_Dao_Fudao_Exam_ExamPaperRelation();

        $arrConds = array(
            'courseId' => intval($intCourseId),
            'relationType' => intval($intRelationType),
            'status' => Hkzb_Ds_Fudao_Exam_ExamPaperRelation::RELATION_STATUS_OK
        );

        $arrFields = array(
            'examId', 'examInfo',
        );

        $ret = $objDaoRelation->getRecordByConds($arrConds, $arrFields);

        if ($ret === false) {
            Bd_Log::warning("db error courseId :{$intCourseId}");

            return false;
        }

        if (empty($ret)) {
            return array();
        }
        $examId = $ret['examId'];

        $arrExamInfo = $ret['examInfo'];

        $arrStudentExamInfo = $this->getStudentExamInfoById($examId, $intUid);

        // 试卷信息
        $arrSubjectList = $arrStudentExamInfo['examInfo']['subject'];
        if (!$arrSubjectList) {
            return array();
        }

        $initTidList = array();
        foreach ($arrSubjectList as $v) {
            if ($v['tid'] <= 0) {
                continue;
            }
            $initTidList[$v['tid']] = array(
                'choise' => array(),
                'isRight' => 2,
            );
        }

        $arrExamList = $arrStudentExamInfo['answerInfo'];

        $intMaxTryNum = $arrExamInfo['maxTryNum']; // 最大考试次数
        $intPassScore = $arrExamInfo['passScore']; // 分数线

        $intMaxScore = 0; // 最高分数
        $intTryNum = 0; // 已经考试的次数
        foreach ($arrExamList as $item) {
            if (1 != $item['isFinish']) {
                continue;
            }
            $intTryNum++;
            if ($intMaxScore < intval($item['score'])) {
                $intMaxScore = intval($item['score']);
            }
        }

        // 如果尝试次数为0 说明要么没有答题，要么第一次答题尚未结束
        if (0 == $intTryNum) {
            return false;
        }

        $intLeftTryNum = $intMaxTryNum - $intTryNum;
        if ($intLeftTryNum < 0) {
            $intLeftTryNum = 0;
        }

        $arrNewExamList = array();

        foreach ($arrExamList as $item) {
            $tidList = $initTidList;
            foreach ($item['examlist'] as $k => $v) {
                if ($v['tid'] <= 0) {
                    continue;
                }
                $tidList[$v['tid']] = array(
                    'choise' => $v['answer'] ? $v['answer'] : array(),
                    'isRight' => 1 == $v['correct'] ? 1 : 2,
                );
            }

            $temp = array(
                'beginTime' => $item['beginTime'],
                'score' => $item['score'],
                'duration' => $item['duration'],
                'tidList' => $tidList,
            );

            $arrNewExamList[] = $temp;
        }

        $arrOutput = array(
            'uid' => $intUid,
            'maxScore' => $intMaxScore,
            'leftTryNum' => $intLeftTryNum,
            'isPass' => ($intMaxScore >= $intPassScore) ? 1 : 0,
            'examList' => $arrNewExamList,
        );

        return $arrOutput;
    }

    protected function getExamPaperInfo($intExamId) {

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

        // 获取试卷信息
        $arrExamPaperInfo = $this->_objDsExamPaper->getExamPaperInfoByExamId($intExamId);

        if (empty($arrExamPaperInfo)) {

            return false;
        }

        $arrRelation = $this->_objDsRelation->getRelationListByExamId($intExamId);

        if (empty($arrRelation) || !isset($arrRelation['courseId'])) {

            return false;
        }

        $intRelationType = $arrRelation['relationType'];
        $intCourseId = $arrRelation['courseId'];

        $arrExamInfo = $arrRelation['examInfo'];

        $examInfo = array(
            'examId' => $intExamId,
            'title' => $arrExamPaperInfo['title'],
            'sumscore' => $arrExamPaperInfo['sumscore'],
            'sumnumber' => $arrExamPaperInfo['sumnumber'],
            'description' => $arrExamPaperInfo['description'],
            'testType' => $intRelationType,
            'passScore' => $arrExamInfo['passScore'],
            'maxTryNum' => $arrExamInfo['maxTryNum'],
            'duration' => $arrExamInfo['duration'],
            'subject' => $arrExamPaperInfo['subject'],
        );

        return $examInfo;
    }

    public function getAfterCourseExamResultListByCourseId($intCourseId, $intRelationType = 3) {

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

        $intCourseId = intval($intCourseId);
        if (empty($intCourseId)) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$intCourseId]");

            return false;
        }

        $objDaoRelation = new Hkzb_Dao_Fudao_Exam_ExamPaperRelation();

        $arrConds = array(
            'courseId' => intval($intCourseId),
            'relationType' => intval($intRelationType),
            'status' => Hkzb_Ds_Fudao_Exam_ExamPaperRelation::RELATION_STATUS_OK
        );

        $arrFields = array(
            'examId', 'examInfo',
        );

        $ret = $objDaoRelation->getRecordByConds($arrConds, $arrFields);

        if ($ret === false) {
            Bd_Log::warning("db error courseId :{$intCourseId}");

            return false;
        }

        if (empty($ret)) {
            return array();
        }
        $examId = $ret['examId'];

        $arrExamPaperInfo = $this->_objDsExamPaper->getExamPaperInfoByExamId($examId);
        $arrSubjectList = $arrExamPaperInfo['subject'];

        $arrStudentConds = array(
            'courseId' => $intCourseId,
        );

        $arrStudentFields = array(
            'id', 'examId', 'courseId', 'lessonId', 'studentUid',
            'relationType', 'status', 'answerInfo',
            'extData', 'createTime', 'updateTime',
        );

        $arrStudentExamInfo = $this->_ObjDaoExamPaperStudent->getListByConds($examId, $arrStudentConds, $arrStudentFields);


        $arrOutput = array();

        $arrExamInfo = $ret['examInfo'];
        $intMaxTryNum = $arrExamInfo['maxTryNum']; // 最大考试次数
        $intPassScore = $arrExamInfo['passScore']; // 分数线

        foreach ($arrStudentExamInfo as $value) {

            $intUid = intval($value['studentUid']);

            $initTidList = array();
            foreach ($arrSubjectList as $v) {
                if ($v['tid'] <= 0) {
                    continue;
                }
                $initTidList[$v['tid']] = array(
                    'choise' => array(),
                    'isRight' => 2,
                );
            }

            $arrExamList = $value['answerInfo'];

            $intMaxScore = 0; // 最高分数
            $intTryNum = 0; // 已经考试的次数
            foreach ($arrExamList as $item) {
                if (1 != $item['isFinish']) {
                    continue;
                }
                $intTryNum++;
                if ($intMaxScore < intval($item['score'])) {
                    $intMaxScore = intval($item['score']);
                }
            }

            // 如果尝试次数为0 说明要么没有答题，要么第一次答题尚未结束
            if (0 == $intTryNum) {
                continue;
            }

            $intLeftTryNum = $intMaxTryNum - $intTryNum;
            if ($intLeftTryNum < 0) {
                $intLeftTryNum = 0;
            }

            $arrNewExamList = array();

            foreach ($arrExamList as $item) {
                $tidList = $initTidList;
                foreach ($item['examlist'] as $k => $v) {
                    if ($v['tid'] <= 0) {
                        continue;
                    }
                    $tidList[$v['tid']] = array(
                        'choise' => $v['answer'] ? $v['answer'] : array(),
                        'isRight' => 1 == $v['correct'] ? 1 : 2,
                    );
                }

                $temp = array(
                    'beginTime' => $item['beginTime'],
                    'score' => $item['score'],
                    'duration' => $item['duration'],
                    'tidList' => $tidList,
                );

                $arrNewExamList[] = $temp;
            }

            $arrOutput[$intUid] = array(
                'uid' => $intUid,
                'maxScore' => $intMaxScore,
                'leftTryNum' => $intLeftTryNum,
                'isPass' => ($intMaxScore >= $intPassScore) ? 1 : 0,
                'examList' => $arrNewExamList,
            );
        }

        return $arrOutput;
    }


    public function getStudentExamResultByCourseId($intExamId, $intStudentUid, $intCourseId, $intRelationType = 4) {

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

        if (empty($intExamId) || empty($intStudentUid) || empty($intCourseId)) {
            Bd_Log::warning("param error uid:{$intStudentUid} courseId:{$intCourseId}");
            return false;
        }
        $obj = new Hkzb_Ds_Examrpc_ExamRpcApi();

        $ret = $obj->getStudentExamResultRpc($intExamId,$intStudentUid,$intRelationType);

        if ($ret === false) {
            Bd_Log::warning("db error courseId :{$intCourseId}");
            return false;
        }

        return $ret;
    }


/*    public function getStudentExamResultByCourseId($intExamId, $intStudentUid, $intCourseId, $intRelationType = 4) {

        //<<<rdtrack<<<
        $rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }
        //>>>rdtrack>>>

        if (empty($intExamId) || empty($intStudentUid) || empty($intCourseId)) {
            Bd_Log::warning("param error uid:{$intStudentUid} courseId:{$intCourseId}");
            return false;
        }


        $arrConds = array(
            'examId' => $intExamId,
            'courseId' => $intCourseId,
            'studentUid' => $intStudentUid,
            'relationType' => $intRelationType,
            'status' => 0,
        );

        $arrFields = array(
            'id', 'examId', 'courseId', 'lessonId', 'studentUid',
            'relationType', 'status', 'answerInfo', 'extData',
        );

        $ret = $this->_ObjDaoExamPaperStudent->getRecordByConds($intExamId, $arrConds, $arrFields);

        if ($ret === false) {
            Bd_Log::warning("db error courseId :{$intCourseId}");
            return false;
        }

        return $ret;
    }*/

    /**
     * 获取课程测试所有学生记录
     * @param int $intExamId
     * @param array $arrFields
     * @param $arrAppendsd
     * @return array|bool|false
     */
    public function getCourseExamResult($intExamId, $arrConds, $arrFields = array(), $intOrder = 0, $offset = 0, $size = 20) {

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

        if (empty($intExamId)) {
            return false;
        }
        if (empty($arrFields)) {
            $arrFields = array(
                'id', 'examId', 'courseId', 'lessonId', 'studentUid',
                'relationType', 'status', 'answerInfo',
                'extData', 'createTime', 'updateTime',
            );
        }

        //调整排序方式
        switch ($intOrder) {
            case 1:
                $arrAppends[] = "ORDER BY best_score DESC";
                break;
            case 2:
                $arrAppends[] = "ORDER BY best_duration DESC";
                break;
            default:
                $arrAppends[] = "ORDER BY best_enter_time DESC";
        }


        $arrAppends[] = 'limit ' . $offset . ',' . $size;
        $arrCourseExamResultList = $this->_ObjDaoExamPaperStudent->getListByConds($intExamId, $arrConds, $arrFields, NULL, $arrAppends);
        return $arrCourseExamResultList;
    }

    /* 内部已经升级3。0*/
    public function getStudentHomeworkStatus($intLessonId, $intStudentUid) {
        return $this->_getExamStatus(0, $intLessonId, $intStudentUid, Hkzb_Ds_Fudao_Exam_ExamPaper::STATUS_EXAM_TYPE_HOMEWORK);
    }

    public function getStudentFinalExamStatus($intLessonId, $intStudentUid) {
        return $this->_getExamStatus(0, $intLessonId, $intStudentUid, Hkzb_Ds_Fudao_Exam_ExamPaper::STATUS_EXAM_TYPE_FINAL);
    }

    //状态有更改，安全起见另起了一个方法提课程展示，有问题@zhangbing
    public function getNewStudentHomeworkStatus($intLessonId, $intStudentUid)
    {
        $examType = Hkzb_Ds_Fudao_Exam_ExamPaper::STATUS_EXAM_TYPE_HOMEWORK;

        $intCourseId = 0;

        $return = array();

        $arrParams = array(
            'bindId'      => 0,
            'bindIdType'  => 0,
            'examType'    => $examType,
            'hasQuestion' => 0,
            'page'        => 0,
            'from'        => 0,
            'off'         => 0,
        );

        if ($intCourseId > 0) {
            $arrParams['bindId'] = $intCourseId;
            $arrParams['bindIdType'] = 1;
            //$arrExamInfo = $this->_objDsRelation->getExamInfoByCourseIdExamType($intCourseId, $examType);
        } elseif ($intLessonId > 0) {
            $arrParams['bindIdType'] = 0;
            $arrParams['bindId'] = $intLessonId;
            //$arrExamInfo = $this->_objDsRelation->getExamInfoByLessonIdExamType($intLessonId, $examType);
        }

        $arrExamInfo = Hk_Service_Rpc::call('zbexam', 'getexaminfobybindid', $arrParams);
        $arrExamInfo = $arrExamInfo['data']['examInfo'];

        if (!isset($arrExamInfo['examId']) || $arrExamInfo['examId'] <= 0) {
            $return = self::HOMEWORK_STATUS_UNASSIGN;
        } elseif ($arrExamInfo['examId'] > 0) {
            $arrParams = array(
                'examId'     => strval($arrExamInfo['examId']),
                'examType'   => $examType,
                'studentUid' => $intStudentUid,
            );
            $ret = Hk_Service_Rpc::call('zbexam', 'getanswerinfo', $arrParams);
            // 原代码
//            $ret = $ret['data'][0];

            if(isset($ret['data']['list'])) {
                $ret = $ret['data']['list'][0];
            } else {
                // /exam/service/getanswerinfo 接口返回值调整
                // 临时做兼容，4月18日之后，以下代码可以删除\
                $ret = $ret['data'][0];
                //Bd_Log::warning("[TAG][get_answer_info_old_type]");
            }

            $objDaoExamPaperStudent = new Hkzb_Dao_Fudao_Exam_ExamPaperStudent();

            $arrStudentHomeworkInfo = array(
                'examId' => $ret['examId'],
                'status' => $ret['status'],    // 状态 为佳
            );
            //$arrStudentHomeworkInfo = $this->_ObjDaoExamPaperStudent->getRecordByConds($arrExamInfo['examId'], $arrConds, $arrFields);
            $arrConds = [
                'lessonId' => $intLessonId,
                'examId' => $arrExamInfo['examId'],
                'studentUid' => $intStudentUid
            ];

            $arrFields = array(
                'id', 'examId', 'courseId', 'lessonId', 'studentUid',
                'relationType', 'status', 'answerInfo',
                'extData', 'createTime', 'updateTime',
            );

            //$arrStudentHomeworkInfo = $objDaoExamPaperStudent->getRecordByConds($arrExamInfo['examId'], $arrConds, $arrFields);
            if (!isset($arrStudentHomeworkInfo['examId'])) {
                $return = 1;
            }else{
                if (in_array($arrStudentHomeworkInfo['status'],[1,3] )) {
                    $return = 2;
                } elseif ($arrStudentHomeworkInfo['status'] == 0) {
                    $return = 3;
                }

            }
        }
        return $return;

    }

    /* 内部已经升级3。0*/
    private function _getExamStatus($intCourseId, $intLessonId, $intStudentUid, $examType) {
        $return = array();

        $arrParams = array(
            'bindId'      => 0,
            'bindIdType'  => 0,
            'examType'    => $examType,
            'hasQuestion' => 0,
            'page'        => 0,
            'from'        => 0,
            'off'         => 0,
        );

        if ($intCourseId > 0) {
            $arrParams['bindId'] = $intCourseId;
            $arrParams['bindIdType'] = 1;
            //$arrExamInfo = $this->_objDsRelation->getExamInfoByCourseIdExamType($intCourseId, $examType);
        } elseif ($intLessonId > 0) {
            $arrParams['bindIdType'] = 0;
            $arrParams['bindId'] = $intLessonId;
            //$arrExamInfo = $this->_objDsRelation->getExamInfoByLessonIdExamType($intLessonId, $examType);
        }

        $arrExamInfo = Hk_Service_Rpc::call('zbexam', 'getexaminfobybindid', $arrParams);
        $arrExamInfo = $arrExamInfo['data']['examInfo'];

        if (!isset($arrExamInfo['examId']) || $arrExamInfo['examId'] <= 0) {
            $return = self::HOMEWORK_STATUS_UNASSIGN;
        } elseif ($arrExamInfo['examId'] > 0) {
            $arrParams = array(
                'examId'     => strval($arrExamInfo['examId']),
                'examType'   => $examType,
                'studentUid' => $intStudentUid,
            );
            $ret = Hk_Service_Rpc::call('zbexam', 'getanswerinfo', $arrParams);
            // 原代码
//            $ret = $ret['data'][0];

            if(isset($ret['data']['list'])) {
	            $ret = $ret['data']['list'][0];
            } else {
                // /exam/service/getanswerinfo 接口返回值调整
                // 临时做兼容，4月18日之后，以下代码可以删除\
	            $ret = $ret['data'][0];
                //Bd_Log::warning("[TAG][get_answer_info_old_type]");
            }

            $arrStudentHomeworkInfo = array(
                'examId' => $ret['examId'],
                'status' => $ret['status'],    // 状态 为佳
            );
            //$arrStudentHomeworkInfo = $this->_ObjDaoExamPaperStudent->getRecordByConds($arrExamInfo['examId'], $arrConds, $arrFields);
            if (!isset($arrStudentHomeworkInfo['examId'])) {
                $return = self::HOMEWORK_STATUS_ASSIGN;
            } elseif ($arrStudentHomeworkInfo['status'] == self::STATUS_REDO) {
                $return = self::HOMEWORK_STATUS_ASSIGN;
            } elseif ($arrStudentHomeworkInfo['status'] == self::STATUS_OK) {
                $return = self::HOMEWORK_STATUS_SUBMIT;
            } elseif ($arrStudentHomeworkInfo['status'] == self::STATUS_HAS_COMMENT) {
                $return = self::HOMEWORK_STATUS_COMMENT;
            }
        }
        return $return;
    }

    /**
     * 获取一个学生的答题结果方法
     * @param int $lessonId    章节id
     * @param int $studentUid  学生uid
     * @return array
     */

    public function getStudentAnswerInfoByLessonId($lessonId,$studentUid,$examType=7){
        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

        if(!$lessonId || !$studentUid){
            return false;
        }

        $examId = $this->getEameIdByLessonId($lessonId,$examType);
        if(!$examId){
            Bd_Log::warning("Error[not find examId] lessonId[$lessonId] student_uid[$studentUid]");
            return false;
        }

        $arrParams = array(
            'examId'     => strval($examId),
            'examType'   => $examType,
            'studentUid' => $studentUid,
        );
        $resp = Hk_Service_Rpc::call('zbexam', 'getanswerinfo', $arrParams);
        if (false == $resp) {
            Bd_Log::warning("Error[rpcError] RpcParam[examId:$examId,examType:$examType,student_uid:$studentUid]");
            return false;
        }
        if ((0 !== $resp['errno']) || !is_array($resp['data'])) {
            Bd_Log::warning("Error[rpcError] RpcErrno[{$resp['errno']}] RpcErrmsg[{$resp['errmsg']}] RpcParam[examId:$examId,examType:$examType,student_uid:$studentUid]");
            return array();
        }
        if (!$resp['data']['list']) {
            return array();
        }
        $firstAnsInfo = reset($resp['data']['list']);
        $rsData = array();
        if($firstAnsInfo['answerList']){
            foreach ($firstAnsInfo['answerList'] as $tid => $ansInfo) {
                if (is_array($ansInfo['list'])) {
                    foreach ($ansInfo['list'] as $idx => $subAns) {
                        $rsData[$tid][] = $subAns['answer'];
                    }
                } else {
                    $rsData[$tid] = array($ansInfo['answer']);
                }
            }
        }
        $rsDataFinal['examId']        = $examId;
        $rsDataFinal['studentAnswer'] = $rsData;
        $rsDataFinal['submitTime']    = $firstAnsInfo['submitTime'];
        $rsDataFinal['bestScore']     = $firstAnsInfo['score'];

        return $rsDataFinal;

        //rpc改造，6月底删除
        /*
        //获取学生答题情况
        $arrConds = array(
            'exam_id' => $examId,
            'lesson_id'=>$lessonId,
            'student_uid'=>$studentUid
        );
        $arrFields = array(
            'answer_info','create_time','best_score'
        );
        $examPaperStudentInfo = $this->_ObjDaoExamPaperStudent->getRecordByConds($examId,$arrConds,$arrFields);
        if($examPaperStudentInfo === false){
            Bd_Log::warning("Error[Db error] lessonId[$lessonId] student_uid[$studentUid] examId[$examId]");
            return false;
        }elseif(empty($examPaperStudentInfo)){
            return array();
        }
        //end

        //组装返回结果
        $rsData = array();
        $examList = json_decode($examPaperStudentInfo['answer_info'],true);
        if(count($examList)>0){
            foreach ($examList as $key => $value) {
                if(!strpos($key,"_")){
                    $rsData[$key] = array($value['answer']);
                }else{
                    $keyL = explode("_", $key);
                    $rsData[$keyL[0]][] = $value['answer'];
                }
                
            }
        }
        $rsDataFinal['studentAnswer'] = $rsData;
        $rsDataFinal['examId'] = $examId;
        $rsDataFinal['submitTime'] = $examPaperStudentInfo['create_time'];
        $rsDataFinal['bestScore'] = $examPaperStudentInfo['best_score'];
        return $rsDataFinal;
        */
    }

    /**
     * 获取分数.
     * @param $examId
     * @param $lessonId
     * @param $studentUid
     * @return array|bool
     */
    public function getScoreByExam($examId,$lessonId,$studentUid){

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

        $arrConds = array(
            'exam_id' => $examId,
            'lesson_id'=>$lessonId,
            'student_uid'=>$studentUid
        );
        $arrFields = array(
            'best_score'
        );
        $examPaperStudentInfo = $this->_ObjDaoExamPaperStudent->getRecordByConds($examId,$arrConds,$arrFields);
        if($examPaperStudentInfo === false){
            Bd_Log::warning("Error[Db error] lessonId[$lessonId] student_uid[$studentUid] examId[$examId]");
            return false;
        }elseif(empty($examPaperStudentInfo)){
            return array();
        }
        $rsDataFinal['bestScore'] = $examPaperStudentInfo['best_score'];
        return $rsDataFinal;
    }


    /**
     * 根据课程ID获取试卷id
     * @param  $lessonId 课程ID
     * @param  $examType 练习类型，默认为课后练习
     * @return int|booler
     */
    public function getEameIdByLessonId($lessonId,$examType=7)
    {

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

        if(!$lessonId){
            Bd_Log::warning("Error [param is empty]");
            return false;
        }

        $bindIdType = 0;
        $arrParams = array(
            'bindId'      => intval($lessonId),
            'bindIdType'  => $bindIdType,
            'examType'    => intval($examType),
        );
        $resp = Hk_Service_Rpc::call('zbexam', 'getexaminfobybindid', $arrParams);
        if (false == $resp) {
            Bd_Log::warning("Error[not find record] lessonId[$lessonId]");
            return false;
        }
        $examId = 0;
        if ((0 === $resp['errno']) && $resp['data']['examInfo']) {
            $examId = intval($resp['data']['examInfo']['examId']);
        } else {
            Bd_Log::warning("Error[rpcError] RpcErrno[{$resp['errno']}] RpcErrmsg[{$resp['errmsg']}] RpcParam[bindId:$lessonId,bindIdType:$bindIdType,examType:$examType]");
        }
        Bd_Log::addNotice("examRMexamid", $examId);
        return $examId;
    }

    /**
     * 获取已提交的学生列表方法
     * @param  $lessonId 课程ID
     * @return array
     */
    public function getStudentListByLessonId($lessonId,$examType=7)
    {

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

        if(!$lessonId){
            Bd_Log::warning("Error [param is empty]");
            return false;
        }

        //获取examId试卷ID
        $examId = $this->getEameIdByLessonId($lessonId,$examType);
        if(!$examId){
            Bd_Log::warning("Error[not find examId] lessonId[$lessonId] student_uid[$studentUid]");
            return false;
        }

        //读取学生答题情况
        $arrConds = array(
            'lesson_id'=>$lessonId,
            'exam_id'=>$examId,
        );

        $arrFields = array(
            'student_uid','create_time'
        );

        $examPaperStudentInfo = $this->_ObjDaoExamPaperStudent->getListByConds($examId,$arrConds,$arrFields);
        //end

        //组装接口
        if(!$examPaperStudentInfo){
            Bd_Log::warning("Error[exanPaperStudentInfo is  empty] examId[$examId] lesson_id[$lessonId]");
            return false;
        }
        $rsData = array();
        foreach ($examPaperStudentInfo as  $v) {
            $rsData[$v['student_uid']] = array('uid'=> $v['student_uid'],'submitTime'=>$v['create_time']);
        }
        return $rsData;
    }

    /**
     * 助教批改完课后作业回写主讲端批改状态的方法
     * @param $studentUid 学生ID
     * @param $lessonId 章节ID
     * @return booler
     */
    public function setAnswerCommetnStatus($studentUid,$lessonId){

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

        //参数合法性验证
        if(!$studentUid || !$lessonId){
            Bd_Log::warning("Error[param error]");
            return false;
        }
        //获取试卷ID
        $examId = $this->getEameIdByLessonId($lessonId);
        if(!$examId){
            Bd_Log::warning("Error[not find examId] lessonId[$lessonId] student_uid[$studentUid]");
            return false;
        }
        //状态修改
        $arrConds = array(
            'lesson_id'=>$lessonId,
            'exam_id'=>$examId,
            'student_uid'=>$studentUid
        );
        $arrFields=array(
            'status'=>self::STATUS_HAS_COMMENT
        );
        $exeRs = $this->_ObjDaoExamPaperStudent->updateByConds($examId,$arrConds, $arrFields);
        if(!$exeRs){
            Bd_Log::warning("Error[update fail] exam_id[$examId] student_uid[$studentUid] lesson_id[$lessonId]");
            return false;
        }
        return true;
    }


        /**
     * 助教批改完课后作业回写主讲端批改状态的方法
     * @param $studentUid 学生ID
     * @param $lessonId 章节ID
     * @return booler
     */
    public function setAnswerCommetnStatusNew($studentUid,$lessonId){

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

        echo "start_0\n";
        //参数合法性验证
        if(!$studentUid || !$lessonId){
            echo "start_1\n";
            Bd_Log::warning("Error[param error]");
            return 10;
        }
        //获取试卷ID
        $examId = $this->getEameIdByLessonId($lessonId);
        if(!$examId){
            echo "start_2\n";
            echo "examId_" . $examId . "\n";
            Bd_Log::warning("Error[not find examId] lessonId[$lessonId] student_uid[$studentUid]");
            return 20;
        }
        //状态修改
        $arrConds = array(
            'lesson_id'=>$lessonId,
            'exam_id'=>$examId,
            'student_uid'=>$studentUid
        );
        $arrFields=array(
            'status'=>self::STATUS_HAS_COMMENT
        );
        $exeRs = $this->_ObjDaoExamPaperStudent->updateByConds($examId,$arrConds, $arrFields);
        echo "updateRes_" . $exeRs . "\n";
        if($exeRs === false){
            echo "start_3\n";
            Bd_Log::warning("Error[update fail false] exam_id[$examId] student_uid[$studentUid] lesson_id[$lessonId]");
            return 30;
        }
        return 100;
    }

    /**
     * 根据examId获取试卷信息（包含有序的题目列表）
     * @param  [type] $intExamId    examId 
     * @return array
     */
    public function getStudentAnswarInfoById($intExamId) {

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

        // 获取试卷信息
        $arrExamPaperInfo = $this->_objDsExamPaper->getExamPaperInfoByExamId($intExamId);

        if (empty($arrExamPaperInfo)) {

            return false;
        }

        $arrRelation = $this->_objDsRelation->getRelationListByExamId($intExamId);

        if (empty($arrRelation) || !isset($arrRelation['courseId'])) {

            return false;
        }

        $intRelationType = $arrRelation['relationType'];
        $intCourseId = $arrRelation['courseId'];

        $arrExamInfo = $arrRelation['examInfo'];

        $examInfo = array(
            'examId' => $intExamId,
            'title' => $arrExamPaperInfo['title'],
            'sumscore' => $arrExamPaperInfo['sumscore'],
            'sumnumber' => $arrExamPaperInfo['sumnumber'],
            'description' => $arrExamPaperInfo['description'],
            'testType' => $intRelationType,
            'passScore' => $arrExamInfo['passScore'],
            'maxTryNum' => $arrExamInfo['maxTryNum'],
            'duration' => $arrExamInfo['duration'],
            'subject' => $arrExamPaperInfo['subject'],
        );

        $arrConds = array(
            'examId' => intval($intExamId),
            
                //'status'     => self::STATUS_OK,
        );

        $arrFields = array(
            'id',
            'examId',
            'courseId',
            'lessonId',
            'studentUid',
            'relationType',
            'status',
            'answerInfo',
            'extData',
            'createTime',
            'updateTime',
            'best_score',
            'best_enter_time',
            'best_duration'
        );

        $arrExamInfoList = $this->_ObjDaoExamPaperStudent->getListByConds($intExamId, $arrConds, $arrFields);

        if (empty($arrExamInfoList)) {
            return $arrExamInfoList;
        }
        $arrOutPut['examInfo'] = $examInfo;
        foreach ($arrExamInfoList as $arrExamInfo) {
            $examStudentInfo = $jsonArrjob = array();
            $arrExamInfo['answerInfo'] = $this->__fetchAnswerInfo($arrExamInfo['answerInfo']);
            
            if(!$arrExamInfo['answerInfo']){
                continue;
            }
            
            $examStudentInfo = array(
                'id' => intval($arrExamInfo['id']),
                'examId' => intval($arrExamInfo['examId']),
                'courseId' => intval($arrExamInfo['courseId']),
                'lessonId' => intval($arrExamInfo['lessonId']),
                'studentUid' => intval($arrExamInfo['studentUid']),
                'relationType' => intval($arrExamInfo['relationType']),
                'status' => intval($arrExamInfo['status']),
                'answerInfo' => $arrExamInfo['answerInfo'],
                'extData' => $arrExamInfo['extData'],
                'createTime' => intval($arrExamInfo['createTime']),
                'updateTime' => intval($arrExamInfo['updateTime']),
                'bestScore' => intval($arrExamInfo['best_score']),
                'bestEnterTime' => intval($arrExamInfo['best_enter_time']),
                'bestDuration' => intval($arrExamInfo['best_duration'])
            );
            $arrOutPut['answerInfo'][] = $examStudentInfo;
        }

        return $arrOutPut;
    }


    /**
     * 输出数据格式化
     * @param  [array]  $arrExamInfo [答题信息]
     * @return [array]              [处理后的信息]
     */
    private function  __fetchAnswerInfo($arrExamInfo){
        if(!$arrExamInfo || !is_array($arrExamInfo)){
            Bd_Log::warning("Error[param error]");
            return false;
        }
        if(isset($arrExamInfo[0]) &&  isset($arrExamInfo[0]['isFinish'])){
            $examAswerInfo = array();
            if(count($arrExamInfo)>1){
                $score = 0;
                foreach ($arrExamInfo as $examInfo) {
                    if($examInfo['isFinish']==0){
                        continue;
                    }
                    if($examInfo['score']>=$score){
                        $examAswerInfo = $examInfo['examlist'];
                        $score = $examInfo['score'];
                    }
                }
            }else{
                if($arrExamInfo[0]['isFinish'] == 1) {
                    $examAswerInfo = $arrExamInfo[0]['examlist'];
                }
            }
            $arrExamInfo = $examAswerInfo;
        }
        return $arrExamInfo;
    }

    /**
     * 获取学生列表答题信息（仅供蓝鲸统计使用，其它调用如有问题后果自负）
     * @param  [int]  $lessonId   [章节id]
     * @param  [array]  $studentUids [学生uid列表]
     * @param  integer $examType   [习题类型，默认为课后作业]
     * @return [array|booler]
     */
    public function getStudentAnswerInfoByUids($lessonId,$studentUids,$examType=7){

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

        if(!$lessonId || !is_array($studentUids)){
            return false;
        }
        $examId = $this->getEameIdByLessonId($lessonId,$examType);
        if(!$examId){
            Bd_Log::warning("Error[not find examId] lessonId[$lessonId] student_uid[$studentUid]");
            return false;
        }

        //获取学生答题情况
        $arrConds = array(
            'lesson_id'=>$lessonId,
            'student_uid in ('.implode(",", $studentUids).')'
        );
        $arrFields = array(
            'answer_info','create_time','student_uid','status'
        );
        $examPaperStudentInfoList = $this->_ObjDaoExamPaperStudent->getListByConds($examId,$arrConds,$arrFields);
        if(empty($examPaperStudentInfoList)){
            Bd_Log::warning("Error[not find record] lessonId[$lessonId] student_uid[$studentUid] examId[$examId]");
            return false;
        }
        //end

        //组装返回结果
        $return  = array();
        foreach ($examPaperStudentInfoList as  $examPaperStudentInfo) {
            $rsDataFinal = $examList = $rsData = array();//容器初始化
            $examList = json_decode($examPaperStudentInfo['answer_info'],true);
            if(count($examList)>0){
                foreach ($examList as $key => $value) {
                    if(!strpos($key,"_")){
                        $rsData[$key] = array($value['answer']);
                    }else{
                        $keyL = explode("_", $key);
                        $rsData[$keyL[0]][] = $value['answer'];
                    }
                    
                }
            }
            $rsDataFinal['studentAnswer'] = $rsData;
            $rsDataFinal['submitTime'] = $examPaperStudentInfo['create_time'];
            $rsDataFinal['status'] = $examPaperStudentInfo['status'];
            $return[$examPaperStudentInfo['student_uid']] = $rsDataFinal;
        }
        return $return;
    }


    public function getAnswerSubmitTimeByStudents($lessonId,$studentUids,$examType=7){

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

        if(!$lessonId || !is_array($studentUids)){
            return false;
        }

//        $lessonId = 178032 ; $examType = 9;
        $examId = $this->getEameIdByLessonId($lessonId,$examType);

        if(!$examId){
            Bd_Log::warning("Error[not find examId] lessonId[$lessonId] student_uid[$studentUid]");
            return false;
        }

        $obj = new Hkzb_Ds_Examrpc_ExamRpcApi();


        $examPaperStudentInfoList = $obj->getAnswerSubmitTimeByStudentUidRpc($examId,$studentUids,$examType);

        if(empty($examPaperStudentInfoList)){
            Bd_Log::warning("Error[not find record] lessonId[$lessonId] student_uid[$studentUid] examId[$examId]");
            return false;
        }


        //组装返回结果
        $return  = array();
        foreach ($examPaperStudentInfoList as  $examPaperStudentInfo) {
            $return[$examPaperStudentInfo['student_uid']] = $examPaperStudentInfo['create_time'];
        }

        return $return;
    }


    /**
     * 通过学生列表获取学生试卷提交时间
     * @param  [type]  $lessonId    [description]
     * @param  [type]  $studentUids [description]
     * @param  integer $examType    [description]
     * @return [array]               [description]
     */
/*    public function getAnswerSubmitTimeByStudents($lessonId,$studentUids,$examType=7){

        //<<<rdtrack<<<
        $rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }
        //>>>rdtrack>>>

        if(!$lessonId || !is_array($studentUids)){
            return false;
        }
        $examId = $this->getEameIdByLessonId($lessonId,$examType);
        if(!$examId){
            Bd_Log::warning("Error[not find examId] lessonId[$lessonId] student_uid[$studentUid]");
            return false;
        }

        //获取学生答题情况
        $arrConds = array(
            'lesson_id'=>$lessonId,
            'student_uid in ('.implode(",", $studentUids).')'
        );
        $arrFields = array(
            'create_time','student_uid'
        );
        $examPaperStudentInfoList = $this->_ObjDaoExamPaperStudent->getListByConds($examId,$arrConds,$arrFields);
        if(empty($examPaperStudentInfoList)){
            Bd_Log::warning("Error[not find record] lessonId[$lessonId] student_uid[$studentUid] examId[$examId]");
            return false;
        }
        //end

        //组装返回结果
        $return  = array();
        foreach ($examPaperStudentInfoList as  $examPaperStudentInfo) {
            $return[$examPaperStudentInfo['student_uid']] = $examPaperStudentInfo['create_time'];
        }
        return $return;
    }*/

    /*
     * 获取学生试卷答题情况
     *
     * */
    public function getResultInfo($intStudentUid ,$intExamId) {

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

        // 校验参数
        if($intStudentUid <= 0 || $intExamId <= 0) {
            Bd_Log::warning("param error , studentId[$intStudentUid] , examId[$intExamId]");
            return false;
        }

        // 查找到对应的学生信息
        $objStudetn = new Hkzb_Ds_Fudao_Student();
        $arrStudentInfo = $objStudetn->getStudentInfo($intStudentUid);


        // 根据学生  查找对应试卷
        $arrStudentAnswerInfo = $this->getStudentExamInfoById($intExamId, $intStudentUid);

        // 获取试卷信息
        $arrExamInfo = $this->_objDsExamPaper->getExamInfoById($intExamId);

        // 试卷绑定信息
        $arrPaperRelation = $this->getStudentExamInfoById($intExamId,$intStudentUid);
        $intLessonId = $arrPaperRelation['lessonId'];
        $objLesson = new Hkzb_Ds_Fudao_Lesson();
        $arrLessonInfo = $objLesson->getLessonInfo($intLessonId);
        $intCourseId = $arrLessonInfo['courseId'];



        $objCourse = new Hkzb_Ds_Fudao_Course();
        $arrCourseInfo = $objCourse->getCourseInfo($intCourseId);
        $strTeacherName = $arrCourseInfo['teacherName'];
        $intTeacherUid  = $arrCourseInfo['teacherUid'];
        $objTeacher  = new Hkzb_Ds_Fudao_Teacher();
        $teacherInfo = $objTeacher->getTeacherInfo($intTeacherUid);




        $arrOutPut = array();
        $arrOutPut['basicInfo']['examName'] = $arrExamInfo['title'];
        $arrOutPut['basicInfo']['userName'] = isset($arrStudentInfo['studentName']) ? $arrStudentInfo['studentName'] : "";
        $arrOutPut['basicInfo']['grade'] = $arrCourseInfo['grade'];
        $arrOutPut['basicInfo']['gradeName'] = Hk_Util_Category::$GRADE[$arrCourseInfo['grade']];
        $arrOutPut['basicInfo']['fullScore'] = intval($arrExamInfo['content']['tot'] / 10);
        $arrOutPut['basicInfo']['stuScore'] = $arrStudentAnswerInfo['bestScore'];      // 学生答了多少分
        $arrOutPut['basicInfo']['answerDuration'] = intval($arrStudentAnswerInfo['extData']['answerTime']);     // 学生答题用了多少秒
        $arrOutPut['basicInfo']['rightNum'] = intval($arrStudentAnswerInfo['extData']['correctNum']);
        $arrOutPut['basicInfo']['totalNum'] = count($arrExamInfo['tidList']);
        $strteacherAvatar = Hkzb_Util_FuDao::getFileUrl(trim($teacherInfo['teacherAvatar']));
        $arrOutPut['teacherMarkInfo']['teacherAvatar'] = isset($strteacherAvatar) ? $strteacherAvatar : '';
        $arrOutPut['teacherMarkInfo']['teacherName'] = $teacherInfo['teacherName'];

        $extData = $arrStudentAnswerInfo['extData'];
        $isSignList = $extData['arrIsSignTidList'];
        $arrAnswerCard = array();

        $arrController = array();
        $flag = 1;
        foreach($arrStudentAnswerInfo['answerInfo'] as $key => $val) {

            $controller = array();
            $tmp = array();
            $tmp['isSign'] = 0;
            if(in_array($key , $isSignList)) {
                $tmp['isSign'] = 1;
            }

            $tmp['topicType'] = $val['topicType'];
            $tmp['topicIndex'] = $flag;
//            if(is_string($key)) {
            $tmp['littleTid'] = is_string($key)?$key:'';
            $intTid = intval($val['tid']);
            $tmp['tid'] = $intTid;
            $tmp['userAnswer'] = isset($val['answer']) ? $val['answer'] : array();

            $collectionStatus = (new Hkzb_Ds_Fudao_ExerciseNote())->getExerciseNoteInfoByTid($intStudentUid,$intLessonId,$intTid,array('deleted'));
            if(empty($collectionStatus) || $collectionStatus['deleted']==1) {
                $controller = array(
                    'tid' => $intTid,
                    'isCollection' => Hkzb_Ds_Fudao_ExerciseNote::DEL_STATUS_OK,
                );
            }else{
                $controller = array(
                    'tid' => $intTid,
                    'isCollection' => Hkzb_Ds_Fudao_ExerciseNote::DEL_STATUS_DELETED,
                );
            }

//            }else {
//                $tmp['littleId'] = '';
//                $tmp['tid'] = $key;
//                $tmp['userAnswer'] = $val['answer'];
//                // 收藏
//                $collectionStatus = (new Hkzb_Ds_Fudao_ExerciseNote())->getExerciseNoteInfoByTid($intStudentUid,$intLessonId,$key,array('deleted'));
//                if(empty($collectionStatus) || $collectionStatus['deleted']==1) {
//                    $controller = array(
//                        'tid' => $key,
//                        'isCollection' => Hkzb_Ds_Fudao_ExerciseNote::DEL_STATUS_DELETED,
//                    );
//                }else{
//                    $controller = array(
//                        'tid' => $key,
//                        'isCollection' => Hkzb_Ds_Fudao_ExerciseNote::DEL_STATUS_OK,
//                    );
//                }
//
//            }
            $tmp['correct'] = isset($val['correct']) ? $val['correct'] : 0;
            $arrAnswerCard[] = $tmp;
            $arrController[] = $controller;
            $flag++;
        }
        $arrOutPut['answerCardResult'] = !empty($arrAnswerCard) ? $arrAnswerCard : array();

        $arrOutPut['knowledgeDetail'] = !empty($arrStudentAnswerInfo['extData']['knowledge'])?$arrStudentAnswerInfo['extData']['knowledge']:array();
        $arrOutPut['collectionList'] = !empty($arrController) ? $arrController : array();

        return $arrOutPut;


        //$extData = $arrStudentAnswerInfo['extData'];
//        $intCourseId = $arrStudentAnswerInfo['courseId'];
//
    }


    // 修改学生作答信息
    public function updateExamPaperStudent($intExamId = 0, $id, $arrParams) {

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>
        
        if($intExamId <= 0 || empty($arrParams)) {
            return false;
        }

        $arrConds = array();

        $arrConds = array(
            'id' => $id,
        );

        if(empty($arrParams)) {
            return false;
        }

        $arrFields = array(
            'id', 'examId', 'courseId', 'lessonId', 'studentUid',
            'relationType', 'status', 'answerInfo',
            'extData', 'createTime', 'updateTime','bestScore',
        );

        $updateConds = array();
        foreach($arrParams as $key => $val) {
            if(!in_array($key, $arrFields)) {
                continue;
            }
            $updateConds[$key] = $val;
        }

        if(isset($updateConds['answerInfo'])) {
            $updateConds['answerInfo'] = json_encode($updateConds['answerInfo']);
        }
        $res = $this->_ObjDaoExamPaperStudent->updateByConds(intval($intExamId), $arrConds, $updateConds);
        return $res;
    }



}
