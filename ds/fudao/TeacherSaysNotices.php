<?php
/**
 * Created by PhpStorm.
 * User: renka<PERSON><EMAIL>
 * Date: 2018/4/23
 * Time: 12:42
 */
class Hkzb_Ds_Fudao_TeacherSaysNotices
{

    //评论或点赞者类型
    const PUBLISH_STUDENT = 0;
    const PUBLISH_TEACHER = 1;
    static $PUBLISH_ARRAY = array(
        self::PUBLISH_STUDENT => '学生',
        self::PUBLISH_TEACHER => '老师',
    );

    //类型
    const TYPE_LIKE   = 0;
    const TYPE_REVIEW_NO = 1;
    const TYPE_REVIEW_YES = 2;
    const TYPE_UNLIKE = 3;
    const TYPE_REMOVE_REVIEW = 4;
    const TYPE_SHARE_MSG = 5;
    const TYPE_COMMENT_LIKE = 6;
    const TYPE_COMMNET_UNLIKE   = 7;
    static $TYPE_ARRAY = array(
        self::TYPE_LIKE    => '点赞',
        self::TYPE_REVIEW_NO  => '未回复评论',
        self::TYPE_REVIEW_YES => '已回复评论',
        self::TYPE_UNLIKE => '取消点赞',
        self::TYPE_REMOVE_REVIEW => '删除评论',
	    self::TYPE_SHARE_MSG    => '分享动态',
	    self::TYPE_COMMENT_LIKE => '评论点赞',
	    self::TYPE_COMMNET_UNLIKE   => '取消评论点赞'
    );

    //通知状态
    const STATUS_NO  = 0;
    const STATUS_YES = 1;
    static $STATUS_ARRAY = array(
        self::STATUS_NO   => '未读',
        self::STATUS_YES  => '已读',
    );

    const ALL_FIELDS = 'id,pubUid,pubType,msgId,commentId,parentId,msgPubUid,content,type,stats,createTime,updateTime';

    private $objDaoTeacherSaysNotices;

    /**
     * 构造函数
     *
     */
    public function __construct()
    {
        $this->objDaoTeacherSaysNotices = new Hkzb_Dao_Fudao_TeacherSaysNotices();
    }

    /**
     * 新增通知
     *
     * @param  array $arrParams 通知属性
     * @return bool true/false
     */
    public function addTeacherSaysNotices($arrParams)
    {
        if (empty($arrParams) || intval($arrParams['pubUid']) <= 0 || intval($arrParams['msgId']) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[param: " . json_encode($arrParams) . "]");
            return false;
        }

        $arrFields = array(
            'pubUid'       => isset($arrParams['pubUid']) ? intval($arrParams['pubUid']) : 0,
            'pubType'      => isset($arrParams['pubType']) ? intval($arrParams['pubType']) : 0,
            'msgId'        => isset($arrParams['msgId']) ? intval($arrParams['msgId']) : 0,
            'commentId'    => isset($arrParams['commentId']) ? intval($arrParams['commentId']) : 0,
            'parentId'     => isset($arrParams['parentId']) ? intval($arrParams['parentId']) : 0,
            'msgPubUid'    => isset($arrParams['msgPubUid']) ? intval($arrParams['msgPubUid']) : 0,
            'content'      => isset($arrParams['content']) ? json_encode($arrParams['content']) : '',
            'type'         => isset($arrParams['type']) ? intval($arrParams['type']) : 0,
            'stats'       => isset($arrParams['stats']) ? intval($arrParams['stats']) : 0,
            'createTime'   => time(),
            'updateTime'   => time(),
        );

        $ret = $this->objDaoTeacherSaysNotices->insertRecords($arrParams['msgPubUid'], $arrFields);

        return intval($ret) ? $this->objDaoTeacherSaysNotices->getInsertId() : 0;
    }


    /**
     * 更新评论
     *
     * @param  int $msgPubUid    动态发布者uid，用于分表
     * @param  int $id id      通知id
     * @param  array $arrParams  通知属性
     * @return bool true/false
     */
    public function updateTeacherSaysNotices($msgPubUid, $id, $arrParams)
    {
        if (empty($arrParams) || intval($msgPubUid) <= 0 || intval($id) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id msgPubUid:$msgPubUid arrParams:$arrParams]");
            return false;
        }

        $arrConds = array(
            'id' => intval($id),
        );

        $arrFields = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach ($arrParams as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }

            $arrFields[$key] = $value;
        }

        //content由ps补全，json处理在ds
        if(isset($arrFields['content'])) {
            $arrFields['content'] = json_encode($arrFields['content']);
        }

        //补全更新时间
        $arrFields['updateTime'] = time();

        $ret = $this->objDaoTeacherSaysNotices->updateByConds($msgPubUid, $arrConds, $arrFields);

        return $ret;
    }


    /**
     * 按条件更新评论
     *
     * @param  int $msgPubUid    动态发布者uid，用于分表
     * @param  array $arrConds    条件
     * @param  array $arrParams  通知属性
     * @return bool true/false
     */
    public function updateTeacherSaysNoticesByConds($msgPubUid, $arrConds, $arrParams)
    {
        if (intval($msgPubUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[msgPubUid:$msgPubUid]");
            return false;
        }

        $ret = $this->objDaoTeacherSaysNotices->updateByConds($msgPubUid, $arrConds, $arrParams);

        return $ret;
    }


    /**
     * 获取指定通知信息
     *
     * @param  int $msgPubUid    动态发布者uid，用于分表
     * @param  int $id id       通知id
     * @param  array $arrFields 通知属性
     * @return array|false
     */
    public function getTeacherSaysNoticesInfo($msgPubUid, $id, $arrFields = array())
    {
        if (intval($msgPubUid) <= 0 || intval($id) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id msgPubUid:$msgPubUid]");
            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'id' => intval($id),
        );

        $ret = $this->objDaoTeacherSaysNotices->getRecordByConds($msgPubUid, $arrConds, $arrFields);

        return $ret;
    }


    /**
     * 根据条件获取通知信息个数
     *
     * @param  int $msgPubUid   动态发布者uid，用于分表
     * @param  array $arrConds  条件
     * @return array|false
     */
    public function getTeacherSaysNoticesCntByConds($msgPubUid, $arrConds)
    {
        if (intval($msgPubUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[msgPubUid:$msgPubUid]");
            return false;
        }

        $ret = $this->objDaoTeacherSaysNotices->getCntByConds($msgPubUid, $arrConds);

        return $ret;
    }


    /**
     * 根据条件获取通知信息
     *
     * @param  int $msgPubUid   动态发布者uid，用于分表
     * @param  array $arrConds  条件
     * @param  array $arrFields 通知属性
     * @param  array  $arrAppends
     * @return array|false
     */
    public function getTeacherSaysNoticesByConds($msgPubUid, $arrConds, $arrFields = array(), $arrAppends = array())
    {
        if (intval($msgPubUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[msgPubUid:$msgPubUid]");
            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        if (empty($arrAppends)) {
            $arrAppends = null;
        }

        $ret = $this->objDaoTeacherSaysNotices->getListByConds($msgPubUid, $arrConds, $arrFields, NULL, $arrAppends);
        return $ret;
    }


    /**
     * 删除通知
     *
     * @param  int $msgPubUid   动态发布uid，用于分表
     * @param  int  $id        通知id
     * @return bool true/false
     */
    public function deleteTeacherSaysNotices($msgPubUid, $id)
    {
        if (intval($msgPubUid) <= 0 || intval($id) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id msgPubUid:$msgPubUid]");
            return false;
        }

        $noticeInfo = self::getTeacherSaysNoticesInfo($msgPubUid, $id);
        $type = $noticeInfo['type'];
        $ret = false;
        if ($type == self::TYPE_LIKE) {  //点赞通知
            $arrParams = array(
                'type' => self::TYPE_UNLIKE,
            );
            $ret = $this->updateTeacherSaysNotices($msgPubUid, $id, $arrParams);
        }
        else {  //评论通知
            $arrParams = array(
                'type' => self::TYPE_REMOVE_REVIEW,
            );
            $ret = $this->updateTeacherSaysNotices($msgPubUid, $id, $arrParams);
        }

        return $ret;
    }


    /**
     * 删除通知（不是该状态）
     *
     * @param  int $msgPubUid   动态发布uid，用于分表
     * @param  array  $arrConds    条件
     * @return bool true/false
     */
    public function deleteTeacherSaysNoticesByConds($msgPubUid, $arrConds)
    {
        if (intval($msgPubUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[msgPubUid:$msgPubUid]");
            return false;
        }

        $ret = $this->objDaoTeacherSaysNotices->deleteByConds($msgPubUid, $arrConds);

        return $ret;
    }
}
