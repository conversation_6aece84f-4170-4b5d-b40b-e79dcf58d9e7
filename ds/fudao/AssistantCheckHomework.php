<?php
/***************************************************************************
 *
 * Copyright (c) 2015 zybang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file AssistantCheckHomework.php
 * <AUTHOR>
 * @date 2017/08/28 16:16:24
 * @brief 作业质检信息表
 *
 **/
class Hkzb_Ds_Fudao_AssistantCheckHomework {
	const DELETED_NO  = 0;
	const DELETED_YES = 1;
	static $DELETED_ARRAY = array(
		self::DELETED_NO  => '正常',
		self::DELETED_YES => '删除',
	);
    const QUALIFIED_YES  = 1;
    const QUALIFIED_NO   = 0;
    static $QUALIFIED_ARRAY = array(
        self::QUALIFIED_YES  => '合格',
        self::QUALIFIED_NO   => '不合格',
    );
    static $NOT_PASS_REASON = array(
        array(
            'reasonCode' => 1,
            'describe'  =>'其它',
            'sort'      => 10000,//排序在最后
        ),
        array(
            'reasonCode' => 2,
            'describe'  => '批改错误',
            'sort'      => 1,
        ),
        array(
            'reasonCode' => 3,
            'describe'  => '批改字迹潦草、不工整',
            'sort'      => 2,
        ),
        array(
            'reasonCode' => 4,
            'describe'  => '无语音讲解',
            'sort'      => 3,
        ),
        array(
            'reasonCode' => 5,
            'describe'  => '语音讲解错误',
            'sort'      => 4,
        ),
        array(
            'reasonCode' => 6,
            'describe'  => '语音讲解不清晰、有杂音',
            'sort'      => 5,
        ),
        array(
            'reasonCode' => 7,
            'describe'  => '语音讲解的普通话不标准',
            'sort'      => 6,
        ),
    );


	const ALL_FIELDS='id,studentUid,lessonId,answerUid,qualified,reasonCode,operatorUid,deleted,createTime,updateTime,extData';

	private $objDaoAssistantCheckHomework;

	private $orderBy = array('create_time','update_time');

	/**
	 * 构造函数
	 *
	 */
	public function __construct(){
		$this->objDaoAssistantCheckHomework = new Hkzb_Dao_Fudao_AssistantCheckHomework();
	}

	/**
	 * 新增记录
	 *
	 * @param  mix  $arrParams 属性
	 * @return bool true/false
	 */
	public function addAssistantCheckHomework($arrParams){
		if(intval($arrParams['studentUid']) <=0 || intval($arrParams['lessonId']) <= 0){
			Bd_Log::warning("Error:[param error],Detail:[".json_encode($arrParams)."]");
		}
		$arrFields = array(
			'studentUid'   =>isset($arrParams['studentUid']) ? intval($arrParams['studentUid']):0,
			'lessonId'     =>isset($arrParams['lessonId']) ? intval($arrParams['lessonId']):0,
			'answerUid'    =>isset($arrParams['answerUid']) ? intval($arrParams['answerUid']):0,
			'qualified'    =>isset($arrParams['qualified']) ? intval($arrParams['qualified']):0,
			'reasonCode'   =>isset($arrParams['reasonCode']) ? intval($arrParams['reasonCode']):0,
			'operatorUid'  =>isset($arrParams['operatorUid']) ? intval($arrParams['operatorUid']):0,
			'deleted'      =>self::DELETED_NO,
			'createTime'   =>time(),
			'updateTime'   =>time(),
			'extData'      =>isset($arrParams['extData']) ? json_encode($arrParams['extData']):'[]',
		);
		$ret = $this->objDaoAssistantCheckHomework->insertRecords($arrFields);
		if($ret){
			$ret = $this->objDaoAssistantCheckHomework->getInsertId();
		}
		return $ret;
	}
	/**
	 * 更新
	 *
	 * @param  int $id
	 * @param  array $arrParams
	 * @return bool true/false
	 */
	public function updateAssistantCheckHomework($id,$arrParams){
		if(intval($id <= 0)){
			Bd_Log::warning("Error:[param error],Detail:[id:$id]");
			return false;
		}
		$arrConds = array(
			'id' => intval($id),
		);
		$arrFields = array();
		$arrAllFields = explode(',',self::ALL_FIELDS);
		foreach($arrParams as $key => $value){
			if(!in_array($key,$arrAllFields)){
				continue;
			}
			$arrFields[$key] = $value;
		}
		if(isset($arrFields['extData'])){
			$arrFields['extData'] = json_encode($arrFields['extData']);
		}
		$arrFields['updateTime'] = time();
		$ret = $this->objDaoAssistantCheckHomework->updateByConds($arrConds,$arrFields);
		return $ret;
	}

	/**
	 * 获取记录
	 *
	 * @param  int $id
	 * @return  mix $arrFields  属性
	 */
	public function getAssistantCheckHomework($studentUid,$lessonId,$arrFields = array()){
		if(intval($studentUid <= 0) || intval($lessonId) <=0){
			Bd_Log::warning("Error:[param error],Detail:[studentUid:$studentUid lessonId:$lessonId]");
			return false;
		}
		if(empty($arrFields)){
			$arrFields = explode(',',self::ALL_FIELDS);
		}
		$arrConds = array(
			'lessonId'    => $lessonId,
		    'studentUid' => $studentUid,
		);
		$ret = $this->objDaoAssistantCheckHomework->getRecordByConds($arrConds,$arrFields);
		return $ret;
	}

	/**
	 * 获取数量
	 * @param $arrConds
	 * @return false|int
	 */
	public function getCntByConds($arrConds){
		return $this->objDaoAssistantCheckHomework->getCntByConds($arrConds);
	}

	/**
	 * @param array $arrConds
	 * @param array $arrFields
	 * @param string $order
	 * @param string $by
	 * @param int $offset
	 * @param int $limit
	 * @return list|false
	 */
	public function getListByConds($arrConds,$arrFields = array(),$order = '',$by = '',$offset = 0,$limit = 0){
		if(empty($arrFields)){
			$arrFields = explode(',',self::ALL_FIELDS);
		}

		$orderBy = '';
		if($order != '' && in_array($order,$this->orderBy)){
			$orderBy .= 'order by '. $order . ' ';
			$orderBy .= ($by == 'desc') ? 'desc' : 'asc';
		}
		if($offset >= 0 && $limit > 0 ){
			$orderBy .= " limit $offset,$limit";
		}else if($limit > 0){
			$orderBy .= " limit $limit";
		}
		$arrAppends = ($orderBy != '')? array($orderBy) : null;
		$ret = $this->objDaoAssistantCheckHomework->getListByConds($arrConds,$arrFields,NULL,$arrAppends);
		return $ret;
	}
}
