<?php

/**
 * @file   GuoyuanPlayerLog.php
 * <AUTHOR>
 * @date   2016-03-11
 * @brief  果园播放器日志
 *
 **/
class Hkzb_Ds_Fudao_GuoyuanPlayerLog
{
    const OS_TYPE_WEB     = 'web';
    const OS_TYPE_ANDROID = 'android';
    const OS_TYPE_IOS     = 'ios';
    public $_osTypeIdMap = array(
        self::OS_TYPE_WEB     => 1,
        self::OS_TYPE_ANDROID => 2,
        self::OS_TYPE_IOS     => 3,
    );

    public $_osTypeNameMap = array(
        1 => self::OS_TYPE_WEB,
        2 => self::OS_TYPE_ANDROID,
        3 => self::OS_TYPE_IOS,
    );


    private $objDaoGuoyuanPlayerLog;

    /**
     * 构造函数
     *
     */
    public function __construct() {
        $this->objDaoGuoyuanPlayerLog = new Hkzb_Dao_Fudao_GuoyuanPlayerLog();
    }


    /**
     * 新增记录
     *
     * @param  array $arrParams
     * @return bool true/false
     */
    public function addRecord($arrParams) {
        if (!intval($arrParams['lessonId']) || !intval($arrParams['uid'])) {
            Bd_Log::warning("Error:[param error], Detail:[param: " . json_encode($arrParams) . "]");

            return false;
        }

        $arrFields = array(
            'uid'      => intval($arrParams['uid']),
            'lessonId' => strval($arrParams['lessonId']),
            'ctime'    => isset($arrParams['ctime']) ? intval($arrParams['ctime']) : time(),
            'action'   => strval($arrParams['action']),
            'os'       => strval($arrParams['os']),
            'extBit'   => isset($arrParams['extBit']) ? intval($arrParams['extBit']) : 0,
            'extData'  => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '[]',
        );

        $ret = $this->objDaoGuoyuanPlayerLog->insertRecords($arrFields);

        return $ret;
    }

    /**
     * @param       $lessonId
     * @param       $uid
     * @param array $arrFields
     * @return bool
     */
    public function getCallRecordInfoByUid($lessonId, $uid, $arrFields = array()) {
        if (!intval($lessonId) || !intval($uid)) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$lessonId uid:$uid]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = Hkzb_Dao_Fudao_GuoyuanPlayerLog::$allFields;
        }

        $arrConds = array(
            'lessonId' => intval($lessonId),
            'uid'      => intval($uid),
        );

        $ret = $this->objDaoGuoyuanPlayerLog->getRecordByConds($arrConds, $arrFields);

        return $ret;
    }

    /**
     * @param $lessonId
     * @param $uid
     * @return bool|false|int
     */
    public function getCallRecordCntByUid($lessonId, $uid) {
        if (!intval($lessonId) || !intval($uid)) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$lessonId uid:$uid]");

            return false;
        }

        $arrConds = array(
            'lessonId' => intval($lessonId),
            'uid'      => intval($uid),
        );

        $ret = $this->objDaoGuoyuanPlayerLog->getCntByConds($arrConds);

        return $ret;
    }

    /**
     * 返回当前库中所有的课节
     * @return array|false
     */
    public function getLessonList() {
        $arrFields = array('distinct lesson_id as lessonId');
        $ret       = $this->objDaoGuoyuanPlayerLog->getListByConds(array('1=1'), $arrFields);

        return $ret;
    }

    /**
     * 获取当前课节所有学生列表
     * @param $lessonId
     * @return array|bool|false
     */
    public function getUidListByLesson($lessonId) {
        if (!intval($lessonId)) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$lessonId]");

            return false;
        }
        $arrFields = array('distinct uid');
        $arrConds  = array(
            'lessonId' => intval($lessonId),
        );
        $ret       = $this->objDaoGuoyuanPlayerLog->getListByConds($arrConds, $arrFields);

        return $ret;
    }


    /**
     * 获取当前课节所有学生列表
     * @param $lessonId
     * @param $uid
     * @return array|bool|false
     */
    public function getUserLog($lessonId, $uid) {
        if (!intval($lessonId) || !intval($uid)) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$lessonId uid:$uid]");

            return false;
        }
        $arrFields  = Hkzb_Dao_Fudao_GuoyuanPlayerLog::$allFields;
        $arrConds   = array(
            'lessonId' => intval($lessonId),
            'uid'      => intval($uid),
        );
        $arrAppends = array('order by ctime');
        $ret        = $this->objDaoGuoyuanPlayerLog->getListByConds($arrConds, $arrFields, null, $arrAppends);

        return $ret;
    }

    /**
     * 获取当前课节所有学生在线时长
     * @param $lessonId
     * @param $startTime
     * @param $stopTime
     * @return array|bool|false
     */
    public function getLessonOnlineTime($lessonId, $startTime, $stopTime) {
        if (!intval($lessonId)) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$lessonId]");

            return false;
        }
        $arrFields  = array('uid', 'max(ctime) maxTime', 'min(ctime) minTime');
        $arrConds   = array(
            'lessonId' => intval($lessonId),
        );
        $arrAppends = array('group by uid');
        $ret        = $this->objDaoGuoyuanPlayerLog->getListByConds($arrConds, $arrFields, null, $arrAppends);
        if (!$ret) {
            return $ret;
        }
        foreach ($ret as $n => $record) {
            $onlineTime            = max(min($record['maxTime'], $stopTime) - max($record['minTime'], $startTime), 0);
            $ret[$n]['onlineTime'] = $onlineTime;
        }

        return $ret;
    }

    /**
     *根据lessonID获取全部学生的日志
     * @param $lessonId
     * @return array|bool|false
     */
    public function getUserLogList($lessonId) {
        if (!intval($lessonId)) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$lessonId ]");

            return false;
        }
        $arrFields  = Hkzb_Dao_Fudao_GuoyuanPlayerLog::$allFields;
        /*
        $logField=<<<SQL
concat('[',group_concat(concat('["',ctime,'","',action,'","',os,'","',id,'"]')),']') as logs
SQL;
         */
        $logField=<<<SQL
concat(group_concat(concat(ctime,';',action,';',os,';',id,';',ext_data,';'),'|')) as logs
SQL;

        $arrFields =array(
            'uid',
            'lessonId',
            $logField,
            'extData'
        );
        $arrConds   = array(
            'lessonId' => intval($lessonId),
        );
        $arrAppends = array('group by uid');
        $ret        = $this->objDaoGuoyuanPlayerLog->getListByConds($arrConds, $arrFields, null, $arrAppends);
        return $ret;
    }

    /**
     * 根据动作获取日志
     * @param $lessonId
     * @param $action
     * @return array|bool;
     */
    public function getPlayerLogByAction($lessonId, $action) {
         if (!intval($lessonId) || $action == null) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$lessonId uid:$action]");
            return false;
        }

        $arrFields  = Hkzb_Dao_Fudao_GuoyuanPlayerLog::$allFields;
        $arrConds   = array(
            'lessonId' => intval($lessonId),
            'action'      => $action,
        );
        $arrAppends = array('order by ctime');
        $ret = $this->objDaoGuoyuanPlayerLog->getListByConds($arrConds, $arrFields, null, $arrAppends);
        return $ret;
    }


}
