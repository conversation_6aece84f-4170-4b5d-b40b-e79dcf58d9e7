<?php
/***************************************************************************
 *
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file BookSendList.php
 * <AUTHOR>
 * @date 2017/4/24
 * @brief 教材发送列表
 * 
 **/

class Hkzb_Ds_Fudao_BookSendList {
    //状态
    const STATUS_UNDERWAY = 0; //生成中
    const STATUS_UNIMPORT = 1; //未导入
    const STATUS_IMPORT   = 2; //已导入
    static $STATUS_ARRAY = array(
        self::STATUS_UNDERWAY => '生成中',
        self::STATUS_UNIMPORT => '未导入',
        self::STATUS_IMPORT   => '已导入',
    );
    //类型
    const TYPE_COMMON = 0; 
    const TYPE_LATEST = 1;
    static $TYPE_ARRAY = array(
        self::TYPE_COMMON => '普通数据',
        self::TYPE_LATEST => '最新课程ids',
    );
    const ALL_FIELDS = 'listId,packCourseIds,orderCnt,expressCnt,type,status,createTime,extData';

    private $objDaoBookSendList;

    /**
     * 构造函数
     *
     */
    public function __construct() {
        $this->objDaoBookSendList = new Hkzb_Dao_Fudao_BookSendList();
    }

    /**
     * 新增数据
     *
     * @param  array  $arrParams 发送数据属性
     * @return bool true/false
     */
    public function addBookSendList($arrParams) {
        if(empty($arrParams['packCourseIds'])) {
            Bd_Log::warning("Error:[param error], Detail:[param: ". json_encode($arrParams). "]");
            return false;
        }
        $arrFields = array(
            'packCourseIds' => isset($arrParams['packCourseIds']) ? strval($arrParams['packCourseIds']) : '',
            'orderCnt'      => 0,
            'expressCnt'    => 0,
            'status'        => self::STATUS_UNDERWAY, //初始状态为生成中
            'type'          => isset($arrParams['type']) ? intval($arrParams['type']) : 0,
            'createTime'    => time(),
            'extData'       => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
        );
        $ret = $this->objDaoBookSendList->insertRecords($arrFields);
        return $ret;
    }
    /**
     * 获取教材发送列表详情
     *
     * @param  int  $listId  列表id
     * @param  mix  $arrFields 指定属性
     * @return mix
     */
    public function getBookSendListInfo($listId, $arrFields = array()) {
        if(intval($listId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[listId:$listId]");
            return false;
        }
        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrConds = array(
            'listId' => intval($listId),
        );
        $ret = $this->objDaoBookSendList->getRecordByConds($arrConds, $arrFields);
        return $ret;

    }
    
    /**
     * @param $arrConds
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getBookSendListByConds($arrConds, $arrFields = array(), $offset = 0, $limit = 20) {
        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrAppends = array(
            "order by create_time desc",
            "limit $offset, $limit",
        );

        $ret = $this->objDaoBookSendList->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }
    
    /**
     * 获取数量
     * @param $arrConds
     * @return false|int
     */
    public function getBookSendListCntByConds($arrConds) {
        $ret = $this->objDaoBookSendList->getCntByConds($arrConds);
        return $ret;
    }
    
    /**
     * 更新
     *
     * @param  int $listId  列表id
     * @param  array $arrParams 属性
     * @return bool true/false
     */
    public function updateBookSendList($listId, $arrParams){
        if (intval($listId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[listId:$listId]");

            return false;
        }

        $arrConds = array(
            'listId' => intval($listId),
        );

        $arrFields    = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach ($arrParams as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }

            $arrFields[$key] = $value;
        }

        //ext由ps补全，json处理在ds
        if (isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }

        $ret = $this->objDaoBookSendList->updateByConds($arrConds, $arrFields);

        return $ret;
    }

}