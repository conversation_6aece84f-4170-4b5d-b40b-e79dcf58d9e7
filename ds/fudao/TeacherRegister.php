<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file TeacherRegister.php
 * <AUTHOR>
 * @date 2016/08/15 13:26:46
 * @brief 老师招募表
 *  
 **/

class Hkzb_Ds_Fudao_TeacherRegister {

    const FILE_STATUS_UNFINISH  = 0; //待审核
    const FILE_STATUS_APPROVE   = 1; //已通过
    const FILE_STATUS_UNAPPROVE = 2; //未通过
    static $FILE_STATUS_ARRAY = array(
        self::FILE_STATUS_UNFINISH  => '待审核',
        self::FILE_STATUS_APPROVE   => '已通过',
        self::FILE_STATUS_UNAPPROVE => '未通过',
    );
    const VIDEO_STATUS_UNFINISH  = 0; //未上传
    const VIDEO_STATUS_FINISHED  = 1; //待审核
    const VIDEO_STATUS_APPROVE   = 2; //已通过
    const VIDEO_STATUS_UNAPPROVE = 3; //未通过重新提交
    const VIDEO_STATUS_NOCHANCE  = 4; //未通过不再提交
    static $VIDEO_STATUS_ARRAY = array(
        self::VIDEO_STATUS_UNFINISH  => '未上传',
        self::VIDEO_STATUS_FINISHED  => '待审核',
        self::VIDEO_STATUS_APPROVE   => '已通过',
        self::VIDEO_STATUS_UNAPPROVE => '未通过重新提交',
        self::VIDEO_STATUS_NOCHANCE  => '未通过不再提交',
    );
    const CONTRACT_ON_DUE   = 0; //已签约
    const CONTRACT_OVER_DUE = 1; //合同已过期
    const CONTRACT_FORZEN   = 2; //合同已冻结
    const CONTRACT_NO       = 3; //待审核
    const CONTRACT_SET      = 4; //设置签约条件
    const CONTRACT_NO_DUE   = 5; //待签约
    static $CONTRACT_ARRAY = array(
        self::CONTRACT_ON_DUE   => '已签约',
        self::CONTRACT_OVER_DUE => '合同已过期',
        self::CONTRACT_FORZEN   => '合同已冻结',
        self::CONTRACT_NO       => '待审核',
        self::CONTRACT_SET      => '设置签约条件',
        self::CONTRACT_NO_DUE   => '待签约',
    );
    const DELETED_OK      = 0; //正常
    const DELETED_DELETED = 1; //已删除
    static $DELETED_ARRAY = array(
        self::DELETED_OK      => '正常',
        self::DELETED_DELETED => '已删除',
    ); 
    const NETWORK_STATUS_UNFINISH  = 0; //网速未测试
    const NETWORK_STATUS_APPROVE   = 1; //已通过 
    const NETWORK_STATUS_UNAPPROVE = 2; //未通过
    const NETWORK_STATUS_FINISHED  = 3; //待审核
    static $NETWORK_STATUS_ARRAY = array(
        self::NETWORK_STATUS_UNFINISH  => '未测试',
        self::NETWORK_STATUS_APPROVE   => '已通过',
        self::NETWORK_STATUS_UNAPPROVE => '未通过',
        self::NETWORK_STATUS_FINISHED  => '待审核',
    );
    const TRAIN_STATUS_UNFINISH  = 0; //未培训
    const TRAIN_STATUS_APPROVE   = 1; //已通过
    const TRAIN_STATUS_UNAPPROVE = 2; //未通过
    const TRAIN_STATUS_FINISHED  = 3; //待审核
    static $TRAIN_STATUS_ARRAY = array(
        self::TRAIN_STATUS_UNFINISH  => '未培训',
        self::TRAIN_STATUS_APPROVE   => '已通过',
        self::TRAIN_STATUS_UNAPPROVE => '未通过',
        self::TRAIN_STATUS_FINISHED  => '待审核',
    );
    const ALL_FIELDS = 'id,uid,phone,name,area,grade,subject,teachAge,introduce,fileStatus,videoStatus,networkStatus,videoTime,contract,deleted,operatorUid,operator,createTime,updateTime,extData';

    private $objDaoTeacherRegister;

    /**
     * 构造函数
     *
     */
    public function __construct() {
        $this->objDaoTeacherRegister = new Hkzb_Dao_Fudao_TeacherRegister();
    }

    /**
     * 新增老师注册记录
     *
     * @param  mix  $arrParams 老师注册记录属性
     * @return bool true/false
     */
    public function addTeacherRegister($arrParams) {

        $arrFields = array(
            'uid'           => isset($arrParams['uid']) ? intval($arrParams['uid']) : 0,
            'phone'         => isset($arrParams['phone']) ? strval($arrParams['phone']) : '',
            'name'          => isset($arrParams['name']) ? strval($arrParams['name']) : '',
            'area'          => isset($arrParams['area']) ? strval($arrParams['area']) : '',
            'grade'         => isset($arrParams['grade']) ? intval($arrParams['grade']) : 0,
            'subject'       => isset($arrParams['subject']) ? strval($arrParams['subject']) : '',
            'teachAge'      => isset($arrParams['teachAge']) ? intval($arrParams['teachAge']) : 0,
            'introduce'     => isset($arrParams['introduce']) ? strval($arrParams['introduce']) : '',
            'fileStatus'    => self::FILE_STATUS_UNFINISH,
            'videoStatus'   => self::VIDEO_STATUS_UNFINISH,
            'videoTime'     => 0,
            'contract'      => self::CONTRACT_NO,
            'deleted'       => self::DELETED_OK,
            'operatorUid'   => isset($arrParams['operatorUid']) ? intval($arrParams['operatorUid']) : 0,
            'operator'      => isset($arrParams['operator']) ? strval($arrParams['operator']) : '',
            'createTime'    => time(),
            'updateTime'    => time(),
            'extData'       => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
        );

        $ret = $this->objDaoTeacherRegister->insertRecords($arrFields);
        
        return $ret;
    }

    /**
     * 检查老师是否已经注册过
     *
     * @param  int  $uid  uid
     * @param  mix  $arrFields 指定属性
     * @return mix
     */
    public function checkTeacherValid($uid){
        if($uid <= 0){
            Bd_Log::warning("Error:[param error], Detail:[uid:$uid ]");
            return false;
        }
        $arrConds = array(
            'uid'     => $uid,
            'deleted' => self::DELETED_OK,
        );

        $ret = $this->objDaoTeacherRegister->getRecordByConds($arrConds, array('id'));
        return $ret;
    }

    /**
     * 重新提交指定用户信息
     *
     * @param  int  $uid  uid
     * @param  mix  $arrFields 指定属性
     * @return mix
     */
    public function submitTeacherRegisterByUid($uid, $arrParams){
        if($uid <= 0){
            Bd_Log::warning("Error:[param error], Detail:[uid:$uid ]");
            return false;
        }
        $arrConds = array(
            'uid'     => $uid,
            'deleted' => self::DELETED_OK,
        );

        $arrFields = array(
            'name'          => isset($arrParams['name']) ? strval($arrParams['name']) : '',
            'area'          => isset($arrParams['area']) ? strval($arrParams['area']) : '',
            'grade'         => isset($arrParams['grade']) ? intval($arrParams['grade']) : 0,
            'subject'       => isset($arrParams['subject']) ? strval($arrParams['subject']) : '',
            'teachAge'      => isset($arrParams['teachAge']) ? intval($arrParams['teachAge']) : 0,
            'introduce'     => isset($arrParams['introduce']) ? strval($arrParams['introduce']) : '',
            'fileStatus'    => self::FILE_STATUS_UNFINISH,
            'operatorUid'   => isset($arrParams['operatorUid']) ? intval($arrParams['operatorUid']) : 0,
            'operator'      => isset($arrParams['operator']) ? strval($arrParams['operator']) : '',
            'createTime'    => time(),
            'updateTime'    => time(),
            'extData'       => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
        );

        $ret = $this->objDaoTeacherRegister->updateByConds($arrConds, $arrFields);
        return $ret;
    }
    /**
     * 更新操作
     *
     * @param  int  $uid  uid
     * @param  mix  $arrFields 指定属性
     * @return mix
     */
    public function updateTeacherRegisterByUid($uid, $arrParams){
        if($uid <= 0){
            Bd_Log::warning("Error:[param error], Detail:[uid:$uid ]");
            return false;
        }
        $arrConds = array(
            'uid'     => $uid,
            'deleted' => self::DELETED_OK,
        );
        $arrFields = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach($arrParams as $key => $value) {
            if(!in_array($key, $arrAllFields)) {
                continue;
            }

            $arrFields[$key] = $value;
        }

        //ext由ps补全，json处理在ds
        if(isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }

        $ret = $this->objDaoTeacherRegister->updateByConds($arrConds, $arrFields);
        return $ret;
    }

    /**
     * 更新扩展字段
     *
     * @param  int  $uid  uid
     * @param  mix  $arrFields 指定属性
     * @return mix
     */
    public function updateTeacherRegisterExt($uid, $arrExt) {
        if (intval($uid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[uid:$uid]");
            return false;
        }

        $this->objDaoTeacherRegister->startTransaction();

        $teacherRegisterInfo = $this->getTeacherRegisterInfoByUid($uid, array('extData'));
        if(empty($teacherRegisterInfo)) {
            Bd_Log::warning("Error:[teacherRegister empty], Detail:[uid:$uid]");
            $this->objDaoTeacherRegister->rollback();
            return false;
        }

        $arrTeacherRegisterExt = $teacherRegisterInfo['extData'];
        foreach($arrExt as $key => $value) {
            $arrTeacherRegisterExt[$key] = $value;
        }

        $arrConds = array(
            'uid' => intval($uid),
        );

        $arrFields    = array(
            'extData' => json_encode($arrTeacherRegisterExt),
        );

        $ret = $this->objDaoTeacherRegister->updateByConds($arrConds, $arrFields);

        $this->objDaoTeacherRegister->commit();

        return $ret;
    }

    /**
     * 获取注册老师记录
     *
     * @param  int  $uid  uid
     * @param  mix  $arrFields 指定属性
     * @return mix
     */
    public function getTeacherRegisterInfoByUid($uid, $arrFields = array()) {
        if(intval($uid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[uid:$uid ]");
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'uid'     => intval($uid),
            'deleted' => self::DELETED_OK,
        );

        $ret = $this->objDaoTeacherRegister->getRecordByConds($arrConds, $arrFields);

        return $ret;
    }
    /**
     * 获取注册老师记录
     *
     * @param  int  $phone  phone
     * @param  mix  $arrFields 指定属性
     * @return mix
     */
    public function getTeacherRegisterInfoByPhone($phone, $arrFields = array()) {
        if(empty($phone)) {
            Bd_Log::warning("Error:[param error], Detail:[phone:$phone ]");
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'phone'   => strval($phone),
            'deleted' => self::DELETED_OK,
        );

        $ret = $this->objDaoTeacherRegister->getRecordByConds($arrConds, $arrFields);

        return $ret;
    }
    /**
     * @param $arrConds
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getTeacherRegisterListByConds($arrConds, $arrFields = array(), $offset = 0, $limit = 20) {
        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrAppends = array(
            "order by create_time desc",
            "limit $offset, $limit",
        );

        $ret = $this->objDaoTeacherRegister->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /**
     * 获取数量
     * @param $arrConds
     * @return false|int
     */
    public function getTeacherRegisterCntByConds($arrConds) {
        $ret = $this->objDaoTeacherRegister->getCntByConds($arrConds);
        return $ret;
    }
}
