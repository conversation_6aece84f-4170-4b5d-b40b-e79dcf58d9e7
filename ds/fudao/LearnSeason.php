<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 zybang.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file LearnSeason.php
 * <AUTHOR>
 * @date 2016/10/21 15:30:15
 * @brief 学季
 *  
 **/

class Hkzb_Ds_Fudao_LearnSeason {
    const ALL_FIELDS = 'id,learnSeason,grade,subject,season_id,content,createTime,updateTime,image,extData';

    /**
     * 构造函数
     *
     */
    public function __construct() {
        $this->objDaoCourseCard = new Hkzb_Dao_Fudao_LearnSeason();
    }
    /**
     * 获取学季信息
     *
     * @param  string $learnSeason 学季
     * @param  int    $grade       年级
     * @param  int    $subject     学科
     * @return  mix   属性
     */
    public function getLearnSeason($learnSeason, $grade = 0, $subject = 0) {
        if(strlen($learnSeason) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[learnSeason:$learnSeason]");
            return false;
        }

        //读缓存
        $objMemcached = Hk_Service_Memcached::getInstance("zhiboke");
        $strParam = "$learnSeason $grade $subject";
        $cacheKey = 'zhibo_ds_learnseason_' . md5($strParam);
        $cacheValue = $objMemcached->get($cacheKey);
        if($cacheValue) {
            Hk_Util_Log::incrKey("zhibo_ds_learnseason_hit", 1);
            $learnSeason = json_decode(utf8_encode($cacheValue), true);
            return $learnSeason;
        }
        Hk_Util_Log::incrKey("zhibo_ds_learnseason_miss", 1);

        //学季
        $arrFields = array('content', 'seasonId', 'image', 'id');
        $arrConds  = array(
            'learnSeason' => $learnSeason,
            'grade'       => $grade,
            'subject'     => $subject,    
        );
        $objDaoLearnSeason = new Dao_LearnSeason();
        $learnSeason = $objDaoLearnSeason->getRecordByConds($arrConds, $arrFields);
        if(false === $learnSeason) {
            Bd_Log::warning("Error:[getRecordByConds error], Detail:[learnSeason:$learnSeason grade:$grade subject:$subject]");
            return false;
        }

        //写缓存
        $cacheValue = json_encode($learnSeason);
        $objMemcached->set($cacheKey, $cacheValue);

        return $learnSeason;
    }
    public function getLearnSeasonById($id){
        if(strlen($id) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id]");
            return false;
        }
        //学季
        $arrFields = array('content', 'seasonId', 'image', 'id', 'learnSeason', 'grade', 'subject');
        $arrConds  = array(
            'id'   => $id,
        );

        $learnSeason = $this->objDaoCourseCard->getRecordByConds($arrConds, $arrFields);
        if(false === $learnSeason) {
            Bd_Log::warning("Error:[getRecordByConds error], Detail:[learnSeason:$learnSeason grade:$grade subject:$subject]");
            return false;
        }

        return $learnSeason;
    }
    public function getCourseCardListByConds($arrConds, $arrFields = array(),$offset = 0, $limit = 20) {
        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrAppends = array(
            "order by create_time desc",
            " limit $offset, $limit",
        );
        $ret = $this->objDaoCourseCard->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    public function getCourseCardCntByConds($arrConds){
        $ret = $this->objDaoCourseCard->getCntByConds($arrConds);
        return $ret;
    }

    /**
     * 更新CourseCard
     *
     * @param  int  $CourseCard  CourseCardid
     * @param  array  $arrParams CourseCard属性
     * @return bool true/false
     */
    public function updateCourseCard($CourseCardId, $arrParams) {
        if(intval($CourseCardId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[CourseCard:$CourseCardId]");
            return false;
        }

        $arrConds = array(
            'id' => intval($CourseCardId),
        );


        $arrFields = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach($arrParams as $key => $value) {
            if(!in_array($key, $arrAllFields)) {
                continue;
            }

            $arrFields[$key] = $value;
        }

        //ext由ps补全，json处理在ds
        if(isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }

        //补全更新时间
        $arrFields['updateTime'] = time();

        $ret = $this->objDaoCourseCard->updateByConds($arrConds, $arrFields);

        //清理两个缓存
        $learnSeasonInfo = $this->getLearnSeasonById($CourseCardId);
        $strParam = "$CourseCardId";
        $cacheKey1 = 'zhibo_ds_learnseasonid_' . md5($strParam);
        $strParam = "{$learnSeasonInfo['learnSeason']} {$learnSeasonInfo['grade']} {$learnSeasonInfo['subject']}";
        $cacheKey2 = 'zhibo_ds_learnseason_' . md5($strParam);
        $objMemcached = Hk_Service_Memcached::getInstance("zhiboke");
        $objMemcached->delete($cacheKey1);
        $objMemcached->delete($cacheKey2);


        return $ret;
    }
}
