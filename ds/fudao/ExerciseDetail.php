<?php
/***************************************************************************
 *
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   ExerciseDetail.php
 * <AUTHOR>
 * @date   2015/11/17 14:13:18
 * @brief  作业详情
 *
 **/
class Hkzb_Ds_Fudao_ExerciseDetail
{
    //状态
    const STATUS_TOANSWER = 0; //待作答
    const STATUS_TOREVIEW = 1; //待批改
    const STATUS_REVIEWED = 2; //已批改
    const STATUS_REANSWER = 3; //待重作
    const STATUS_EMPTY = 10;//未布置

    static $STATUS_ARRAY = array(
        self::STATUS_TOANSWER => '待作答',
        self::STATUS_TOREVIEW => '待批改',
        self::STATUS_REVIEWED => '已批改',
        self::STATUS_REANSWER => '待重作',
        self::STATUS_EMPTY => '未布置',
    );

    const REMARK_RIGHT = 'y';  //答对题目
    const REMARK_ERROR = 'n';  //答错题目

    const REVIEWER_SYSTEM = 1;

    const EXERCISE_STUDENT_TOP_RIGHT = 'exercise_student_top_right_';
    const EXERCISE_STUDENT_TOP_ERROR = 'exercise_student_top_error_';
    const STUDENT_ANSWER = 'student_answer_';

    const ALL_FIELDS = 'id,studentUid,courseId,lessonId,purpose,exerciseId,answer,score,reviewerUid,remark,status,createTime,updateTime,extData';

    private $objDaoExerciseDetail;

    /**
     * 构造函数
     *
     */
    public function __construct()
    {
        $this->objDaoExerciseDetail = new Hkzb_Dao_Fudao_ExerciseDetail();
    }

    /**
     * 批量发送答题卡
     * 按照业务设置的时间来更新
     * add by liukai01
     * @param $couseId
     * @param $lessonId
     * @param $exerciseList
     * @param $purpose
     * @param $studentList
     * @return array|false
     */
    public function addExerciseDetailMultiByTime($couseId, $lessonId, $exerciseList, $purpose, $studentList, $createTime)
    {
        $table = "tblExerciseDetail" . intval($couseId / 1000);
        if ($createTime < 10) {
            $cTime = time();
        }
        $cTime = $createTime;
        $uTime = $cTime;
        foreach ($exerciseList as $exerciseId) {
            $params = array();
            foreach ($studentList as $studentId) {
                $params[] = "(" . $couseId . ", " . $lessonId . ", " . $purpose . ", " . $exerciseId . ", " . $studentId . ", " . self::STATUS_TOANSWER . ", " . $cTime . ", " . $uTime . ")";
            }
            if (!$this->objDaoExerciseDetail->query("INSERT INTO $table (course_id, lesson_id, purpose, exercise_id, student_uid, status, create_time, update_time) VALUES " . implode(",", $params))) {
                $this->objDaoExerciseDetail->rollback();
                return false;
            }
        }
        if (!$this->objDaoExerciseDetail->query("UPDATE tblExercise SET status=1 WHERE exercise_id IN (" . implode(",", $exerciseList) . ")")) {
            $this->objDaoExerciseDetail->rollback();
            return false;
        }
        return true;
    }

    /**
     * 批量布置作业
     * @param $couseId
     * @param $lessonId
     * @param $exerciseList
     * @param $purpose
     * @param $studentList
     * @return array|false
     */
    public function addExerciseDetailMulti($couseId, $lessonId, $exerciseList, $purpose, $studentList)
    {
        $table = "tblExerciseDetail" . intval($couseId / 1000);
        $cTime = time();
        $uTime = $cTime;
        foreach ($exerciseList as $exerciseId) {
            $params = array();
            foreach ($studentList as $studentId) {
                $params[] = "(" . $couseId . ", " . $lessonId . ", " . $purpose . ", " . $exerciseId . ", " . $studentId . ", " . self::STATUS_TOANSWER . ", " . $cTime . ", " . $uTime . ")";
            }
            if (!$this->objDaoExerciseDetail->query("INSERT INTO $table (course_id, lesson_id, purpose, exercise_id, student_uid, status, create_time, update_time) VALUES " . implode(",", $params))) {
                $this->objDaoExerciseDetail->rollback();
                return false;
            }
        }
        if (!$this->objDaoExerciseDetail->query("UPDATE tblExercise SET status=1 WHERE exercise_id IN (" . implode(",", $exerciseList) . ")")) {
            $this->objDaoExerciseDetail->rollback();
            return false;
        }
        return true;
    }

    /**
     * 布置作业
     *
     * @param  array $arrParams 习题属性
     * @return bool true/false
     */
    public function addExerciseDetail($arrParams)
    {
        if (intval($arrParams['courseId']) <= 0 || intval($arrParams['studentUid']) <= 0 || intval($arrParams['exerciseId']) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[param: " . json_encode($arrParams) . "]");

            return false;
        }

        $arrFields = array(
            'studentUid' => intval($arrParams['studentUid']),
            'courseId' => intval($arrParams['courseId']),
            'lessonId' => intval($arrParams['lessonId']),
            'purpose' => intval($arrParams['purpose']),
            'exerciseId' => intval($arrParams['exerciseId']),
            'status' => self::STATUS_TOANSWER,
            'createTime' => time(),
            'updateTime' => time(),
            'extData' => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
        );

        $ret = $this->objDaoExerciseDetail->insertRecords(intval($arrParams['courseId']), $arrFields);

        return $ret;
    }

    /**
     * 提交作业
     *
     * @param  int $courseId 课程id
     * @param  int $studentUid 学生uid
     * @param  int $exerciseId 习题id
     * @param  array $answer 作答
     * @return mix
     */
    public function answerExercise($courseId, $studentUid, $exerciseId, $answer, $purpose = 1)
    {
        if (intval($courseId) <= 0 || intval($studentUid) <= 0 || intval($exerciseId) <= 0 || empty($answer)) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId studentUid:$studentUid exerciseId:$exerciseId answer:$answer]");

            return false;
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
            'exerciseId' => intval($exerciseId),

        );

        if ($purpose != Hkzb_Ds_Fudao_Exercise::PURPOSE_INCLASS) {
            $arrConds['status'] = self::STATUS_TOANSWER;
        }


        $arrFields = array(
            'answer' => json_encode($answer),
            'status' => self::STATUS_TOREVIEW,
            'updateTime' => time(),
        );

        $ret = $this->objDaoExerciseDetail->updateByConds(intval($courseId), $arrConds, $arrFields);
        return $ret;
    }

    /**
     * 更新习题详细
     *
     * @param  int $courseId 提交答案表id
     * @param  array $arrParams 学生答案表属性
     * @return bool true/false
     */
    public function updateExerciseDetail($courseId, $id, $arrParams)
    {
        if (intval($id) <= 0 || intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id]");
            return false;
        }

        $arrConds = array(
            'id' => intval($id),
        );

        $arrFields = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach ($arrParams as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }

            $arrFields[$key] = $value;
        }

        //ext由ps补全，json处理在ds
        if (isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }

        //补全更新时间
        $arrFields['updateTime'] = time();
        $ret = $this->objDaoExerciseDetail->updateByConds(intval($courseId), $arrConds, $arrFields);

        return $ret;
    }

    /**
     * 批改作业
     *
     * @param  int $courseId 课程id
     * @param  int $studentUid 学生uid
     * @param  int $exerciseId 习题id
     * @param  int $reviewerUid 批改老师uid
     * @param  string $mark 评语
     * @param  int $score 得分
     * @return mix
     */
    public function reviewExercise($courseId, $studentUid, $exerciseId, $reviewerUid, $remark, $score, $extData = '', $purpose = 1)
    {
        if (intval($courseId) <= 0 || intval($studentUid) <= 0 || intval($exerciseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId studentUid:$studentUid exerciseId:$exerciseId reviewerUid:$reviewerUid remark:$remark score:$score]");

            return false;
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
            'exerciseId' => intval($exerciseId),
        );

        //互动题可以重复更新
        if ($purpose != Hkzb_Ds_Fudao_Exercise::PURPOSE_INCLASS) {
            $arrConds['status'] = self::STATUS_TOREVIEW;
        }

        $arrFields = array(
            'reviewerUid' => intval($reviewerUid),
            'remark' => json_encode($remark),
            'score' => intval($score),
            'status' => self::STATUS_REVIEWED,
            'updateTime' => time(),
        );

        if ($extData <> '') {
            $arrFields['extData'] = json_encode($extData);
        }

        $ret = $this->objDaoExerciseDetail->updateByConds(intval($courseId), $arrConds, $arrFields);

        return $ret;
    }

    /**
     * 批改记录录音，不置状态
     *
     * @param  int $courseId 课程id
     * @param  int $studentUid 学生uid
     * @param  int $exerciseId 习题id
     * @param  int $reviewerUid 批改老师uid
     * @param  string $mark 评语
     * @param  int $score 得分
     * @return mix
     */
    public function reviewRecord($courseId, $studentUid, $exerciseId, $reviewerUid, $remark, $score, $extData = '')
    {
        if (intval($courseId) <= 0 || intval($studentUid) <= 0 || intval($exerciseId) <= 0 || intval($reviewerUid) <= 0 || empty($remark)) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId studentUid:$studentUid exerciseId:$exerciseId reviewerUid:$reviewerUid remark:$remark score:$score]");

            return false;
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
            'exerciseId' => intval($exerciseId),
            'status' => self::STATUS_TOREVIEW,
        );

        $arrFields = array(
            'reviewerUid' => intval($reviewerUid),
            'remark' => json_encode($remark),
            'score' => intval($score),
            //'status'      => self::STATUS_TOANSWER,//不修改状态,之后合成录音再修改
            'updateTime' => time(),
        );

        if ($extData <> '') {
            $arrFields['extData'] = json_encode($extData);
        }

        $ret = $this->objDaoExerciseDetail->updateByConds(intval($courseId), $arrConds, $arrFields);

        return $ret;
    }

    /**
     * 获取指定学生指定习题作业详情
     *
     * @param  int $courseId 课程id
     * @param  int $studentUid 学生uid
     * @param  int $exerciseId 习题id
     * @param  mix $arrFields 指定属性
     * @param  int $lessonId 课节id
     * @return mix
     */
    public function getStudentExerciseDetail($courseId, $studentUid, $exerciseId, $arrFields = array(), $lessonId = 0, $purpose = 0)
    {
        if (intval($courseId) <= 0 || intval($studentUid) <= 0 || intval($exerciseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[couseId:$courseId studentUid:$studentUid exerciseId:$exerciseId]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'courseId' => intval($courseId),
            'studentUid' => intval($studentUid),
            'exerciseId' => intval($exerciseId),
        );
        if (0 < $lessonId) {
            $arrConds['lessonId'] = intval($lessonId);
        }
        if ($purpose > 0) {
            $arrConds['purpose'] = intval($purpose);
        }

        $ret = $this->objDaoExerciseDetail->getRecordByConds(intval($courseId), $arrConds, $arrFields);

        return $ret;
    }

    /**
     * 获取指定学生数组指定习题作业详情
     *
     * @param  int $courseId 课程id
     * @param array $studentUids 学生uids
     * @param  int $exerciseId 习题id
     * @param  mix $arrFields 指定属性
     * @param  int $lessonId 课节id
     * @return mix
     */
    public function getArrStudentExerciseDetail($courseId, $arrStudentUid, $exerciseId, $arrFields = array(), $lessonId = 0)
    {
        if (intval($courseId) <= 0 || empty($arrStudentUid) || intval($exerciseId) <= 0) {
            $studentUid = json_encode($studentUid);
            Bd_Log::warning("Error:[param error], Detail:[couseId:$courseId studentUid:$studentUid exerciseId:$exerciseId]");
            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'courseId' => intval($courseId),
            'exerciseId' => intval($exerciseId),
        );
        $arrConds[] = 'student_uid in (' . implode(',', $arrStudentUid) . ')';

        if (0 < $lessonId) {
            $arrConds['lessonId'] = intval($lessonId);
        }

        $ret = $this->objDaoExerciseDetail->getListByConds(intval($courseId), $arrConds, $arrFields);

        return $ret;
    }

    /**
     * 获取指定习题作业数量
     *
     * @param  int $exerciseId 习题id
     * @param  int $courseId 课程id
     * @param  int $lessonId 课程id
     * @param  int $purpose 课程id
     * @return int
     */
    public function getExerciseDetailCntByExerciseId($exerciseId, $courseId, $lessonId, $purpose)
    {
        if (intval($courseId) <= 0 || intval($exerciseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[exerciseId:$exerciseId couseId:$courseId]");

            return false;
        }

        $arrConds = array(
            'courseId' => intval($courseId),
            'lessonId' => intval($lessonId),
            'purpose' => intval($purpose),
            'exerciseId' => intval($exerciseId),
        );

        $ret = $this->objDaoExerciseDetail->getCntByConds(intval($courseId), $arrConds);

        return $ret;
    }

    /**
     * 获取指定习题作业已做人数
     *
     * @param  int $exerciseId 习题id
     * @return int
     */
    public function getExerciseDetailCntByHasAnswer($courseId, $exerciseId)
    {
        if (intval($exerciseId) <= 0 || intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[exerciseId:$exerciseId courseId:$courseId]");

            return false;
        }

        $arrConds = array(
            'exerciseId' => intval($exerciseId),
            'answer' => array('', '<>'),
        );

        $ret = $this->objDaoExerciseDetail->getCntByConds(intval($courseId), $arrConds);

        return $ret;
    }

    /**
     * 获取指定习题作业做对人数
     *
     * @param  int $exerciseId 习题id
     * @return int
     */
    public function getExerciseDetailCntByHasCorrect($courseId, $exerciseId)
    {
        if (intval($exerciseId) <= 0 || intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[exerciseId:$exerciseId courseId:$courseId]");

            return false;
        }

        $arrConds = array(
            'exerciseId' => intval($exerciseId),
            'remark' => '{"result":"y"}',
        );

        $ret = $this->objDaoExerciseDetail->getCntByConds(intval($courseId), $arrConds);

        return $ret;
    }

    /**
     * 获取指定习题指定学生作业做对人数
     *
     * @param  int $exerciseId 习题id
     * @return int
     */
    public function getExerciseDetailCntByHasCorrectArrStudent($courseId, $lessonId, $exerciseId, $arrStudent, $purpose)
    {
        if (intval($exerciseId) <= 0 || intval($courseId) <= 0 || intval($lessonId) <= 0 || empty($arrStudent)) {
            Bd_Log::warning("Error:[param error], Detail:[exerciseId:$exerciseId courseId:$courseId lessonId:$lessonId " . implode(',', $arrStudent) . "]");

            return false;
        }

        $arrConds = array(
            'courseId' => intval($courseId),
            'lessonId' => intval($lessonId),
            'exerciseId' => intval($exerciseId),
            'purpose' => intval($purpose),
            'remark' => '{"result":"y"}',
        );
        $arrConds[] = 'student_uid in (' . implode(',', $arrStudent) . ')';

        $ret = $this->objDaoExerciseDetail->getCntByConds(intval($courseId), $arrConds);

        return $ret;
    }

    /**
     * 获取指定习题作业列表
     *
     * @param  int $exerciseId 习题id
     * @param  int $courseId 课程id
     * @param  int $lessonId 课程id
     * @param  int $purpose 课程id
     * @param  mix $arrFields 指定属性
     * @param  int $offset 偏移
     * @param  int $limit 限制
     * @return mix
     */
    public function getExerciseDetailListByExerciseId($exerciseId, $courseId, $lessonId, $purpose, $arrFields = array(), $offset = 0, $limit = 20)
    {
        if (intval($courseId) <= 0 || intval($exerciseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[exerciseId:$exerciseId couseId:$courseId]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'courseId' => intval($courseId),
            'lessonId' => intval($lessonId),
            'purpose' => intval($purpose),
            'exerciseId' => intval($exerciseId),
        );

        $arrAppends = array(
            "order by create_time",
            "limit $offset, $limit",
        );

        $ret = $this->objDaoExerciseDetail->getListByConds(intval($courseId), $arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    public function getExerciseDetailScoreListByExerciseId($exerciseId, $courseId, $lessonId, $purpose, $arrFields = array(), $offset = 0, $limit = 20)
    {
        if (intval($courseId) <= 0 || intval($exerciseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[exerciseId:$exerciseId couseId:$courseId]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'courseId' => intval($courseId),
            'lessonId' => intval($lessonId),
            'purpose' => intval($purpose),
            'exerciseId' => intval($exerciseId),
        );

        $arrAppends = array(
            "order by score DESC",
            "limit $offset, $limit",
        );

        $ret = $this->objDaoExerciseDetail->getListByConds(intval($courseId), $arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /**
     * 批量获取指定习题作业列表
     *
     * @param  int $studentUid 學生id
     * @param  int $exerciseId 习题id
     * @param  int $courseId 课程id
     * @param  int $lessonId 课程id
     * @param  int $purpose 课程id
     * @param  mix $arrFields 指定属性
     * @param  int $offset 偏移
     * @param  int $limit 限制
     * @return mix
     */
    public function getExerciseDetailListByExerciseList($studentUid, $arrExerciseId, $courseId, $lessonId, $purpose, $arrFields = array(), $offset = 0, $limit = 100)
    {
        if (intval($studentUid) <= 0 || intval($courseId) <= 0 || empty($arrExerciseId) || !is_array($arrExerciseId)) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:" . $studentUid . " arrExerciseId:$arrExerciseId couseId:$courseId]");

            return false;
        }
        $strExerciseIdList = implode(",", $arrExerciseId);
        if (empty($strExerciseIdList)) {
            Bd_Log::warning("Error:[implode error], Detail:[exerciseId:" . $strExerciseIdList . "]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'studentUid' => $studentUid,
            'courseId' => intval($courseId),
            'lessonId' => intval($lessonId),
            'purpose' => intval($purpose),
            'exercise_id in (' . implode(",", $arrExerciseId) . ')',
        );

        $arrAppends = array(
            "order by create_time",
            "limit $offset, $limit",
        );

        $ret = $this->objDaoExerciseDetail->getListByConds(intval($courseId), $arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /**
     * 获取指定学生作业数量
     *
     * @param  int $studentUid 学生uid
     * @param  int $courseId 课程id
     * @param  int $lessonId 课程id
     * @param  int $purpose 课程id
     * @return int
     */
    public function getExerciseDetailCntByStudentUid($studentUid, $courseId, $lessonId = 0, $purpose)
    {
        if (intval($studentUid) <= 0 || intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid couseId:$courseId]");

            return false;
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
            'courseId' => intval($courseId),
            'lessonId' => intval($lessonId),
            'purpose' => intval($purpose),
        );
        $ret = $this->objDaoExerciseDetail->getCntByConds(intval($courseId), $arrConds);

        return $ret;
    }

    /**
     * 获取指定学生作业详情
     *
     * @param  int $studentUid 学生uid
     * @param  int $courseId 课程id
     * @param  int $lessonId 课程id
     * @param  int $purpose 课程id
     * @param  mix $arrFields 指定属性
     * @param  int $offset 偏移
     * @param  int $limit 限制
     * @return mix
     */
    public function getExerciseDetailListByStudentUid($studentUid, $courseId, $lessonId, $purpose, $arrFields = array(), $offset = 0, $limit = 20)
    {
        if (intval($studentUid) <= 0 || intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid couseId:$courseId]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
            'courseId' => intval($courseId),
            'lessonId' => intval($lessonId),
            'purpose' => intval($purpose),
        );

        $arrAppends = array(
            "order by create_time",
            "limit $offset, $limit",
        );

        $ret = $this->objDaoExerciseDetail->getListByConds(intval($courseId), $arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /**
     * 获取指定课程生作业数量
     *
     * @param  int $courseId 课程id
     * @param  int $lessonId 课程id
     * @param  int $purpose 课程id
     * @return int
     */
    public function getExerciseDetailCntByCourseId($courseId, $lessonId, $purpose = -1)
    {
        if (intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[couseId:$courseId]");

            return false;
        }

        $arrConds = array(
            'courseId' => intval($courseId),
            'lessonId' => intval($lessonId),
        );

        if (intval($purpose) >= 0) {
            $arrConds['purpose'] = intval($purpose);
        }

        $ret = $this->objDaoExerciseDetail->getCntByConds(intval($courseId), $arrConds);

        return $ret;
    }

    /**
     * 获取指定课程作业详情
     *
     * @param  int $courseId 课程id
     * @param  int $lessonId 课程id
     * @param  int $purpose 课程id
     * @param  mix $arrFields 指定属性
     * @param  int $offset 偏移
     * @param  int $limit 限制
     * @return mix
     */
    public function getExerciseDetailListByCourseId($courseId, $lessonId, $purpose = -1, $arrFields = array(), $offset = 0, $limit = 20)
    {
        if (intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[couseId:$courseId]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'courseId' => intval($courseId),
            'lessonId' => intval($lessonId),
        );
        if (intval($purpose) >= 0) {
            $arrConds['purpose'] = intval($purpose);
        }

        $arrAppends = array(
            "limit $offset, $limit",
        );

        $ret = $this->objDaoExerciseDetail->getListByConds(intval($courseId), $arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /**
     * 获取指定课程下所有课节作业数量(用于批改作业)
     *
     * @param  int $courseId 课程id
     * @param  int $lessonId 课程id
     * @param  int $purpose 课程id
     * @return int
     */
    public function getExerciseDetailCntByCourseIdLessonIds($courseId, $arrLessonId, $purpose = -1)
    {
        if (intval($courseId) <= 0 || empty($arrLessonId)) {
            Bd_Log::warning("Error:[param error], Detail:[couseId:$courseId]");

            return false;
        }

        $arrConds = array(
            'courseId' => intval($courseId),
        );
        $arrConds[] = 'lesson_id in (' . implode(',', $arrLessonId) . ')';

        if (intval($purpose) >= 0) {
            $arrConds['purpose'] = intval($purpose);
        }

        $ret = $this->objDaoExerciseDetail->getCntByConds(intval($courseId), $arrConds);

        return $ret;
    }

    /**
     * 获取指定课程下所有课节作业详情(用于批改作业)
     *
     * @param  int $courseId 课程id
     * @param array $arrLessonIds 课节id
     * @param  int $purpose 课程id
     * @param  mix $arrFields 指定属性
     * @param  int $offset 偏移
     * @param  int $limit 限制
     * @return mix
     */
    public function getExerciseDetailListByCourseIdLessonIds($courseId, $arrLessonId, $purpose = -1, $arrFields = array(), $offset = 0, $limit = 20)
    {
        if (intval($courseId) <= 0 || empty($arrLessonId)) {
            Bd_Log::warning("Error:[param error], Detail:[couseId:$courseId]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'courseId' => intval($courseId),
        );

        $arrConds[] = 'lesson_id in (' . implode(',', $arrLessonId) . ')';

        if (intval($purpose) >= 0) {
            $arrConds['purpose'] = intval($purpose);
        }

        $arrAppends = array(
            "limit $offset, $limit",
        );

        $ret = $this->objDaoExerciseDetail->getListByConds(intval($courseId), $arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }


    /**
     * 获取指定学生入门练习
     *
     * @param  int $studentUid 学生uid
     * @param  int $courseId 课程id
     * @param  mix $arrFields 指定属性
     * @param  int $offset 偏移
     * @param  int $limit 限制
     * @return mix
     */
    public function getPreDetailListByStudentUids($arrStudentUid, $courseId, $arrFields = array(), $offset = 0, $limit = 20)
    {
        if (empty($arrStudentUid) || intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[arrStudentUid:$arrStudentUid couseId:$courseId]");

            return false;
        }

        //输入的id不应该有非数字之外的其他字符
        $strStudentUids = implode(',', $arrStudentUid);
        if (!preg_match("/^[0-9,\\s]+$/", $strStudentUids)) {
            Bd_Log::warning("Error:[param error], Detail:[strStudentUids:$strStudentUids]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'courseId' => intval($courseId),
            'lessonId' => intval(0),
            'purpose' => Hkzb_Ds_Fudao_Exercise::PURPOSE_PRECLASS,
        );

        $arrConds[] = "student_uid in ($strStudentUids)";

        $arrAppends = array(
            "order by create_time",
            "limit $offset, $limit",
        );

        $ret = $this->objDaoExerciseDetail->getListByConds(intval($courseId), $arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /**
     * 获取指定学生数组入门练习总数
     *
     * @param  array $studentUid 学生uid
     * @param  int $courseId 课程id
     * @param  mix $arrFields 指定属性
     * @return mix
     */
    public function getPreDetailCntByStudentUids($arrStudentUid, $courseId)
    {
        if (empty($arrStudentUid) || intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[arrStudentUid:$arrStudentUid couseId:$courseId]");

            return false;
        }

        //输入的id不应该有非数字之外的其他字符
        $strStudentUids = implode(',', $arrStudentUid);
        if (!preg_match("/^[0-9,\\s]+$/", $strStudentUids)) {
            Bd_Log::warning("Error:[param error], Detail:[strStudentUids:$strStudentUids]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'courseId' => intval($courseId),
            'lessonId' => intval(0),
            'purpose' => Hkzb_Ds_Fudao_Exercise::PURPOSE_PRECLASS,
        );

        $arrConds[] = "student_uid in ($strStudentUids)";

        $ret = $this->objDaoExerciseDetail->getCntByConds(intval($courseId), $arrConds);

        return $ret;
    }

    /**
     * 获取指定学生课后作业详情
     *
     * @param  int $studentUid 学生uid
     * @param  int $courseId 课程id
     * @param  int $lessonId 课程id
     * @param  int $purpose 课程id
     * @param  mix $arrFields 指定属性
     * @param  int $offset 偏移
     * @param  int $limit 限制
     * @return mix
     */
    public function getHomeworkDetailListByStudentUids($arrStudentUid, $courseId, $lessonId, $arrFields = array(), $offset = 0, $limit = 20, $orderby = 'create_time', $asc = 'asc')
    {
        if (empty($arrStudentUid) || intval($courseId) <= 0 || intval($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[arrStudentUid:$arrStudentUid couseId:$courseId] lessonId:{$lessonId} ");

            return false;
        }

        //输入的id不应该有非数字之外的其他字符
        $strStudentUids = implode(',', $arrStudentUid);
        if (!preg_match("/^[0-9,\\s]+$/", $strStudentUids)) {
            Bd_Log::warning("Error:[param error], Detail:[strStudentUids:$strStudentUids]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'courseId' => intval($courseId),
            'lessonId' => intval($lessonId),
            'purpose' => Hkzb_Ds_Fudao_Exercise::PURPOSE_SUFCLASS,
        );

        $arrConds[] = "student_uid in ($strStudentUids)";

        $arrAppends = array(
            "order by $orderby $asc",
            "limit $offset, $limit",
        );

        $ret = $this->objDaoExerciseDetail->getListByConds(intval($courseId), $arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /**
     * 获取指定学生数组课后作业总数
     *
     * @param  array $studentUid 学生uid
     * @param  int $courseId 课程id
     * @param  int $lessonId 课程id
     * @param  int $purpose 课程id
     * @param  mix $arrFields 指定属性
     * @return mix
     */
    public function getHomeworkDetailCntByStudentUids($arrStudentUid, $courseId, $lessonId)
    {
        if (empty($arrStudentUid) || intval($courseId) <= 0 || intval($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[arrStudentUid:$arrStudentUid couseId:$courseId] lessonId:{$lessonId} ");

            return false;
        }

        //输入的id不应该有非数字之外的其他字符
        $strStudentUids = implode(',', $arrStudentUid);
        if (!preg_match("/^[0-9,\\s]+$/", $strStudentUids)) {
            Bd_Log::warning("Error:[param error], Detail:[strStudentUids:$strStudentUids]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'courseId' => intval($courseId),
            'lessonId' => intval($lessonId),
            'purpose' => Hkzb_Ds_Fudao_Exercise::PURPOSE_SUFCLASS,
        );

        $arrConds[] = "student_uid in ($strStudentUids)";

        $ret = $this->objDaoExerciseDetail->getCntByConds(intval($courseId), $arrConds);

        return $ret;
    }

    /**
     * 获取指定学生课堂答题详情
     *
     * @param  int $studentUid 学生uid
     * @param  int $courseId 课程id
     * @param  int $lessonId 课程id
     * @param  int $purpose 课程id
     * @param  mix $arrFields 指定属性
     * @param  int $offset 偏移
     * @param  int $limit 限制
     * @return mix
     */
    public function getInClassDetailListByStudentUids($arrStudentUid, $courseId, $lessonId, $arrFields = array(), $offset = 0, $limit = 20)
    {
        if (empty($arrStudentUid) || intval($courseId) <= 0 || intval($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[arrStudentUid:$arrStudentUid couseId:$courseId] lessonId:{$lessonId} ");

            return false;
        }

        //输入的id不应该有非数字之外的其他字符
        $strStudentUids = implode(',', $arrStudentUid);
        if (!preg_match("/^[0-9,\\s]+$/", $strStudentUids)) {
            Bd_Log::warning("Error:[param error], Detail:[strStudentUids:$strStudentUids]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'courseId' => intval($courseId),
            'lessonId' => intval($lessonId),
            'purpose' => Hkzb_Ds_Fudao_Exercise::PURPOSE_INCLASS,
        );

        $arrConds[] = "student_uid in ($strStudentUids)";

        $arrAppends = array(
            "order by create_time",
            "limit $offset, $limit",
        );

        $ret = $this->objDaoExerciseDetail->getListByConds(intval($courseId), $arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /**
     * 获取指定学生数组课堂答题总数
     *
     * @param  array $studentUid 学生uid
     * @param  int $courseId 课程id
     * @param  int $lessonId 课程id
     * @param  int $purpose 课程id
     * @param  mix $arrFields 指定属性
     * @return mix
     */
    public function getInClassDetailCntByStudentUids($arrStudentUid, $courseId, $lessonId)
    {
        if (empty($arrStudentUid) || intval($courseId) <= 0 || intval($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[arrStudentUid:$arrStudentUid couseId:$courseId] lessonId:{$lessonId} ");

            return false;
        }

        //输入的id不应该有非数字之外的其他字符
        $strStudentUids = implode(',', $arrStudentUid);
        if (!preg_match("/^[0-9,\\s]+$/", $strStudentUids)) {
            Bd_Log::warning("Error:[param error], Detail:[strStudentUids:$strStudentUids]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'courseId' => intval($courseId),
            'lessonId' => intval($lessonId),
            'purpose' => Hkzb_Ds_Fudao_Exercise::PURPOSE_INCLASS,
        );

        $arrConds[] = "student_uid in ($strStudentUids)";

        $ret = $this->objDaoExerciseDetail->getCntByConds(intval($courseId), $arrConds);

        return $ret;
    }


    /**
     * 提交作业
     *
     * @param  int $courseId 课程id
     * @param  int $studentUid 学生uid
     * @param  int $exerciseId 习题id
     * @param  mix $answers 作答 {answerId:{'answer':answer,'remark':remark,},}
     * @return mix
     */
    public function answerExerciseInClass($courseId, $lessonId, $studentUid, $answers)
    {
        if (intval($courseId) <= 0 || intval($studentUid) <= 0 || intval($lessonId) <= 0 || empty($answers)) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId lessonId:$lessonId studentUid:$studentUid answers:" . json_encode($answers) . "]");

            return false;
        }

        //统一的更新条件
        $arrConds = array(
            'studentUid' => intval($studentUid),
            'courseId' => intval($courseId),
            'lessonId' => intval($lessonId),
            //'status'     => self::STATUS_TOANSWER, 去掉本条件 支持重复更新
            'purpose' => Hkzb_Ds_Fudao_Exercise::PURPOSE_INCLASS,
        );

        //开启事务
        $result = $this->objDaoExerciseDetail->startTransaction();
        if (!$result) {
            Bd_Log::warning("Error:[db error], Detail:[start transaction failed]");
            return false;
        }

        //开始更新
        $errMsg = "";
        foreach ($answers as $exerciseId => $detail) {
            $arrConds['exerciseId'] = intval($exerciseId);
            $arrFields = array(
                'answer' => json_encode(is_array($detail['answer']) ? $detail['answer'] : array()),
                'status' => self::STATUS_TOREVIEW,
                'remark' => strval($detail['remark']),
                'reviewer' => 0,
                'updateTime' => time(),
            );
            $ret = $this->objDaoExerciseDetail->updateByConds(intval($courseId), $arrConds, $arrFields);
            //记录更新是否失败
            if ($ret === false) {
                $errMsg = "[update error courseId:$courseId studentUid:$studentUid exerciseId:$exerciseId]";
                break;
            }
        }
        //更新失败回退
        if (!empty($errMsg)) {
            $this->objDaoExerciseDetail->rollback();
            Bd_Log::warning("Error:[db error], Detail:$errMsg");
            return false;
        }
        //提交
        $result = $this->objDaoExerciseDetail->commit();
        if (!$result) {
            Bd_Log::warning("Error:[db error], Detail:[commit transaction failed]");
            return false;
        }
        return true;
    }


    /**
     * 提交更新英语作业
     * @param $arrParams
     * @return bool
     */
    public function updateEnglisExercise($arrParams = array())
    {
        if (empty($arrParams)) {
            Bd_Log::warning("param is empty " . json_encode($arrParams));
            return false;
        }
        $remarkCache = isset($arrParams['remark']) ? $arrParams['remark'] : '';

        $courseId = isset($arrParams['courseId']) ? intval($arrParams['courseId']) : 0;
        $studentUid = isset($arrParams['studentUid']) ? intval($arrParams['studentUid']) : 0;
        $exerciseId = isset($arrParams['exerciseId']) ? intval($arrParams['exerciseId']) : 0;
        $reviewerUid = isset($arrParams['reviewerUid']) ? intval($arrParams['reviewerUid']) : 0;
        $remark = isset($arrParams['remark']) ? json_encode($arrParams['remark']) : '';
        $answer = isset($arrParams['answer']) ? json_encode($arrParams['answer']) : '';
        $score = isset($arrParams['score']) ? intval($arrParams['score']) : 0;
        $extData = isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '';
        $purpose = isset($arrParams['purpose']) ? intval($arrParams['purpose']) : 0;
        $status = isset($arrParams['status']) ? intval($arrParams['status']) : 0;
        $lessonId = isset($arrParams['lessonId']) ? intval($arrParams['lessonId']) : 0;

        if (intval($courseId) <= 0 || intval($studentUid) <= 0 || intval($exerciseId) <= 0 || empty($remark) || empty($answer)) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId studentUid:$studentUid exerciseId:$exerciseId reviewerUid:$reviewerUid remark:$remark score:$score]");

            return false;
        }

        // 课中增加TOP学生答题列表
        if ($purpose == Hkzb_Ds_Fudao_Exercise::PURPOSE_INCLASS) {

            $this->setExerciseStendentTopN($exerciseId, $studentUid, $answer, $remarkCache);

        }

        $arrConds = array(
            'studentUid' => $studentUid,
            'exerciseId' => $exerciseId,
            'lessonId' => $lessonId,
        );

        $arrFields = array(
            'reviewerUid' => $reviewerUid,
            'remark'      => $remark,
            'answer'      => $answer,
            'score'       => $score,
            'purpose'     => $purpose,
            'status'      => $status,
            'updateTime'  => time(),
            'extData'     => $extData,
        );

        $ret = $this->objDaoExerciseDetail->updateByConds(intval($courseId), $arrConds, $arrFields);

        if ($ret === false) {
            Bd_Log::warning("db error" . json_encode($arrParams));
        }

        return $ret;
    }

    /**
     * 提交英语作业
     * @param array $arrParams
     * @return bool
     */
    public function addEnglishExercise($arrParams = array())
    {
        if (empty($arrParams)) {
            Bd_Log::warning("param is empty " . json_encode($arrParams));
            return false;
        }

        $remarkCache = isset($arrParams['remark']) ? $arrParams['remark'] : '';


        $courseId = isset($arrParams['courseId']) ? intval($arrParams['courseId']) : 0;
        $studentUid = isset($arrParams['studentUid']) ? intval($arrParams['studentUid']) : 0;
        $exerciseId = isset($arrParams['exerciseId']) ? intval($arrParams['exerciseId']) : 0;
        $reviewerUid = isset($arrParams['reviewerUid']) ? intval($arrParams['reviewerUid']) : 0;
        $remark = isset($arrParams['remark']) ? json_encode($arrParams['remark']) : '';
        $answer = isset($arrParams['answer']) ? json_encode($arrParams['answer']) : '';
        $score = isset($arrParams['score']) ? intval($arrParams['score']) : 0;
        $extData = isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '[]';
        $purpose = isset($arrParams['purpose']) ? intval($arrParams['purpose']) : 0;
        $status = isset($arrParams['status']) ? intval($arrParams['status']) : 0;
        $lessonId = isset($arrParams['lessonId']) ? intval($arrParams['lessonId']) : 0;

        if (intval($courseId) <= 0 || intval($studentUid) <= 0 || intval($exerciseId) <= 0 || empty($remark) || empty($answer) || empty($lessonId)) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId studentUid:$studentUid exerciseId:$exerciseId reviewerUid:$reviewerUid remark:$remark score:$score] lessonId:$lessonId");

            return false;
        }

        // 课中增加TOP学生答题列表
        if ($purpose == Hkzb_Ds_Fudao_Exercise::PURPOSE_INCLASS) {

            $this->setExerciseStendentTopN($exerciseId, $studentUid, $answer, $remarkCache);

        }

        $arrFields = array(
            'courseId' => $courseId,
            'lessonId' => $lessonId,
            'studentUid' => $studentUid,
            'exerciseId' => $exerciseId,
            'reviewerUid' => $reviewerUid,
            'remark' => $remark,
            'answer' => $answer,
            'score' => $score,
            'purpose' => $purpose,
            'status' => $status,
            'extData' => $extData,
            'createTime' => time(),
        );

        $ret = $this->objDaoExerciseDetail->insertRecords($courseId, $arrFields);

        if ($ret === false) {
            Bd_Log::warning("db error" . json_encode($arrParams));
        }

        return $ret;
    }

    /**
     * 课中增加TOP学生答题列表
     * @param $exerciseId
     * @param $studentUid
     * @param $data
     * @param $remark
     * @return mixed
     */

    private function setExerciseStendentTopN($exerciseId, $studentUid, $data, $remark)
    {
        $objStored = Hkzb_Util_StoredService::getZhiboInstance();

        $objDsExercise = new Hkzb_Ds_Fudao_Exercise();
        $res = $objDsExercise->getExerciseInfo($exerciseId);
        $answer = json_decode($res['answer'], TRUE);
        $option = json_decode($data, TRUE);
        $option_array = json_decode($option['result'], TRUE);
        $i = 1;
        //课中只有听后选择和听后填空有多道题的情况
        if (is_array($answer) && is_array($option_array) && in_array($res['type'], Hkzb_Ds_Fudao_Exercise::$MULTI_TYPE)) {
            foreach ($answer as $key => $val) {
                if (!is_array($val)) {
                    $val = [$val];
                }
                $arrExerciseAnswer = array_map('trim', $val);
                if (in_array(trim($option_array[$key]), $arrExerciseAnswer)) {
                    $cacheKey = self::EXERCISE_STUDENT_TOP_RIGHT . $exerciseId . '_' . $i;
                    $objStored->zadd($cacheKey, time(), $studentUid);
                    $objStored->expire($cacheKey, 10800);

                } else {
                    $cacheKey = self::EXERCISE_STUDENT_TOP_ERROR . $exerciseId . '_' . $i;
                    $objStored->zadd($cacheKey, time(), $studentUid);
                    $objStored->expire($cacheKey, 10800);
                }
                if (in_array($res['type'], Hkzb_Ds_Fudao_Exercise::$ANSWER_INCLASS)) {
                    $answerKey = self::STUDENT_ANSWER . $exerciseId . '_' . $studentUid . '_' . $i;
                    $objStored->set($answerKey, $option_array[$key]);
                    $objStored->expire($answerKey, 10800);
                }
                $i++;
            }


        } else {
            if ($remark['result'] == 'y') {
                $cacheKey = self::EXERCISE_STUDENT_TOP_RIGHT . $exerciseId . '_' . $i;
                $objStored->zadd($cacheKey, time(), $studentUid);
                $objStored->expire($cacheKey, 10800);
            } else {
                $cacheKey = self::EXERCISE_STUDENT_TOP_ERROR . $exerciseId . '_' . $i;
                $objStored->zadd($cacheKey, time(), $studentUid);
                $objStored->expire($cacheKey, 10800);
            }

            if (in_array($res['type'], Hkzb_Ds_Fudao_Exercise::$ANSWER_INCLASS)) {
                $answerKey = self::STUDENT_ANSWER . $exerciseId . '_' . $studentUid . '_' . $i;
                $objStored->set($answerKey, $option['result']);
                $objStored->expire($answerKey, 10800);
            }

        }


        return true;

    }

    /**
     * 对外提供课中学生答题列表功能
     * @param $arrInput
     * @return array
     */


    public function getInClassExerciseTopNAnswerResult($arrInput)
    {
        $exerciseId = intval($arrInput['exerciseId']);
        $topN = intval($arrInput['topN']) ? $arrInput['topN'] : 50;

        if (empty($exerciseId)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'exerciseId is empty', $arrInput);
        }

        $objDsExercise = new Hkzb_Ds_Fudao_Exercise();
        $res = $objDsExercise->getExerciseInfo($exerciseId);
        $answer = json_decode($res['answer'], TRUE);

        $count = count($answer);

        if (is_array($answer) && in_array($res['type'], Hkzb_Ds_Fudao_Exercise::$MULTI_TYPE)) {
            for ($i = 1; $i <= $count; $i++) {
                $arrOutput[] = [
                    'rightList' => $this->getExerciseTopNStudent($exerciseId, $topN, self::EXERCISE_STUDENT_TOP_RIGHT, $i),
                    'wrongList' => $this->getExerciseTopNStudent($exerciseId, $topN, self::EXERCISE_STUDENT_TOP_ERROR, $i),
                ];
            }
        } else {
            $i = 1;
            $arrOutput[] = [
                'rightList' => $this->getExerciseTopNStudent($exerciseId, $topN, self::EXERCISE_STUDENT_TOP_RIGHT, $i),
                'wrongList' => $this->getExerciseTopNStudent($exerciseId, $topN, self::EXERCISE_STUDENT_TOP_ERROR, $i),
            ];

        }
        return $arrOutput;


    }

    /**
     * 从redis获取TOP N学生答题列表
     * @param $exerciseId
     * @param $topN //获取个数
     * @param $cacheKey
     * @param  $i
     * @return array
     */

    private function getExerciseTopNStudent($exerciseId, $topN, $cacheKey, $i)
    {
        $objStored = Hkzb_Util_StoredService::getZhiboInstance();
        $zList = $objStored->zrange($cacheKey . $exerciseId . '_' . $i, 0, $topN);
        $list = [];
        if (empty($zList)) {
            return $list;
        }
        $objUcloud = new Hk_Ds_User_Ucloud();
        $studentArs = $objUcloud->getUserInfoByArray($zList);
        foreach ($zList as $k => $v) {
            $list[$k]['userName'] = isset($studentArs[$v]['uname']) ? Hkzb_Util_Fudao_Tools::phoneCheckHandle($studentArs[$v]['uname']) : '';
            $answerKey = self::STUDENT_ANSWER . $exerciseId . '_' . $v . '_' . $i;
            $list[$k]['answer'] = $objStored->get($answerKey);
            $list[$k]['uid'] = $v;
            $list[$k]['duration'] = 0;
        }
        return $list;

    }


    public function updateAnswer($lessonId, $courseId, $studentUid, $exerciseId, $answer, $purpose = 0)
    {
        if (empty($studentUid) || empty($exerciseId) || empty($answer) || empty($courseId)) {
            Bd_Log::warning("param is empty studentUid:{$studentUid} exerciseId:{$exerciseId} answer:{$answer} courseId:{$courseId}");
            return false;
        }

        $studentUid = intval($studentUid);
        $exerciseId = intval($exerciseId);
        $answer = json_encode($answer);

        $arrConds = array(
            'studentUid' => $studentUid,
            'exerciseId' => $exerciseId,
            'lessonId' => $lessonId,
        );
        if ($purpose > 0) {
            $arrConds['purpose'] = $purpose;
        }
        $arrFields = array(
            'answer' => $answer,
        );
        $res = $this->objDaoExerciseDetail->updateByConds(intval($courseId), $arrConds, $arrFields);

        if ($res === false) {
            Bd_Log::warning("db error studentUid:{$studentUid} exerciseId:{$exerciseId} answer:{$answer} courseId:{$courseId}");
        }

        return $res;
    }

    /**
     * 获取指定学生作业详情
     *
     * @param  int $studentUid 学生uid
     * @param  int $courseId 课程id
     * @param  int $purpose 课程id
     * @param  mix $arrFields 指定属性
     * @param  int $offset 偏移
     * @param  int $limit 限制
     * @return mix
     */
    public function getExerciseDetailList($arrConds, $arrFields = array())
    {
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrAppends = array(
            "order by create_time",
        );
        $course_id = intval($arrConds['courseId']);
        $ret = $this->objDaoExerciseDetail->getListByConds($course_id, $arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /**
     * 获取该学生课堂习题情况
     * @param  $studentUid
     * @param  $courseId
     * @param  $lessonId
     * @param  $status
     * @return $arrStudentExerciseDetial
     * @throws Hk_Util_Exception
     */
    public function getCurrentExerciseInfo($courseId, $status, $studentUid = 0)
    {
        $arrStudentExerciseDetial = array();
        //获取一个学生的课堂习题答题情况
        if (Hkzb_Ds_Fudao_LessonDetail::STATUS_CLASS_IN == $status || Hkzb_Ds_Fudao_LessonDetail::STATUS_CLASS_STOPED == $status) {
            //获取用户课堂练习信息
            $arrConds = array(
                'courseId' => intval($courseId),
                'purpose' => Hkzb_Ds_Fudao_Exercise::PURPOSE_INCLASS,
            );
            $exerciseList = $this->getExerciseDetailList($arrConds, array());

            $exeList = $exeStuList = array();
            //获取练习题的答对数目和答题数目
            foreach ($exerciseList as $ek => $ev) {
                $time = date('Y/m/d', $ev['createTime']);
                $exeList[$time][] = $ev;
                if (($ev['studentUid'] == $studentUid) && ($studentUid)) {
                    $exeStuList[$time][] = $ev;
                } else {
                    $exeStuList[$time][] = $ev;
                }
            }
            foreach ($exeStuList as $ek => $exerciseDetail) {
                $arrRightAndAnswerNum = $this->getRightAndAnswerNum($exerciseDetail);
                //获取练习题正确率
                $intRightRate = $this->getRightRate($arrRightAndAnswerNum);
                $arrStudentExerciseDetial[$ek] = $intRightRate;
            }
            return $arrStudentExerciseDetial;
        }
    }

    /**
     * 获取练习题的答对数目和答题数目
     * @param  $exerciseDetail
     * @return $arrRightAndAnswerNum
     * @throws Hk_Util_Exception
     */
    public function getRightAndAnswerNum($exerciseDetail)
    {
        $arrRightAndAnswerNum = array('rightNum' => 0, 'answerNum' => 0);
        if (!empty($exerciseDetail)) {
            foreach ($exerciseDetail as $exercise) {
                if ($exercise['remark']['result'] == Hkzb_Ds_Fudao_ExerciseDetail::REMARK_RIGHT) {
                    ++$arrRightAndAnswerNum['rightNum'];
                    ++$arrRightAndAnswerNum['answerNum'];
                } else if ($exercise['remark']['result'] == Hkzb_Ds_Fudao_ExerciseDetail::REMARK_ERROR) {
                    ++$arrRightAndAnswerNum['answerNum'];
                } else {
                    continue;
                }
            }
        }

        return $arrRightAndAnswerNum;
    }

    /**
     * 获取练习题正确率
     * @param  $arrRightAndAnswerNum
     * @return $intRightRate
     * @throws Hk_Util_Exception
     */
    public function getRightRate($arrRightAndAnswerNum)
    {
        $intRightRate = 0;
        if (0 != $arrRightAndAnswerNum['answerNum']) {
            $intRightRate = round(intval($arrRightAndAnswerNum['rightNum'] * 100 / $arrRightAndAnswerNum['answerNum']), 2);
        }

        return $intRightRate;
    }

    /**
     * 获取一个学生一个章节的老师对习题的批改信息
     * @param  $lessonId
     * @param  $studentUid
     * @return false | array
     * @throws Hk_Util_Exception
     */
    public function getStudentExerciseCorrectInfo($lessonId, $studentUid)
    {
        if (0 >= $lessonId || 0 >= $studentUid) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$lessonId studentUid:$studentUid]");
            return false;
        }

        $arrCorrect = array(
            'correctInfo' => array(),
            'remarkInfo' => array(),
        );

        //获取章节信息
        $objDsLesson = new Hkzb_Ds_Fudao_Lesson();
        $lessonInfo = $objDsLesson->getLessonInfo($lessonId, array('courseId'));
        if (false == $lessonInfo || empty($lessonInfo)) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$lessonId]");
            return false;
        }

        //获取课程信息
        $objDsCourse = new Hkzb_Ds_Fudao_Course();
        $courseInfo = $objDsCourse->getCourseInfo($lessonInfo['courseId'], array('courseId', 'grade', 'subject'));
        if (fales == $courseInfo) {
            $courseId = $lessonInfo['courseId'];
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");
            return false;
        }

        //获取章节状态信息
        $objDsLessonState = new Hkzb_Ds_Fudao_LessonState();
        $arrLesssonState = array();
        $arrLesssonState = $objDsLessonState->getLessonState($studentUid, $lessonInfo['courseId'], $lessonId, array('homework', 'extData'));
        if (false === $arrLesssonState) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid lessonId:$lessonId]");
            return false;
        }

        //获取评级标准数组
        $arrRemarkMap = Hkzb_Util_Fudao_RemarkConfig::$remarkMap;

        //只有在老师批改了作业时才取获取老师的批改信息
        if ($arrLesssonState['homework'] == Hkzb_Ds_Fudao_ExerciseDetail::STATUS_REVIEWED) {
            //获取习题批改信息
            $arrExerciseDetial = array();
            $arrExerciseDetial = $this->getExerciseDetailListByStudentUid($studentUid, $lessonInfo['courseId'], $lessonId, Hkzb_Ds_Fudao_Exercise::PURPOSE_SUFCLASS, array(), 0, 200);
            if (false === $arrExerciseDetial) {
                Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid lessonId:$lessonId]");
                return false;
            }
        }

        if (!empty($arrExerciseDetial)) {
            foreach ($arrExerciseDetial as $key => $value) {
                $exercise = array();
                $exercise['exerciseId'] = $value['exerciseId'];
                $exercise['recordAll'] = array('name' => '', 'time' => 0, 'title' => '');
                $exercise['recordAll']['name'] = isset($value['remark']['record']) ? $value['remark']['record'] : '';
                $exercise['recordAll']['time'] = isset($value['remark']['recordDuration']) ? $value['remark']['recordDuration'] : 0;

                // $exercise['recordAll']['name'] = '';
                // $exercise['recordAll']['time'] = 0;
                // if(isset($value['remark']['record']) && '' != $value['remark']['record']) {
                //     $exercise['recordAll']['name'] = $value['remark']['record'];
                //     if('' != $value['extData']['record']['name'] && '' != $value['extData']['recordAll']['name']) {
                //         $exercise['recordAll']['time'] =  $value['extData']['recordAll']['time'];
                //     }
                //     else {
                //         $exercise['recordAll']['time'] = $value['remark']['recordDuration'];
                //     }
                // }
                // else {
                //     $exercise['recordAll']['name'] = isset($value['extData']['recordAllM4a']['name']) ? $value['extData']['recordAllM4a']['name'] : '';
                //     $exercise['recordAll']['time'] = isset($value['extData']['record']['time']) ? $value['extData']['record']['time'] : 0;
                //     if('' == $exercise['recordAll']['name']) {
                //         $exercise['recordAll']['name'] = isset($value['extData']['recordM4a']['name']) ? $value['extData']['M4a']['name'] : '';
                //         $exercise['recordAll']['time'] = isset($value['extData']['recordAll']['time']) ? $value['extData']['recordAll']['time'] : 0;
                //     }
                // }
                // if(0 == $exercise['recordAll']['time']) {
                //     $exercise['recordAll']['name'] = '';
                // }

                $exercise['correctList'] = isset($value['remark']['pic']) ? $value['remark']['pic'] : array();
                $arrCorrect['correctInfo'][] = $exercise;
            }
        }

        //组装输出数据
        $arrCorrect['remarkInfo']['level'] = isset($arrLesssonState['extData']['level']) ? intval($arrLesssonState['extData']['level']) : 0;
        $arrCorrect['remarkInfo']['remarkVoice'] = isset($arrLesssonState['extData']['remarkVoiceOther']) ? $arrLesssonState['extData']['remarkVoiceOther'] : array();
        // $arrCorrect['remarkInfo']['remarkVoice']  = isset($arrLesssonState['extData']['remarkVoice']) ? $arrLesssonState['extData']['remarkVoice'] : array();
        $arrCorrect['remarkInfo']['levelExplain'] = '';
        if (in_array($courseInfo['grade'], array(11, 12, 13, 14, 15, 16))) {
            $arrCorrect['remarkInfo']['levelExplain'] = isset($arrRemarkMap[1][$courseInfo['subject']][$arrCorrect['remarkInfo']['level']]) ? $arrRemarkMap[1][$courseInfo['subject']][$arrCorrect['remarkInfo']['level']] : '';
        } else if (in_array($courseInfo['grade'], array(2, 3, 4))) {
            $arrCorrect['remarkInfo']['levelExplain'] = isset($arrRemarkMap[1][$courseInfo['subject']][$arrCorrect['remarkInfo']['level']]) ? $arrRemarkMap[2][$courseInfo['subject']][$arrCorrect['remarkInfo']['level']] : '';
        }

        return $arrCorrect;
    }

    /**
     * 将作业学生作业置为初始值
     * @param $couseId
     * @param $lessonId
     * @param $exerciseList
     * @param $purpose
     * @param $studentList
     * @return array|false
     */
    public function resetExerciseDetail($oldExerciseId, $newExerciseId, $courseId, $lessonId, $purpose)
    {
        if (0 >= $oldExerciseId || 0 >= $newExerciseId || 0 >= $courseId || 0 >= $lessonId) {
            Bd_Log::warning("Error:[param error], Detail:[oldExerciseId:$oldExerciseId newExerciseId:$newExerciseId courseId:$courseId lessonId:$lessonId]");
            return false;
        }
        $table = "tblExerciseDetail" . intval($couseId / 1000);

        if (!$this->objDaoExerciseDetail->query("UPDATE $table SET exercise_id=$newExerciseId, answer='', status=0, ext_data='' WHERE exercise_id=$oldExerciseId AND course_id=$courseId AND lesson_id=$lessonId AND purpose=2")) {
            return false;
        }
        return true;
    }

    /**
     * (新)批改记录录音，不置状态
     *
     * @param  int $courseId 课程id
     * @param  int $studentUid 学生uid
     * @param  int $exerciseId 习题id
     * @param  int $reviewerUid 批改老师uid
     * @param  string $mark 评语
     * @param  int $score 得分
     * @return mix
     */
    public function reviewRecordNew($courseId, $studentUid, $exerciseId, $reviewerUid, $remark, $score, $extData = '')
    {
        if (intval($courseId) <= 0 || intval($studentUid) <= 0 || intval($exerciseId) <= 0 || intval($reviewerUid) <= 0 || empty($remark)) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId studentUid:$studentUid exerciseId:$exerciseId reviewerUid:$reviewerUid remark:$remark score:$score]");

            return false;
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
            'exerciseId' => intval($exerciseId),
            'status' => self::STATUS_TOANSWER,
        );

        $arrFields = array(
            'reviewerUid' => intval($reviewerUid),
            'remark' => json_encode($remark),
            'score' => intval($score),
            //'status'      => self::STATUS_TOANSWER,//不修改状态,之后合成录音再修改
            'updateTime' => time(),
        );

        if ($extData <> '') {
            $arrFields['extData'] = json_encode($extData);
        }

        $ret = $this->objDaoExerciseDetail->updateByConds(intval($courseId), $arrConds, $arrFields);

        return $ret;
    }

    /**
     * 获取指定学生作业数量
     *
     * @param  int $courseId 课程id
     * @param  int $lessonId 课程id
     * @param  int $studentUid 学生uid
     * @param  int $purpose 课程id
     * @param  int $answer 答题结果 1正确 2错误 3未答题
     * @return int
     */
    public function getExerciseDetailCntForLessonReport($courseId, $lessonId, $studentUid = 0, $purpose = -1, $answer = 0)
    {
        if (intval($lessonId) <= 0 || intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$lessonId couseId:$courseId]");
            return false;
        }

        $arrConds = array(
            'courseId' => intval($courseId),
            'lessonId' => intval($lessonId),
        );

        if($studentUid > 0){
            $arrConds['studentUid'] = intval($studentUid);
        }

        if($purpose >= 0){
            $arrConds['purpose'] = intval($purpose);
        }

        if(intval($answer) == 1){
            $arrConds['remark'] = '{"result":"y"}';
        }elseif(intval($answer) == 2){
            $arrConds['remark'] = '{"result":"n"}';
        }elseif(intval($answer) == 3){
            $arrConds['remark'] = '';
        }

        $ret = $this->objDaoExerciseDetail->getCntByConds(intval($courseId), $arrConds);

        return $ret;
    }

}
