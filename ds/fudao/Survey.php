<?php
/**
 * File: Survey.php
 * User: lihongjie
 * Date: 2018/2/7
 * Time: 下午2:17
 * Desc: 课后评价
 */
class Hkzb_Ds_Fudao_Survey
{
    const SURVEY_SCORE = 2;

    public function getSurveyIdByIds($courseId, $lessonId)
    {
        $objAdvancedCourse = new Hkzb_Ds_Fudao_Advanced_Course();
        $course = $objAdvancedCourse->getCourseInfo($courseId);
        if (!$course) {
            Bd_Log::warning('Error:[getCourseInfo error] Detail:[courseId:' . $courseId . ' lessonId:' . $lessonId . ']');
            return false;
        }
        if ($course['type'] != Hkzb_Ds_Fudao_Advanced_Course::TYPE_PRIVATE_LONG) {
            return 0;
        }
        return 1000;
    }
}