<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file StudentCollect.php
 * <AUTHOR>
 * @date 2015/11/17 14:13:18
 * @brief 课程收集
 *  
 **/

class Hkzb_Ds_Fudao_StudentCollect {
    //状态
    const STATUS_OK      = 0;
    const STATUS_DELETED = 1;

    const ALL_FIELDS = 'studentUid,courseId,deleted,createTime,updateTime,extData';

    const CACHE_KEY = 'shoppingcart_cache_key_';

    private $objDaoStudentCollect;

    private static $redisInstance;

    /**
     * 构造函数
     *
     */
    public function __construct() {
        $this->objDaoStudentCollect = new Hkzb_Dao_Fudao_StudentCollect();
    }

    /**
     * 新增学生课程
     *
     * @param  mix  $arrParams 班级属性
     * @return bool true/false
     */
    public function addStudentCollect($arrParams) {
        if(intval($arrParams['studentUid']) <= 0 || intval($arrParams['courseId']) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[param: ". json_encode($arrParams). "]");
            return false;
        }

        $arrFields = array(
            'studentUid'   => intval($arrParams['studentUid']),
            'courseId'     => intval($arrParams['courseId']),
            'deleted'      => self::STATUS_OK,
            'createTime'   => time(),
            'updateTime'   => time(),
            'extData'      => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
        );

        $ret = $this->objDaoStudentCollect->insertRecords($arrFields);
        if ($ret) {
            $cacheKey = self::CACHE_KEY . $arrParams['studentUid'];
            self::delCache($cacheKey);
        }

        return $ret;
    }

   /**
     * 更新学生收集课程
     *
     * @param  int  $studentUid 学生uid
     * @param  int  $courseId   课程id
     * @return bool true/false
     */
    public function updateStudentCollect($studentUid, $courseId) {
        if(intval($studentUid) <= 0 || intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId]");
            return false;
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
            'courseId'   => intval($courseId),
        );

        $arrFields = array(
            'deleted'    => self::STATUS_OK,
            'updateTime' => time(),
        );

        $ret = $this->objDaoStudentCollect->updateByConds($arrConds, $arrFields);
        if ($ret) {
            $cacheKey = self::CACHE_KEY . $studentUid;
            self::delCache($cacheKey);
        }

        return $ret;
    }

    
    /**
     * 删除学生收集课程
     *
     * @param  int  $studentUid 学生uid
     * @param  int  $courseId   课程id
     * @return bool true/false
     */
    public function deleteStudentCollect($studentUid, $courseId) {
        if(intval($studentUid) <= 0 || intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId]");
            return false;
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
            'courseId'   => intval($courseId),
        );

        $arrFields = array(
            'deleted'    => self::STATUS_DELETED,
            'updateTime' => time(),    
        );

        $ret = $this->objDaoStudentCollect->updateByConds($arrConds, $arrFields);
        if ($ret) {
            $cacheKey = self::CACHE_KEY . $studentUid;
            self::delCache($cacheKey);
        }

        return $ret;
    }


    /**
     * 获取学生课程详情
     *
     * @param  int  $studentUid 学生uid
     * @param  int  $courseId   课程id
     * @param  mix  $arrFields  指定属性
     * @return mix
     */
    public function getStudentCollectInfo($studentUid, $courseId, $arrFields = array()) {
        if(intval($studentUid) <= 0 || intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId]");
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
            'courseId'   => intval($courseId),
        );

        $ret = $this->objDaoStudentCollect->getRecordByConds($arrConds, $arrFields);

        return $ret;
    }
    /**
     * 获取学生课程详情
     *
     * @param  array  $arrStudentUid 学生uids
     * @param  int  $courseId   课程id
     * @param  mix  $arrFields  指定属性
     * @return mix
     */

    public function getArrStudentCollectInfo($arrStudentUid, $courseId, $arrFields = array()) {
        if(empty($arrStudentUid)  || intval($courseId) <= 0) {
            $studentuid = json_encode($arrStudentUid);
            Bd_Log::warning("Error:[param error], Detail:[studentUid $studentuid courseId:$courseId]");
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'courseId'   => intval($courseId),
        );
        $arrConds[] = 'student_uid in (' . implode(',',$arrStudentUid) .')';

        $ret = $this->objDaoStudentCollect->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }



    /** 
     * @param $arrConds
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getCourseListByConds($arrConds, $arrFields = array(),$offset = 0, $limit = 20,$order = 'create_time',$sort = 'desc') {
        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }   

        $arrAppends = array(
            "order by $order $sort",
            "limit $offset, $limit",
        );  

        $arrConds['deleted'] = self::STATUS_OK;
    

        $ret = $this->objDaoStudentCollect->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    } 
    /** 
     * 获取数量
     * @param $arrConds
     * @return false|int
     */
    public function getCourseCntByConds($arrConds) {
        $arrConds['deleted'] = self::STATUS_OK;
        $ret = $this->objDaoStudentCollect->getCntByConds($arrConds);
        return $ret;
    }


    /**
     * 获取指定学生课程列表
     *
     * @param  int  $studentUid  学生uid
     * @param  mix  $arrFields   指定属性
     * @return mix
     */
    public function getCourseListByStudentUid($studentUid, $arrFields = array(),$order = 'create_time',$sort = 'desc',$offset = 0, $limit = 20) {
        if(intval($studentUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid]");
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
            'deleted'    => self::STATUS_OK,
        );
        
        $arrAppends = array(
            "order by $order $sort",
            "limit $offset, $limit",
        );

        $ret = $this->objDaoStudentCollect->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /** 
     * 获取数量
     * @param $arrConds
     * @return false|int
     */
    public function getCourseCntByStudentUid($studentUid) {
        $arrConds = array(
            'studentUid' => intval($studentUid),
            'deleted'    => self::STATUS_OK,
        );
        $ret = $this->objDaoStudentCollect->getCntByConds($arrConds);
        return $ret;
    }

    /**
     * 获取redis实例
     *
     * @return Hk_Service_Redis|null
     */
    public static function getRedisInstance()
    {
        if (!static::$redisInstance) {
            static::$redisInstance = Hk_Service_RedisClient::getInstance("zhibo");
        }
        return static::$redisInstance;
    }

    /**
     * 删除key
     *
     * @param $cacheKey
     */
    public static function delCache($cacheKey)
    {
        $objRedis = self::getRedisInstance();
        $objRedis->del($cacheKey);
    }

}
