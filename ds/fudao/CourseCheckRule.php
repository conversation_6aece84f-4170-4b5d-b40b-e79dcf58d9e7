<?php
/***************************************************************************
 *
 * Copyright (c) 2015 zybang.com, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file Illegal.php
 * <AUTHOR>
 * @date 2017/09/23 14:30:15
 * @brief 质检规则
 *
 **/

class Hkzb_Ds_Fudao_CourseCheckRule {

    const ALL_FIELDS = 'ruleId,subject,type,ruleTitle,isShow,ruleScore,ruleCode,catId,ruleName,score,remark,sort,createTime,deleted,checkStatus,operatorUid,operator,extData,ruleLevel';
        private $objDaoCourseCheckRule;
    const HANDOUT_CONTENT = 1; //讲义内容
    const HANDOUT_STANDARD = 5; //讲义规范
    const IS_SHOW_DISPLAY = 1; //规则展示
    const IS_SHOW_HIDDEN = 2; //规则隐藏
    static $COURSE_RULE = array(
        self::HANDOUT_CONTENT => '讲义内容',
        self::HANDOUT_STANDARD      => '讲义规范',

    );
    /**
     * 构造函数
     *
     */
    public function __construct() {
        $this->objDaoCourseCheckRule = new Hkzb_Dao_Fudao_CourseCheckRule();
    }

    public function getIllegalListByConds($arrConds, $arrFields = [], $offset = 0, $limit = 2000) {
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrAppends = [
            "order by create_time desc",
            "limit $offset, $limit",
        ];

        $ret = $this->objDaoCourseCheckRule->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /**
     *  添加规则
     * @param $arrParams
     * @return bool|int
     */
    public function addCourseCheckRule($arrParams) {

        if (intval($arrParams['subject']) <= 0 || intval($arrParams['catId']) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[". json_encode($arrParams) . "]");
            return false;
        }

        $arrFields = array(
            'subject'      => isset($arrParams['subject']) ? intval($arrParams['subject']) : 0,
            'catId'        => isset($arrParams['catId']) ? intval($arrParams['catId']) : 0,
            'ruleName'     => isset($arrParams['ruleName']) ? strval($arrParams['ruleName']) : 0,
            'score'        => isset($arrParams['score']) ? $arrParams['score'] : 0,
            'sort'         => isset($arrParams['sort']) ? intval($arrParams['sort']) : 0,
            'remark'       => isset($arrParams['remark']) ? strval($arrParams['remark']) : '',
            'checkStatus'  => isset($arrParams['checkStatus']) ? intval($arrParams['checkStatus']) : Hkzb_Ds_Fudao_Lesson::CHECK_LIGHT_VIOLATION,
            'createTime'   => time(),
            'operatorUid'  => isset($arrParams['operatorUid']) ? intval($arrParams['operatorUid']) : 0,
            'operator'     => isset($arrParams['operator']) ? strval($arrParams['operator']) : '',
            'extData'      => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
            'ruleLevel'    => isset($arrParams['ruleLevel']) ? strval($arrParams['ruleLevel']): '',
            'type'         => isset($arrParams['type']) ? intval($arrParams['type']) :'',
            'ruleTitle'    => isset($arrParams['ruleTitle']) ? trim($arrParams['ruleTitle']):'',
            'ruleScore'    => isset($arrParams['ruleScore']) ? json_encode($arrParams['ruleScore']) : '',
            'ruleCode'     => isset($arrParams['ruleCode']) ? trim($arrParams['ruleCode']) :''
        );
        $ret = $this->objDaoCourseCheckRule->insertRecords($arrFields);
        if ($ret) {
            $ret = $this->objDaoCourseCheckRule->getInsertId();
        }
        return $ret;
    }

    /**
     * 获取数量
     * @param $arrConds
     * @return false|int
     */
    public function getRuleCntByConds($arrConds) {

        $ret = $this->objDaoCourseCheckRule->getCntByConds($arrConds);
        return $ret;
    }

    /**
     * @param $arrConds
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getRuleListByConds($arrConds, $arrFields = array(), $offset = 0, $limit = 20) {

        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrAppends = array(
            "order by create_time desc",
            "limit $offset, $limit",
        );

        $ret =$this->objDaoCourseCheckRule->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /**
     * 按着规则 ruleId 隐藏
     * @param $ruleId
     * @return bool
     */
    public function deleteCourseCheckRule($ruleId) {

        if (intval($ruleId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[ruleId:$ruleId]");
            return false;
        }
        $arrConds = array(
            'ruleId' => intval($ruleId),
        );
        $arrFields = array(
            'deleted'  => 1,
        );

        $ret = $this->objDaoCourseCheckRule->updateByConds($arrConds, $arrFields);

        return $ret;
    }

    /** 隐藏分类下的规则
     * @param $catId
     * @param $subjectId
     * @return bool
     */
    public function deleteCourseCheckRuleByCat($catId, $subjectId) {

        if (intval($catId) < 0 || intval($subjectId) < 0) {
            Bd_Log::warning("Error:[param error], Detail:[catId:$catId, subjectId:$subjectId]");
            return false;
        }
        $arrConds = array(
            'catId'   => intval($catId),
            'subject' => intval($subjectId),
        );

        $arrFields = array(
            'deleted'  => 1,
        );

        $ret = $this->objDaoCourseCheckRule->updateByConds($arrConds, $arrFields);

        return $ret;
    }

    /**
     * 根据 ruleId 更新
     * @param $ruleId
     * @param $arrParams
     * @return bool
     */
    public function updateCourseCheckRule($ruleId, $arrParams) {

        if (intval($ruleId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[ruleId:$ruleId]");
            return false;
        }
        $arrConds = array(
            'ruleId' => intval($ruleId),
        );
        $arrFields    = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach ($arrParams as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }

            $arrFields[$key] = $value;
        }

        //ext由ps补全，json处理在ds
        if (isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }
        if (isset($arrFields['ruleScore'])){
            $arrFields['ruleScore'] = json_encode($arrFields['ruleScore']);
        }
        $ret = $this->objDaoCourseCheckRule->updateByConds($arrConds, $arrFields);

        return $ret;
    }
}
