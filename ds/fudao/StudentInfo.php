<?php
/**
 * Copyright (c) 2016 zuoyebang.com, Inc. All Rights Reserved
 * @file  :StudentInfo.php
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @date  : 2016/11/5
 * @time  : 14:21
 * @desc  : 用户信息状态相关
 */

class Hkzb_Ds_Fudao_StudentInfo
{
    const CURRENT_LEARN_SEASON_START_TIME = 1506564000;//strtotime('20170928 10:00:00')秋二期课程开售时间
    private static $studentInfo         = array();
    private static $userTradeRecordInfo = array();

    public function __construct()
    {
        $this->_objDsShoppingCart = new Hkzb_Ds_Fudao_ShoppingCart();
        $this->_objDsCourse       = new Hkzb_Ds_Fudao_Course();
    }

    /**
     * 获取学生选课单有效课程数量（这里的有效指未被删除）
     * @param $studentUid
     * @return bool|false|int
     */
    public function getStudentValidCartCourseCnt($studentUid)
    {
        if (empty($studentUid)) {
            return false;
        }
        $validCartList = $this->_objDsShoppingCart->getValidCourseList($studentUid, array('courseId'));
        if ($validCartList === false) {
            Bd_Log::warning("Error:[db error], Detail:[studentUid:$studentUid");

            return false;
        }
        if (empty($validCartList)) {
            return 0;
        }
        $arrCourseList = array();
        foreach ($validCartList as $course) {
            $arrCourseList[] = $course['courseId'];
        }
        //跟产品思思沟通后 临时处理方案
        if(!empty($arrCourseList)){
            $normalCourse = count($arrCourseList);
            return $normalCourse;
        }

        $arrConds     = array(
            'status <> ' . Hkzb_Ds_Fudao_Course::STATUS_DELETED,
            'course_id in (' . join(',', $arrCourseList) . ')',
        );
        $normalCourse = $this->_objDsCourse->getCourseCntByConds($arrConds);
        if ($normalCourse === false) {
            Bd_Log::warning("Error:[db error], Detail:[studentUid:$studentUid");

            return false;
        }

        return $normalCourse;
    }

    /**
     * 选课单批量添加课程接口
     * @param      $actionList
     * @param bool $needTrans
     * @return array
     */
    public function processShoppingCartCourse($actionList, $needTrans = false)
    {
        $arrRes = array(
            'errNo'  => 0,
            'errMsg' => '',
            'data'   => array(),
        );
        $dbName = Zhibo_Static::DBCLUSTER_FUDAO;
        if ($needTrans) {
            $db = Hk_Service_Db::getDB($dbName);
            if (empty($db)) {
                Bd_Log::warning("connect to db fail, db[$dbName]");
                $arrRes['errNo']  = Hk_Util_ExceptionCodes::DB_ERROR;
                $arrRes['errMsg'] = '获取fudao数据库连接失败';

                return $arrRes;
            }
            $res = $db->startTransaction();
            if (empty($res)) {
                Bd_Log::warning("start transaction fail, db[$dbName]");
                $arrRes['errNo']  = Hk_Util_ExceptionCodes::DB_ERROR;
                $arrRes['errMsg'] = 'fudao数据库开启事务失败';

                return $arrRes;
            }
        }
        try {
            $this->_processActionList($actionList);
        } catch (Hk_Util_Exception $e) {
            $arrRes['errNo']  = $e->getErrNo();
            $arrRes['errMsg'] = $e->getErrMsg();
            if ($needTrans) {
                $res = $db->rollback();
                if (empty($res)) {
                    Bd_Log::warning("rollback fail, db[$dbName]");
                    $arrRes['errNo']  = Hk_Util_ExceptionCodes::DB_ERROR;
                    $arrRes['errMsg'] = 'fudao数据库回滚失败';
                }
            }

            return $arrRes;
        }
        if ($needTrans) {
            $res = $db->commit();
            if (empty($res)) {
                Bd_Log::warning("commit fail, db[$dbName]");
                $arrRes['errNo']  = Hk_Util_ExceptionCodes::DB_ERROR;
                $arrRes['errMsg'] = 'fudao数据库提交失败';

                return $arrRes;
            }
        }

        return $arrRes;
    }

    /**
     * 新增或更新
     * @param $actionList
     * @throws Hk_Util_Exception
     */
    private function _processActionList($actionList)
    {
        foreach ($actionList as $act) {
            if ($act['act'] === 'add') {
                $arrFields = $act['arrFields'];
                $res       = $this->_objDsShoppingCart->addCourseToCart($arrFields);
                if ($res === false) {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::INSERT_ERROR, '', $arrFields);
                }
            } elseif ($act['act'] === 'update') {
                $studentUid = $act['studentUid'];
                $courseId   = $act['courseId'];
                $arrFields  = $act['arrFields'];
                $ext        = $act['ext'];
                $res        = $this->_objDsShoppingCart->updateShoppingCart($studentUid, $courseId, $arrFields, $ext);
                if ($res === false) {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::UPDATE_ERROR, '', $act);
                }
            }
        }
    }

    /**
     * 获取用户信息（逻辑上的用户状态，非用户资料）
     * @param $intStudentUid
     * @param $isCache 是否读缓存，新用户首页可能会用到 added by sunxiancan
     * @return array
     */
    public static function getUserInfo($intStudentUid, $isCache = true)
    {
        $arrOutput = array(
            'isLearnSeasonOld'     => 0,//是否老学员
            'isSerialCourseUser'   => 0,//是否班课用户
            'isSpecificCourseUser' => 0,//是否专题课用户
            'isQiu2OldUser'        => 0,//是否秋2老用户
            'isNewUser'            => 1,//是否新用户
        );
        if (empty($intStudentUid)) {
            return $arrOutput;
        }
        $intStudentUid = intval($intStudentUid);
        if (isset(self::$studentInfo[$intStudentUid])) {
            $studentInfo = self::$studentInfo[$intStudentUid];
        } else {
            $objStudent  = new Hkzb_Ds_Fudao_Student();
            $studentInfo = $objStudent->getStudentInfo($intStudentUid, array('oldestTime', 'classTime'), true);
            if ($studentInfo === false) {
                return $arrOutput;
            }
            self::$studentInfo[$intStudentUid] = $studentInfo;//添加缓存
        }
        if (empty($studentInfo)) {
            return $arrOutput;
        }
        //按学季处理新老用户判定
        $oldestTime = $studentInfo['oldestTime'];//用户最早购买的课程（特指购买后会被标记为老用户的课程，包括班课和部分指定课程）的开始时间
        //用户上课时间
        $classTime = $studentInfo['classTime'];
        if ($oldestTime > 0) {//新策略为买过班课就是老学员
            $arrOutput['isLearnSeasonOld']   = 1;//老学员
            $arrOutput['isSerialCourseUser'] = 1;//班课用户
            $arrOutput['isNewUser']          = 0;
        }
        //按购课情况处理用户属性
        if (isset(self::$userTradeRecordInfo[$intStudentUid])) {
            $userTradeInfo = self::$userTradeRecordInfo[$intStudentUid];
        } else {
            //===dar=====
            if(empty($oldestTime)) {
                $userTradeList = Zb_Service_Dar_Trade::getListByUserId($intStudentUid, array('userId', 'tradeId'), array('userId'),array('userId'));
                $resData = self::getRpcData($userTradeList);
                if(!empty($resData)){
                    self::$userTradeRecordInfo[$intStudentUid]['isSpecificCourseUser'] = 1 ;
                }
            }
            $userTradeInfo = self::$userTradeRecordInfo[$intStudentUid];
        }
        if ($userTradeInfo['isQiu2OldUser']) {
            $arrOutput['isQiu2OldUser'] = 1;
            $arrOutput['isNewUser']     = 0;
        } elseif ($userTradeInfo['isSpecificCourseUser']) {
            $arrOutput['isSpecificCourseUser'] = 1;//专题课用户
            $arrOutput['isNewUser']            = 0;
        }

        return $arrOutput;
    }

    /**
     * 处理rpc返回来的数据，把data数据返回
     * @param array $dataArr
     * @return array|mixed
     */
    static public function getRpcData($dataArr = array())
    {
        $arrRes = array();
        if (empty($dataArr) || !is_array($dataArr)) {
            Bd_Log::warning("param dataArr is empty");
            return $arrRes;
        }

        if ($dataArr['errNo'] > 0) {
            $errStr = $dataArr['errStr'];
            Bd_Log::warning("接口有错误信息返回：errStr:[{$errStr}]");
            return $arrRes;
        }

        $arrRes = isset($dataArr['data']) ? $dataArr['data'] : array();

        return $arrRes;
    }

    /**
     * 检查一个星期内是否买过试听课
     * @param $studentUid
     * @return bool
     */
    public static function checkTrialFilter($studentUid)
    {
        if (!$studentUid) {
            return false;
        }
//        $objUserTradeRecord = new Hkzb_Ds_Fudao_UserTradeRecord();
//        $ret = $objUserTradeRecord->getOrdersForWeek($studentUid);
	    /**
	     * @desc:订单新旧接口替换
	     * @author:jiahaijun<<EMAIL>>
	     * @time:2018-11-14
	     */
	    $nowTime            = Hkzb_Util_FuDao::getCurrentTimeStamp();
	    $tradeFields        = array('tradeId');
	    $subTradeFields     = array('subTradeId','courseId','status','createTime');
	    $orderListInfo      = Zb_Service_Dar_Trade::getListByUserId($studentUid,$tradeFields,$subTradeFields,array(),0,0);
	    $ret                = array();
	    if(is_array($orderListInfo) && !empty($orderListInfo)){
		    foreach($orderListInfo['data'] as $orderItem){
			    $subOrderList   = isset($orderItem['subTradeList']) && is_array($orderItem['subTradeList']) && !empty($orderItem['subTradeList'])
				    ? $orderItem['subTradeList'] : array();
			    if(is_array($subOrderList) && !empty($subOrderList)){
				    foreach($subOrderList as $subOrderItem){
					    if(isset($subOrderItem['courseId']) && $subOrderItem['courseId']
						    && $subOrderItem['status'] == Hkzb_Ds_Fudao_TradeRecord::STATUS_PAYED
						    && $subOrderItem['createTime'] >= ($nowTime - 604800)
					    ){
						    $ret[]    = array(
							    'courseId'=>$subOrderItem['courseId']
						    );
					    }
				    }
			    }
		    }
	    }
	    /*
	     * end
	     */
        if (!$ret || !is_array($ret)) {
            return false;
        }
        $courseIds = array();
        foreach ($ret as $value) {
            if (!$value['courseId']) {
                continue;
            }
            $courseIds[] = $value['courseId'];
        }
        $objAdvancedCourse = new Hkzb_Ds_Fudao_Advanced_Course();
        $ret = $objAdvancedCourse->getCourseInfoArr($courseIds, array(), true);
        if (!$ret || !is_array($ret)) {
            return false;
        }
        foreach ($ret as $value) {
            if ($value['type'] == Hkzb_Ds_Fudao_Advanced_Course::TYPE_PRE_LONG) {
                return true;
            }
        }
        return false;
    }
}
