<?php

/**
 * @file   Product.php
 * <AUTHOR>
 * @date   2017/12/25 16:33
 * @brief  简介
 */
class Hkzb_Ds_Fudao_Product_GroupProduct
{
    //删除状态
    const DELETE_STATUS_OK      = 0;
    const DELETE_STATUS_DELETED = 1;
    static public $deletedMap = array(
        self::DELETE_STATUS_OK      => '正常',
        self::DELETE_STATUS_DELETED => '已删除'
    );

    //是否对外展示
    const SHOW_NO  = 0;
    const SHOW_YES =1 ;
    static public $showMap = array(
        self::SHOW_NO  => '隐藏',
        self::SHOW_YES => '展示'
    );

    // 内外部
    const INNER_NO  = 0; //对外发布
    const INNER_YES = 1; //内部测试
    public static $arrInnerMap = array(
        self::INNER_NO  => '对外发布',
        self::INNER_YES => '内部测试',
    );

    //状态
    const STATUS_TOONLINE  = 1;
    const STATUS_ONLINE    = 2;
    const STATUS_OFFLINE   = 3;
    const STATUS_END       = 4;
    static $arrStatusMap = array(
        self::STATUS_TOONLINE => '待上架',
        self::STATUS_ONLINE   => '已上架',
        self::STATUS_OFFLINE  => '已下架',
        self::STATUS_END      => '已结束',
    );

    //资源类型
    const TYPE_BIN   = 0; //二进制
    const TYPE_PIC   = 1; //图片
    const TYPE_VIDEO = 2; //视频
    const TYPE_URL   = 3; //站外URL
    const TYPE_AUDIO = 4; //音频

    //商品标签信息
    static $TAGLIST = array(
        Hk_Util_Category::GRADE_STAGE_PRIMARY => array(
            Hkzb_Ds_Fudao_Course::TYPE_PRIVATE_LONG => array(
                array(
                    "title" => '服',
                    "desc" => "直播授课",
                    "content" => "直播授课，讲练测多维结合，还原真实上课场景，授课与课堂互动相结合，配合课堂测试让学习效果全面提升。"
                ),
                array(
                    "title" => '服',
                    "desc" => "学习报告",
                    "content" => "课下查看孩子学习报告。学习报告中全方位展示孩子到课情况、课上测试成绩、班级排名、以及孩子课上互动参与情况"
                ),
                array(
                    "title" => '服',
                    "desc" => "课程回放",
                    "content" => "直播课程结束12小时之内就可以查看课程回放，学生可以根据自己的学习情况多次收看课程回放，加深对课程的理解。"
                ),
                array(
                    "title" => '服',
                    "desc" => "随堂测试",
                    "content" => "在直播过程中穿插随堂测试，通过及时检测可以实时了解在线学习效果，加深孩子对重点知识点的巩固，同时做到对解题技巧的掌握。"
                ),
                array(
                    "title" => '服',
                    "desc" => "纸质教材",
                    "content" => "为每个直播课程准备相应的纸质教材，让孩子做到课前预习、课下练习。"
                ),
                array(
                    "title" => '服',
                    "desc" => "及时答疑",
                    "content" => "直播结束后，学生可以通过APP或微信等方式咨询老师或助教，解决课上课下难题。"
                ),
                array(
                    "title" => '服',
                    "desc" => "随时退款",
                    "content" => "学员购课后，如对课程教学或课程时间不满意，可对未开始直播的课程申请退款。"
                ),
                array(
                    "title" => '服',
                    "desc" => "小班授课",
                    "content" => "打造25人小班化的学习场景，激活学生的学习热情，确保学生能够充分参与课堂，达到预期学习效果。"
                ),
                array(
                    "title" => '服',
                    "desc" => "分层教学",
                    "content" => "为不同阶段的学生提供相适应的教学内容，提高课程与学员的匹配度，让个学生都能够学到适合自己的课程。"
                ),
                array(
                    "title" => '服',
                    "desc" => "三师伴学",
                    "content" => "每位长期班学员配有3位老师，各自分工，课上课下巩固孩子的学习效果。"
                ),
                array(
                    "title" => '服',
                    "desc" => "眼保健操",
                    "content" => '眼保健操：每一节长期班课的课间都配有眼保健操，改善孩子视力、缓解眼部疲劳。'
                ),
                array(
                    "title" => '服',
                    "desc"=> "作业批改",
                    "content"=>'作业批改：每节课的课后作业，均会有专属助教老师进行批改，及时检测孩子本堂课的学习效果，针对孩子不同的学习问题，提供个性化的批改、语音讲解和点评，让孩子感受到满满的关注和最有效的帮助。'
                )
            ),
            Hkzb_Ds_Fudao_Course::TYPE_PRIVATE => array(
                array(
                    "title" => '服',
                    "desc" => "直播授课",
                    "content" => "直播授课，讲练测多维结合，还原真实上课场景，授课与课堂互动相结合，配合课堂测试让学习效果全面提升。"
                ),
                array(
                    "title" => '服',
                    "desc" => "课程回放",
                    "content" => "直播课程结束12小时之内就可以查看课程回放，学生可以根据自己的学习情况多次收看课程回放，加深对课程的理解。"
                ),
                array(
                    "title" => '服',
                    'desc' => '电子讲义',
                    'content' => '学员购课后，可提前查看电子讲义'
                ),
                array(
                    "title" => '服',
                    'desc' => '随堂测试',
                    'content' => '在直播过程中穿插随堂测试，通过及时检测可以实时了解在线学习效果，加深孩子对重点知识点的巩固，同时做到对解题技巧的掌握。'
                )

            )
        ) ,
        Hk_Util_Category::GRADE_STAGE_JUNIOR => array(
            Hkzb_Ds_Fudao_Course::TYPE_PRIVATE_LONG => array(
                array(
                    "title" => '服',
                    "desc" => "直播授课",
                    "content" => "直播授课，讲练测多维结合，还原真实上课场景，授课与课堂互动相结合，配合课堂测试让学习效果全面提升。"
                ),
                array(
                    "title" => '服',
                    "desc" => "学习报告",
                    "content" => "课下查看孩子学习报告。学习报告中全方位展示孩子到课情况、课上测试成绩、班级排名、以及孩子课上互动参与情况。"
                ),
                array(
                    "title" => '服',
                    "desc" => "课程回放",
                    "content" => "直播课程结束12小时之内就可以查看课程回放，学生可以根据自己的学习情况多次收看课程回放，加深对课程的理解。"
                ),
                array(
                    "title" => '服',
                    "desc" => "随堂测试",
                    "content" => "在直播过程中穿插随堂测试，通过及时检测可以实时了解在线学习效果，加深孩子对重点知识点的巩固，同时做到对解题技巧的掌握。"
                ),
                array(
                    "title" => '服',
                    "desc" => "纸质教材",
                    "content" => "为每个直播课程准备相应的纸质教材，让孩子做到课前预习、课下练习。"
                ),
                array(
                    "title" => '服',
                    "desc" => "及时答疑",
                    "content" => "直播结束后，学生可以通过APP或微信等方式咨询老师或助教，解决课上课下难题。"
                ),
                array(
                    "title" => '服',
                    "desc" => "随时退款",
                    "content" => "学员购课后，如对课程教学或课程时间不满意，可对未开始直播的课程申请退款。"
                ),
                array(
                    "title" => '服',
                    "desc" => "小班授课",
                    "content" => "打造25人小班化的学习场景，激活学生的学习热情，确保学生能够充分参与课堂，达到预期学习效果。"
                ),
                array(
                    "title" => '服',
                    "desc" => "分层教学",
                    "content" => "为不同阶段的学生提供相适应的教学内容，提高课程与学员的匹配度，让个学生都能够学到适合自己的课程。"
                ),
                array(
                    "title" => '服',
                    "desc" => "三师伴学",
                    "content" => "每位长期班学员配有3位老师，各自分工，课上课下巩固孩子的学习效果。"
                ),
            ),
            Hkzb_Ds_Fudao_Course::TYPE_PRIVATE => array(
                array(
                    "title" => '服',
                    "desc" => "直播授课",
                    "content" => "直播授课，讲练测多维结合，还原真实上课场景，授课与课堂互动相结合，配合课堂测试让学习效果全面提升。"
                ),
                array(
                    "title" => '服',
                    "desc" => "课程回放",
                    "content" => "直播授课，讲练测多维结合，还原真实上课场景，授课与课堂互动相结合，配合课堂测试让学习效果全面提升。"
                ),
                array(
                    "title" => '服',
                    "desc" => "随时退款",
                    "content" => "直播授课，讲练测多维结合，还原真实上课场景，授课与课堂互动相结合，配合课堂测试让学习效果全面提升。"
                ),
                array(
                    "title" => '服',
                    'desc' => '电子讲义',
                    'content'  => '学员购课后，可提前查看电子讲义。'
                ),
            )
        ),
        Hk_Util_Category::GRADE_STAGE_SENIOR => array(
            Hkzb_Ds_Fudao_Course::TYPE_PRIVATE_LONG => array(
                array(
                    "title" => '服',
                    "desc" => "直播授课",
                    "content" => "直播授课，讲练测多维结合，还原真实上课场景，授课与课堂互动相结合，配合课堂测试让学习效果全面提升。"
                ),
                array(
                    "title" => '服',
                    "desc" => "学习报告",
                    "content" => "课下查看孩子学习报告。学习报告中全方位展示孩子到课情况、课上测试成绩、班级排名、以及孩子课上互动参与情况。"
                ),
                array(
                    "title" => '服',
                    "desc" => "课程回放",
                    "content" => "直播课程结束12小时之内就可以查看课程回放，学生可以根据自己的学习情况多次收看课程回放，加深对课程的理解。"
                ),
                array(
                    "title" => '服',
                    "desc" => "随堂测试",
                    "content" => "在直播过程中穿插随堂测试，通过及时检测可以实时了解在线学习效果，加深孩子对重点知识点的巩固，同时做到对解题技巧的掌握。"
                ),
                array(
                    "title" => '服',
                    "desc" => "纸质教材",
                    "content" => "为每个直播课程准备相应的纸质教材，让孩子做到课前预习、课下练习。"
                ),
                array(
                    "title" => '服',
                    "desc" => "及时答疑",
                    "content" => "直播结束后，学生可以通过APP或微信等方式咨询老师或助教，解决课上课下难题。"
                ),
                array(
                    "title" => '服',
                    "desc" => "随时退款",
                    "content" => "学员购课后，如对课程教学或课程时间不满意，可对未开始直播的课程申请退款。"
                ),
                array(
                    "title" => '服',
                    "desc" => "小班授课",
                    "content" => "打造25人小班化的学习场景，激活学生的学习热情，确保学生能够充分参与课堂，达到预期学习效果。"
                ),
                array(
                    "title" => '服',
                    "desc" => "分层教学",
                    "content" => "为不同阶段的学生提供相适应的教学内容，提高课程与学员的匹配度，让个学生都能够学到适合自己的课程。"
                ),
                array(
                    "title" => '服',
                    "desc" => "三师教学",
                    "content" => "每位长期班学员配有3位老师，各自分工，课上课下巩固孩子的学习效果。"
                ),
                array(
                    "title" => '服',
                    "desc" => "双师教学",
                    "content" => "每位长期班学员配有主讲、班主任2位老师，课上课下巩固孩子的学习效果。"
                )

            ),
            Hkzb_Ds_Fudao_Course::TYPE_PRIVATE => array(
                array(
                    "title" => "服",
                    "desc"  => "直播授课"
,                   "content" => "直播授课，讲练测多维结合，还原真实上课场景，授课与课堂互动相结合，配合课堂测试让学习效果全面提升。"
                ),

                array(
                    "title" => "服",
                    "desc"  => "课程回放",
                    "content" => "直播授课，讲练测多维结合，还原真实上课场景，授课与课堂互动相结合，配合课堂测试让学习效果全面提升。"
                ),
                array(
                    "title" => "服",
                    "desc"  => "随时退款",
                    "content" => "直播授课，讲练测多维结合，还原真实上课场景，授课与课堂互动相结合，配合课堂测试让学习效果全面提升。"
                ),
                array(
                    'title' => '服',
                    'desc'  => '电子讲义',
                    'content' => '学员购课后，可提前查看电子讲义。'
                ),
            )
        )
    );

    const ALL_FIELDS = "skuId,skuName,registerStartTime,registerStopTime,onlineStart,onlineStop,price,originPrice,studentCnt,studentMaxCnt,productStatus,productInner,isShow,createTime,updateTime,tags,operatorUid,operator,deleted,content,resource,extData";

    private $_objDaoGroupProduct;

    public function __construct()
    {
        $this->_objDaoGroupProduct = new Hkzb_Dao_Fudao_Product_GroupProduct();

    }

    public function addProduct($arrParams){
        if (intval($arrParams['skuId']) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[param: " . json_encode($arrParams) . "]");

            return false;
        }

        $arrFields = array(
            'skuId'             => intval($arrParams['skuId']),
            'skuName'           => isset($arrParams['skuName']) ? strval($arrParams['skuName']) : '',
            'registerStartTime' => isset($arrParams['registerStartTime']) ? intval($arrParams['registerStartTime']) : 0,
            'registerStopTime'  => isset($arrParams['registerStopTime']) ? intval($arrParams['registerStopTime']) : 0,
            'onlineStart'       => isset($arrParams['onlineStart']) ? intval($arrParams['onlineStart']) : 0,
            'onlineStop'        => isset($arrParams['onlineStop']) ? intval($arrParams['onlineStop']) : 0,
            'price'             => isset($arrParams['price']) ? intval($arrParams['price']) : 0,
            'originPrice'       => isset($arrParams['originPrice']) ? intval($arrParams['originPrice']) : 0,
            'studentCnt'        => isset($arrParams['studentCnt']) ? intval($arrParams['studentCnt']) : 0,
            'studentMaxCnt'     => isset($arrParams['studentMaxCnt']) ? intval($arrParams['studentMaxCnt']) : 0,
            'productStatus'     => self::STATUS_TOONLINE, //初始状态为待上线
            'productInner'      => isset($arrParams['productInner']) ? intval($arrParams['productInner']) : self::INNER_NO,
            'isShow'            => isset($arrParams['isShow']) ? intval($arrParams['isShow']) : self::SHOW_YES,
            'createTime'        => time(),
            'updateTime'        => time(),
            'tags'              => isset($arrParams['tags']) ? json_encode($arrParams['tags']) : '',
            'operatorUid'       => isset($arrParams['operatorUid']) ? intval($arrParams['operatorUid']) : 0,
            'operator'          => isset($arrParams['operator']) ? strval($arrParams['operator']) : '',
            'deleted'           => self::DELETE_STATUS_OK,
            'content'           => isset($arrParams['content']) ? json_encode($arrParams['content']) : '',
            'resource'          => isset($arrParams['resource']) ? json_encode($arrParams['resource']) : '',
            'extData'           => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
            );

        $ret = $this->_objDaoGroupProduct->insertRecords($arrFields);

        return $ret;
    }

    /**
     * @param $arrParams
     * @return array|bool
     */
    public function addToMisCourse($arrParams){
        if (intval($arrParams['skuId']) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[param: " . json_encode($arrParams) . "]");

            return false;
        }

        $content = array();
        if (!empty($arrParams['content'])){
            $content = $this->formatSkuContent($arrParams['content']);
        }

        $resource = array();
        if (!empty($arrParams['resource'])){
            $skuResource = $arrParams['resource'];
            //每单个资源包含字段 res，desc，type，size，len 分别代表： 资源pid或URL, 资源描述，资源类型，资源大小，资源长度（通常用于视频）

            if (!empty($skuResource['detailImgApp'])){ //课程详情页图片（APP）列表  多个
                $resource['detailImgApp'] = $skuResource['detailImgApp'];
            }
            if (!empty($skuResource['coverImgApp'])){ //课程封面图片（APP）列表  是多个，
                $resource['coverImgApp'] = $skuResource['coverImgApp'];
            }
            if (!empty($skuResource['detailVideo'])){ //课程详情页视频介绍 列表  是多个
                $resource['detailVideo'] = $skuResource['detailVideo'];
            }
            if (!empty($skuResource['detailVideoPic'])){ //课程详情页视频封面 列表  是多个
                $resource['detailVideoPic'] = $skuResource['detailVideoPic'];
            }
        }

        $tags = array();
        if (!empty($arrParams['tags'])){
            $tags['sale'] = $arrParams['tags']['sale'];
        }
        $arrFields = array(
            'skuId'             => intval($arrParams['skuId']),
            'skuName'           => isset($arrParams['skuName']) ? strval($arrParams['skuName']) : '',
            'courseIdList'      => isset($arrParams['courseIdList']) ? $arrParams['courseIdList'] : array(),
            'learnSeason'       => isset($arrParams['learnSeason']) ? intval($arrParams['learnSeason']) : 0,
            'content'           => $content,
            'resource'          => $resource,
            'registerStartTime' => isset($arrParams['registerStartTime']) ? intval($arrParams['registerStartTime']) : 0, //报名开始时间
            'registerStopTime'  => isset($arrParams['registerStopTime']) ? intval($arrParams['registerStopTime']) : 0,   //报名结束时间
            'price'             => isset($arrParams['price']) ? intval($arrParams['price']) : 0,                         //价格（单位：分）
            'originPrice'       => isset($arrParams['originPrice']) ? intval($arrParams['originPrice']) : 0,             //原价（单位：分）
            'tags'              => $tags,
            'isShow'            => isset($arrParams['isShow']) ? intval($arrParams['isShow']) : self::SHOW_YES,          //课程是否展示
            'isInner'           => isset($arrParams['productInner']) ? intval($arrParams['productInner']) : self::INNER_NO, //是否是内部课
            'onlineStrategyId'  => isset($arrParams['onlineStrategyId']) ? intval($arrParams['onlineStrategyId']) : 0,                //上架策略id
        );

        return $arrFields;
    }

    /**
     * 格式化  商品信息
     * @param $content
     * @return array
     */
    public function formatSkuContent($content){
        $arrOutput = array(
            'courseForPeople'   => empty($content['courseForPeople']) ? '' : $content['courseForPeople'], //适合人群
            'courseTarget'      => empty($content['courseTarget']) ? '' : $content['courseTarget'],       //课程目标、学习目标
            'courseSpecial'     => empty($content['courseSpecial']) ? '' : $content['courseSpecial'],     //课程特色
            'courseService'     => empty($content['courseService']) ? '' : $content['courseService'],     //课程服务
            'courseAbstract'    => empty($content['courseAbstract']) ? '' : $content['courseAbstract'],   //课程简介
            'courseContent'     => empty($content['courseContent']) ? '' : $content['courseContent'],     //课程内容、教学内容
            'courseLength'      => empty($content['courseLength']) ? '' : $content['courseLength'],       //课程时长
            'courseLecture'     => empty($content['courseLecture']) ? '' : $content['courseLecture'],     //赠送讲义
        );
        return $arrOutput;
//        $arrOutput = array();
//        if (!empty($content['courseForPeople'])){//适合人群
//            $arrOutput['courseForPeople'] = strval($content['courseForPeople']);
//        }
//        if (!empty($content['courseTarget'])){//学习目标
//            $arrOutput['courseTarget'] = strval($content['courseTarget']);
//        }
//        if (!empty($content['courseSpecial'])){//课程特色
//            $arrOutput['courseSpecial'] = strval($content['courseSpecial']);
//        }
//        if (!empty($content['courseService'])){//课程服务
//            $arrOutput['courseService'] = strval($content['courseService']);
//        }
//        if (!empty($content['courseAbstract'])){//课程简介
//            $arrOutput['courseAbstract'] = strval($content['courseAbstract']);
//        }
//        if (!empty($content['courseContent'])){//教学内容
//            $arrOutput['courseContent'] = strval($content['courseContent']);
//        }
//        if (!empty($content['courseLecture'])){//赠送讲义
//            $arrOutput['courseLecture'] = strval($content['courseLecture']);
//        }
//        if (!empty($content['courseLength'])){//课程时长
//            $arrOutput['courseLength'] = strval($content['courseLength']);
//        }

    }

    /**
     * format 商品上传的资料 音视频、课程封面等
     *
     * @param $oneClassResource
     * @param $type
     * @return array
     */
    public function formatSkuResource($oneClassResource, $type){
        $arrOutput = array();
        if (empty($oneClassResource)) {
            return $arrOutput;
        }

        foreach ($oneClassResource as $item) {
            if (empty($item) || empty($item['res'])) {  //保证上传的资料一起生效
                return $arrOutput;
            }
            $arrOutput[] = array(
                'res'  => $item['res'],
                'desc' => empty($item['name']) ? '' : $item['name'],
                'type' => empty($item['type']) ? 1 : $type,
                'size' => empty($item['size']) ? 0 : $item['size'],
                'len'  => empty($item['len']) ? 0 : $item['len'],
            );
        }

        return $arrOutput;
    }

    /**
     * 获取商品信息列表
     * @param $arrConds
     * @param $arrFields
     * @param $offset
     * @param $limit
     * @return array|false
     */
    public function getProductListByConds($arrConds, $arrFields = array(), $offset = 0, $limit = 20)
    {
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrAppends = array(
            'order by sku_id desc',
            'limit ' . $offset . ',' . $limit,
        );

        $ret = $this->_objDaoGroupProduct->getListByConds($arrConds, $arrFields, null, $arrAppends);

        return $ret;
    }

    /**
     * 获取数量
     * @param $arrConds
     * @return false|int
     */
    public function getCntByConds($arrConds)
    {
        return $this->_objDaoGroupProduct->getCntByConds($arrConds);
    }


    /**
     * 更新商品信息
     * @param $skuId
     * @param $arrFields
     * @return bool
     */
    public function updateProductInfo($skuId, $arrParams)
    {
        if (intval($skuId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[productId:$skuId]");
            return false;
        }

        $arrConds = array();
        $arrConds['skuId'] = intval($skuId);

        $arrFields    = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach ($arrParams as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }

            $arrFields[$key] = $value;
        }

        if (isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }
        if (isset($arrFields['tags'])){
            $arrFields['tags'] = json_encode($arrFields['tags']);
        }
        if (isset($arrFields['content'])){
            $arrFields['content'] = json_encode($arrFields['content']);
        }
        if (isset($arrFields['resource'])){
            $arrFields['resource'] = json_encode($arrFields['resource']);
        }

        $arrFields['updateTime'] = time();

        $ret = $this->_objDaoGroupProduct->updateByConds($arrConds, $arrFields);
        return $ret;
    }

    /**
     * @param $skuId
     * @param array $arrFields
     * @return array|bool|false
     */
    public function getProductInfo($skuId, $arrFields = array()){
        if (intval($skuId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[skuId:$skuId]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'skuId' => intval($skuId),
        );

        $ret = $this->_objDaoGroupProduct->getRecordByConds($arrConds, $arrFields);

        return $ret;
    }
}