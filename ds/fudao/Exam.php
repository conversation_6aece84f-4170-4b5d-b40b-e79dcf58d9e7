<?php

/**
 * @file   Exam.php
 * <AUTHOR>
 * @date   2017-09-03
 * @brief  考试系统相关API
 *          涉及到四张表
 *          practice/tblExamDetail      : 存放试卷信息
 *          homework/tblHomework        : 存放题目信息（做了分表处理，tid / 10000000）
 *          fudao/tblCourseExamPaper    : 存放章节与试卷绑定关系
 *          fudao/tblCourseExampaperMap : 存放课程与试卷绑定关系
 *
 **/
class Hkzb_Ds_Fudao_Exam {


    // 复制试卷 pathinfo
    const PATHINFO_COPY_EXAM = '/tikumis/examapi/submitexamcopy';      //复制试卷
    const PATHINFO_CREATE_EXAM = '/tikumis/examapi/submitexamadd';       //创建试卷
    const PATHINFO_GET_EXAM_LIST = '/tikumis/examapi/getexamlist';         //根据examids获得试卷列表
    const PATHINFO_GET_EXAM_INFO = '/tikumis/examapi/getexaminfo';         //根据examid获得试卷信息
    const PATNINFO_UPDATE_EXAMINFO = '/tikumis/examapi/submitexamedit';      //修改试卷信息：试卷标题、试卷说明
    const PATHINFO_UPDATE_EXAM_TEACHERUID = '/tikumis/examapi/submitexamuid';       //更改评语uid
    const PATHINFO_EXAM_INSERT = '/tikumis/examapi/submitexaminsert';    //整体更新试卷信息（试题排序、设置分数）

    const PATHINFO_SUBJECT_EDIT = '/tikumis/examapi/submitsubjectedit';   //新增or修改单题
    const PATHINFO_MULTI_SUBJECT_EDIT = '/tikumis/examapi/SubmitmultisubjectEdit'; //新增or修改材料题（阅读理解、完型填空）
    const PATHINFO_UPDATE_COMMENT = '/tikumis/examapi/submitexamcomment';      //编辑试卷评语


    // 试卷信息字段
    static $EXAM_DETAIL_FIELDS = array();

    // 题目信息字段
    static $HOMEWORK_FIELDS = array();

    // 章节与试卷绑定关系字段
    static $LESSON_EXAM_MAP_FIELDS = array();

    // 课程与试卷绑定关系字段
    static $COURSE_EXAM_MAP_FIELDS = array();


    /* 题目相关api begin */

    public function addSubject() {

        $strDbname = 'homework/homework';

        // 参数校验

        // 分配tid


        $objDb = Hk_Service_Db::getDB( $strDbname );

        $objDaoHomework = new Hk_Dao_Tiku_Homework();


        $intTid = 0;


        return $intTid;
    }

    /* 题目相关api end */

    /* 试卷相关api begin */

    // 添加试卷
    public function addExampaper($arrInput) {
        // 校验参数

        // 校验题目tid
        $objDaoHomework = new Hk_Dao_Tiku_Homework();

        $arrTidList = array();

        foreach ( $arrTidList as $value ) {
            $intTid     = intval( $value );
            $arrFields  = array(
                'tid'
            );
            $arrConds   = array(
                'tid' => $intTid,
            );
            $arrTidInfo = $objDaoHomework->getHomeworkById( $intTid, $arrFields, $arrConds );

            if ( empty( $arrTidInfo ) ) {

                return false;
            }
        }

        // 插入数据库
        $objDaoExampaper = new Hk_Dao_Practice_ExamDetail();

        $arrInsertField = array();


        $ret = $objDaoExampaper->insertRecords( $arrInsertField );

        return $ret;
    }

    // 修改试卷
    public function updateExampaper($intExamId, $arrInput) {


    }

    //用于整体更新试卷信息（试题顺序调整、试题删除）
    public function examInsert($arrInput) {
        $strPathInfo = self::PATHINFO_EXAM_INSERT;
        // header信息
        $header = array(
            'pathinfo' => $strPathInfo,
            'cookie'   => $_COOKIE,
        );
        //请求参数
        $arrParams = array(
            'examId'      => isset( $arrInput[ "examid" ] ) ? intval( $arrInput[ "examid" ] ) : 0,
            'title'       => isset( $arrInput[ "title" ] ) ? strval( $arrInput[ "title" ] ) : "",
            'typeId'      => isset( $arrInput[ "typeId" ] ) ? intval( $arrInput[ "typeId" ] ) : 0,
            'typeName'    => isset( $arrInput[ "typeName" ] ) ? $arrInput[ "typeName" ] : "",
            'year'        => isset( $arrInput[ "year" ] ) ? $arrInput[ "year" ] : "",
            'courseId'    => isset( $arrInput[ "courseId" ] ) ? intval( $arrInput[ "courseId" ] ) : 0,
            'gradeId'     => isset( $arrInput[ "gradeId" ] ) ? intval( $arrInput[ "gradeId" ] ) : 0,
            'semesterId'  => isset( $arrInput[ "semesterId" ] ) ? intval( $arrInput[ "semesterId" ] ) : 0,
            'provinceId'  => isset( $arrInput[ "provinceId" ] ) ? intval( $arrInput[ "provinceId" ] ) : 0,
            'versionId'   => isset( $arrInput[ "versionId" ] ) ? intval( $arrInput[ "versionId" ] ) : 0,
            'versionName' => isset( $arrInput[ "versionName" ] ) ? $arrInput[ "versionName" ] : "",
            'content'     => isset( $arrInput[ "content" ] ) ? $arrInput[ "content" ] : "",
            'tidList'     => isset( $arrInput[ "tidList" ] ) ? $arrInput[ "tidList" ] : "",
            'ext'         => isset( $arrInput[ "ext" ] ) ? $arrInput[ "ext" ] : "",
            'mark'        => 1,
            //堂堂侧、报前测、报后测mark值为1
        );

        $strResponse = ral( 'platmis', 'POST', $arrParams, 123, $header );
        if ( false === $strResponse ) {
            $errNo          = ral_get_errno();
            $errMsg         = ral_get_error();
            $protocolStatus = ral_get_protocol_code();
            Bd_Log::warning(
                "Error:[service tiku connect error], Detail:[errno:$errNo errmsg:$errMsg protocol_status:$protocolStatus]"
            );
            throw new Hk_Util_Exception( Hk_Util_ExceptionCodes::OTHER_ERROR, '题库接口调用失败' );
        }

        $arrResponse = json_decode( $strResponse, true );
        if ( !isset( $arrResponse[ 'data' ][ 'examid' ] ) ) {
            return false;
        }

        $intNewExamId = intval( $arrResponse[ 'data' ][ 'examid' ] );

        return $intNewExamId;
    }

    public function createExamPaper($arrInput) {

        //参数校验
        if ( empty( $arrInput[ 'title' ] ) || intval( $arrInput[ 'courseid' ] ) <= 0 || intval( $arrInput[ 'gradeid' ] ) <= 0
        ) {
            return false;
        }

        $objDsExamPaper = new Hkzb_Ds_Fudao_Exam_ExamPaper();

        //请求参数
        $arrParams = array(
            'title'       => strval( $arrInput[ 'title' ] ),
            'subject'     => intval( $arrInput[ 'courseid' ] ),
            'grade'       => intval( $arrInput[ 'gradeid' ] ),
            'comment'     => strval( $arrInput[ 'comment' ] ),
            'description' => strval( $arrInput[ 'description' ] ),
        );

        $intExamId = $objDsExamPaper->createExamPaper( $arrParams );

        return $intExamId;
    }

    /**
     * 创建试卷
     * @param $arrInput
     * @return bool|int
     * @throws Hk_Util_Exception
     */
    public function createExamPaper2($arrInput) {
        //参数校验
        if ( empty( $arrInput[ 'examPaperName' ] ) || intval( $arrInput[ 'subject' ] ) <= 0 || intval(
                $arrInput[ 'grade' ]
            )
        ) {
            return false;
        }

        $strPathInfo = self::PATHINFO_CREATE_EXAM;
        // header信息
        $header = array(
            'pathinfo' => $strPathInfo,
            'cookie'   => $_COOKIE,
        );
        //请求参数
        $arrParams = array(
            'title'       => strval( $arrInput[ 'examPaperName' ] ),
            'courseid'    => intval( $arrInput[ 'subject' ] ),
            'gradeid'     => intval( $arrInput[ 'grade' ] ),
            'comment'     => strval( $arrInput[ 'comment' ] ),
            'description' => strval( $arrInput[ 'description' ] ),
        );

        $strResponse = ral( 'platmis', 'POST', $arrParams, 123, $header );
        if ( false === $strResponse ) {
            $errNo          = ral_get_errno();
            $errMsg         = ral_get_error();
            $protocolStatus = ral_get_protocol_code();
            Bd_Log::warning(
                "Error:[service tiku connect error], Detail:[errno:$errNo errmsg:$errMsg protocol_status:$protocolStatus]"
            );
            throw new Hk_Util_Exception( Hk_Util_ExceptionCodes::OTHER_ERROR, '题库接口调用失败' );
        }

        $arrResponse = json_decode( $strResponse, true );
        if ( !isset( $arrResponse[ 'data' ][ 'examid' ] ) ) {
            return false;
        }

        $intNewExamId = intval( $arrResponse[ 'data' ][ 'examid' ] );

        return $intNewExamId;
    }

    // 复制试卷
    public function copyExampaper($arrInput) {

        $intExamId = intval( $arrInput[ 'examid' ] );

        if ( $intExamId <= 0 ) {
            return false;
        }
        // 发送目标pathinfo
        $strPathInfo = self::PATHINFO_COPY_EXAM;
        // header信息
        $header = array(
            'pathinfo' => $strPathInfo,
            'cookie'   => $_COOKIE,
        );
        // 参数
        $arrParams = array(
            'examid' => $intExamId,
        );
        // 调用ral
        $strResponse = ral( 'platmis', 'POST', $arrParams, 123, $header );

        if ( false === $strResponse ) {
            $errNo          = ral_get_errno();
            $errMsg         = ral_get_error();
            $protocolStatus = ral_get_protocol_code();
            Bd_Log::warning(
                "Error:[service tiku connect error], Detail:[errno:$errNo errmsg:$errMsg protocol_status:$protocolStatus]"
            );
            throw new Hk_Util_Exception( Hk_Util_ExceptionCodes::OTHER_ERROR, '题库接口调用失败' );
        }

        $arrResponse = json_decode( $strResponse, true );

        if ( !isset( $arrResponse[ 'data' ][ 'examid' ] ) ) {
            return false;
        }

        $intNewExamId = intval( $arrResponse[ 'data' ][ 'examid' ] );

        return $intNewExamId;
    }

    /**
     * 根据试卷id查询试卷，返回单个试卷信息
     * @param $examid int
     * @return bool|mixed
     * @throws Hk_Util_Exception
     */
    public function getExampaperById($examId) {
        if ( intval( $examId ) <= 0 ) {
            return false;
        }

        $strPathInfo = self::PATHINFO_GET_EXAM_INFO;
        $header      = array(
            'pathinfo' => $strPathInfo,
            'cookie'   => $_COOKIE,
        );

        $arrParams = array(
            'examid' => intval( $examId ),
        );

        $strResponse = ral( 'platmis', 'POST', $arrParams, 123, $header );

        if ( false == $strResponse ) {
            $errNo          = ral_get_errno();
            $errMsg         = ral_get_error();
            $protocolStatus = ral_get_protocol_code();
            Bd_Log::warning(
                "Error:[service tiku connect error], Detail:[errno:$errNo errmsg:$errMsg protocol_status:$protocolStatus]"
            );
            throw new Hk_Util_Exception( Hk_Util_ExceptionCodes::OTHER_ERROR, '题库接口调用失败' );
        }

        $arrResponse = json_decode( $strResponse, true );
        $arrResponse = $arrResponse[ 'data' ];

        return $arrResponse;
    }

    // 根据试卷ids查询试卷，返回单个试卷信息
    public function getExampaperListByIds($arrExamIds) {

        $arrList = array();

        $objDsExamPaper = new Hkzb_Ds_Fudao_Exam_ExamPaper();

        foreach ($arrExamIds as $examId) {
            $arrExamPaperInfo = $objDsExamPaper->getExamPaperInfoByExamId($examId);

            if ( empty($arrExamPaperInfo) ) {
                continue;
            }

            $arrTemp = array(
                'examid'=>intval($arrExamPaperInfo['examid']),
                'sumscore'=>intval($arrExamPaperInfo['sumscore']),
                'sumnumber'=>intval($arrExamPaperInfo['sumnumber']),
            );

            $arrList[] = $arrTemp;
        }

        $arrOutput = array(
            'data'=>$arrList,
        );

        return $arrOutput;

        //$strPathInfo = self::PATHINFO_GET_EXAM_LIST;
        //
        //$header = array(
        //    'pathinfo' => $strPathInfo,
        //    'cookie'   => $_COOKIE,
        //);
        //
        //$arrParams = array(
        //    'examid' => $arrExamIds,
        //);
        //
        //$strResponse = ral( 'platmis', 'POST', $arrParams, 123, $header );
        //
        //if ( false == $strResponse ) {
        //    $errNo          = ral_get_errno();
        //    $errMsg         = ral_get_error();
        //    $protocolStatus = ral_get_protocol_code();
        //    Bd_Log::warning(
        //        "Error:[service tiku connect error], Detail:[errno:$errNo errmsg:$errMsg protocol_status:$protocolStatus]"
        //    );
        //    throw new Hk_Util_Exception( Hk_Util_ExceptionCodes::OTHER_ERROR, '题库接口调用失败' );
        //}
        //
        //$arrResponse = json_decode( $strResponse, true );
        //$arrResponse = $arrResponse[ 'data' ];
        //
        //return $arrResponse;
    }

    // 根据条件查询试卷（放在其他地方）


    /**
     * 更新试卷对应的教师uid（更新评语里面老师uid）
     * 当teacherUid = 0. 接触试卷评语里与老师的关系
     * @param $arrInput
     * @return bool|mixed
     * @throws Hk_Util_Exception
     */
    public function changeExamTeacherUid($arrInput) {
        $intExamId = intval( $arrInput[ 'examPaperId' ] );

        $intTeacherUid = intval( $arrInput[ 'teacherUid' ] );

        if ( $intExamId <= 0 ) {
            return false;
        }

        $strPathInfo = self::PATHINFO_UPDATE_EXAM_TEACHERUID;

        $header      = array(
            'pathinfo' => $strPathInfo,
            'cookie'   => $_COOKIE,
        );
        $arrParams   = array(
            'examid'     => $intExamId,
            'teacheruid' => $intTeacherUid,
        );
        $strResponse = ral( 'platmis', 'POST', $arrParams, 123, $header );

        if ( false == $strResponse ) {
            $errNo          = ral_get_errno();
            $errMsg         = ral_get_error();
            $protocolStatus = ral_get_protocol_code();
            Bd_Log::warning(
                "Error:[service tiku connect error], Detail:[errno:$errNo errmsg:$errMsg protocol_status:$protocolStatus]"
            );
            throw new Hk_Util_Exception( Hk_Util_ExceptionCodes::OTHER_ERROR, '题库接口调用失败' );
        }

        $arrResponse = json_decode( $strResponse, true );
        if ( !isset( $arrResponse[ 'data' ][ 'examid' ] ) ) {
            return false;
        }

        $intNewExamId = intval( $arrResponse[ 'data' ][ 'examid' ] );

        return $intNewExamId;
    }

    /**
     * 根据试卷id，修改试卷基本属性：试卷标题，试卷说明
     * @param $arrInput
     * @return bool|int
     * @throws Hk_Util_Exception
     */
    public function updateExamInfo($arrInput) {
        $intExamId = intval( $arrInput[ 'examPaperId' ] );
        //参数校验
        if ( $intExamId <= 0 ) {
            return false;
        }
        if ( empty( $arrInput[ 'title' ] ) && empty( $arrInput[ 'description' ] ) ) {
            return false;
        }

        $strPathInfo = self::PATNINFO_UPDATE_EXAMINFO;
        $header      = array(
            'pathinfo' => $strPathInfo,
            'cookie'   => $_COOKIE,
        );

        //请求参数
        $arrParams             = array();
        $arrParams[ 'examid' ] = $intExamId;
        if ( $arrInput[ 'title' ] ) {
            $arrParams[ 'title' ] = strval( $arrInput[ 'title' ] );
        }
        if ( $arrInput[ 'description' ] ) {
            $arrParams[ 'description' ] = strval( $arrInput[ 'description' ] );
        }

        $strResponse = ral( 'platmis', 'POST', $arrParams, 123, $header );
        if ( false == $strResponse ) {
            $errNo          = ral_get_errno();
            $errMsg         = ral_get_error();
            $protocolStatus = ral_get_protocol_code();
            Bd_Log::warning(
                "Error:[service tiku connect error], Detail:[errno:$errNo errmsg:$errMsg protocol_status:$protocolStatus]"
            );
            throw new Hk_Util_Exception( Hk_Util_ExceptionCodes::OTHER_ERROR, '题库接口调用失败' );
        }

        $arrResponse = json_decode( $strResponse, true );
        if ( !isset( $arrResponse[ 'data' ][ 'examid' ] ) ) {
            return false;
        }

        $intNewExamId = intval( $arrResponse[ 'data' ][ 'examid' ] );

        return $intNewExamId;
    }

    /**
     * 新增or编辑 小题（单选、多选、判断）
     * @param $arrInput
     * @return bool|int  返回 examId
     * @throws Hk_Util_Exception
     */
    public function subjectEdit($arrInput) {
        if ( intval(
                $arrInput[ 'examid' ]
            ) <= 0 || !$arrInput[ 'score' ] || !$arrInput[ 'tp' ] || !$arrInput[ 'question' ] || !$arrInput[ 'answer' ]
        ) {
            return false;
        }

        $strPathInfo = self::PATHINFO_SUBJECT_EDIT;
        $header      = array(
            'pathinfo' => $strPathInfo,
            'cookie'   => $_COOKIE,
        );
        $arrParams   = array(
            'examid'   => isset( $arrInput[ "examid" ] ) ? intval( $arrInput[ "examid" ] ) : 0,
            'question' => isset( $arrInput[ "question" ] ) ? $arrInput[ "question" ] : array(),
            'answer'   => isset( $arrInput[ "answer" ] ) ? $arrInput[ "answer" ] : array(),
            'analysis' => isset( $arrInput[ "analysis" ] ) ? $arrInput[ "analysis" ] : array(),
            'tid'      => isset( $arrInput[ 'tid' ] ) ? intval( $arrInput[ "tid" ] ) : 0,
            'score'    => isset( $arrInput[ 'score' ] ) ? intval( $arrInput[ "score" ] ) : 0,
            'tp'       => isset( $arrInput[ 'tp' ] ) ? intval( $arrInput[ "tp" ] ) : 0,
        );

        $strResponse = ral( 'platmis', 'POST', $arrParams, 123, $header );
        if ( false == $strResponse ) {
            $errNo          = ral_get_errno();
            $errMsg         = ral_get_error();
            $protocolStatus = ral_get_protocol_code();
            Bd_Log::warning(
                "Error:[service tiku connect error], Detail:[errno:$errNo errmsg:$errMsg protocol_status:$protocolStatus]"
            );
            throw new Hk_Util_Exception( Hk_Util_ExceptionCodes::OTHER_ERROR, '题库接口调用失败' );
        }

        $arrResponse = json_decode( $strResponse, true );
        if ( !isset( $arrResponse[ 'data' ][ 'examid' ] ) ) {
            return false;
        }
        $intNewExamId = intval( $arrResponse[ 'data' ][ 'examid' ] );

        return $intNewExamId;
    }

    /**
     * 新增 or 修改 材料题（阅读理解、完型填空题）
     * @param $arrInput
     * @return bool|int  返回 examId
     * @throws Hk_Util_Exception
     */
    public function multiSubjectEdit($arrInput) {
        if ( intval(
                $arrInput[ 'examid' ]
            ) <= 0 || !$arrInput[ 'score' ] || !$arrInput[ 'tp' ] || !$arrInput[ 'content' ] || !$arrInput[ 'subjects' ]
        ) {
            return false;
        }

        $strPathInfo = self::PATHINFO_MULTI_SUBJECT_EDIT;
        $header      = array(
            'pathinfo' => $strPathInfo,
            'cookie'   => $_COOKIE,
        );
        $arrParams   = array(
            'examid'   => isset( $arrInput[ "examid" ] ) ? intval( $arrInput[ "examid" ] ) : 0,
            'content'  => isset( $arrInput[ "content" ] ) ? $arrInput[ "content" ] : array(),
            'subjects' => isset( $arrInput[ "subjects" ] ) ? $arrInput[ "subjects" ] : array(),
            'tid'      => isset( $arrInput[ 'tid' ] ) ? intval( $arrInput[ "tid" ] ) : 0,
            'score'    => isset( $arrInput[ 'score' ] ) ? $arrInput[ "score" ] : array(),
            'tp'       => isset( $arrInput[ 'tp' ] ) ? intval( $arrInput[ "tp" ] ) : 0,
        );

        $strResponse = ral( 'platmis', 'POST', $arrParams, 123, $header );
        if ( false == $strResponse ) {
            $errNo          = ral_get_errno();
            $errMsg         = ral_get_error();
            $protocolStatus = ral_get_protocol_code();
            Bd_Log::warning(
                "Error:[service tiku connect error], Detail:[errno:$errNo errmsg:$errMsg protocol_status:$protocolStatus]"
            );
            throw new Hk_Util_Exception( Hk_Util_ExceptionCodes::OTHER_ERROR, '题库接口调用失败' );
        }

        $arrResponse = json_decode( $strResponse, true );
        if ( !isset( $arrResponse[ 'data' ][ 'examid' ] ) ) {
            return false;
        }
        $intNewExamId = intval( $arrResponse[ 'data' ][ 'examid' ] );

        return $intNewExamId;
    }

    //编辑试卷评语
    public function updateExamComment($arrInput) {
        if ( intval( $arrInput[ 'examid' ] ) <= 0 || !$arrInput[ 'comment' ] ) {
            return false;
        }

        $strPathInfo = self::PATHINFO_UPDATE_COMMENT;
        $header      = array(
            'pathinfo' => $strPathInfo,
            'cookie'   => $_COOKIE,
        );
        $arrParams   = array(
            'examid'  => isset( $arrInput[ "examid" ] ) ? intval( $arrInput[ "examid" ] ) : 0,
            'comment' => isset( $arrInput[ "comment" ] ) ? $arrInput[ "comment" ] : array(),
        );

        $strResponse = ral( 'platmis', 'POST', $arrParams, 123, $header );
        if ( false == $strResponse ) {
            $errNo          = ral_get_errno();
            $errMsg         = ral_get_error();
            $protocolStatus = ral_get_protocol_code();
            Bd_Log::warning(
                "Error:[service tiku connect error], Detail:[errno:$errNo errmsg:$errMsg protocol_status:$protocolStatus]"
            );
            throw new Hk_Util_Exception( Hk_Util_ExceptionCodes::OTHER_ERROR, '题库接口调用失败' );
        }

        $arrResponse = json_decode( $strResponse, true );
        if ( !isset( $arrResponse[ 'data' ][ 'examid' ] ) ) {
            return false;
        }
        $intNewExamId = intval( $arrResponse[ 'data' ][ 'examid' ] );

        return $intNewExamId;
    }


    /* 试卷相关api end */

    /* 试卷绑定关系相关api begin */

    // 绑定试卷到章节
    public function bindExamToLesson($arrInput) {

    }

    // 解绑试卷到章节
    public function unbindExamToLesson($arrInput) {

    }

    // 绑定试卷到课程
    public function bindExamToCourse($arrInput) {
        $intExamId   = intval( $arrInput[ 'examid' ] );
        $intCourseId = intval( $arrInput[ 'courseId' ] );

        // 启动事务
        $objDaoCourseExam = new Hkzb_Dao_Fudao_CourseExamPaper();

        $objDaoCourseExam->startTransaction();
        // 判断课程是否已经绑定了试卷


        $arrConds  = array(
            ''
        );
        $arrFields = array();
        $ret       = $objDaoCourseExam->getListByConds( $arrConds, $arrFields );


    }

    // 获取某一个课程的绑定试卷信息
    public function getCourseBindInfoByCourseId($intCourseId) {

    }

    /* 试卷绑定关系相关api end */

}