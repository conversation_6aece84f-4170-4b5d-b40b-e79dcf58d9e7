<?php

/**
 * @file   StudentScore.php
 * <AUTHOR>
 * @date   2015-11-26
 * @brief  学生访谈记录信息(辅导员维护) 
 *
 **/
class Hkzb_Ds_Fudao_StudentInterview
{
    const ALL_FIELDS = 'id,studentUid,interviewTime,type,content,deleted,createTime,updateTime,operatorUid,operator,extData,courseId,channelType';

    //状态
    const STATUS_OK      = 0; //未删除
    const STATUS_DELETED = 1; //已删除
    static $STATUS_ARRAY = array(
        self::STATUS_OK      => '未删除',
        self::STATUS_DELETED => '已删除',
    );

    //访谈类型
    const NO_TYPE       = 0;    //未分类
    const DAILY         = 1;    //日常
    const NEW_USER      = 2;    //用户家访
    const ENROLL        = 3;    //续报
    const WITHDRAW      = 4;    //退课
    const NU_CALLBACK   = 5;    //新用户回访
    const BC_INTERVIEW  = 6;    //课中回访
    static $INTERVIEW_TYPE = array(
        self::NO_TYPE       => '',
        self::DAILY         => '日常',
        self::NEW_USER      => '用户家访',
        self::ENROLL        => '续报',
        self::WITHDRAW      => '退课',
        self::NU_CALLBACK   => '新用户回访',
        self::BC_INTERVIEW  => '课中回访',
    );

    //渠道类型
    const CHANNEL_TYPE_PHONE    = 1;
    const CHANNEL_TYPE_WECHANT  = 2 ;
    
    private $objDaoStudentInterview;

    /**
     * 构造函数
     *
     */
    public function __construct() {
        $this->objDaoStudentInterview = new Hkzb_Dao_Fudao_StudentInterview();
    }

    /**
     * 新增学生访谈记录
     *
     * @param  array $arrParams 学生访谈记录
     * @return bool true/false
     */
    public function addStudentInterview($arrParams) {
        if (0 >= intval($arrParams['studentUid'])) {
            Bd_Log::warning("Error:[param error], Detail:[param: " . json_encode($arrParams) . "]");

            return false;
        }

        $arrFields = array(
            'studentUid'    => intval($arrParams['studentUid']),
            'interviewTime' => isset($arrParams['interviewTime']) ? intval($arrParams['interviewTime']) : '0',
            'type'          => isset($arrParams['type']) ? intval($arrParams['type']) : '0',
            'content'       => isset($arrParams['content']) ? strval($arrParams['content']) : '',
            'deleted'       => self::STATUS_OK,
            'createTime'    => time(),
            'updateTime'    => time(),
            'operatorUid'   => isset($arrParams['operatorUid']) ? intval($arrParams['operatorUid']) : 0,
            'operator'      => isset($arrParams['operator']) ? strval($arrParams['operator']) : '',
            'extData'       => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '[]',
            'courseId'      => isset($arrParams['courseId']) ? intval($arrParams['courseId']) : 0,
            'channelType'   => isset($arrParams['channelType']) ? intval($arrParams['channelType']) : 0 ,
        );

        $ret = $this->objDaoStudentInterview->insertRecords($arrFields);
        if (!$ret) {
            Bd_Log::warning("Error[dbError] Detail[db insert failed]");

            return false;
        }
        $result = array(
            'errno' => 0,
            'id'    => $this->objDaoStudentInterview->getInsertId(),
        );

        return $result;
    }

    /**
     * 更新学生访谈记录
     * @param int    $id
     * @param  array $arrParams 学生属性
     * @return bool true/false
     */
    public function updateStudentInterview($id, $arrParams) {
        if (intval($id) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id]");

            return false;
        }

        $arrConds = array(
            'id' => intval($id),
        );

        $arrFields    = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach ($arrParams as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }

            $arrFields[$key] = $value;
        }

        //ext由ps补全，json处理在ds
        if (isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }

        //补全更新时间
        $arrFields['updateTime'] = time();

        $ret = $this->objDaoStudentInterview->updateByConds($arrConds, $arrFields);

        return $ret;
    }

    /**
     * 删除学生访谈记录
     * 调用前请先调用checkValidAssistantTeacherInterview 校验教师权限
     * @param int $id
     * @return bool true/false
     */
    public function deleteStudentInterview($id) {
        if (intval($id) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id]");

            return false;
        }

        $arrConds = array(
            'id' => intval($id),
        );

        $arrFields = array(
            'deleted'    => self::STATUS_DELETED,
            'updateTime' => time(),
        );
        //外部校验权限
        $ret = $this->objDaoStudentInterview->updateByConds($arrConds, $arrFields);

        return $ret;
    }

    /** 
     * 获取单条记录
     * @param int $id
     * @return mix
     */
    public function getStudentInterviewInfo($id,$arrFields = array()) {
        if (intval($id) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id]");

            return false;
        }   

        $arrConds = array(
            'id' => intval($id),
        );  

        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }   
        //外部校验权限
        $ret = $this->objDaoStudentInterview->getRecordByConds($arrConds, $arrFields);

        return $ret;
    }   

    /**
     * 批量获取学生访谈记录信息
     * @param       $studentUid
     * @param array $arrFields
     * @param int   $offset
     * @param int   $limit
     * @return array|bool|false
     */
    public function getStudentInterviewList($studentUid, $arrFields = array(), $offset = 0, $limit = 20) {
        if (intval($studentUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds   = array(
            "student_uid" => $studentUid,
            "deleted"     => self::STATUS_OK,
        );
        $arrAppends = array("order by interview_time desc limit $offset, $limit");
        $ret = $this->objDaoStudentInterview->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /**
     * 批量获取学生访谈记录信息
     * @param       $studentUid
     * @param array $arrFields
     * @param int   $offset
     * @param int   $limit
     * @return array|bool|false
     */
    public function getStudentInterviewCnt($studentUid) {
        if (intval($studentUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid]");

            return false;
        }

        $arrConds = array(
            "student_uid" => $studentUid,
            "deleted"     => self::STATUS_OK,
        );

        $ret = $this->objDaoStudentInterview->getCntByConds($arrConds);

        return $ret;
    }

    /**
     * 检查辅导老师是否合法
     *
     * @param  int $teacherUid 老师uid
     * @param  int $courseId   课程id
     * @param  int $classId    班级id
     * @return bool true/false
     */
    public function checkValidAssistantTeacherInterview($teacherUid, $interviewId) {
        if (intval($teacherUid) <= 0 || intval($interviewId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[teacherUid:$teacherUid interviewId:$interviewId studentUid:$studentUid]");

            return false;
        }

        $arrConds = array(
            'operatorUid' => $teacherUid,
            'id'          => $interviewId,
        );
        $ret      = $this->objDaoStudentInterview->getRecordByConds($arrConds, array('id'), null, $arrAppends);
        if (empty($ret)) {
            return false;
        }

        return true;
    }
     /**
     * 获取学生访谈记录信息
     * @param       conds
     * @param array $arrFields
     * @param int   $offset
     * @param int   $limit
     * @return array|bool|false
     */
    public function getStudentInterviewConds($arrConds, $arrFields = array(), $offset = 0, $limit = 20) {
       if(!isset($arrConds['courseId'])){
         Bd_Log::warning("Error:[param error], Detail:[courseId]");
       }
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        } 

        $arrConds['deleted']   = self::STATUS_OK;
          
        $arrAppends = array("order by interview_time desc limit $offset, $limit");
        $ret = $this->objDaoStudentInterview->getListByConds($arrConds, $arrFields, NULL, $arrAppends);
         if (false === $ret) {
            Bd_Log::warning("Error[dbError] Detail[db insert failed]");

            return false;
        }

        return $ret;
    }
    
      /**
     * 获取学生访谈记录信息
     * @param array $arrFields
     * @param int   $offset
     * @param int   $limit
     * @return array|bool|false
     */
    public function getStudentInterviewCount($arrConds) {   
    if(!isset($arrConds['courseId'])){
            Bd_Log::warning("Error:[param error], Detail:[courseId]");
          }
        $arrConds['deleted']   = self::STATUS_OK;

        $ret = $this->objDaoStudentInterview->getCntByConds($arrConds);
         if (false === $ret) {
            Bd_Log::warning("Error[dbError] Detail[db insert failed]");

            return 0;
        }

        return $ret;
    }
}
