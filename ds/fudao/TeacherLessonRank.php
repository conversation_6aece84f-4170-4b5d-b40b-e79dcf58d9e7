<?php

/**
 * @file   TeacherLessonRank.php
 * <AUTHOR>
 * @date   2017-07-15
 * @brief  老师教学情况排名数据
 *
 **/
class Hkzb_Ds_Fudao_TeacherLessonRank {

    private $_objDaoTeacherLessonRank;

    public function __construct() {
        $this->_objDaoTeacherLessonRank = new Hkzb_Dao_Fudao_TeacherLessonRank();
    }

    const ALL_FIELDS = 'id,packCourseId,courseId,lessonId,grade,gradeSection,subject,teacherUid,lessonDate,registerNum,attentNum,finishNum,rebuyNum,attentRate,finishRate,rebuyRate,createTime,updateTime,isDelete,attentRank,finishRank,extData,rebuyRank';


    // 新增记录
    public function addRankRecord($arrParams) {
        if ( !is_array( $arrParams ) || empty( $arrParams[ 'lessonId' ] ) ) {
            Bd_Log::warning( "Error[param error] Detail[" . @json_encode( $arrParams ) . "]" );

            return false;
        }

        $intLessonId = intval( $arrParams[ 'lessonId' ] );

        if ( 0 == $intLessonId ) {
            Bd_Log::warning( "Error[param error] Detail[" . @json_encode( $arrParams ) . "]" );

            return false;
        }

        // 启动事务，代码层面确保数据库一个lesson只有一条记录
        // 注意事务隔离级别
        $this->_objDaoTeacherLessonRank->startTransaction();

        $arrConds = array(
            'lessonId' => $intLessonId,
        );

        $intCnt = $this->_objDaoTeacherLessonRank->getCntByConds( $arrConds );

        if ( intval( $intCnt ) > 0 ) {
            // 更新操作
            $arrFields    = array();
            $arrAllFields = explode( ',', self::ALL_FIELDS );
            foreach ( $arrParams as $key => $value ) {
                if ( !in_array( $key, $arrAllFields ) ) {
                    continue;
                }

                if ( 'extData' == $key ) {
                    if ( is_array( $value ) ) {
                        $arrFields[ $key ] = json_encode( $value );
                    } else {
                        $arrFields[ $key ] = '[]';
                    }
                } else {
                    $arrFields[ $key ] = $value;
                }
            }

            // 更新updateTime
            $arrFields[ 'updateTime' ] = time();

            $ret = $this->_objDaoTeacherLessonRank->updateByConds( $arrConds, $arrFields );

        } else {

            $jsonExtDate = '[]';
            if ( isset( $arrParams[ 'extData' ] ) ) {
                $jsonExtDate = json_encode( $arrParams[ 'extData' ] );
            }

            // 插入操作
            $arrFields = array(
                'packCourseId' => intval( $arrParams[ 'packCourseId' ] ),
                'courseId'     => intval( $arrParams[ 'courseId' ] ),
                'lessonId'     => intval( $arrParams[ 'lessonId' ] ),
                'grade'        => intval( $arrParams[ 'grade' ] ),
                'gradeSection' => intval( $arrParams[ 'gradeSection' ] ),
                'subject'      => intval( $arrParams[ 'subject' ] ),
                'teacherUid'   => intval( $arrParams[ 'teacherUid' ] ),
                'lessonDate'   => intval( $arrParams[ 'lessonDate' ] ),
                'registerNum'  => intval( $arrParams[ 'registerNum' ] ),
                'attentNum'    => intval( $arrParams[ 'attentNum' ] ),
                'finishNum'    => intval( $arrParams[ 'finishNum' ] ),
                'rebuyNum'     => intval( $arrParams[ 'rebuyNum' ] ),
                'attentRate'   => intval( $arrParams[ 'attentRate' ] ),
                'finishRate'   => intval( $arrParams[ 'finishRate' ] ),
                'rebuyRate'    => intval( $arrParams[ 'rebuyRate' ] ),
                'createTime'   => time(),
                'updateTime'   => time(),
                'isDelete'     => 0,
                'attentRank'   => intval( $arrParams[ 'attentRank' ] ),
                'finishRank'   => intval( $arrParams[ 'finishRank' ] ),
                'extData'      => $jsonExtDate,
                'rebuyRank'    => intval( $arrParams[ 'rebuyRank' ] ),
            );

            $ret = $this->_objDaoTeacherLessonRank->insertRecords( $arrFields );
        }

        if ( false == $ret || empty( $ret ) ) {
            Bd_Log::warning( "Error[param error] Transaction ROLLBACK Detail[" . @json_encode( $arrParams ) . "]" );
            $this->_objDaoTeacherLessonRank->rollback();
        } else {
            $this->_objDaoTeacherLessonRank->commit();
        }

        return $ret;
    }

    // 获取教师排名情况
    public function getRankListByConds($arrConds, $arrFields = array(), $arrAppends = null) {

        if ( !is_array( $arrConds ) ) {
            return false;
        }

        // 添加条件
        $arrConds[ 'isDelete' ] = 0;

        if ( empty( $arrFields ) ) {
            $arrFields = explode( ',', self::ALL_FIELDS );
        }

        $ret = $this->_objDaoTeacherLessonRank->getListByConds( $arrConds, $arrFields, null, $arrAppends );


        return $ret;
    }

    // 获取单个教师统计数据情况
    public function getRecordByConds($arrConds, $arrFields = array()) {

        if ( !is_array( $arrConds ) ) {
            return false;
        }

        // 添加条件
        $arrConds[ 'isDelete' ] = 0;

        if ( empty( $arrFields ) ) {
            $arrFields = explode( ',', self::ALL_FIELDS );
        }

        $ret = $this->_objDaoTeacherLessonRank->getRecordByConds( $arrConds, $arrFields );

        return $ret;
    }

}

