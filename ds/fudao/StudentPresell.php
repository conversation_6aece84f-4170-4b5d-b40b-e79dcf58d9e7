<?php
/**
 * Created by PhpStorm.
 * User: yang<PERSON><PERSON>@zuoyebang.com
 * Date: 2017/11/13
 * Time: 19:10
 * desc:用户预约信息表
 */

class Hkzb_Ds_Fudao_StudentPresell{
    //老师职责
    const STATUS_VALID  = 0; //有效
    const STATUS_INVALID = 1; //无效

    static $ALL_FIELDS = array(
        'id' ,
        'studentUid',
        'courseId',
        'status' ,
        'createTime' ,
        'updateTime' ,
        'extData',
    );
    /**
     * 添加预约数据
     * @param  array  $arrParams 老师属性
     * @return bool true/false
     */
    public function addStudentPresell($arrParams) {
        if(intval($arrParams['studentUid']) <= 0 || strlen($arrParams['courseId']) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[param: ". json_encode($arrParams). "]");
            return false;
        }

        $arrFields = array(
            'studentUid'  =>isset($arrParams['studentUid']) ? intval($arrParams['studentUid']) : 0,
            'courseId'    =>isset($arrParams['courseId']) ? intval($arrParams['courseId']) : 0,
            'status'      =>isset($arrParams['status']) ? intval($arrParams['status']) : 0,
            'createTime'  =>isset($arrParams['createTime']) ? intval($arrParams['createTime']) : time(),
            'updateTime'  =>isset($arrParams['updateTime']) ? intval($arrParams['updateTime']) : 0,
            'extData'     =>isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '[]',
        );

        $objDaoTeacher = new Hkzb_Dao_Fudao_StudentPresell();
        $ret = $objDaoTeacher->insertRecords($arrFields);

        if($ret === false){
            Bd_Log::warning("db error addStudentPresell fail");
        }

        return $ret;
    }

    /**
     * 更新用户预约信息
     * @param $id
     * @param $arrParams
     * @return bool
     */
    public function updateStudentPresell($id, $arrParams) {
        if(intval($id) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id]");
            return false;
        }

        $arrConds = array(
            'id' => intval($id),
        );

        $arrFields = array();
        $arrAllFields = self::$ALL_FIELDS;
        foreach($arrParams as $key => $value) {
            if(!in_array($key, $arrAllFields)) {
                continue;
            }

            $arrFields[$key] = $value;
        }

        //ext由ps补全，json处理在ds
        if(isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }

        //补全更新时间
        $arrFields['updateTime'] = time();

        $objDaoTeacher = new Hkzb_Dao_Fudao_StudentPresell();
        $ret = $objDaoTeacher->updateByConds($arrConds, $arrFields);

        if($ret === false){
            Bd_Log::warning("db error updateStudentPresell fail");
        }

        return $ret;
    }


    /**
     * 获取学生所有预约信息
     * @param $studentUid
     * @param array $arrFields
     * @return array|bool|false
     */
    public function getStudentPresellList($studentUid, $arrFields = array(),$offset=0,$limit=0) {
        if(intval($studentUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid]");
            return false;
        }


        if(empty($arrFields)){
            $arrFields = self::$ALL_FIELDS;
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
            'status'=>self::STATUS_VALID
        );

        $arrAppends = null;
        if($limit > 0){
            $arrAppends = array(
                "order by create_time",
                "limit  $offset,$limit"
            );
        }
        $objDaoTeacher = new Hkzb_Dao_Fudao_StudentPresell();
        $studentPresellInfo = $objDaoTeacher->getRecordByConds($arrConds, $arrFields,null,$arrAppends);
        if(false === $studentPresellInfo) {
            Bd_Log::warning("Error:[getRecordByConds], Detail:[studentUid:$studentUid]");
            return false;
        }

        return $studentPresellInfo;
    }

    public function getStudentPresellInfo($studentUid, $courseId,$arrFields = array()) {
        if(intval($studentUid) <= 0 || $courseId <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid] [courseId:$courseId]");
            return false;
        }


        if(empty($arrFields)){
            $arrFields = self::$ALL_FIELDS;
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
            'courseId' => intval($courseId),
            'status'=>self::STATUS_VALID
        );


        $objDaoTeacher = new Hkzb_Dao_Fudao_StudentPresell();
        $studentPresellInfo = $objDaoTeacher->getRecordByConds($arrConds, $arrFields);
        if(false === $studentPresellInfo) {
            Bd_Log::warning("Error:[getRecordByConds], Detail:[studentUid:$studentUid]");
            return false;
        }

        return $studentPresellInfo;
    }



}
