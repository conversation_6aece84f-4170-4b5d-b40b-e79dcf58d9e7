<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Date: 2017/11/24
 * Time: 16:10
 * 营销管理赠品表
 */
class Hkzb_Ds_Fudao_MarketManagmentFreeGoods extends Hk_Util_Category
{
    //状态-活动商品状态
    const STATUS_UNDELETED = 0;   //未删除
    const STATUS_DELETED   = 1;   //开已经
    static $STATUS_ARRAY = array(
        self::STATUS_UNDELETED     => '未删除',
        self::STATUS_DELETED       => '已经删除',
    );
    const ALL_FIELDS = 'id,activityId,freeId,freeName,freeType,price,maxNum,leftNum,status,extData';

    private $_objDaoMarketManagmentFreeGoods;
    public function __construct(){
        $this->_objDaoMarketManagmentFreeGoods = new Hkzb_Dao_Fudao_MarketManagmentFreeGoods();
    }

    /**
     * 创建营销活动赠品表
     * @param $arrFields 营销活动赠品表
     * @return bool
     */
    public function insertMarketFreeGoods($arrFields)
    {
        $arrInsert = array(
            'activityId'    => isset($arrFields['activityId']) ? intval($arrFields['activityId']) : 0,
            'freeId'        => isset($arrFields['freeId']) ? intval($arrFields['freeId']) : 0,
            'freeName'      => isset($arrFields['freeName']) ? trim($arrFields['freeName']) : '',
            'freeType'      => isset($arrFields['freeType']) ? intval($arrFields['freeType']) : 0,
            'price'         => isset($arrFields['price']) ? intval($arrFields['price']) : 0,
            'maxNum'        => isset($arrFields['maxNum']) ? intval($arrFields['maxNum']) : 0,
            'leftNum'       => isset($arrFields['leftNum']) ? intval($arrFields['leftNum']) : 0,
            'status'        => isset($arrFields['status']) ? intval($arrFields['status']) : 0,
            'extData'       => isset($arrFields['extData']) ? json_encode($arrFields['extData']) : '',
        );
        if (empty($arrInsert['activityId']) || empty($arrInsert['freeId'])) {
            Bd_Log::warning("Error:[param error]");
            return false;
        }

        $ret = $this->_objDaoMarketManagmentFreeGoods->insertRecords($arrInsert);
        return $ret;
    }
    

    /**获取数量
     * @param $arrConds
     * @return false|int
     */
    public function getMarketGoodsCntByConds($arrConds){
        $ret = $this->_objDaoMarketManagmentFreeGoods->getCntByConds($arrConds);
        return $ret;
    }

    /**
     * @param $arrConds
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getMarketGoodsListByConds($arrConds, $arrFields = array(), $offset = 0, $limit = 20){
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrAppends = array(
            "order by create_time desc",
            "limit $offset, $limit",
        );

        $ret = $this->_objDaoMarketManagmentFreeGoods->getListByConds($arrConds, $arrFields, NULL, $arrAppends);
        return $ret;
    }

    /**
     * 通过活动id获得活动信息
     * @param $activityId
     * @param array $arrFields
     * @return array|bool|false
     */
    public function getMarketGoodsByActivityId($activityId, $arrFields = array()){
        $activityId = intval($activityId);
        if($activityId <= 0){
            Bd_Log::warning("Error:[param error] Detail:[activityId:$activityId]");
            return false;
        }

        $arrConds = array(
            'activityId' => $activityId,
        );
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $ret = $this->_objDaoMarketManagmentFreeGoods->getRecordByConds($arrConds, $arrFields);
        return $ret;
    }

    /**
     * 通过赠品的id获得活动信息
     * @param $activityId
     * @param array $arrFields
     * @return array|bool|false
     */
    public function getMarketGoodsByFreeId($freeId, $arrFields = array()){
        $freeId = intval($freeId);
        if($freeId <= 0){
            Bd_Log::warning("Error:[param error] Detail:[activityId:$freeId]");
            return false;
        }
        $arrConds = array(
            'freeId' => $freeId,
        );
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $ret = $this->_objDaoMarketManagmentFreeGoods->getRecordByConds($arrConds, $arrFields);
        return $ret;
    }
    public function deletedActivityFreeGoods($activityId){
        $activityId = intval($activityId);
        if($activityId <= 0){
            Bd_Log::warning("Error:[param error], Detail:[activityId:$activityId]");
            return false;
        }
        $arrConds = array(
            'activityId' => $activityId,
        );
        $arrFields['status'] = self::STATUS_DELETED;

        $ret = $this->_objDaoMarketManagmentFreeGoods->updateByConds($arrConds, $arrFields);
        return $ret;
    }
}