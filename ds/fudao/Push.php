<?php
/***************************************************************************
 *
 * Copyright (c) 2015 zybang.com, Inc. All Rights Reserved
 * $Id$
 *
 **************************************************************************/



/**
 * @file push.php
 * <AUTHOR>
 * @date 2015/11/23 14:24:50
 * @version $Revision$
 * @brief 信令或者消息推送lib
 *        1. 支持按照uid推送
 *        2. 支持按照批量uid推送
 *        3. 支持按照房间号组播推送
 *        4. 支持按照班级号组播推送
 *        5. 消息id产生器，可以在long long time的时间内保持唯一
 *
 **/


/*******************************************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager*************************************
 *
 *
 *
 *
 *
 *
 *
 *
 *
 * 1. 信令定义请写在：Hkzb_Const_FudaoMsgSignal 中
 *
 *
 *
 * 2. 发送信令使用：Hkzb_Ds_Fudao_Messager
 *
 *
 *
 *
 *
 *
 *
 *
******************************************************************************************************************************************************/

/**
 * Class Hkzb_Ds_Fudao_Push
 * @deprecated  请使用Hkzb_Ds_Fudao_Messager发送信令，此类已经废弃，随时删除
 */
class Hkzb_Ds_Fudao_Push {
    //消息类型
    const MSG_TYPE_SEND_EXERCISE_CARD     = 31006; // 通知学生端取课间答题卡
    const MSG_TYPE_NEW_MSG                = 33001; // 有新的消息推送
    const MSG_TYPE_FORBIDDEN_TALK         = 33002; // 禁言推送
    const MSG_TYPE_FREE_TALK              = 33003; // 取消禁言推送
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    const MSG_TYPE_OPEN_SHOOT             = 33004; // 开启弹幕
    const MSG_TYPE_OPEN_ASSISTANG_SHOOT   = 33005; // 仅辅导员开启弹幕
    const MSG_TYPE_CLOSE_SHOOT            = 33006; // 关闭弹幕
    const ZHUJIANG_LESSON_BEFORE_SIGNAL = 33007; // 课前签到主讲端消息推送
    const MSG_TYPE_EVULATE                = 33100; // 课间学生给老师点赞
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    const MSG_TYPE_YESNO                  = 31008; // 是否卡
    const MSG_TYPE_CUR_LESSON_STATUS      = 31009; // 上课状态（直播，暂停）
    const MSG_TYPE_STUDENT_ATTEND         = 31010; // 提醒主讲老师学生上线
    const MSG_TYPE_PUSH_RANK              = 31011; // 推送排行榜信息
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    const MSG_TYPE_PUSH_MEDAL             = 31012; // 推送勋章信息
    const MSG_TYPE_PUSH_RECOMMEND_COURSE  = 31013; // 推送推荐课程信息
    const MSG_TYPE_PUSH_COURSE_REMIND     = 31014; // 上课提醒
    const MSG_TYPE_STUDENT_CLIENT_COMMAND = 31015; // 学生端命令消息（CDN相关）
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    const MSG_TYPE_TEACHER_CLIENT_COMMAND = 31016; // 主讲端命令消息（CDN相关）
    const MSG_TYPE_TEACHER_CLIENT_NOTICE  = 31018; // 主讲端通知消息
    const MSG_TYPE_COURSE_LESSON_RESTART  = 31017; // 课程课节重开
    const MSG_TYPE_SEND_EXERCISE_RESULT   = 31021; // 推送答题结果
    const MSG_TYPE_YESNO_RESULT           = 31019; // 推送是否卡结果
    const MSG_TYPE_PUSH_PRAISE_STUDENT    = 31020; // 推送表扬学生
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    const MSG_TYPE_PUSH_YESNO_STOP        = 31022; // 停止是否卡
    const MSG_TYPE_STOP_EXERCISE          = 31007; // 结束答题
    const MSG_TYPE_FORBIDDEN_PUSH_TEAHCER = 31023; // 辅导老师禁言推送给主讲
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    const MSG_TYPE_STUDENT_LIST_EDIT      = 31024; // 主讲端学生列表通知
    const MSG_TYPE_EXAM_START             = 31025; //开始考试
    const MSG_TYPE_EXAM_STOP              = 31026; //结束考试
    const MSG_TYPE_ADD_SCORE              = 31027; //加分
    const MSG_TYPE_SPEEDEXAM_START        = 31028; //竞速测试

    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/

    const MSG_TYPE_INCLASSSIGN_ON         = 31029; //签到开启
    const MSG_TYPE_INCLASSSIGN_OFF        = 31030; //签到结束

    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/

    const MSG_TYPE_SEND_SCORE_RED_ENVELOPE  = 31031;//课中发积分红包
    const MSG_TYPE_CLOSE_SCORE_RED_ENVELOPE = 31032;//关闭积分红包

    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/

    const MSG_TYPE_STUDENT_ENGLISH_RECORD = 31034;//===英语课中录音信令
    const MSG_TYPE_STUDENT_ENGLISH_MATCH= 31035;//===英语课中匹配题信令
    const MSG_TYPE_STUDENT_CLOSE_ENGLISH_RECORD = 31036;//===英语课中关闭录音题信令
    const MSG_TYPE_STUDENT_CLOSE_ENGLISH_MATCH = 31037;//===英语课中关闭匹配题信令
    const MSG_TYPE_STUDENT_PRAISE_ENGLISH_RECORD= 31038;//===表扬课中英语录音信令

    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/

    const MSG_TYPE_SINGLE_LIVE = 31039;//===课中单点直播信令
    const MSG_TYPE_LIVE_ENCODE_PARAMS = 31040;//直播流编码参数信令号
    const MSG_TYPE_CLASS_COME_REMIND = 31041;//===课中班级到课提醒
    const MSG_TYPE_INCLASS_HTML_CACHE = 31042;//===html5插件缓存

    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/

    // 直播互动类信令
    const SIG_APPLY_MIC                   = 15001;  // 申请连麦
    const SIG_CANCEL_MIC                  = 15002;  // 取消连麦
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    const SIG_SEND_OFFER                  = 15003;  // 发送offer
    const SIG_SEND_ANSWER                 = 15004;  // 发送answer
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    const SIG_SEND_CANDIDATE              = 15005;  // 发送ice cadidate
    const SIG_REFUSE_MIC                  = 15006;  // 拒绝连麦
    const SIG_HANGUP_MIC                  = 15007;  // 挂断
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    const SIG_START_MIC                   = 15008;  // 开启连麦功能
    const SIG_STOP_MIC                    = 15009;  // 关闭连麦功能
    const SIG_APPLY_ACCEPT                = 15010;  // 选择连麦学生
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    const SIG_ACTION_CONNECT_STUDENT      = 15011;  // 当前正在连麦的学生
    const SIG_HANGUP_BROADCAST            = 15012;  // 广播老师已经挂断连麦
    const SIG_MIC_SUCCEESS            	  = 15013;  // 广播老师已经连麦成功
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    const SIG_APPLY_MIC_PRIVILEGE         = 31033;  // 抢麦特权信息

    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/

    // Zybrtc类信令
    const SIG_RTC_PUBSUCC      = 16001; // 新流发布成功
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    const SIG_RTC_ANSWER       = 16002; // answer
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    const SIG_RTC_CANCELPUB    = 16003; // 取消流发布
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/
    const SIG_RTC_FOLLOWER_NUM = 16004; // 订阅粉丝数通知

    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/

    //IM相关
    const MSG_DATA_KEY = 'K_FUDAO_MSG_';
    const MSG_DATA_EXPIRE = 120;
    const MSG_CLEAN_TYPE_STABLE   = 1; //未读不可清理的消息
    const MSG_CLEAN_TYPE_VOLATILE = 2; //未读系统可清理的消息

    /***********************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager**************/

    // 标准返回值
    private $_ret = array(
        'err_no'  => 0,
        'err_msg' => 'success',
    );

    // 推送命令号
    const CMD_SINGLE_UID  = 10; // 单用户推送
    const CMD_BATCH_UID   = 11; // 批量用户推送
    const CMD_ROOM        = 12; // 按照房间推送给房间内所有用户
    const CMD_CLASS       = 13; // 按照班级推送给班级内所有用户
    const CMD_FETCH_MSGID = 14; // 获取消息id

    // 错误码
    const ERRNO_PARAM = -1; // 参数错误
    const ERRNO_RAL   = -2; // ral网络交互错误
    const ERRNO_SLS   = -3; // SLS处理失败


    private $_objMQSMessager; //MQS消息服务

    public function __construct(){
        $this->_objMQSMessager = new Hkzb_Service_MQSMessager(Hkzb_Service_MQSMessager::MQS_PRODUCT_FUDAO);
        Bd_Log::notice("Hkzb_Ds_Fudao_Push已经废弃，请使用Hkzb_Ds_Fudao_Messager发送信令");
    }

    /**
     * @brief 将信令或者消息内容推送给单个指定的uid
     *
     * @param [in] $fromUid  : 信令或者消息发送者uid
     * @param [in] $toUid    : 信令或者消息接收者uid
     * @param [in] $sigNo    : 信令编号或者消息类型编号，请参考信令编号规则
     * @param [in] $msgId    : 信令或者消息id，较长时间请保持唯一，请采用本lib提供的消息id产生器
     * @param [in] $msgBody  : 信令或者消息内容
     * @param [in] $roomId   : 房间ID, 即直播中的lessonId (新版长链接必填)
     * @return  返回值：
     * {
     *     "err_no" => 0, // 0表示成功，其它则表示失败
     *     "err_msg" => "success"
     * }
     *
     * @note: 对于err_no为非0的情况，应用端可以立即知道推送失败，然后进行必要的处理。
     * 但是由于推送过程是异步的，即使err_no=0，只能表明信令或者消息成功投递到发送队列，并未真正发送给对端，
     * 所以这期间仍然有推送失败的可能，但是信令服务可以通过http回调告诉应用端失败的情况，以便做必要的处理
     *
     * <AUTHOR>
     * @date 2015/11/23 14:33:09
     * @deprecated  请使用Hkzb_Ds_Fudao_Messager发送信令，此类已经废弃，随时删除
     **/

    /*******************************************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager*************************************/

    public function pushMsgByUid($fromUid, $toUid, $sigNo, $msgId, $msgBody, $roomId = -1) {

        if (empty($fromUid) || empty($toUid) || empty($sigNo) || empty($msgBody)) {
            $this->_ret['err_no']  = self::ERRNO_PARAM;
            $this->_ret['err_msg'] = "param error";
            return $this->_ret;
        }

        //由于单点直播需要，注释掉判断
        //if ($fromUid == $toUid) {
        //    $this->_ret['err_no']  = self::ERRNO_PARAM;
        //    $this->_ret['err_msg'] = "donot send msg to yourself";
        //    return $this->_ret;
        //}

        $requestPack = array();
        $this->_buildCommonField(self::CMD_SINGLE_UID, $fromUid, $sigNo, $msgId, $msgBody, $requestPack);
        $requestPack['to_uid'] = $toUid;
        if (intval($roomId) > 0) {
            $requestPack['room_id'] = intval($roomId);
        }
        return $this->_send_msg($requestPack);
    }

    /**
     * @brief 将信令或者消息内容推送给多个指定的uid
     *
     * @param [in] $fromUid    : 信令或者消息发送者uid
     * @param [in] $toArrUid   : 信令或者消息接收者uid数组
     *                           参数格式 array(
     *                               array('uid' => 1),
     *                               array('uid' => 2),
     *                               ... ...
     *                           )
     * @param [in] $sigNo      : 信令编号或者消息类型编号，请参考信令编号规则
     * @param [in] $msgId      : 信令或者消息id，在较长一段时间内请保持唯一，采用本lib提供的消息id产生器
     * @param [in] $msgBody    : 信令或者消息内容
     * @param [in] $roomId     : 房间ID, 即直播中的lessonId (新版长链接必填)
     * @return  返回值：
     * {
     *     "err_no" => 0, // 0表示成功，其它则表示失败
     *     "err_msg" => "success"
     * }
     *
     * @note: 对于err_no为非0的情况，应用端可以立即知道推送失败，然后进行必要的处理。
     * 但是由于推送过程是异步的，即使err_no=0，只能表明信令或者消息成功投递到发送队列，并未真正发送给对端，
     * 所以这期间仍然有推送失败的可能，但是信令服务可以通过http回调告诉应用端失败的情况，以便做必要的处理
     *
     * <AUTHOR>
     * @date 2015/11/23 14:33:09
     * @deprecated  请使用Hkzb_Ds_Fudao_Messager发送信令，此类已经废弃，随时删除
     **/

    /*******************************************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager*************************************/

    public function pushMsgByBatchUid($fromUid, $toArrUid, $sigNo, $msgId, $msgBody, $roomId = -1) {

        if (empty($fromUid) || empty($toArrUid) || empty($sigNo) || empty($msgBody)) {
            $this->_ret['err_no']  = self::ERRNO_PARAM;
            $this->_ret['err_msg'] = "param error";
            return $this->_ret;
        }

        $requestPack = array();
        $this->_buildCommonField(self::CMD_BATCH_UID, $fromUid, $sigNo, $msgId, $msgBody, $requestPack);
        $requestPack['to_uids'] = $toArrUid;
        if (intval($roomId) > 0) {
            $requestPack['room_id'] = intval($roomId);
        }
        return $this->_send_msg($requestPack);
    }

    /**
     * @brief 将信令或者消息内容推送给指定房间号的所有用户(推送者自己除外)
     *
     * @param [in] $fromUid   : 信令或者消息发送者uid
     * @param [in] $roomId    : 信令或者消息接收roomId
     * @param [in] $sigNo     : 信令编号或者消息类型编号，请参考信令编号规则
     * @param [in] $msgId     : 信令或者消息id，在较长一段时间内请保持唯一，请采用本lib提供的消息id产生器
     * @param [in] $msgBody   : 信令或者消息内容
     * @return  返回值：
     * {
     *     "err_no" => 0, // 0表示成功，其它则表示失败
     *     "err_msg" => "success"
     * }
     *
     * @note: 对于err_no为非0的情况，应用端可以立即知道推送失败，然后进行必要的处理。
     * 但是由于推送过程是异步的，即使err_no=0，只能表明信令或者消息成功投递到发送队列，并未真正发送给对端，
     * 所以这期间仍然有推送失败的可能，但是信令服务可以通过http回调告诉应用端失败的情况，以便做必要的处理
     *
     * <AUTHOR>
     * @date 2015/11/23 14:33:09
     * @deprecated  请使用Hkzb_Ds_Fudao_Messager发送信令，此类已经废弃，随时删除
     **/

    /*******************************************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager*************************************/

    public function pushMsgByRoomId($fromUid, $roomId, $sigNo, $msgId, $msgBody) {

        if (empty($fromUid) || empty($roomId) || empty($sigNo) || empty($msgBody)) {
            $this->_ret['err_no']  = self::ERRNO_PARAM;
            $this->_ret['err_msg'] = "param error";
            return $this->_ret;
        }

        $requestPack = array();
        $this->_buildCommonField(self::CMD_ROOM, $fromUid, $sigNo, $msgId, $msgBody, $requestPack);
        $requestPack['room_id'] = $roomId;
        return $this->_send_msg($requestPack);
    }

    /**
     * @brief 将信令或者消息内容推送给某个房间的某个班级(消息推送者自己除外)
     *
     * @param [in] $fromUid   : 信令或者消息的发送者uid
     * @param [in] $roomId    : 信令或者消息的接收roomId
     * @param [in] $classId   : 信令或者消息的接收classId
     * @param [in] $sigNo     : 信令编号或者消息类型编号，请参考信令编号规则
     * @param [in] $msgId     : 信令或者消息id，在较长一段时间内请保持唯一，请采用本lib提供的消息id产生器
     * @param [in] $msgBody   : 信令或者消息内容
     * @return  返回值：
     * {
     *     "err_no" => 0, // 0表示成功，其它则表示失败
     *     "err_msg" => "success"
     * }
     *
     * @note: 对于err_no为非0的情况，应用端可以立即知道推送失败，然后进行必要的处理。
     * 但是由于推送过程是异步的，即使err_no=0，只能表明信令或者消息成功投递到发送队列，并未真正发送给对端，
     * 所以这期间仍然有推送失败的可能，但是信令服务可以通过http回调告诉应用端失败的情况，以便做必要的处理
     *
     * <AUTHOR>
     * @date 2015/11/23 14:33:09
     * @deprecated  请使用Hkzb_Ds_Fudao_Messager发送信令，此类已经废弃，随时删除
     **/

    /*******************************************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager*************************************/

    public function pushMsgByClassId($fromUid, $roomId, $classId, $sigNo, $msgId, $msgBody) {

        if (empty($roomId) || empty($classId) || empty($sigNo) || empty($msgBody)) {
            $this->_ret['err_no']  = self::ERRNO_PARAM;
            $this->_ret['err_msg'] = "param error";
            return $this->_ret;
        }

        $requestPack = array();
        $this->_buildCommonField(self::CMD_CLASS, $fromUid, $sigNo, $msgId, $msgBody, $requestPack);
        $requestPack['room_id']  = intval($roomId);
        $requestPack['class_id'] = intval($classId);
        return $this->_send_msg($requestPack);
    }

    /**
     * @brief 消息id发生器
     *
     * @deprecated 调用MQS无需预先获得MSG_ID
     *
     * @return  array(
     *     'err_no' => 0,   // 0正常，其它异常
     *     'err_msg' => 'success',
     *     'data' => array(
     *         'msg_id' => 888
     *     )
     * )
     * <AUTHOR>
     * @date 2015/11/23 17:30:59
     **/
    public function fetchMsgid() {

        return array(
            'err_no' => 0,
            'err_msg' => 'success',
            'data' => array(
                'msg_id' => 1
            )
        );
    }

    /**
     * @deprecated  请使用Hkzb_Ds_Fudao_Messager发送信令，此类已经废弃，随时删除
     */

    /*******************************************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager*************************************/

    private function _buildCommonField($cmdNo, $fromUid, $sigNo, $msgId, $msgBody, &$pack) {
        $pack['cmdno']    = $cmdNo;
        $pack['from_uid'] = intval($fromUid);
        $pack['sig_no']   = intval($sigNo);
        $pushData = array();
        $pushData['from_uid'] = intval($fromUid);
        $pushData['sig_no']   = intval($sigNo);
        $pushData['data']     = $msgBody;
        $pack['data'] = $pushData;
    }

    /**
     * 向MQS发送消息
     *
     * @param array $requestPack 消息信息
     * @return boolean|integer 消息id
     * @deprecated  请使用Hkzb_Ds_Fudao_Messager发送信令，此类已经废弃，随时删除
     */

    /*******************************************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager*************************************/

    private function _talk_with_mqs($requestPack) {

        //协议优化兼容方案
        $toGroupId = isset($requestPack['room_id']) ? intval($requestPack['room_id']) : 0;

        if ($toGroupId <= 0) {
            $arrRequest = Saf_SmartMain::getCgi();
            $toGroupId = $arrRequest['request_param']['lessonId'];
        }
        if ($toGroupId <= 0) {
            Bd_Log::warning("double write failed, cann't get to_group_id");
            return false;
        }

        $requestPack['data']['to_lessonid'] = $toGroupId;
        $requestPack['data']['to_classid'] = intval($requestPack['class_id']);

        $msgKv = array (
            'from_uid' => isset($requestPack['from_uid']) ? $requestPack['from_uid'] : 0,
            'to_uid' => 0,
            'to_group_id' => $toGroupId,
            'msg_id' => isset($requestPack['msg_id']) ? $requestPack['msg_id'] : 0,
            'msg_type' => 0,
            'msg_content' => json_encode($requestPack['data']),
            'msg_time' => intval(microtime(true)*1000),
            'old_protocol' => 1, //旧版协议
        );


        $msgCleantype = self::MSG_CLEAN_TYPE_STABLE;
        $deliveryType = Hkzb_Service_MQSMessager::MSG_DELIVERY_TYPE_ALL;
        if (self::MSG_TYPE_NEW_MSG == $requestPack['sig_no'] || self::MSG_TYPE_STUDENT_LIST_EDIT == $requestPack['sig_no']) {
            $msgCleantype = self::MSG_CLEAN_TYPE_VOLATILE;
            $deliveryType = Hkzb_Service_MQSMessager::MSG_DELIVERY_TYPE_ONLINE;
        }

        //get uid list
        $toUids = array();
        $fromUid = intval($msgKv['from_uid']);
        if (self::CMD_SINGLE_UID == $requestPack['cmdno']) {
            //单uid
            $toUids[] = $requestPack['to_uid'];

        } else if (self::CMD_BATCH_UID == $requestPack['cmdno']) {
            //批量uid
            foreach($requestPack['to_uids'] as $row) {
                if ($fromUid > 0 && $fromUid == $row['uid']) {
                    continue;
                }
                $toUids[] = $row['uid'];
            }

        } else if (self::CMD_ROOM == $requestPack['cmdno']) {
            //发到lesson
            $objLesson = new Hkzb_Ds_Fudao_Lesson();
            $idList = $objLesson->getClassIdList($requestPack['room_id']);
            if (empty($idList)) {
                Bd_Log::warning("double write failed, get classid list failed");
                return false;
            }

            $msgId = $this->_objMQSMessager
                ->setMsgDeliveryType($deliveryType)
                ->setMsgExpireTime(self::MSG_DATA_EXPIRE)
                ->setMsgData($requestPack['data'])
                ->setMsgCleanType($msgCleantype)
                ->send();

            if(false === $msgId){
                Bd_Log::warning("get msgId failed");
                return false;
            }


            foreach($idList as $classid) {
                $tmpUids = $objLesson->getStudentUidList($requestPack['room_id'], $classid, 1);
                if (empty($tmpUids)) {
                    Bd_Log::warning("double write failed, get class uid list failed, lesson_id[" .$requestPack['room_id']. "], class_id[" .$classid. "]");
                    continue;
                }
                $toUids = array();
                foreach($tmpUids as $uid) {
                    if ($fromUid > 0 && $fromUid == $uid) {
                        continue;
                    }
                    $toUids[] = $uid;
                }
                if(empty($toUids)) {
                    continue;
                }
                $this->_objMQSMessager
                    ->setMsgId($msgId)
                    ->setToUids($toUids)
                    ->forward();
            }

            Bd_Log::addNotice('fudao_mqs_request_' . $msgId, json_encode($requestPack));
            Bd_Log::addNotice('fudao_mqs_msgkv_' . $msgId, json_encode($msgKv));

            return $msgId;

        } else if (self::CMD_CLASS == $requestPack['cmdno']) {
            //发到class
            $objLesson = new Hkzb_Ds_Fudao_Lesson();
            $tmpUids = $objLesson->getStudentUidList($requestPack['room_id'], $requestPack['class_id'], 1);
            if (empty($tmpUids)) {
                Bd_Log::warning("double write failed, get class uid list failed, lesson_id[" .$requestPack['room_id']. "], class_id[" .$requestPack['class_id']. "]");
                return false;
            }
            foreach($tmpUids as $uid) {
                if ($fromUid > 0 && $fromUid == $uid) {
                    continue;
                }
                $toUids[] = $uid;
            }

        } else {
            return false;
        }

        $msgId  = $this->_objMQSMessager
            ->setMsgDeliveryType($deliveryType)
            ->setMsgExpireTime(self::MSG_DATA_EXPIRE)
            ->setMsgData($requestPack['data'])
            ->setMsgCleanType($msgCleantype)
            ->setToUids($toUids)
            ->send();

        Bd_Log::addNotice('fudao_mqs_request_' . $msgId, json_encode($requestPack));
        Bd_Log::addNotice('fudao_mqs_msgkv_' . $msgId, json_encode($msgKv));

        return $msgId;
    }

    /**
     * 发送消息
     *
     * @param array $requestPack 消息信息
     * @return array
     * @deprecated  请使用Hkzb_Ds_Fudao_Messager发送信令，此类已经废弃，随时删除
     */

    /*******************************************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager*************************************/

    private function _send_msg($requestPack){

        $msgId = $this->_talk_with_mqs($requestPack);

        if(!$msgId){
            $this->_ret['err_no']  = self::ERRNO_RAL;
            $this->_ret['err_msg'] = 'talk with mqs error';
            return $this->_ret;
        }
        $this->_ret['data'] = array('msg_id' => $msgId);
        $requestPack['msg_id'] = $msgId;
        $requestPack['data']['msg_id'] = $msgId;
        $requestPack['data'] = json_encode($requestPack['data']);

        //写入redis缓存队列
        $this->_save_msg_cache($requestPack);

        return $this->_ret;
    }

    /**
     * 保存指定信令至redis中，用于ajax轮询
     *
     * @param $requestPack 格式化后的消息包体
     * @deprecated  请使用Hkzb_Ds_Fudao_Messager发送信令，此类已经废弃，随时删除
     */

    /*******************************************【注意】此类已经废弃，将会随时删除，请使用此类的同学使用Hkzb_Ds_Fudao_Messager*************************************/

    private function _save_msg_cache($requestPack){

        try {

            if(empty($requestPack['data'])){
                return;
            }

            if (!in_array($requestPack['cmdno'], array(self::CMD_CLASS, self::CMD_ROOM))) {
                return;
            }

            $classId = intval($requestPack['class_id']);
            $isSendToClass = $requestPack['cmdno'] == self::CMD_CLASS;

            if ($isSendToClass && $classId <= 0) {
                return;
            }

            $objLesson = new Hkzb_Ds_Fudao_Lesson();
            $classList = $objLesson->getClassIdList($requestPack['room_id']);
            if (empty($classList) || !is_array($classList) || !$classList[0]) {
                return;
            }
            $classIdInDb = intval($classList[0]);

            if ($isSendToClass && $classList[0] != $classId) {
                return;
            }

            if(self::MSG_TYPE_NEW_MSG != $requestPack['sig_no']){
                Hkzb_Ds_Fudao_ShortMessage::pushShortMessage($requestPack['room_id'], $requestPack['sig_no'], $requestPack['msg_id'], $requestPack['data']);
                return;
            }

            $msgListCacheKey = sprintf(Hkzb_Ds_Fudao_ShortMessage::MSG_LIST_CACHE_KEY, $classIdInDb);
            //$objCache = Hk_Service_RedisClient::getInstance("zhibo");
//            $objCacheV2 =  Liveservice_Util_RedisService::getZhiboInstance();
            $objCacheV2 =  Liveservice_Util_StoredService::getZhiboInstance();
            $objCacheV2->lpush($msgListCacheKey, $requestPack['data']);
            $objCacheV2->expire($msgListCacheKey, Hkzb_Ds_Fudao_ShortMessage::MSG_LIST_CACHED_EXPIRE);
            $listLength = $objCacheV2->llen($msgListCacheKey);

            if($listLength > Hkzb_Ds_Fudao_ShortMessage::MSG_LIST_CACHE_MAX_LEN){
                $objCacheV2->rpop($msgListCacheKey);
            }

        }catch (Exception $ex){

        }
    }
}
