<?php
/**
 * @file OperateActivity.php.
 * <AUTHOR>
 * @date: 2017/8/6
 * @brief: 运营活动配置
 */

class Hkzb_Ds_Fudao_OperateActivity extends Hk_Util_Category
{
    //课程类型
    const TYPE_ALL     = 20;  //全部
    const TYPE_PRIVATE = 0;
    const TYPE_PUBLIC  = 1;
    const TYPE_PRIVATE_LONG  = 2;
    const TYPE_PARENT_COURSE = 3;
    static $COURSE_TYPE_ARRAY = array(
        self::TYPE_ALL     => '全部',
        self::TYPE_PRIVATE => '专题课',
        self::TYPE_PUBLIC  => '公开课',
        self::TYPE_PRIVATE_LONG  => '班课',
        self::TYPE_PARENT_COURSE => '家长课',
    );

    //状态
    const STATUS_TO_ONLINE = 0;   //待上线
    const STATUS_ONLINE    = 1;   //上线
    const STATUS_OFFLINE   = 2;   //下线
    static $STATUS_ARRAY = array(
        self::STATUS_TO_ONLINE => '待上线',
        self::STATUS_ONLINE    => '已上线',
        self::STATUS_OFFLINE   => '已下线',
    );

    //使用类型
    const USE_TYPE_TO_SPEAKER = 1;   //用在主讲端
    const USE_TYPE_TO_APP     = 2;   //用在app
    static $USE_TYPE_ARRAY = array(
        self::USE_TYPE_TO_SPEAKER => '主讲端',
        self::USE_TYPE_TO_APP     => 'app',
    );

    const ALL_GRADE  = 0; //全部年级
    const ALL_COURSE = 0; //全部学科
    /**
     * 年级对应所在的二进制位（从右往左）
     * 如果Hk_Util_Category类年级对应的id值改了这里也要对应改
     */
    static public $GRADEMAPBIT = array(
        11 => 1,  //一年级，对应数值 2^(0) = 1
        12 => 2,  //二年级，对应数值 2^(2-1) = 2
        13 => 3,  //三年级，对应数值 2^(3-1) = 4
        14 => 4,  //四年级，对应数值 2^(4-1) = 8
        15 => 5,  //五年级，对应数值 2^(5-1) = 16
        16 => 6,  //六年级，对应数值 2^(6-1) = 32
        2  => 7,  //初一   对应数值 2^(7-1) = 64
        3  => 8,  //初二   对应数值 2^(8-1) = 128
        4  => 9,  //初三   对应数值 2^(9-1) = 256
        5  => 10, //高一   对应数值 2^(10-1) = 512
        6  => 11, //高二   对应数值 2^(11-1) = 1024
        7  => 12, //高三   对应数值 2^(12-1) = 2048
    );

    static public $FACULTYMAP = array(
        0  => 4095,  //全部 二进制 111111111111 的十进制值，
        1  => 63,    //小学 二进制 000000111111 的十进制值
        20 => 448,   //初中 二进制 000111000000 的十进制值
        30 => 3584,  //高中 二进制 111000000000 的十进制值
    );

    /**
     * 学科对应所在的二进制位（从右往左）
     * 如果Hk_Util_Category类学科对应的id值改了这里也要对应改
     */
    static public $COURSEMAPBIT = array (
        1 => 1,  // 语文                对应数值 2^(1-1) = 1
        2 => 2,  // 数学                对应数值 2^(2-1) = 2
        3 => 3,  // 英语                对应数值 2^(3-1) = 4
        4 => 4,  // 物理                对应数值 2^(4-1) = 8
        5 => 5,  // 化学                对应数值 2^(5-1) = 16
        6 => 6,  // 生物                对应数值 2^(6-1) = 32
        7 => 7,  // 政治                对应数值 2^(7-1) = 64
        8 => 8,  // 历史                对应数值 2^(8-1) = 128
        9 => 9,  // 地理                对应数值 2^(9-1) = 256
        10=> 10, // 兴趣课   直播课使用   对应数值 2^(10-1) = 512
        11=> 11, // 思想品德 直播课使用   对应数值 2^(11-1) = 1024
        12=> 12, // 讲座    直播课使用    对应数值 2^(12-1) = 2048
    );

    //学部关联到所有年级
    static $XBMAPGRADE = array(
        0   => array(11,12,13,14,15,16,2,3,4,5,6,7),  //全部
        1   => array(11,12,13,14,15,16),              //小学
        20  => array(2,3,4),                       //初中
        30  => array(5,6,7),                       //高中  答疑、直播
        50  => array(5,6,7),                       //高中  题库
        11  => array(11),
        12  => array(12),
        13  => array(13),
        14  => array(14),
        15  => array(15),
        16  => array(16),
        2   => array(2),
        3   => array(3),
        4   => array(4),
        5   => array(5),
        6   => array(6),
        7   => array(7),
    );

    //全部对应所有学科
    static public $ALLMAPCOURSE = array(
        0 => array(1,2,3,4,5,6,7,8,9,10,11,12),
    );


    const ALL_FIELDS = 'activityId,activityName,courseType,grade,subject,startTime,stopTime,createTime,updateTime,status,useType,operatorUid,operatorName,deleted,extData';

    private $_objDaoOperateActivity;
    public function __construct(){
        $this->_objDaoOperateActivity = new Hkzb_Dao_Fudao_OperateActivity();
    }

    /**
     * 创建运营活动
     * @param $arrFields 运营活动属性
     * @return bool
     */
    public function insertActivity($arrFields)
    {
        $arrInsert = array(
            'activityName' => isset($arrFields['activityName']) ? trim($arrFields['activityName']) : '',
            'courseType'   => isset($arrFields['courseType']) ? intval($arrFields['courseType']) : 0,
            'grade'        => isset($arrFields['grade']) ? trim($arrFields['grade']) : '',
            'subject'      => isset($arrFields['subject']) ? trim($arrFields['subject']) : '',
            'startTime'    => isset($arrFields['startTime']) ? intval($arrFields['startTime']) : 0,
            'stopTime'     => isset($arrFields['stopTime']) ? intval($arrFields['stopTime']) : 0,
            'createTime'   => time(),
            'updateTime'   => time(),
            'status'       => isset($arrFields['status']) ? intval($arrFields['status']) : self::STATUS_TO_ONLINE,
            'useType'      => isset($arrFields['useType']) ? intval($arrFields['useType']) : 0,
            'operatorUid'  => isset($arrFields['operatorUid']) ? intval($arrFields['operatorUid']) : 0,
            'operatorName' => isset($arrFields['operatorName']) ? trim($arrFields['operatorName']) : '',
            'deleted'      => 0,
            'extData'      => isset($arrFields['extData']) ? json_encode($arrFields['extData']) : '',
        );
        if (empty($arrInsert['activityName']) || $arrInsert['courseType'] < 0
            || empty($arrInsert['grade']) || empty($arrInsert['subject'])
        ) {
            Bd_Log::warning("Error:[param error]");
            return false;
        }

        $ret = $this->_objDaoOperateActivity->insertRecords($arrInsert);
        return $ret;
    }

    /**
     * 更新活动
     * @param $activityId 活动id
     * @param $arrParams  活动属性
     * @return bool
     */
    public function updateActivity($activityId, $arrParams){
        $activityId = intval($activityId);
        if($activityId <= 0 || empty($arrParams)){
            Bd_Log::warning("Error:[param error], Detail:[activityId:$activityId]");
            return false;
        }

        $arrConds = array(
            'activityId' => $activityId,
        );

        $arrFields = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach ($arrParams as $key => $value) {
            if (in_array($key, $arrAllFields)) {
                $arrFields[$key] = $value;
            }
        }

        //ext由ps补全，json处理在ds
        if (isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }
        //补全更新时间
        $arrFields['updateTime'] = time();

        $ret = $this->_objDaoOperateActivity->updateByConds($arrConds, $arrFields);
        return $ret;
    }

    /**获取数量
     * @param $arrConds
     * @return false|int
     */
    public function getActivityCntByConds($arrConds){
        $ret = $this->_objDaoOperateActivity->getCntByConds($arrConds);
        return $ret;
    }

    /**
     * @param $arrConds
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getActivityListByConds($arrConds, $arrFields = array(), $offset = 0, $limit = 20){
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrAppends = array(
            "order by create_time desc",
            "limit $offset, $limit",
        );

        $ret = $this->_objDaoOperateActivity->getListByConds($arrConds, $arrFields, NULL, $arrAppends);
        return $ret;
    }

    /**
     * 通过活动id获得活动信息
     * @param $activityId
     * @param array $arrFields
     * @return array|bool|false
     */
    public function getActivityById($activityId, $arrFields = array()){
        $activityId = intval($activityId);
        if($activityId <= 0){
            Bd_Log::warning("Error:[param error] Detail:[activityId:$activityId]");
            return false;
        }

        $arrConds = array(
            'activityId' => $activityId,
        );
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $ret = $this->_objDaoOperateActivity->getRecordByConds($arrConds, $arrFields);
        return $ret;
    }

    /**
     * 根据年级、学科、其他查询条件获得活动信息
     *
     * @param $intGrade    只能一个年级
     * @param $intSubject  只能一个学科
     * @param array $arrConds
     * @param array $arrFields
     * @return array|false
     */
    public function getActivityByGradeSubjectConds($intGrade, $intSubject, $arrConds = array(), $arrFields = array()){
        $intGrade   = intval($intGrade);
        $intGrade   = pow(2, (self::$GRADEMAPBIT[$intGrade]) - 1);
        $intSubject = intval($intSubject);
        $intSubject = pow( 2, (self::$COURSEMAPBIT[$intSubject] - 1) );

        $arrConds[] = '(grade & ' . $intGrade . ' > 0)';
        $arrConds[] = '(subject & ' . $intSubject . ' > 0)';

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $ret = $this->_objDaoOperateActivity->getListByConds($arrConds,$arrFields);
        return $ret;
    }

    /**
     * 根据学部、学科、其他查询条件获得活动信息
     * @param $intGrade   //只能一个学部
     * @param $intSubject //只能一个学科
     * @param array $arrConds
     */
    public function getActivityByFacultySubjectConds($intFaculty, $intSubject, $arrConds = array(), $arrFields = array()){
        $intFaculty   = intval($intFaculty);
        $intFaculty = self::$FACULTYMAP[$intFaculty];
        $intSubject = intval($intSubject);
        $intSubject = pow( 2, (self::$COURSEMAPBIT[$intSubject] - 1) );

        $arrConds[] = '(grade & ' . $intFaculty . ' > 0)';
        $arrConds[] = '(subject & ' . $intSubject . ' > 0)';

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $ret = $this->_objDaoOperateActivity->getListByConds($arrConds,$arrFields);
        return $ret;
    }

    /**将带有学部转换为年级
     * array(11,20,30) --> array(11,2,3,4,5,6,7)
     *
     * @param $intArrGrade  array(1,20,30)
     * @return array|bool   array(1,7,8,9,10,11,12)
     */
    public function getGradeValue($intArrGrade){
        if(empty($intArrGrade) || !is_array($intArrGrade)){
            Bd_Log::warning("Error:[param error]");
            return false;
        }

        $intGradeList= array();
        foreach ($intArrGrade as $item){
            $item = intval($item);
            $intGradeList = array_merge($intGradeList, self::$XBMAPGRADE[$item]);
        }

        if(empty($intGradeList)){
            Bd_Log::warning("Error:[param error] Detail[intArrGrade: ".json_encode($intArrGrade)."]");
            return false;
        }

        return $intGradeList;
    }

    /**将学科转换为所有学科
     * 例：array(0) --> array(1,2,3,4,5,6,7,8,9,10,11,12,13,14)
     * @param $intArrSubject
     * @return array|bool
     */
    public function getSubjectValue($intArrSubject){
        if(empty($intArrSubject) || !is_array($intArrSubject)){
            Bd_Log::warning("Error:[param error]");
            return false;
        }

        if($intArrSubject[0] == self::ALL_COURSE){
            $intSubjectList = self::$ALLMAPCOURSE[self::ALL_COURSE];
        }else{
            $intSubjectList = $intArrSubject;
        }

        if(empty($intSubjectList)){
            Bd_Log::warning("Error:[param error] Detail[arrSubject: ".json_encode($intArrSubject)."]");
            return false;
        }

        return $intSubjectList;
    }

    /**
     * 将多个学科的数组转换为十进制数
     * 例：array(1,3,5) --> 21
     * 转换过程：
     *
     * @param $arrSubjectIds
     * @return bool|int|number
     */
    public function arrSubjectIdsToIntSubject($arrSubjectIds){
        if(empty($arrSubjectIds) || !is_array($arrSubjectIds)){
            Bd_Log::warning("Error:[param error] Detail:[arrGradeIds: ".json_encode($arrSubjectIds)."]");
            return false;
        }

        $intSubject = 0;
        foreach ($arrSubjectIds as $subject){
            $intSubject += pow( 2, (self::$COURSEMAPBIT[$subject] - 1) ) ;
        }

        return $intSubject;
    }

    /**
     * 将多个年级的数组转换为十进制数
     * 例：array(11,13,15) --> 21
     * 转换过程：
     *
     * @param $arrGradeIds
     * @return bool|int|number
     */
    public function arrGradeIdsToIntGrade($arrGradeIds){
        if(empty($arrGradeIds) || !is_array($arrGradeIds)){
            Bd_Log::warning("Error:[param error] Detail:[arrGradeIds: ".json_encode($arrGradeIds)."]");
            return false;
        }

        $intGrade = 0;
        foreach ($arrGradeIds as $grade){
            $intGrade += pow( 2, (self::$GRADEMAPBIT[$grade] - 1) );
        }

        return $intGrade;
    }

    /**
     * 将多个年级的十进制数转化为数组：
     * 例： 21 --> array(11,13,15)
     * 转换过程：十进制21 转为二进制 10101 其中第1,3,5位为1，则根据 $GRADEMAPBIT 中的定义得出对应的年级为 array(11,13,15)
     *
     * @param $intGrade 21
     * @return array    array(11,13,15)
     */
    public function intGradeToArrGradeIds($intGrade){
        if($intGrade <= 0){
            Bd_Log::warning("Error:[param error] Detail:[grade: $intGrade]");
            return false;
        }

        //由十进制数，获得 1 所在的位
        $arrBitGrade = array();
        $i= 1;
        while($intGrade > 0){
            if($intGrade & 1){
                $arrBitGrade[] = $i;
            }
            $intGrade >>= 1;
            $i++;
        }

        //由 1 所在的位得到年级
        $arrGradeIds = array();
        foreach (self::$GRADEMAPBIT as $key => $value){
            foreach ($arrBitGrade as $item){
                if($value == $item){
                    $arrGradeIds[] = $key;
                }
            }
        }

        return $arrGradeIds;
    }

    /**
     * 将多个学科的十进制数转化为数组：
     * 例： 21 --> array(1,3,5)
     * 转换过程：十进制21 转为二进制 10101 其中第1,3,5位为1，则根据 $COURSEMAPBIT 中的定义得出对应的学科为 array(1,3,5)
     *
     * @param $intSubject  21
     * @return array|bool  array(1,3,5)
     */
    public function intSubjectToArrSubjectIds($intSubject){
        if($intSubject <= 0){
            Bd_Log::warning("Error:[param error] Detail:[grade: $intSubject]");
            return false;
        }

        //由十进制数，获得 1 所在的位
        $arrBitSubject = array();
        $i= 1;
        while($intSubject > 0){
            if($intSubject & 1){
                $arrBitSubject[] = $i;
            }
            $intSubject >>= 1;
            $i++;
        }

        //由 1 所在的位得到年级
        $arrSubjectIds = array();
        foreach (self::$COURSEMAPBIT as $key => $value){
            foreach ($arrBitSubject as $item){
                if($value == $item){
                    $arrSubjectIds[] = $key;
                }
            }
        }

        return $arrSubjectIds;
    }

    // 获取某一个课程当前可用的运营活动
    // 新建活动接口已经确保了一个课程在某一个时间点内最多只有一个运营活动
    public function getNowActivityInfo($arrInput) {
        $intGrade = intval($arrInput['grade']);
        $intSubject = intval($arrInput['subject']);
        $intType = intval($arrInput['type']);

        $intBitGrade = $this->intGradeToDecimalGrade($intGrade);
        $intBitSubject = $this->intSubjectToDecimalGrade($intSubject);

        $arrConds = array(
            'start_time <= ' . time(),
            'stop_time > ' . time(),
            'status' => self::STATUS_ONLINE,
            'course_type in (' . self::TYPE_ALL . ',' . $intType . ')',
            'grade & '.$intBitGrade . " > 0",
            'subject & '.$intBitSubject . " > 0",
        );

        $arrFields = explode(',', self::ALL_FIELDS);

        $ret = $this->_objDaoOperateActivity->getRecordByConds($arrConds, $arrFields );

        $arrOutput = array();

        if ($ret) {
            $arrOutput =  array(
                'activityList' => $ret['extData']['imgUrl'],
            );

        }else {
            $arrOutput = false;
        }

        return $arrOutput;
    }


    /**
     * 给定一个年级或者学部，通过二进制位对应将其转换为十进制
     * 例为学部的时候：1  --> 62
     * 过程： 1 --> array(1) --> array(12,13,14,15,16) --> 62
     * 为年级的时候：  13 --> 4
     *
     * @param $intGrade  只能为一个
     * @return bool|int|number
     */
    public function intGradeToDecimalGrade($intGrade){
        $intGrade = intval($intGrade);
        $arrGradeIds = $this->getGradeValue(array($intGrade));
        $decimalGrade = $this->arrGradeIdsToIntGrade($arrGradeIds);
        return $decimalGrade;
    }

    /**给定一个学科，通过二进制位对应将其转换为十进制
     * 例 4  --> 8
     * 全部学科时：
     *   0 --> array(0) --> array(1,2,3,4,5,6,7,8,9,10,11,12) -->4095
     * @param $intSubject
     * @return bool|int|number
     */
    public function intSubjectToDecimalGrade($intSubject){
        $intSubject = intval($intSubject);
        $arrSubject = $this->getSubjectValue(array($intSubject));
        $decimalSubject = $this->arrSubjectIdsToIntSubject($arrSubject);
        return $decimalSubject;
    }
}