<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file TeacherAppMessage.php
 * <AUTHOR>
 * @date 2017-5-17
 * @brief 一课教师端消息通知
 *  
 **/

class Hkzb_Ds_Fudao_TeacherAppMessage {

    const ALL_FIELDS = 'messageId,title,description,content,fullTime,partTime,zhuanTime,teacherUidsCheck,createTime,operatorUid,operator,extData,msgType,teacherUid,deleted';
    private $objDaoTeacherAppMessage;

    // 消息类型
    const MSG_SYSTEM            = 1; // 系统通知
    const MSG_ON_CLASS          = 2; // 上课通知
    const MSG_NOT_UP_LECTURE    = 3; // 讲义上传提醒
    const MSG_LECTURE_NOT_PASS  = 4; // 讲义审核不通过
    const MSG_COURSE_CHECK      = 5; // 质检通知
    const MSG_RESERVE_LIVE_ROOM = 6; // 预定直播间
    static $MSG_TYPE_ARRAY = array(
        self::MSG_SYSTEM              => '系统通知',
        self::MSG_ON_CLASS            => '上课通知',
        self::MSG_NOT_UP_LECTURE      => '讲义上传提醒',
        self::MSG_LECTURE_NOT_PASS    => '讲义审核不通过',
        self::MSG_COURSE_CHECK        => '质检通知',
        self::MSG_RESERVE_LIVE_ROOM   => '预定直播间',
    );

    /**
     * 构造函数
     *
     */
    public function __construct() {
        $this->objDaoTeacherAppMessage = new Hkzb_Dao_Fudao_TeacherAppMessage();
    }

    /**
     * 新增消息
     *
     * @param  array  $arrParams 消息属性
     * @return bool true/false
     */
    public function teacherAppMessageAdd($arrParams) {
        if(strlen($arrParams['title']) <= 0 || strlen($arrParams['description']) <= 0 || strlen($arrParams['content']) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[param: ". json_encode($arrParams). "]");
            return false;
        }

        $arrFields = array(
            'title'            => strval($arrParams['title']),
            'description'      => strval($arrParams['description']),
            'content'          => strval($arrParams['content']),
            'fullTime'         => isset($arrParams['fullTime']) ? intval($arrParams['fullTime']): 0,
            'partTime'         => isset($arrParams['partTime']) ? intval($arrParams['partTime']): 0,
            'zhuanTime'        => isset($arrParams['zhuanTime']) ? intval($arrParams['zhuanTime']): 0,
            'teacherUidsCheck' => isset($arrParams['teacherUidsCheck']) ? intval($arrParams['teacherUidsCheck']): 0,
            'createTime'       => time(),
            'operatorUid'      => isset($arrParams['operatorUid']) ? intval($arrParams['operatorUid']) : 0,
            'operator'         => isset($arrParams['operator']) ? strval($arrParams['operator']) : '',
            'extData'          => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
            'msgType'          => isset($arrParams['msgType']) ? intval($arrParams['msgType']) : 1,
            'teacherUid'       => isset($arrParams['teacherUid']) ? intval($arrParams['teacherUid']) : 0,
        );
        $ret =$this->objDaoTeacherAppMessage->insertRecords($arrFields);
        if($ret){
            $ret = $this->objDaoTeacherAppMessage->getInsertId();
        }
           
        return $ret;
    }
    
    /**
     * 获取消息详情
     *
     * @param  int  $messageId  消息id
     * @param  mix  $arrFields  指定属性
     * @return mix
     */
    public function getTeacherAppMessageInfo($messageId, $arrFields = array()) {
        if(intval($messageId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[messageId:$messageId]");
            return false;
        }

        $arrFields = explode(',', self::ALL_FIELDS);
        $arrConds = array(
            'messageId' => intval($messageId),
        );

        $messageInfo =$this->objDaoTeacherAppMessage->getRecordByConds($arrConds, $arrFields);
        if(false === $messageInfo) {
            Bd_Log::warning("Error:[getRecordByConds], Detail:[messageId:$messageId]");
            return false;
        }
        return $messageInfo;
    }

    /**
     * @param $arrConds
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getMessageListByConds($arrConds, $arrFields = array(), $offset = 0, $limit = 20) {
        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrAppends = array(
            "order by create_time desc",
            "limit $offset, $limit",
        );

        $ret =$this->objDaoTeacherAppMessage->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

   /**
     * 获取数量
     * @param $arrConds
     * @return false|int
     */
    public function getMessageCntByConds($arrConds) {

        $ret = $this->objDaoTeacherAppMessage->getCntByConds($arrConds);
        return $ret;
    }

    /**
     * 消息状态变更
     * @param $msgId
     * @return bool
     */
    public function updateMessageHasRead($msgId) {
        if (intval($msgId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[msgId:$msgId]");
            return false;
        }

        $arrConds = array(
            'messageId' => intval($msgId),
        );
        $messageInfo = $this->getTeacherAppMessageInfo($msgId);
        if (empty($messageInfo)) {
            Bd_Log::warning("Error:[message null, Detail:[msgId:$msgId]");
        }
        $messageInfo['extData']['hasRead'] = 1;
        $messageInfo['extData']['readTime'] = time();

        if (isset($messageInfo['extData'])) {
            $arrFields['extData'] = json_encode($messageInfo['extData']);
        }
        $ret = $this->objDaoTeacherAppMessage->updateByConds($arrConds, $arrFields);
        return $ret;
    }



    /**
     * @param $lessonId int
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getMessageListBylessonId($lessonId,$arrFields = array(), $offset = 0, $limit = 20) {
        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'msg_type' => self::MSG_LECTURE_NOT_PASS,
        );

        $lessonId = '"lessonId":'.$lessonId;
        $arrConds[] = "ext_data like '%{$lessonId}%'";
        $arrAppends = array(
            "order by create_time desc",
            "limit $offset, $limit",
        );

        $ret =$this->objDaoTeacherAppMessage->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

}
