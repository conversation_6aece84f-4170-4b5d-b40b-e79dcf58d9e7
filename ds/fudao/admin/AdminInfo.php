<?php
/**
 * @file AdminInfo.php.
 * <AUTHOR>
 * @date: 2017/11/17
 */
class Hkzb_Ds_Fudao_Admin_AdminInfo
{
    const STATUS_OK      = 0; //在职
    const STATUS_DELETED = 1; //离职
    static $ARRAY_STATUS = array(
        self::STATUS_OK     => '在职',
        self::STATUS_DELETED=> '离职',
    );

    const ALL_FIELDS = "id,uid,uname,uRole,nickName,uMail,phone,rbacId,gradeId,courseId,deleted,ext,status,createTime,updateTime,operatorUid,operator";

    private $_objDaoAdminInfo;

    public function __construct()
    {
        $this->_objDaoAdminInfo = new Hkzb_Dao_Fudao_Admin_AdminInfo();
    }

    /**新增
     * @param $arrParams
     * @return bool
     */
    public function insertAdminInfo($arrParams){
        if (intval($arrParams['uid']) <= 0){
            Bd_Log::warning("Error:[param error], Detail:[param: " . json_encode($arrParams) . "]");
            return false;
        }

        $arrFields = array(
            'uid'         => intval($arrParams['uid']),
            'uname'       => isset($arrParams['uname']) ? strval($arrParams['uname']) : '',
            'uRole'       => isset($arrParams['uRole']) ? intval($arrParams['uRole']) : 0,
            'nickName'    => isset($arrParams['nickName']) ? strval($arrParams['nickName']) : '',
            'uMail'       => isset($arrParams['uMail']) ? strval($arrParams['uMail']) : '',
            'phone'       => isset($arrParams['phone']) ? strval($arrParams['phone']) : '',
            'rbacId'      => isset($arrParams['rbacId']) ? json_encode($arrParams['rbacId']) : '[]',
            'gradeId'     => isset($arrParams['gradeId']) ? intval($arrParams['gradeId']) : 0,
            'courseId'    => isset($arrParams['courseId']) ? intval($arrParams['courseId']) : 0,
            'deleted'     => isset($arrParams['deleted']) ? intval($arrParams['deleted']) : 0,
            'ext'         => isset($arrParams['ext']) ? json_encode($arrParams['ext']) : '[]',
            'status'      => isset($arrParams['status']) ? intval($arrParams['status']) : 0,
            'createTime'  => time(),
            'updateTime'  => time(),
            'operatorUid' => isset($arrParams['operatorUid']) ? intval($arrParams['operatorUid']) : 0,
            'operator'    => isset($arrParams['operator']) ? intval($arrParams['operator']) : '',
        );

        $ret = $this->_objDaoAdminInfo->insertRecords($arrFields);

        return $ret;
    }

    /**更新
     * @param $adminId
     * @param $arrParams
     * @return bool
     */
    public function updateAdminInfo($uid, $arrParams){
        if (intval($uid) < 0 || empty($arrParams)){
            Bd_Log::warning("Error:[param error], Detail:[uid: $uid params: " . json_encode($arrParams) . "]");
            return false;
        }
        $arrCond = array(
            'uid' => intval($uid),
        );

        $arrFields = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach ($arrParams as $key=>$val){
            if(in_array($key, $arrAllFields)){
                $arrFields[$key] = $val;
            }
        }

        if (isset($arrFields['ext'])){
            $arrFields['ext'] = json_encode($arrFields['ext']);
        }
        if (isset($arrFields['rbacId'])){
            $arrFields['rbacId'] = json_encode($arrFields['rbacId']);
        }
        //补全更新时间
        $arrFields['updateTime'] = time();

        $ret = $this->_objDaoAdminInfo->updateByConds($arrCond, $arrFields);
        return $ret;
    }

    /**
     * @param $uid  、用户id
     * @param array $arrFields
     * @return array|bool|false
     */
    public function getAdminInfoByUid($uid, $arrFields = array()){
        if (intval($uid) <= 0){
            Bd_Log::warning("Error:[param error], Detail:[uid: $uid ]");
            return false;
        }

        $arrCond = array(
            'uid'     => intval($uid),
            'deleted' => 0,
        );

        if (empty($arrFields)){
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $ret = $this->_objDaoAdminInfo->getRecordByConds($arrCond, $arrFields, NULL, NULL);
        return $ret;
    }

    /**
     * @param $id
     * @param array $arrFields
     * @return array|bool|false
     */
    public function getAdminInfoById($id, $arrFields = array()){
        if (intval($id) <= 0){
            Bd_Log::warning("Error:[param error], Detail:[id: $id ]");
            return false;
        }

        $arrCond = array(
            'id'     => intval($id),
        );

        if (empty($arrFields)){
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $ret = $this->_objDaoAdminInfo->getRecordByConds($arrCond, $arrFields, NULL, NULL);
        return $ret;
    }

    /**
     * @param $arrConds
     * @return false|int
     */
    public function getAdminInfoCntByConds($arrConds){

        $ret = $this->_objDaoAdminInfo->getCntByConds($arrConds);
        return $ret;

    }

    /**
     * @param $arrConds
     * @param array $arrFields
     * @param $offset
     * @param $limit
     * @return array|false
     */
    public function getAdminInfoListByConds($arrConds, $arrFields = array(), $offset, $limit){
        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrAppends = array(
            "order by update_time desc",
            "limit $offset, $limit",
        );

        $ret = $this->_objDaoAdminInfo->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }
}