<?php

/**
 * @file   InvitedUser.php
 * <AUTHOR>
 * @date   2018-03-07
 * @brief  老带新活动邀请
 **/
class Hkzb_Ds_Invited_InvitedUser
{

    //状态-活动商品状态
    const STATUS_UNDELETED = 0;   //未删除
    const STATUS_DELETED   = 1;   //已删除
    const STATUS_HASRECEIVE = 2;
    static $STATUS_ARRAY = array(
        self::STATUS_UNDELETED     => '未删除',
        self::STATUS_DELETED       => '已经删除',
        self::STATUS_HASRECEIVE    => '已领取',
    );

    private $_objDaoInvitedUser;


    public static $allFields = array(
        'uid',
        'award',
        'status',
        'inviteCode',
        'inviteId',
        'activityId',
        'courseId',
        'courseName',
        'createTime',
        'updateTime',
        'extData'
    );

    public function __construct()
    {
        $this->_objDaoInvitedUser = new Hkzb_Dao_Invited_InvitedUser();
    }

    /**
     * 新增
     *
     * @param  array $arrParams 属性
     * @return bool true/false
     */
    public function addUser($arrParams)
    {
        if (empty($arrParams['uid'])) {
            Bd_Log::warning("Error:[param error], Detail:[" . json_encode($arrParams) . "]");
            return false;
        }
        $curTime = time();
        $arrFields = array(
            'uid'        => $arrParams['uid'],
            'status'     => 0,
            'activityId' => isset($arrParams['activityId']) ? $arrParams['activityId'] : 0,
            'inviteCode' => isset($arrParams['inviteCode']) ? $arrParams['inviteCode'] : 0, //某一个优惠券的Id
            'inviteId'   => isset($arrParams['inviteId']) ? $arrParams['inviteId'] : 0,
            'courseId'   => isset($arrParams['courseId']) ? $arrParams['courseId'] : 0,
            'courseName' => isset($arrParams['courseName']) ? $arrParams['courseName'] : "",
            'createTime' => $curTime,
            'updateTime' => $curTime,
            'extData'    => isset($arrParams['extData']) ?$arrParams['extData'] : '',
        );
        $ret = $this->_objDaoInvitedUser->insertRecords($arrFields, null, $arrFields);
        if (!$ret) {
            Bd_Log::warning("Error[dbError] Detail[db insert failed]");

            return false;
        }
        return $ret;
    }

    /**
     * 更新某一个用户的信息
     *
     * @param  int $uid
     * @param array $data
     * @return  bool
     */
    public function updateInfoByUid($uid, $data)
    {
        $uid = intval($uid);
        if ($uid <= 0 || empty($data)) {
            Bd_Log::warning("Error:[param error], Detail:[$uid , " . json_encode($data) . "]");

            return false;
        }

        $arrConds           = array(
            'uid' => $uid,
        );
        $data['updateTime'] = time();

        if (false === $this->_objDaoInvitedUser->updateByConds($arrConds, $data)) {
            Bd_Log::warning("Error:[update error], Detail:[$uid , " . json_encode($data) . "]");

            return false;
        }

        return true;
    }

    /**
 * 获取老用户带新用户数据表
 *
 * @param  int $uid
 * @param array $data
 * @return  bool
 */
    public function getUndeleteInvitedInfo()
    {
        $arrConds           = array(
            'status' => array(self::STATUS_DELETED,'<>'),
        );
        $arrFields   = self::$allFields;

        $ret = $this->_objDaoInvitedUser->getListByConds($arrConds, $arrFields);
        return $ret;
    }

    /**
     * 获取邀请记录（包括邀请过的和邀请失败的）
     *
     * @param  int $uid
     * @param array $data
     * @return  bool
     */
    public function getAllInvitedInfo($uid,$arrFields=array())
    {
        $arrConds      = array(
            'inviteId'     => $uid,
            'status'       => array(self::STATUS_UNDELETED,'<>'),
        );

        if(empty($arrFields)){
            $arrFields   = self::$allFields;
        }

        $ret = $this->_objDaoInvitedUser->getListByConds($arrConds, $arrFields);
        return $ret;
    }


    /**
     *  获取新用户的数据信息
     *
     * @param  int $uid
     * @return  array
     */
    public function getInfoByUidFromDb($uid)
    {
        $ret = array();
        if (empty($uid)) {
            Bd_Log::warning("Error:[param error], Detail:[$uid]");

            return $ret;
        }

        $arrFields   =  self::$allFields;
        $arrConds    = array(
            'uid' => $uid,
        );
        $objDatiUser = new Hkzb_Dao_Invited_InvitedUser();
        $ret         = $objDatiUser->getRecordByConds($arrConds, $arrFields);

        return $ret;
    }

    /**
     *  根据老用户uid，获取老用户
     *
     * @param  int $uid
     * @return  array
     */
    public function getInfoByInvitedFromDb($invited)
    {
        $ret = array();
        if (empty($invited)) {
            Bd_Log::warning("Error:[param error], Detail:[$invited]");

            return $ret;
        }

        $arrFields   =  self::$allFields;
        $arrConds    = array(
            'inviteId' => $invited,
            'status'  => array(self::STATUS_DELETED,'<>'),
        );
        $arrConds['courseId'] = array(0, '>');
        
        $objInvitedUser = new Hkzb_Dao_Invited_InvitedUser();
        $ret         = $objInvitedUser->getListByConds($arrConds, $arrFields);
        return $ret;
    }


    /**
     * 提现接口
     *
     * @param integer $uid   用户id
     * @param integer $money 提现金额
     * @return boolean
     */
    public function draw($uid, $money)
    {
        $uid   = intval($uid);
        $money = intval($money);

        if ($money <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[uid:{$uid}, money:{$money}]");

            return false;
        }

        return $this->modifyUserBalance($uid, $money);
    }

    /**
     * 返还接口
     *
     * @param integer $uid   用户id
     * @param integer $money 返还金额
     * @return boolean
     */
    public function returnMoney($uid, $money)
    {
        $uid   = intval($uid);
        $money = intval($money);

        if ($money <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[uid:{$uid}, money:{$money}]");

            return false;
        }

        return $this->modifyUserBalance($uid, (0 - $money));
    }

    /**
     * 修改用户余额
     *
     * @param integer $uid   用户id
     * @param integer $money 被修改的金额
     * @return bool
     */
    private function modifyUserBalance($uid, $money)
    {
        $uid   = intval($uid);
        $money = intval($money);

        if ($uid <= 0 || $money == 0) {
            Bd_Log::warning("Error:[param error], Detail:[uid:{$uid}, money:{$money}]");

            return false;
        }

        $objDsUser = new Hkzb_Ds_Invited_InvitedUser();
        $userInfo  = $objDsUser->getInfoByUidFromDb($uid);

        if (empty($userInfo)) {
            Bd_Log::warning("Error[user info not exist] Detail[uid:$uid]");

            return false;
        }

        $balance    = intval($userInfo['balance']);
        $balanceNew = $balance - intval($money);

        if (intval($balanceNew) < 0) {
            Bd_Log::warning("Error[money is not enough], Detail:[uid:{$uid}, money:{$money}]");

            return false;
        }

        $arrConds = array(
            'uid' => $uid,
        );

        $data = array(
            'balance' => intval($balanceNew),
        );

        $data['updateTime'] = time();

        $objInvitedAwardRecord = new Hkzb_Dao_Invited_InvitedAwardRecord();
        if (false === $objInvitedAwardRecord->updateByConds($arrConds, $data)) {
            Bd_Log::warning("Error:[update error], Detail:[uid:{$uid}, money:{$money}]");

            return false;
        }

        return $balanceNew;
    }


    /**
     * 开启事务
     *
     * @return bool
     */
    public function startTransaction()
    {
        return $this->_objDaoInvitedUser->startTransaction();
    }

    /**
     * 回滚
     *
     * @return bool
     */
    public function rollback()
    {
        return $this->_objDaoInvitedUser->rollback();
    }

    /**
     * 提交事务
     *
     * @return bool
     */
    public function commit()
    {
        return $this->_objDaoInvitedUser->commit();
    }


}
