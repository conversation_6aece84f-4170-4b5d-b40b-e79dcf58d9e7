<?php
/**
 * Created by Growth AutoCode.
 * User: <EMAIL>
 * Date: 2019/06/18
 * Time: 19:57
 */
class Qdlib_Ds_QudaoCostEntry
{
    const ALL_FIELDS = 'id,businessTime,settleType,type,typeName,typeShowName,channel,channelLv1,channelLv2,channelLv3,account,bookCost,payoutCost,sharedWalletCost,operator,deleted,createTime,updateTime,channelLv1Id,channelLv2Id,channelLv3Id';
    const DELETE_DEL = 1;

    private $_objDaoQudaoCostEntry;

    public function __construct()
    {
        $this->_objDaoQudaoCostEntry = new Qdlib_Dao_QudaoCostEntry();
    }

    //新增
    public function addQudaoCostEntry($arrParams) {
        $arrFields = array(
//            'id'          => isset($arrParams['id']) ? intval($arrParams['id']) : 0,
            'businessTime'        => isset($arrParams['businessTime']) ? intval($arrParams['businessTime']) : 0,
            'settleType'   => isset($arrParams['settleType']) ? intval($arrParams['settleType']) : 0,
            'type'  => isset($arrParams['type']) ? intval($arrParams['type']) : 0,
            'typeName'      => isset($arrParams['typeName']) ? strval($arrParams['typeName']) : '-1',
            'typeShowName' => isset($arrParams['typeShowName']) ? strval($arrParams['typeShowName']) : '-1',
            'channel'  => isset($arrParams['channel']) ? strval($arrParams['channel']) : '',
            'channelLv1'  => isset($arrParams['channelLv1']) ? strval($arrParams['channelLv1']) : '-1',
            'channelLv2'  => isset($arrParams['channelLv2']) ? strval($arrParams['channelLv2']) : '-1',
            'channelLv3'  => isset($arrParams['channelLv3']) ? strval($arrParams['channelLv3']) : '-1',
            'account'    => isset($arrParams['account']) ? strval($arrParams['account']) : '-1',
            'bookCost'      => isset($arrParams['bookCost']) ? intval($arrParams['bookCost']) : '-1',
            'payoutCost'    => isset($arrParams['payoutCost']) ? intval($arrParams['payoutCost']) : '-1',
            'sharedWalletCost'  => isset($arrParams['sharedWalletCost']) ? intval($arrParams['sharedWalletCost']) : '-1',
            'operator' => isset($arrParams['operator']) ? strval($arrParams['operator']) : '',
            'deleted' => isset($arrParams['deleted']) ? intval($arrParams['deleted']) : 0,
            'createTime' => isset($arrParams['createTime']) ? intval($arrParams['createTime']) : 0,
            'updateTime' => isset($arrParams['updateTime']) ? intval($arrParams['updateTime']) : 0,
            'channelLv1Id'  => isset($arrParams['channelLv1Id']) ? intval($arrParams['channelLv1Id']) : '-1',
            'channelLv2Id'  => isset($arrParams['channelLv2Id']) ? intval($arrParams['channelLv2Id']) : '-1',
            'channelLv3Id'  => isset($arrParams['channelLv3Id']) ? intval($arrParams['channelLv3Id']) : '-1',
        );
        $dup = [
            'typeShowName' => isset($arrParams['typeShowName']) ? strval($arrParams['typeShowName']) : '-1',
            'bookCost'      => isset($arrParams['bookCost']) ? intval($arrParams['bookCost']) : '-1',
            'payoutCost'    => isset($arrParams['payoutCost']) ? intval($arrParams['payoutCost']) : '-1',
            'sharedWalletCost'  => isset($arrParams['sharedWalletCost']) ? intval($arrParams['sharedWalletCost']) : '-1',
            'operator' => isset($arrParams['operator']) ? strval($arrParams['operator']) : '',
            'deleted' => isset($arrParams['deleted']) ? intval($arrParams['deleted']) : 0,
            'updateTime' => isset($arrParams['updateTime']) ? intval($arrParams['updateTime']) : 0
        ];
        $result = $this->_objDaoQudaoCostEntry->insertRecords($arrFields,null,$dup);
        if ($result === false) {
            Qdlib_Util_Log::warning('Qdlib_Ds', 'QudaoCostEntry', 'addQudaoCostEntry', '数据库插入失败', json_encode($arrFields));
            return false;
        }
        $id = $this->_objDaoQudaoCostEntry->getInsertId();
        return $id;
    }

    //编辑
    public function updateQudaoCostEntry($arrConds, $arrParams) {
        $arrAllFields = explode(',',self::ALL_FIELDS);
        foreach($arrParams as $key => $value){
            if(!in_array($key, $arrAllFields)){
                continue;
            }
            $arrFields[$key] = $value;
        }
        if (isset($arrFields['systemContact'])) {
            $arrFields['systemContact'] = empty($arrFields['systemContact']) ? '{}' : json_encode($arrFields['systemContact']);
        }
        $result = $this->_objDaoQudaoCostEntry->updateByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Qdlib_Ds', 'QudaoCostEntry', 'updateQudaoCostEntry', '数据库更新失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //通过id编辑
    public function updateQudaoCostEntryById($id, $arrFields) {
        $arrConds = ['id' => $id];
        return $this->updateQudaoCostEntry($arrConds, $arrFields);
    }

    //软删除
    public function deleteQudaoCostEntry($arrConds)
    {
        $arrFields = ['deleted' => self::DELETE_DEL];
        return $this->updateQudaoCostEntry($arrConds, $arrFields);
    }

    //获取信息
    public function getQudaoCostEntryInfoById($id, $arrFields = array(), $arrConds = [])
    {
        $arrConds['id'] = $id;

        if (!$arrFields) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrConds = ['id' => $id];
        $result = $this->_objDaoQudaoCostEntry->getRecordByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Qdlib_Ds', 'QudaoCostEntry', 'getQudaoCostEntryInfoById', '查询数据库信息失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //获取列表
    public function getQudaoCostEntryList($arrConds = array(), $arrFields = array(), $arrAppends = null) {
        if (!$arrFields){
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $result = $this->_objDaoQudaoCostEntry->getListByConds($arrConds, $arrFields, null, $arrAppends);
        if ($result === false) {
            Qdlib_Util_Log::warning('Qdlib_Ds', 'QudaoCostEntry', 'getQudaoCostEntryList', '查询数据库列表失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds, 'appends' => $arrAppends]));
            return false;
        }
        return $result;
    }

    //获取总数
    public function getQudaoCostEntryTotal($arrConds = array())
    {
        $res = $this->_objDaoQudaoCostEntry->getCntByConds($arrConds);
        if ($res === false) {
            Qdlib_Util_Log::warning('Qdlib_Ds', 'QudaoCostEntry', 'getQudaoCostEntryTotal', '查询数据库总数失败', json_encode(['conds' => $arrConds]));
        }
        return $res;
    }

    //批量获取信息
    public function getQudaoCostEntryInfoByIds($ids, $arrFields = array(), $arrConds = [])
    {
        if (empty($ids) || !is_array($ids)) {
            Qdlib_Util_Log::warning('Qdlib_Ds', 'QudaoCostEntry', 'getQudaoCostEntryInfoByIds', '缺少必要的id列表', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        $idsStr = implode(',',$ids);

        $arrConds[] = "id in ($idsStr)";
        if (!$arrFields) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $result = $this->_objDaoQudaoCostEntry->getListByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Qdlib_Ds', 'QudaoCostEntry', 'getQudaoCostEntryInfoByIds', '查询数据库信息失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    public function query($sql) {
        return $this->_objDaoQudaoCostEntry->query($sql);
    }

}