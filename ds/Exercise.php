<?php
/**
 * Copyright (c) 2016 zuoyebang.com, Inc. All Rights Reserved
 * @file:Exercise.php
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @date: 2016/7/8
 * @time: 11:02
 * @desc:
 */

class Zhibo_Ds_Exercise
{

    //习题类型
    const TYPE_SINGLE = 0; //单选题
    const TYPE_MULTI = 1; //多选题
    const TYPE_SUBJECTIVE = 2; //主观题
    //const TYPE_FILLBLACK  = 3; //填空题
    static $TYPE_ARRAY = array(
        self::TYPE_SINGLE => '单选题',
        self::TYPE_MULTI => '多选题',
        self::TYPE_SUBJECTIVE => '主观题',
        //self::TYPE_FILLBLACK => '填空题',
    );

    //习题用途
    const PURPOSE_PRECLASS = 0; //入门测试
    const PURPOSE_INCLASS = 1; //课间习题
    const PURPOSE_SUFCLASS = 2; //课后习题
    static $PURPOSE_ARRAY = array(
        self::PURPOSE_PRECLASS => '入门测试',
        self::PURPOSE_INCLASS => '课间习题',
        self::PURPOSE_SUFCLASS => '课后习题',
    );

    //状态
    const STATUS_OK = 0; //正常
    const STATUS_DELETED = 1; //已删除
    static $STATUS_ARRAY = array(
        self::STATUS_OK => '正常',
        self::STATUS_DELETED => '已删除',
    );

    //发送状态
    const SEND_STATUS_Y = 1;
    const SEND_STATUS_N = 0;
    static $SEND_STATUS_ARRAY = array(
        self::SEND_STATUS_Y => '已发送',
        self::SEND_STATUS_N => '未发送',
    );

    private $objDaoExercise;
    
    public function __construct()
    {
        $this->objDaoExercise = new Zhibo_Dao_Exercise();
    }

    /**
     * 获取习题列表
     *
     * @param  int $exerciseId 习题id
     * @param  array $arrFields 指定属性
     * @return array|bool
     */
    public function getExerciseListByIds($courseId, $lessonId, $arrExerciseIds, $arrFields = array())
    {
        if (intval($courseId) <= 0 || empty($arrExerciseIds)) {
            Bd_Log::warning("Error:[param error], Detail:[$arrExerciseIds:" . implode(',', $arrExerciseIds) . "]");
            return false;
        }
        $strExerciseIds = implode(',', $arrExerciseIds);
        if (!preg_match("/^[0-9,\\s]+$/", $strExerciseIds)) {
            Bd_Log::warning("Error:[param error], Detail:[$strExerciseIds:$strExerciseIds]");
            return false;
        }
        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_Exercise::$allFields;
        }
        $arrConds = array(
            "course_id" => $courseId,
            "lesson_id" => $lessonId,
            "exercise_id in ($strExerciseIds)",
        );
        $ret = $this->objDaoExercise->getListByConds($arrConds, $arrFields);
        return $ret;
    }
    
    /**
     * 获取习题列表
     *
     * @param  int  $courseId   课程id
     * @param  int  $lessonId   课节id
     * @param  int  $purpose    习题用途
     * @param  mix  $arrFields  指定属性
     * @param  int  $offset     偏移
     * @param  int  $limit      限制
     * @return mix
     */
    public function getExerciseList($courseId, $lessonId, $purpose, $arrFields = array(), $offset = 0, $limit = 20) {
        $courseId = intval($courseId);
        $lessonId = intval($lessonId);
        if($courseId <= 0 || $lessonId < 0) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$lessonId]");
            return array();
        }
        if(empty($arrFields)) {
            $arrFields = Zhibo_Dao_Exercise::$allFields;
        }
        $arrConds = array(
            'courseId' => $courseId,
            'lessonId' => $lessonId,
            'purpose'  => intval($purpose),
            'deleted'  => self::STATUS_OK,
        );
        $arrAppends = array(
            "order by create_time desc",
            "limit $offset, $limit",
        );
        $ret = $this->objDaoExercise->getListByConds($arrConds, $arrFields, NULL, $arrAppends);
        return $ret;
    }
}