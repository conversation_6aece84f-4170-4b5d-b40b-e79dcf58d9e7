<?php

/**
 * Class Qdlib_Ds_SkuGroupConf
 */

class Qdlib_Ds_SkuRcdConf
{
    const ALL_FIELDS = 'id,uniqid,type,apiName,selectFilter,isAb,interfaceStatus,compileStatus,projectId,editUid,editName,editTime,createTime,updateTime,saleChannel,createName,createUid,interfaceAttr';
    const DELETE_DEL = 1;

    private $objDaoSkuRcdConf;

    public function __construct()
    {
        $this->objDaoSkuRcdConf = new Qdlib_Dao_SkuRcdConf();
    }

    //新增
    public function addSkuRcdConf($arrParams) {
        $arrFields = array(
            'id'              => isset($arrParams['id']) ? intval($arrParams['id']) : 0,
            'uniqid'          => isset($arrParams['uniqid']) ? strval($arrParams['uniqid']) : '',
            'type'            => isset($arrParams['type']) ? intval($arrParams['type']) : 0,
            'apiName'         => isset($arrParams['apiName']) ? strval($arrParams['apiName']) : '',
            'selectFilter'     => isset($arrParams['selectFilter']) ? strval($arrParams['selectFilter']) : '',
            'isAb'            => isset($arrParams['isAb']) ? intval($arrParams['isAb']) : 0,
            'interfaceStatus' => isset($arrParams['interfaceStatus']) ? intval($arrParams['interfaceStatus']) : 0,
            'compileStatus'   => isset($arrParams['compileStatus']) ? intval($arrParams['compileStatus']) : 0,
            'projectId'       => isset($arrParams['projectId']) ? intval($arrParams['projectId']) : 0,
            'editUid'         => isset($arrParams['editUid']) ? strval($arrParams['editUid']) : '',
            'editName'        => isset($arrParams['editName']) ? strval($arrParams['editName']) : '',
            'editTime'        => isset($arrParams['editTime']) ? intval($arrParams['editTime']) : 0,
            'createTime'      => isset($arrParams['createTime']) ? intval($arrParams['createTime']) : 0,
            'updateTime'      => isset($arrParams['updateTime']) ? intval($arrParams['updateTime']) : 0,
            'saleChannel'     => isset($arrParams['saleChannel']) ? strval($arrParams['saleChannel']) : '',
            'createName'      => isset($arrParams['createName']) ? strval($arrParams['createName']) : '',
            'createUid'       => isset($arrParams['createUid']) ? strval($arrParams['createUid']) : '',
            'interfaceAttr'   => isset($arrParams['interfaceAttr']) ? intval($arrParams['interfaceAttr']) : 0,
        );

        $result = $this->objDaoSkuRcdConf->insertRecords($arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'SkuRcdConf', 'addSkuRcdConf', '数据库插入失败', json_encode($arrFields));
            return false;
        }
        $id = $this->objDaoSkuRcdConf->getInsertId();
        return $id;
    }

    //编辑
    public function updateSkuRcdConf($arrConds = null, $arrParams) {
        $arrAllFields = explode(',',self::ALL_FIELDS);
        foreach($arrParams as $key => $value){
            if(!in_array($key, $arrAllFields)){
                continue;
            }
            $arrFields[$key] = $value;
        }
        $result = $this->objDaoSkuRcdConf->updateByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'SkuRcdConf', 'updateSkuRcdConf', '数据库更新失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //通过id编辑
    public function updateSkuRcdConfById($id, $arrFields) {
        $arrConds = ['id' => $id];
        return $this->updateSkuRcdConf($arrConds, $arrFields);
    }

    //软删除
    public function deleteSkuRcdConf($arrConds = null)
    {
        $arrFields = ['deleted' => self::DELETE_DEL];
        return $this->updateSkuRcdConf($arrConds, $arrFields);
    }

    //获取信息
    public function getSkuRcdConfInfoById($id, $arrFields = array(), $arrConds = null)
    {
        $arrConds['id'] = $id;

        if (!$arrFields) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrConds = ['id' => $id];
        $result = $this->objDaoSkuRcdConf->getRecordByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'SkuRcdConf', 'getSkuRcdConfInfoById', '查询数据库信息失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //获取列表
    public function getSkuRcdConfList($arrConds = null, $arrFields = array(), $arrAppends = null) {
        if (!$arrFields){
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $result = $this->objDaoSkuRcdConf->getListByConds($arrConds, $arrFields, null, $arrAppends);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'SkuRcdConf', 'getSkuRcdConfList', '查询数据库列表失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds, 'appends' => $arrAppends]));
            return false;
        }
        return $result;
    }

    //获取总数
    public function getSkuRcdConfTotal($arrConds = null)
    {
        $res = $this->objDaoSkuRcdConf->getCntByConds($arrConds);
        if ($res === false) {
            Qdlib_Util_Log::warning('Service_Data', 'SkuRcdConf', 'getSkuRcdConfTotal', '查询数据库总数失败', json_encode(['conds' => $arrConds]));
        }
        return $res;
    }
}