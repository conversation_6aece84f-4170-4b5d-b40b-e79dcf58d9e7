<?php

/**
 * @file   GuoyuanChargeRefund.php
 * <AUTHOR>
 * @date    2016-07-23
 * @brief  果园退款
 *
 **/
class Zhibo_Ds_GuoyuanChargeRefund
{
    //工作流状态
    const STATUS_INIT           = 0;
    const STATUS_CHECKING       = 1;
    const STATUS_BACK           = 3;
    const STATUS_FAILED         = 126;
    const STATUS_SUCCESS        = 127;
    const STATUS_REFUNDFAIL     = 100;
    const STATUS_MANUAL_REFUND  = 101;
    
    static $statusMap = array(
        self::STATUS_INIT           => '待处理',
        self::STATUS_CHECKING       => '财务审核中',
        self::STATUS_FAILED         => '审核拒绝',
        self::STATUS_SUCCESS        => '审核通过',
        self::STATUS_REFUNDFAIL     => "退款失败",
        self::STATUS_MANUAL_REFUND  => "手动退款成功",
    );
    const PAY_STATUS_NOPAY   = 0;//未支付
    const PAY_STATUS_PAY     = 1;//已支付
    const PAY_STATUS_REFUND  = 2;//已退款
    const PAY_STATUS_REQUEST = 3;//已冻结

    static  $payStatusMap = array(
        self::PAY_STATUS_NOPAY   => "未支付",
        self::PAY_STATUS_PAY     => "已支付",
        self::PAY_STATUS_REFUND  => "已退款",
        self::PAY_STATUS_REQUEST => "已冻结",
    );

    const MATERIAL_STATUS_NO  = 0;//无需教材
    const MATERIAL_STATUS_YES = 1;//待寄回
    const MATERIAL_STATUS_BACK_NO  = 2;//未寄回
    const MATERIAL_STATUS_BAKC_YES = 3;//已寄回

    static  $MaterialStatusMap = array(
        self::MATERIAL_STATUS_NO   => "无需教材",
        self::MATERIAL_STATUS_YES  => "待寄回",
        self::MATERIAL_STATUS_BAKC_YES  => "已寄回",
        self::MATERIAL_STATUS_BACK_NO   => "未寄回",
    );

    //记录删除状态
    const DELETED_NO  = 0;
    const DELETED_YES = 1;
    
    //退款类型标识
    const SIGN_OLD = 0; //老退款逻辑，包含课程（和教材）
    const SIGN_COURSE = 1; //课程退款（可能暂扣教材）
    const SIGN_MATERIAL = 2; //教材退款
   //审核员操作状态
    const OP_TYPE_APPLY             = 0;
    const OP_TYPE_PASSED            = 1;
    const OP_TYPE_FAILED            = 2;
    const OP_TYPE_BACK              = 3;
    const OP_TYPE_REFUNDFAIL        = 4;
    const OP_TYPE_MARKREFUNDSUCCESS = 5;
    static $opTypeMap = array(
        self::OP_TYPE_APPLY               => '客服提交申请',
        self::OP_TYPE_PASSED              => '审核通过',
        self::OP_TYPE_FAILED              => '审核拒绝',
        self::OP_TYPE_BACK                => '审核回退',
        self::OP_TYPE_REFUNDFAIL          => '退款失败',
        self::OP_TYPE_MARKREFUNDSUCCESS   => '手动标记退款成功',
    );

    private $objDaoGuoyuanChargeRefund;

    /**
     * 构造函数
     *
     */
    public function __construct() {
        $this->objDaoGuoyuanChargeRefund = new Zhibo_Dao_GuoyuanChargeRefund();
    }

    /**
     * @return Dao_ChargeRefund
     */
    public function getDao() {
        return $this->objDaoGuoyuanChargeRefund;
    }

   


    /**
     * 审核操作,请在调用前check用户是否有当前的审核员权限
     * @param $id
     * @param $status
     * @param $nextOpRole
     * @param $workflowId
     * @return bool
     */
    public function updateStatus($id, $status, $nextOpRole, $workflowId = 0) {
        if (!$id) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id status:$status nextOpRole:$nextOpRole]");
            return false;
        }

        $arrConds  = array('id' => $id);
        $arrFields = array('status' => $status, 'nextOpRole' => $nextOpRole);
        if ($workflowId) {
            $arrFields['workflowId'] = $workflowId;
        }
        $ret = $this->objDaoGuoyuanChargeRefund->updateByConds($arrConds, $arrFields);

        return $ret !== false;
    }
    /**
     * 审核操作,请在调用前check用户是否有当前的审核员权限
     * @param $id
     * @param $status
     * @return bool
     */
    public function updateGuoyuanChargeRefund($id, $arrParams) {
        if (!$id) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id]");

            return false;
        }
        $arrConds  = array('id' => intval($id));
        $arrAllFields = Zhibo_Dao_GuoyuanChargeRefund::$allFields;
        foreach($arrParams as $key => $value) {
            if(!in_array($key, $arrAllFields)) {
                continue;
            }

            $arrFields[$key] = $value;
        }

        //ext由ps补全，json处理在ds
        if(isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }

        $ret = $this->objDaoGuoyuanChargeRefund->updateByConds($arrConds, $arrFields);

        return $ret !== false;
    }

    /**
     * 审核操作,请在调用前check用户是否有当前的审核员权限
     * @param $id
     * @param $workflowId
     * @return bool
     */
    public function updateWorkflowId($id, $workflowId) {
        if (!$id || !$workflowId) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id workflowId:$workflowId]");

            return false;
        }

        $arrConds  = array('id' => $id);
        $arrFields = array('workflowId' => $workflowId);

        $ret = $this->objDaoGuoyuanChargeRefund->updateByConds($arrConds, $arrFields);

        return $ret;
    }

    /**
     * 获取详情
     *
     * @param  string $id        id
     * @param  array  $arrFields 指定属性
     * @return array
     */
    public function getGuoyuanRefundInfo($id, $arrFields = array()) {
        if (intval($id) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_GuoyuanChargeRefund::$allFields;
        }

        $arrConds = array(
            'id' => strval($id),
        );

        $ret = $this->objDaoGuoyuanChargeRefund->getRecordByConds($arrConds, $arrFields);

        return $ret;
    }

    /**
     * 获取详情
     *
     * @param  int $uid
     * @param  int $orderId
     * @return array
     */
    public function getGuoyuanRefundByUidOrderId($uid, $orderId) {
        if (intval($uid) <= 0 || intval($orderId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[uid:$uid orderId:$orderId]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_GuoyuanChargeRefund::$allFields;
        }

        $arrConds   = array(
            'orderUid' => intval($uid),
            'orderId'  => intval($orderId),
        );
        $arrAppends = array(
            'ORDER BY create_time desc ',
        );

        $ret = $this->objDaoGuoyuanChargeRefund->getListByConds($arrConds, $arrFields, null, $arrAppends);

        return $ret;
    }


  
    
    


    /**
     * 获取详情
     *
     * @param  int $uid
     * @param  int $orderId
     * @return array
     */
    public function checkRefundByUidOrderId($uid, $orderId) {
        if (intval($uid) <= 0 || intval($orderId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[uid:$uid orderId:$orderId]");
            return false;
        }

        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_GuoyuanChargeRefund::$allFields;
        }

        $arrConds = array(
            'orderUid' => intval($uid),
            'orderId'  => intval($orderId),
            'status != ' . self::STATUS_FAILED,
        );

        $ret = $this->objDaoGuoyuanChargeRefund->getRecordByConds($arrConds, $arrFields);
        if (count($ret) > 0) {
            return true;
        }

        return false;
    }
    
    /**
     * 获取详情
     *
     * @param  int $uid
     * @param  int $orderId
     * @return array
     */
    public function getRefundByUidOrderId($uid, $orderId, $isMaster=false) {
        $uid = intval($uid);
        $orderId = intval($orderId);
        if ($uid <= 0 || $orderId <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[uid:$uid orderId:$orderId]");
            return array();
        }
        $arrFields = Zhibo_Dao_GuoyuanChargeRefund::$allFields;
        if ($isMaster) {
            $ret = $this->objDaoGuoyuanChargeRefund->startTransaction();
            if (false === $ret) {
                return false;
            }
        }
        $arrConds = array(
            'orderUid' => $uid,
            'orderId'  => $orderId,
        );
        $ret = $this->objDaoGuoyuanChargeRefund->getRecordByConds($arrConds, $arrFields);
        if (false === $ret) {
            return false;
        }
        if ($isMaster) {
            $this->objDaoGuoyuanChargeRefund->commit();
        }
        return $ret;
    }

    /**
     *
     * @param       $arrConds
     * @param array $arrFields
     * @param int   $offset
     * @param int   $limit
     * @return array|false
     */
    public function getRefundListByConds($arrConds, $arrFields = array(), $offset = 0, $limit = 20) {
        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_GuoyuanChargeRefund::$allFields;
        }

        $arrAppends = array(
            "order by status,create_time desc ",
            "limit $offset, $limit",
        );

        $ret = $this->objDaoGuoyuanChargeRefund->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }
    
    /**获取退款信息
     * @param       $arrConds
     * @param array $arrFields
     * @return array|false
     */
    public function getRefundListByStatus($arrConds, $arrFields = array()) {
        if (empty($arrConds)) {
            Bd_Log::warning("Error:[param error], Detail:[ arrParams:$arrConds]");
            return false;
        }
        $Conds = array(
            'create_time'      => array($arrConds['startTime'],'>',$arrConds['endTime'],'<'),
            'status in (0,1)',
        );
    
        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_GuoyuanChargeRefund::$allFields;
        }
    
        $ret = $this->objDaoGuoyuanChargeRefund->getListByConds($Conds, $arrFields);
    
        return $ret;
    }

    /**
     * 获取总记录数
     * @param       $arrConds
     * @return array|false
     */
    public function getRefundCnt($arrConds) {

        $ret = $this->objDaoGuoyuanChargeRefund->getCntByConds($arrConds);

        return $ret;
    }

    /**
     * 更新信息
     * 审核操作,请在调用前check用户是否有当前的审核员权限
     * @param $arrConds
     * @param $arrParams
     * @return bool
     */
    public function updateChargeRefundByArrConds($arrConds, $arrParams) {
        if (empty($arrConds) || empty($arrParams)) {
            Bd_Log::warning("Error:[param error], Detail:[id:$arrConds arrParams:$arrParams]");

            return false;
        }

        do {
            $tmpArrFields   =   Zhibo_Dao_GuoyuanChargeRefund::$allFields;
            $data           =   $this->objDaoGuoyuanChargeRefund->getRecordByConds($arrConds, $tmpArrFields);
            if (!$data) {
                Bd_Log::warning("Error:[param error], Detail:[" . json_encode($arrConds) . "]");

                return false;
            }
            //待更新的所有字段
            $arrFields = array();

            //3. 拿回id 和 extdata
            $extData = $data['extData'];
            //extData补全
            if (isset($arrParams['extData']) && is_array($arrParams['extData'])) {
                foreach ($arrParams['extData'] as $key => $value) {
                    if (strpos($key, 'arr_') === 0) {
                        $extData[$key][] = $value;
                    } else {
                        $extData[$key] = $value;
                    }
                }
                $arrFields['extData'] = json_encode($extData);
                $data['extData']      = $extData;
                unset($arrParams['extData']);
            }

            //4. 其他字段补全
            $arrAllFields = Zhibo_Dao_ChargeRefund::$allFields;
            foreach ($arrParams as $key => $value) {
                //不允许把值置空
                if (!$value && !in_array($key, $arrAllFields)) {
                    continue;
                }
                $arrFields[$key] = $value;
                $data[$key]      = $value;
            }

            //5. 更新道数据库
            $ret = $this->objDaoGuoyuanChargeRefund->updateByConds($arrConds, $arrFields);

            if ($ret === false) {
                //提交失败
                Bd_Log::warning("Error:[update callRecord error], Detail:[" . json_encode($arrConds) . "]");

                return false;
            }
        }while(0);
        return true;
    }
    
     /**
     * 退款接口
     * @param $uid
     * @param $orderId
     * @param $refundAmount
     * @param $refundPayment
     * @return array|bool
     */
    public function callCourseToMaterialRefund($arrInput) {
        $arrOutput = array('errNo' => 0);
        $header    = array(
            'pathinfo' => "/course/api/usertradematerialrefundreq",
            'cookie'   => $_COOKIE,
        );
        $arrParams = array(
            'studentUid'    => intval($arrInput['studentUid']),
            'orderId'       => intval($arrInput['orderId']),
            'source'        => strval($arrInput['source']),
            'requestNo'     => intval($arrInput['requestNo']),
            'refundPayment' => intval($arrInput['materialRefundPayment']),
            'status'        => intval($arrInput['status']),
        ); 
        //加入加密串
        $arrParams = Dayi_Util_GnSign::getSign($arrParams); 
        $ret       = ral('course', 'POST', $arrParams, rand(), $header);
        Bd_Log::addNotice('ral_course_usertradematerialrefundreq_ret', $ret);
        if (false === $ret) {
            $errno           = ral_get_errno();
            $errMsg          = ral_get_error();
            $protocol_status = ral_get_protocol_code();
            Bd_Log::warning("Error:[service guoyuan connect error], Detail:[errno:$errno errMsg:$errMsg protocol_status:$protocol_status]");
            $arrOutput['errNo']  = $errno;
            $arrOutput['errMsg'] = $errMsg;
        } else {
            $ret    = json_decode($ret, true);
            $errno  = intval($ret['errNo']);
            $errMsg = strval($ret['errstr']);
            if (!is_array($ret) || $errno > 0) {
                Bd_Log::warning("Error:[service guoyuan process error], Detail:[errno:$errno errMsg:$errMsg]");
                $arrOutput['errNo'] = $errno;
                $arrOutput['errMsg'] = $errMsg;
            }
        }
        return $arrOutput;
    }
    
    
}
