<?php
/**
 * file: QudaoPyqData.php
 * User: <EMAIL>
 * Date: 2019/7/12
 * brief:
 */
class Qdlib_Ds_QudaoPyqData
{
    private $_objDaoQudaoPyqData;

    public function __construct()
    {
        $this->_objDaoQudaoPyqData = new Qdlib_Dao_QudaoPyqData();
    }

    public function addPyqData($arrParams)
    {
        if (empty($arrParams)) {
            Qdlib_Util_Log::warning('syncdata', 'Qdlib_Ds_QudaoPyqData', 'addPyqData', "param: empty");
            return false;
        }
        $arrFields = array(
            'agencyAccountId'     => isset($arrParams['agencyAccountId']) ? strval($arrParams['agencyAccountId']) : '',
            'account'              => isset($arrParams['account']) ? strval($arrParams['account']) : '',
            'wechatCampaignId'    => isset($arrParams['wechatCampaignId']) ? intval($arrParams['wechatCampaignId']) : 0,
            'thirdAdId'            => isset($arrParams['thirdAdId']) ? intval($arrParams['thirdAdId']) : 0,
            'adgroupId'            => isset($arrParams['adgroupId']) ? intval($arrParams['adgroupId']) : 0,
            'dt'                    => isset($arrParams['dt']) ? intval($arrParams['dt']) : 0,
            'hour'                  => isset($arrParams['hour']) ? intval($arrParams['hour']) : 0,
            'orifrom'               => isset($arrParams['orifrom']) ? strval($arrParams['orifrom']) : '',
            'pageUrl'               => isset($arrParams['pageUrl']) ? urlencode($arrParams['pageUrl']) : '',
            'showPv'                => isset($arrParams['showPv']) ? intval($arrParams['showPv']) : 0,
            'showUv'                => isset($arrParams['showUv']) ? intval($arrParams['showUv']) : 0,
            'clickPv'               => isset($arrParams['clickPv']) ? intval($arrParams['clickPv']) : 0,
            'clickUv'               => isset($arrParams['clickUv']) ? intval($arrParams['clickUv']) : 0,
            'cost'                   => isset($arrParams['cost']) ? intval($arrParams['cost']) : 0,
            'download'              => isset($arrParams['download']) ? intval($arrParams['download']) : 0,
            'conversion'            => isset($arrParams['conversion']) ? intval($arrParams['conversion']) : 0,
            'activation'            => isset($arrParams['activation']) ? intval($arrParams['activation']) : 0,
            'createTime'            => isset($arrParams['createTime']) ? intval($arrParams['createTime']) : time(),
            'updateTime'            => isset($arrParams['updateTime']) ? intval($arrParams['updateTime']) : time(),
            'systemStatus'          => isset($arrParams['systemStatus']) ? intval($arrParams['systemStatus']) : 0,
            'dataSource'            => isset($arrParams['dataSource']) ? strval($arrParams['dataSource']) : '',
            'extData'               => isset($arrParams['extData']) ? strval($arrParams['extData']) : json_encode([]),
        );
        $res = $this->_objDaoQudaoPyqData->insertRecords($arrFields);
        if (false === $res) {
            Qdlib_Util_Log::warning('syncdata', 'Qdlib_Ds_QudaoPyqData', 'addPyqData', 'db error',
                json_encode($arrFields));
            return false;
        }
        return $res;
    }

    public function updatePyqData($arrConds,$arrInput){
        if(empty($arrInput) || empty($arrConds)){
            Qdlib_Util_Log::warning('syncdata', 'Qdlib_Ds_QudaoPyqData', 'updatePyqData', "param: empty");
            return false;
        }
        $arrInput[] = 'update_time='.time();
        /*$arrAppends = [
            "limit 1"
        ];*/
        $res = $this->_objDaoQudaoPyqData->updateByConds($arrConds, $arrInput, null, null);
        if($res===false){
            Qdlib_Util_Log::warning('syncdata', 'Qdlib_Ds_QudaoPyqData', 'updatePyqData', 'db error',
                json_encode($arrInput));
        }
        return $res;
    }

    public function getPyqDataList($where, $field = [], $arrAppends = null)
    {
        if (!$field) {
            $field = $this->_objDaoQudaoPyqData->getAllFields();
        }
        $res = $this->_objDaoQudaoPyqData->getListByConds($where, $field, null, $arrAppends);
        if ($res === false) {
            $sql = $this->_objDaoQudaoPyqData->getLastSQL();
            $where = json_encode($where);
            Qdlib_Util_Log::warning('syncdata', 'Qdlib_Ds_QudaoPyqData', 'getPyqDataList', "sql:{$sql}", "{$where}");
        }
        return $res;
    }

}