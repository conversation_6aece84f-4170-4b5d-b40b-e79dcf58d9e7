<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Time: 2017/7/27 17:24
 */

class Zhibo_Ds_LmsLessonLecture
{
    // type
    const TYPE_MODIFIER = 1; // 操作人
    const TYPE_LESSON   = 2; // 章节
    static $TYPE_ARRAY = array(
        self::TYPE_MODIFIER => '操作人',
        self::TYPE_LESSON   => '章节',
    );

    const ALL_FIELDS = 'id,lectureId,type,typeId,deleted';
    private $objDaoLmsLessonLecture;

    public function __construct() {
        $this->objDaoLmsLessonLecture = new Zhibo_Dao_LmsLessonLecture();
    }

    /**
     * 新增
     * @param $arrParams
     * @return bool
     */
    public function addRecord($arrParams) {
        if (empty($arrParams['lectureId']) || empty($arrParams['typeId'])) {
            Bd_Log::warning("Error:[param error], Detail:[param: ". json_encode($arrParams). "]");
            return false;
        }
        $arrFields = array(
            'lectureId' => intval($arrParams['lectureId']),
            'typeId'    => intval($arrParams['typeId']),
            'type'      => intval($arrParams['type']),
            'deleted'   => isset($arrParams['deleted']) ? $arrParams['deleted'] : 0,
        );
        $ret = $this->objDaoLmsLessonLecture->insertRecords($arrFields);
        return $ret;
    }

    /**
     * @param $typeId
     * @param $type
     * @param array $arrFields
     * @return bool
     */
    public function getRecordByTypeId($typeId = 0, $type = 1 , $arrFields = array()) {
        if (intval($typeId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[typeId:$typeId]");
            return false;
        }
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrCond = array(
            'typeId' => intval($typeId),
            'type'   => $type
        );
        $ret = $this->objDaoLmsLessonLecture->getRecordByConds($arrCond, $arrFields);
        return $ret;
    }

    /**
     * @param $lectureId
     * @param $type
     * @param $arrParams
     * @return bool
     */
    public function updateRecordByCond($lectureId, $type, $arrParams) {
        if(intval($lectureId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[lectureId:$lectureId]");
            return false;
        }
        $arrCond = array(
            'lectureId' => intval($lectureId),
            'type'      => intval($type),
        );
        $arrFields = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach ($arrParams as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }
            $arrFields[$key] = $value;
        }
        $ret = $this->objDaoLmsLessonLecture->updateByConds($arrCond, $arrFields);

        return $ret;
    }

    /**
     * 获取数量
     * @param $arrCond
     * @return false|int
     */
    public function getLectureCntByCond($arrCond) {

        $ret = $this->objDaoLmsLessonLecture->getCntByConds($arrCond);
        return $ret;
    }

    /**
     * @param $arrCond
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getLectureListByCond($arrCond, $arrFields = array(), $offset = 0, $limit = 20) {
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrAppends = array(
            "order by id desc",
            "limit $offset, $limit",
        );
        $ret = $this->objDaoLmsLessonLecture->getListByConds($arrCond, $arrFields, NULL, $arrAppends);
        return $ret;
    }
}