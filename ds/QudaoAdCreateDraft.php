<?php
/**
 * Created by Growth AutoCode.
 * User: <EMAIL>
 * Date: 2021/01/18
 * Time: 06:48
 */
class Qdlib_Ds_QudaoAdCreateDraft
{
    const ALL_FIELDS = 'id,taskId,mediaId,mediaExt,channel,account,name,level,lv1,lv2,ext,draft,status,errCode,errMsg,creator,operator,createTime,updateTime,deleted,onlineVersion,zybLastfrom';
    const DELETE_DEL = 1;

    protected $objDaoQudaoAdCreateDraft;

    public function __construct($db = null)
    {
        $this->objDaoQudaoAdCreateDraft = new Qdlib_Dao_QudaoAdCreateDraft($db);
    }

    //新增
    public function addQudaoAdCreateDraft($arrParams) {
        $arrFields = array(
            'id'         => isset($arrParams['id']) ? intval($arrParams['id']) : 0,
            'taskId'     => isset($arrParams['taskId']) ? intval($arrParams['taskId']) : 0,
            'mediaId'    => isset($arrParams['mediaId']) ? strval($arrParams['mediaId']) : '',
            'mediaExt'   => isset($arrParams['mediaExt']) ? json_encode($arrParams['mediaExt']) : '{}',
            'channel'    => isset($arrParams['channel']) ? strval($arrParams['channel']) : '',
            'account'    => isset($arrParams['account']) ? strval($arrParams['account']) : '',
            'name'       => isset($arrParams['name']) ? strval($arrParams['name']) : '',
            'level'      => isset($arrParams['level']) ? intval($arrParams['level']) : 0,
            'lv1'        => isset($arrParams['lv1']) ? intval($arrParams['lv1']) : 0,
            'lv2'        => isset($arrParams['lv2']) ? intval($arrParams['lv2']) : 0,
            'ext'        => isset($arrParams['ext']) ? json_encode($arrParams['ext']) : '{}',
            'draft'      => isset($arrParams['draft']) ? json_encode($arrParams['draft']) : '{}',
            'status'     => isset($arrParams['status']) ? intval($arrParams['status']) : 0,
            'errCode'    => isset($arrParams['errCode']) ? intval($arrParams['errCode']) : 0,
            'errMsg'     => isset($arrParams['errMsg']) ? strval($arrParams['errMsg']) : '',
            'creator'    => isset($arrParams['creator']) ? strval($arrParams['creator']) : '',
            'operator'   => isset($arrParams['operator']) ? strval($arrParams['operator']) : '',
            'createTime' => isset($arrParams['createTime']) ? intval($arrParams['createTime']) : 0,
            'updateTime' => isset($arrParams['updateTime']) ? intval($arrParams['updateTime']) : 0,
            'deleted'    => isset($arrParams['deleted']) ? intval($arrParams['deleted']) : 0,
            'onlineVersion' => isset($arrParams['onlineVersion']) ? intval($arrParams['onlineVersion']) : 0,
            'zybLastfrom'   => isset($arrParams['zybLastfrom']) ? json_encode($arrParams['zybLastfrom']) : '{}',
        );
        $result = $this->objDaoQudaoAdCreateDraft->insertRecords($arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoAdCreateDraft', 'addQudaoAdCreateDraft', '数据库插入失败', json_encode($arrFields));
            return false;
        }
        $id = $this->objDaoQudaoAdCreateDraft->getInsertId();
        return $id;
    }

    //编辑
    public function updateQudaoAdCreateDraft($arrConds = null, $arrParams) {
        $arrAllFields = explode(',',self::ALL_FIELDS);
        foreach($arrParams as $key => $value){
            if(!in_array($key, $arrAllFields)){
                continue;
            }
            $arrFields[$key] = in_array($key, ['mediaExt', 'ext', 'draft','zybLastfrom']) ? json_encode($value) : $value;
        }
        $result = $this->objDaoQudaoAdCreateDraft->updateByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoAdCreateDraft', 'updateQudaoAdCreateDraft', '数据库更新失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //通过id编辑
    public function updateQudaoAdCreateDraftById($id, $arrFields) {
        $arrConds = ['id' => $id];
        return $this->updateQudaoAdCreateDraft($arrConds, $arrFields);
    }

    //软删除
    public function deleteQudaoAdCreateDraft($arrConds = null)
    {
        $arrFields = ['deleted' => self::DELETE_DEL];
        return $this->updateQudaoAdCreateDraft($arrConds, $arrFields);
    }

    //获取信息
    public function getQudaoAdCreateDraftInfoById($id, $arrFields = array(), $arrConds = null)
    {
        $arrConds['id'] = $id;

        if (!$arrFields) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrConds = ['id' => $id];
        $result = $this->objDaoQudaoAdCreateDraft->getRecordByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoAdCreateDraft', 'getQudaoAdCreateDraftInfoById', '查询数据库信息失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //获取列表
    public function getQudaoAdCreateDraftList($arrConds = null, $arrFields = array(), $arrAppends = null) {
        if (!$arrFields){
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $result = $this->objDaoQudaoAdCreateDraft->getListByConds($arrConds, $arrFields, null, $arrAppends);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoAdCreateDraft', 'getQudaoAdCreateDraftList', '查询数据库列表失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds, 'appends' => $arrAppends]));
            return false;
        }
        return $result;
    }

    //获取总数
    public function getQudaoAdCreateDraftTotal($arrConds = null)
    {
        $res = $this->objDaoQudaoAdCreateDraft->getCntByConds($arrConds);
        if ($res === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoAdCreateDraft', 'getQudaoAdCreateDraftTotal', '查询数据库总数失败', json_encode(['conds' => $arrConds]));
        }
        return $res;
    }
}