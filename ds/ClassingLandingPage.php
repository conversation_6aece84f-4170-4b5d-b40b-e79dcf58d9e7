<?php
/**
 * 班课ing体验页
 * <AUTHOR>
 * @DateTime 2019-03-28
 */
class Oplib_Ds_ClassingLandingPage
{
    // 数据访问对象
    private $landingPage;
    private $_objRedis;

    /**
     * 构造方法
     * <AUTHOR>
     * @DateTime 2019-03-28
     */
    public function __construct()
    {
        $this->landingPage  = new Oplib_Dao_ClassingLandingPage();
        $this->_objRedis    = Oplib_Util_Redis_Instance::getInstance();
    }

    /**
     * 添加运营活动用户信息数据
     * <AUTHOR>
     * @DateTime 2019-03-28
     * @param    array   $arrParams 班课ing体验页信息
     * @return   bool
     */
    public function addLandingPage($arrParams)
    {
        if ($arrParams['courseId'] <= 0 || $arrParams['lessonId'] <= 0 || $arrParams['gradeId'] <= 0
            || empty($arrParams['skuId']) || empty($arrParams['mp4Url']) || empty($arrParams['m3u8Url'])
            ) {

            $strParams = json_encode($arrParams);
            Bd_Log::warning("Abstract[oplib] Error:[" . __METHOD__ . "] Detail:[strParams:{$strParams}]");
            return false;
        }

        $arrData = [
            'courseId'    => isset($arrParams['courseId']) ? intval($arrParams['courseId']) : 0,
            'lessonId'    => isset($arrParams['lessonId']) ? intval($arrParams['lessonId']) : 0,
            'gradeId'     => isset($arrParams['gradeId']) ? intval($arrParams['gradeId']) : 0,
            'skuId'       => isset($arrParams['skuId']) ? json_encode($arrParams['skuId']) : [],
            'mp4Url'      => isset($arrParams['mp4Url']) ? strval($arrParams['mp4Url']) : '',
            'm3u8Url'     => isset($arrParams['m3u8Url']) ? strval($arrParams['m3u8Url']) : '',
            'pid'         => isset($arrParams['pid']) ? strval($arrParams['pid']) : '',
            'videoLength' => isset($arrParams['videoLength']) ? intval($arrParams['videoLength']) : 0,
            'createTime'  => time(),
            'updateTime'  => time(),
            'ext'         => isset($arrParams['ext']) ? json_encode($arrParams['ext']) : [],
        ];

        $id = Oplib_Util_Tool::getPageId();
        if(!$id){
            Bd_Log::warning("Error:[Hk_Service_IdAlloc->getIdAlloc失败],Detail:[id:$id]");
            return false;
        }
        $arrData['id'] = $id;

        $ret = $this->landingPage->insertRecords($arrData);
        $ret && $ret = $this->landingPage->getInsertId();

        return $ret;
    }

    /**
     * 获取体验页详情
     * <AUTHOR>
     * @DateTime 2019-03-29
     * @param    int                $id        主键
     * @param    array              $arrFields 字段信息
     * @return   bool|array
     */
    public function getClassingLandingPage($id, $arrFields = [] ,$bCache = false)
    {
        if ($id <= 0) {
            Bd_Log::warning("Abstract[oplib] Error:[" . __METHOD__ . "] Detail:[id:{$id}]");
            return false;
        }

        $arrConds = ['id' => $id];
        empty($arrFields) && $arrFields = Oplib_Dao_ClassingLandingPage::getFields();

        $keyCache = sprintf(Oplib_Const_Cache::CLASSING_PAGE_INDEX,$id);
        if($bCache){
            $result = $this->_objRedis->get($keyCache);
            if($result){
                $arrOutPut = json_decode($result,true);
                if(!$arrOutPut){
                    Bd_Log::warning("Error:[xiaxian_cache],Detail:[ ret:".json_encode($arrOutPut)."key: ".$keyCache.";]");
                }
                return $arrOutPut;
            }
        }
        $arrOutPut = $this->landingPage->getRecordByConds($arrConds, $arrFields);

        if($bCache){
            $redisRet = $this->_objRedis->set($keyCache,json_encode($arrOutPut),3600);
            if($redisRet === false){
                Bd_Log::warning("Error:[redis set  error],Detail:[ key ".$keyCache."]");
            }
        }

        return $arrOutPut;
    }


    /**
     * 获取体验页详情
     * <AUTHOR>
     * @DateTime 2019-03-29
     * @param    int                $ids        []主键
     * @param    array              $arrFields 字段信息
     * @return   bool|array
     */
    public function getClassingLandingPages($ids, $arrFields = [] ,$bCache = false)
    {
        if (empty($ids)) {
            Bd_Log::warning("Abstract[oplib] Error:[" . __METHOD__ . "] Detail:[id:".json_encode($ids)."]");
            return false;
        }
        $arrConds = array();
        $arrConds[] = "id in (".implode(",", $ids).")";
        empty($arrFields) && $arrFields = Oplib_Dao_ClassingLandingPage::getFields();

        $keyCache = sprintf(Oplib_Const_Cache::CLASSING_PAGE_INDEX, implode(',', $ids));
        if($bCache){
            $result = $this->_objRedis->get($keyCache);
            if($result){
                $arrOutPut = json_decode($result,true);
                if(!$arrOutPut){
                    Bd_Log::warning("Error:[xiaxian_cache],Detail:[ ret:".json_encode($arrOutPut)."key: ".$keyCache.";]");
                }
                return $arrOutPut;
            }
        }
        $arrOutPut = $this->landingPage->getListByConds($arrConds, $arrFields);

        if($bCache){
            $redisRet = $this->_objRedis->set($keyCache,json_encode($arrOutPut),3600);
            if($redisRet === false){
                Bd_Log::warning("Error:[redis set  error],Detail:[ key ".$keyCache."]");
            }
        }
        return $arrOutPut;
    }

    // 更新观看次数
    public function updateSeeNumByActId($actId, $count)
    {
        if ($actId <= 0 || $count <= 0) {
            Bd_Log::warning("Abstract[oplib] Error:[" . __METHOD__ . "] Detail:[lessonId:{$actId},count:{$count}]");
            return false;
        }

        $arrConds  = ['id' => $actId];
        $arrFields = ["see_num={$count}"];
        $ret = $this->landingPage->updateByConds($arrConds, $arrFields);
        if (false === $ret) {
            Bd_Log::warning("Abstract[oplib] Error:[" . __METHOD__ . "], Detail:[lessonId:{$actId},count:{$count}]");
            return false;
        }

        return $ret;
    }

    // 批量获取观看次数
    public function getSeeNumByByActIds($arrActId = [], $arrFields = [])
    {
        if (empty($lessonIds)) {
            $strActIds = json_encode($arrActId);
            Bd_Log::warning("Abstract[oplib] Error:[" . __METHOD__ . "] Detail:[strLessonIds:{$strActIds}]");
            return false;
        }

        $arrConds = ['`id` IN(' . implode(',', $arrActId) . ')'];

        empty($arrFields) && $arrFields = Oplib_Dao_ClassingLandingPage::getFields();
        return $this->landingPage->getListByConds($arrConds, $arrFields);
    }

    // 查询体验页列表
    public function getLandingPageList($id, $gradeId, $arrFields = [], $offset = null, $limit = null, $order = '', $by = 'DESC')
    {
        if ($id <= 0 && $gradeId <= 0) {
            Bd_Log::warning("Abstract[oplib] Error:[" . __METHOD__ . "] Detail:[id:{$id},gradeId:{$gradeId}]");
            return false;
        }

        $arrConds = [];
        ($id > 0) && $arrConds['id'] = $id;
        ($gradeId > 0) && $arrConds['gradeId'] = $gradeId;
        empty($arrFields) && $arrFields = Oplib_Dao_ClassingLandingPage::getFields();
        $arrAppends = Oplib_Util_Tool::makeSelectOrderAndLimit($offset, $limit, $order, $by);

        return $this->landingPage->getListByConds($arrConds, $arrFields, null, $arrAppends);
    }

    // 获取体验页总数
    public function getLandingPageListCount($id, $gradeId)
    {
        if ($id <= 0 && $gradeId <= 0) {
            Bd_Log::warning("Abstract[oplib] Error:[" . __METHOD__ . "] Detail:[id:{$id},gradeId:{$gradeId}]");
            return false;
        }

        $arrConds = [];
        ($id > 0) && $arrConds['id'] = $id;
        ($gradeId > 0) && $arrConds['gradeId'] = $gradeId;

        return $this->landingPage->getCntByConds($arrConds);
    }

    // 更新体验页数据
    public function updatLandingPageById($id, $arrFields)
    {
        if ($id <= 0 || empty($arrFields)) {
            $strFields = json_encode($arrFields);
            Bd_Log::warning("Abstract[oplib] Error:[" . __METHOD__ . "] Detail:[id:{$id},strFields:{$strFields}]");
            return false;
        }
        $fields = ['id', 'pid'];
        $experience = $this->landingPage->getRecordByConds(['id' => $id], $fields);
        if (empty($experience)) {
            $strFields = json_encode($arrFields);
            Bd_Log::warning("Abstract[oplib] Error:[" . __METHOD__ . "] Detail:[id:{$id},strFields:{$strFields}]");
            return false;
        }

        $arrConds = ['id' => $id];
        $arrFields['updateTime'] = time();
        $ret = $this->landingPage->updateByConds($arrConds, $arrFields);
        if (false === $ret) {
            $strFields = json_encode($arrFields);
            Bd_Log::warning("Abstract[oplib] Error:[" . __METHOD__ . "], Detail:[id:{$id},strFields:{$strFields}]");
            return false;
        }
        foreach ($arrFields as $k => $v) {
            $experience[$k] = $v;
        }

        // 更新ing首页数据
        $posId = Oplib_Const_Operation::POS_COURSE_ING;
        $pageInfo = Opmis_Classing_Common::landingPageDetail($id);
        $dsAd = new Service_Data_OperationPosAd();
        $daoAd = new Dao_OperationPosAd();
        $cond = array(
            "pos_id = $posId",
            "ext like '%:{$id},%'",
        );
        $feilds = ['id', 'grade', 'status', 'ext'];
        $list = $daoAd->getListByConds($cond, $feilds);
        $hostName = Opmis_Common_OpmisCommonTool::getH5HostName();
        foreach ($list as $one) {
            $arr = $one['ext']['experience'] ?? [];
            $newExt = [];
            foreach ($arr as $key => $item) {
                if ($item['eId'] != $id) {
                    continue;
                }
                if (empty($newExt)) {
                    $newExt = $one['ext'];
                }
                $newExt['experience'][$key] = [
                    'eId'       => intval($id),
                    'ePid'      => $item['ePid'],
                    'elUrl'     => $hostName . strval($pageInfo['landingpageUrl']),
                    'eName'     => strval($pageInfo['lessonList'][0]['lessonName']),
                    'eSubject'  => strval($pageInfo['lessonList'][0]['subject']),
                    'teacherTagDesc' => strval($item['teacherTagDesc']),
                ];
            }
            if (!empty($newExt)) {
                $dsAd->updateOperationPosAd($one['id'], ['ext' => $newExt]);
            }
            if (!empty($newExt) && $one['status'] == Oplib_Ds_OperationPosAd::STATUS_ONLINE) {
                $res = Oplib_Common_Cache::deleteCacheByPosId($posId, $one['grade'], $one['id']);
                $res = Oplib_Common_Cache::deleteCacheByPosId($posId, $one['grade'], $one['id']);
            }
        }

        return $ret;
    }

    // 根据lessonId获取体验页数据
    public function getLandingPageCountByLessonId($lessonId)
    {
        if ($lessonId <= 0) {
            Bd_Log::warning("Abstract[oplib] Error:[" . __METHOD__ . "] Detail:[lessonId:{$lessonId}]");
            return false;
        }

        $arrConds = ['lessonId' => $lessonId];
        return $this->landingPage->getCntByConds($arrConds);
    }
}
