<?php
/**
 * Class Hkzb_Ds_Homework_Level
 * 作业等级.
 */

class Hkzb_Ds_Homework_Level
{

    public static $level = [
        1=>'S',
        2=>'A',
        3=>'B'
    ];

    /**
     * 测试计算等级.
     * @param array $alist
     * @return int
     */
    public static function getLevel(array $alist) {
        $intCuCorrect = 0;
        $intTotal = 0;
        $n = 0;
        if(!empty($alist) && is_array($alist))
        foreach ($alist as $value) {
            $intTotal++;
            if($value['correct'] == 1){
                $intCuCorrect++;
            }
        }
        if($intTotal == 0){
            return $n;
        }
        $num = ceil($intCuCorrect/$intTotal*100);
        if( $num >= 100 ) {
            $n = 1;
        } else if( $num>=50 && $num<100 ) {
            $n = 2;
        } else {
            $n = 3;
        }
        return $n;
    }

    /**
     * 返回等级.
     * @param $level
     * @return mixed|string
     */
    public static function getLevelStr($level){
        return isset(self::$level[$level]) ? self::$level[$level] : '';
    }
}

