<?php

/**
 * author      : <EMAIL>
 * createTime  : 2020/6/30 14:55
 * description :缓存版本
 */
class Qdlib_Ds_CourseApiVersion
{
    private $objApiVersion;
    const ALL_FIELDS = 'id,cacheKey,version,cacheValue,createTime,updateTime';

    public function __construct()
    {
        $this->objApiVersion = new Qdlib_Dao_CourseApiVersion();
    }


    public function updateRecord($arrConds, $arrParams)
    {
        if (empty($arrConds) || empty($arrParams)) {
            return false;
        }

        $ret = $this->objApiVersion->updateByConds($arrConds, $arrParams);
        if ($ret === false) {
            $sql = $this->objApiVersion->getLastSQL();
            Qdlib_Util_Log::warning("activity", "Qdlib_Ds_CourseApiVersion", "updateRecord", "sql: {$sql}", json_encode(['where' => $arrConds, 'params' => $arrParams]));
            return false;
        }

        return $ret;
    }

    public function addRecord($arrParams)
    {
        if (empty($arrParams)) {
            return false;
        }

        $ret = $this->objApiVersion->insertRecords($arrParams);

        if ($ret === false) {
            $sql = $this->objApiVersion->getLastSQL();
            Qdlib_Util_Log::warning("activity", "Qdlib_Ds_CourseApiVersion", "addRecord", "sql: {$sql}", json_encode($arrParams));
            return false;
        }

        $id = $this->objApiVersion->getInsertId();

        return $id;
    }

    //查找
    public function getList($arrConds, $arrFields = array(), $arrAppends = null)
    {
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $res = $this->objApiVersion->getListByConds($arrConds, $arrFields, null, $arrAppends);
        if (false === $res) {
            $sqlStr = $this->objApiVersion->getLastSQL();
            Qdlib_Util_Log::warning("sms", 'Qdlib_Ds_CourseApiVersion', 'getList', $sqlStr,
                json_encode(['arrConds' => $arrConds, 'arrFields' => $arrFields, 'arrAppends' => $arrAppends]));

            return false;
        }

        return $res;
    }

    //删除
    public function deleteData($arrConds)
    {
        if (empty($arrConds)) {
            return false;
        }

        $ret = $this->objApiVersion->deleteByConds($arrConds);

        if (false === $ret) {
            $sqlStr = $this->objApiVersion->getLastSQL();
            Qdlib_Util_Log::warning("sms", 'Qdlib_Ds_CourseApiVersion', 'deleteData', $sqlStr,
                json_encode(['arrConds' => $arrConds]));
            return false;
        }

        return $ret;
    }
}