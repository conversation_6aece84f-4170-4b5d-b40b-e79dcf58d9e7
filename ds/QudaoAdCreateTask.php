<?php
/**
 * Created by Growth AutoCode.
 * User: <EMAIL>
 * Date: 2021/01/18
 * Time: 06:46
 */
class Qdlib_Ds_QudaoAdCreateTask
{
    const ALL_FIELDS    = 'id,type,descr,version,channel,projectLv2,account,ext,status,creator,operator,createTime,updateTime,deleted';
    const DELETE_DEL    = 1;
    const DELETE_DEL_ON = 0;

    protected $objDaoQudaoAdCreateTask;
    protected $objDsQudaoAdCreateDraft;

    public function __construct()
    {
        $db = Qdlib_Util_DB::getQudaoDb();
        $this->objDaoQudaoAdCreateTask = new Qdlib_Dao_QudaoAdCreateTask($db);
        $this->objDsQudaoAdCreateDraft = new Qdlib_Ds_QudaoAdCreateDraft($db);
    }

    //新增
    public function addQudaoAdCreateTask($arrParams) {
        $arrFields = array(
            'id'         => isset($arrParams['id']) ? intval($arrParams['id']) : 0,
            'type'       => isset($arrParams['type']) ? intval($arrParams['type']) : 0,
            'descr'      => isset($arrParams['descr']) ? strval($arrParams['descr']) : '',
            'version'    => isset($arrParams['version']) ? intval($arrParams['version']) : 0,
            'channel'    => isset($arrParams['channel']) ? strval($arrParams['channel']) : '',
            'projectLv2' => isset($arrParams['projectLv2']) ? intval($arrParams['projectLv2']) : 0,
            'account'    => isset($arrParams['account']) ? strval($arrParams['account']) : '',
            'ext'        => isset($arrParams['ext']) ? json_encode($arrParams['ext']) : '{}',
            'status'     => isset($arrParams['status']) ? intval($arrParams['status']) : 0,
            'creator'    => isset($arrParams['creator']) ? strval($arrParams['creator']) : '',
            'operator'   => isset($arrParams['operator']) ? strval($arrParams['operator']) : '',
            'createTime' => isset($arrParams['createTime']) ? intval($arrParams['createTime']) : 0,
            'updateTime' => isset($arrParams['updateTime']) ? intval($arrParams['updateTime']) : 0,
            'deleted'    => isset($arrParams['deleted']) ? intval($arrParams['deleted']) : 0,
            'fork'       => isset($arrParams['fork']) ? intval($arrParams['fork']) : 0,
        );
        $result = $this->objDaoQudaoAdCreateTask->insertRecords($arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoAdCreateTask', 'addQudaoAdCreateTask', '数据库插入失败', json_encode($arrFields));
            return false;
        }
        $id = $this->objDaoQudaoAdCreateTask->getInsertId();
        return $id;
    }

    //编辑
    public function updateQudaoAdCreateTask($arrConds = null, $arrParams) {
        $arrAllFields = explode(',',self::ALL_FIELDS);
        foreach($arrParams as $key => $value){
            if(!in_array($key, $arrAllFields)){
                continue;
            }
            $value = in_array($key, ['ext']) ? json_encode($value) : $value;
            $arrFields[$key] = $value;
        }
        $result = $this->objDaoQudaoAdCreateTask->updateByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoAdCreateTask', 'updateQudaoAdCreateTask', '数据库更新失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //通过id编辑
    public function updateQudaoAdCreateTaskById($id, $arrFields) {
        $arrConds = ['id' => $id];
        return $this->updateQudaoAdCreateTask($arrConds, $arrFields);
    }

    //软删除
    public function deleteQudaoAdCreateTask($arrConds = null)
    {
        $arrFields = ['deleted' => self::DELETE_DEL];
        return $this->updateQudaoAdCreateTask($arrConds, $arrFields);
    }

    //获取信息
    public function getQudaoAdCreateTaskInfoById($id, $arrFields = array(), $arrConds = null)
    {
        $arrConds['id'] = $id;

        if (!$arrFields) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrConds = ['id' => $id];
        $result = $this->objDaoQudaoAdCreateTask->getRecordByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoAdCreateTask', 'getQudaoAdCreateTaskInfoById', '查询数据库信息失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //获取列表
    public function getQudaoAdCreateTaskList($arrConds = null, $arrFields = array(), $arrAppends = null) {
        if (!$arrFields){
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $result = $this->objDaoQudaoAdCreateTask->getListByConds($arrConds, $arrFields, null, $arrAppends);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoAdCreateTask', 'getQudaoAdCreateTaskList', '查询数据库列表失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds, 'appends' => $arrAppends]));
            return false;
        }
        return $result;
    }

    //获取总数
    public function getQudaoAdCreateTaskTotal($arrConds = null)
    {
        $res = $this->objDaoQudaoAdCreateTask->getCntByConds($arrConds);
        if ($res === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoAdCreateTask', 'getQudaoAdCreateTaskTotal', '查询数据库总数失败', json_encode(['conds' => $arrConds]));
        }
        return $res;
    }

    /**
     * 新增任务以及草稿
     * @param $task array 任务详情
     * @param $drafts array 草稿数组 [
     *      ['params' => [], 'children' => []]
     * ]
     * @return int|bool 任务id
     */
    public function addTaskAndDraft($task, $drafts)
    {
        $this->objDaoQudaoAdCreateTask->startTransaction();
        $taskId = $this->addQudaoAdCreateTask($task);
        if ($taskId === false) {
            $this->objDaoQudaoAdCreateTask->rollback();
            return false;
        }
        foreach ($drafts as $lv1) {
            $ret = $this->addTask($taskId, $lv1);
            if ($ret === false) {
                $this->objDaoQudaoAdCreateTask->rollback();
                return false;
            }
        }
        $this->objDaoQudaoAdCreateTask->commit();
        return $taskId;
    }

    /**
     * @param $task
     * @param $drafts
     * @return bool
     */
    public function addTaskDrafts($insertTask, $updateTask, $insertDrafts, $updateDrafts)
    {
        $this->objDaoQudaoAdCreateTask->startTransaction();
        try {

            if (!empty($insertTask)) {
                $taskRet = $this->addQudaoAdCreateTask($insertTask);
                if (empty($taskRet)) {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, "插入任务失败" . json_encode($insertTask));
                }
            }
            if (!empty($updateTask)) {
                $taskRet = $this->updateQudaoAdCreateTaskById($updateTask['id'], $updateTask);
                if (empty($taskRet)) {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, "更新任务失败" . json_encode($insertTask));

                }
            }
            foreach ($insertDrafts as $lv) {
                $draftRet = $this->objDsQudaoAdCreateDraft->addQudaoAdCreateDraft($lv);
                if (empty($draftRet)) {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, "插入草稿失败".json_encode($lv));
                }
            }
            foreach ($updateDrafts as $lvInfo) {
                $draftRet = $this->objDsQudaoAdCreateDraft->updateQudaoAdCreateDraftById($lvInfo['id'], $lvInfo);
                if (empty($draftRet)) {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, "更新草稿失败".json_encode($lvInfo));
                }
            }
        } catch (Exception $e) {
            $this->objDaoQudaoAdCreateTask->rollback();
            throw $e;
        }
        $this->objDaoQudaoAdCreateTask->commit();
        return true;
    }

    protected function addTask($taskId, $draft, $lv1 = 0, $lv2 = 0, $level = 1)
    {
        $draft['params']['taskId']  = $taskId;
        if (empty($draft['params']['level'])) {
            $draft['params']['level']   = $level;
        }
        $draft['params']['lv1']     = $lv1;
        $draft['params']['lv2']     = $lv2;
        $id = $this->objDsQudaoAdCreateDraft->addQudaoAdCreateDraft($draft['params']);
        if ($id === false) {
            return false;
        }
        if (empty($draft['children'])) {
            return true;
        }
        $level++;
        if (empty($lv1)) {
            $lv1 = $id;
        } else {
            if (empty($lv2)) {
                $lv2 = $id;
            }
        }

        foreach ($draft['children'] as $val) {
            $ret = $this->addTask($taskId, $val, $lv1, $lv2, $level);
            if ($ret === false) {
                return false;
            }
        }
        return true;
    }
}