<?php
/**
 *  Qdlib_Ds_QudaoReport
 * 文件描述
 * Created on 2019/7/31  15:44
 * Create by xux<PERSON><PERSON>@zuoyebang.com
 */


class Qdlib_Ds_QudaoSkuStock
{
    const ALL_FIELDS = 'id,dt,type,allSellCnt,sellCnt,grade,skuId,leftCnt,courseId,skuDate,skuTime,createTime,updateTime';
    private $objDaoSkuStock;

    public function __construct()
    {
        $this->objDaoSkuStock = new Qdlib_Dao_SkuStock();
    }

    public function updateReportStatus($arrConds, $arrInput, $arrAppends = null)
    {
        if (empty($arrInput)) {
            return false;
        }
        $res = $this->objDaoSkuStock->updateByConds($arrConds, $arrInput, null, $arrAppends);
        if ($res === false) {
            Qdlib_Util_Log::warning('activity', 'Qdlib_Ds_QudaoSkuStock', 'updateReportStatus', 'db error', json_encode($arrInput));
        }

        return $res;
    }


    public function addRecord($arrParams)
    {
        if (empty($arrParams)) {
            return false;
        }

        $res = $this->objDaoSkuStock->insertRecords($arrParams);
        if (false === $res) {
            Qdlib_Util_Log::warning('activity', 'Qdlib_Ds_QudaoSkuStock', 'addRecord', 'db error',
                json_encode($arrParams));
            return false;
        }

        return $res;
    }

    public function getAllSkuInfoList($where, $field = [], $arrAppends = null)
    {
        if (!$field) {
            $field = explode(',', self::ALL_FIELDS);
        }

        $res = $this->objDaoSkuStock->getListByConds($where, $field, null, $arrAppends);
        if ($res === false) {
            $sql = $this->objDaoSkuStock->getLastSQL();
            $where = json_encode($where);
            Qdlib_Util_Log::warning('activity', 'Qdlib_Ds_QudaoSkuStock', 'getAllReportList', "sql:{$sql}", "{$where}");
        }

        return $res;
    }
}