<?php
/**
 * file FinanceBase.php.
 * author: <EMAIL>
 * Date: 2020/3/16
 * brief:
 */

abstract class Qdlib_Ds_Finance_FinanceBase
{
    protected $_tableDaoObj = null;

    public function __construct()
    {
    }

    abstract protected function getFormatDbData($arrParams);

    public function updateData($id, $arrParams)
    {
        $arrConds = array(
            'id' => intval($id),
        );
        $arrFields = $arrParams;
        if (isset($arrParams['updateTime']) && !empty($arrParams['updateTime'])) {
            $arrFields['updateTime'] = $arrParams['updateTime'];
        }else{
            $arrFields['updateTime'] = date("Y-m-d H:i:s");
        }
        $calledClass = get_called_class();
        if(in_array($calledClass,['Qdlib_Ds_Finance_FinanceProjectLog','Qdlib_Ds_Finance_FinanceLog','Qdlib_Ds_Finance_FinanceAccountLog'])){
            $arrFields['updateTime'] = time();
        }

        //$this->dbReconnect();
        $res = $this->_tableDaoObj->updateByConds($arrConds, $arrFields);
        if (false === $res) {
            Qdlib_Util_Log::warning('finance', 'Qdlib_Ds_Finance_FinanceBase', 'updateData', 'db error',
                json_encode($arrFields));
            return false;
        }
        return $res;
    }

    public function getDbAllFields () {
        return $this->_tableDaoObj->getAllFields();
    }

    public function getCnt($where) {
        //$this->dbReconnect();
        $res = $this->_tableDaoObj->getCntByConds($where);
        if ($res === false) {
            $sql = $this->_tableDaoObj->getLastSQL();
            $where = json_encode($where);
            Qdlib_Util_Log::warning('finance', 'Qdlib_Ds_Finance_FinanceBase', 'getList', "sql:{$sql}", "{$where}");
        }
        return $res;
    }

    public function getList($where, $field = [], $arrAppends = null, $strIndex = null)
    {
        if (!$field) {
            $field = $this->_tableDaoObj->getAllFields();
        }
        //$this->dbReconnect();
        $res = $this->_tableDaoObj->getListByConds($where, $field, null, $arrAppends, $strIndex);
        if ($res === false) {
            $sql = $this->_tableDaoObj->getLastSQL();
            $where = json_encode($where);
            Qdlib_Util_Log::warning('finance', 'Qdlib_Ds_Finance_FinanceBase', 'getList', "sql:{$sql}", "{$where}");
        }
        return $res;
    }

    public function get($where, $field = [], $arrAppends = null, $strIndex = null)
    {
        if (!$field) {
            $field = $this->_tableDaoObj->getAllFields();
        }
        //$this->dbReconnect();
        $res = $this->_tableDaoObj->getRecordByConds($where, $field, null, $arrAppends, $strIndex);
        if ($res === false) {
            $sql = $this->_tableDaoObj->getLastSQL();
            $where = json_encode($where);
            Qdlib_Util_Log::warning('finance', 'Qdlib_Ds_Finance_FinanceBase', 'getList', "sql:{$sql}", "{$where}");
        }
        return $res;
    }

    public function addData($arrParams)
    {
        if (empty($arrParams)) {
            Qdlib_Util_Log::warning('finance', 'Qdlib_Ds_Finance_FinanceBase', 'addData', "param: empty");
            return false;
        }
        $arrFields = $this->getFormatDbData($arrParams);

        if (isset($arrParams['createTime']) && !empty($arrParams['createTime'])) {
            $arrFields['createTime'] = $arrParams['createTime'];
        }else{
            $arrFields['createTime'] = date("Y-m-d H:i:s");
        }
        if (isset($arrParams['updateTime']) && !empty($arrParams['updateTime'])) {
            $arrFields['updateTime'] = $arrParams['updateTime'];
        }else{
            $arrFields['updateTime'] = date("Y-m-d H:i:s");
        }

        $calledClass = get_called_class();
        if(in_array($calledClass,['Qdlib_Ds_Finance_FinanceProjectLog','Qdlib_Ds_Finance_FinanceLog','Qdlib_Ds_Finance_FinanceAccountLog'])){
            $arrFields['createTime'] = time();
            $arrFields['updateTime'] = time();
        }

        //$this->dbReconnect();
        $res = $this->_tableDaoObj->insertRecords($arrFields);
        if (false === $res) {
            Qdlib_Util_Log::warning('finance', 'Qdlib_Ds_Finance_FinanceBase', 'addData', 'db error',
                json_encode($arrFields));
            return false;
        }
        return $res;
    }

    public function updateReportStatus($ids = array())
    {
        if (count($ids) < 1) {
            return false;
        }
        $ids = array_map('intval', $ids);
        $arrConds = array(
            "id in (" . implode(',', $ids) . ")",
        );
        $arrFields = array(
            'report_status' => time(),
        );
        //$this->dbReconnect();
        $res = $this->_tableDaoObj->updateByConds($arrConds, $arrFields);
        if (false === $res) {
            Qdlib_Util_Log::warning('finance', 'Qdlib_Ds_Finance_FinanceBase', 'updateReportStatus', 'upfail', json_encode($arrConds));
            return false;
        }
        return $res;
    }

    protected function dbReconnect()
    {
        if (method_exists($this->_tableDaoObj, 'reconnect')) {
            $this->_tableDaoObj->reconnect();
        }
    }

}
