<?php
/**
 * file ChanelCost.php.
 * author: <EMAIL>
 * Date: 2020/3/16
 * brief: 财务sk_channel_cost数据表
 */

class Qdlib_Ds_Finance_ChannelCost extends Qdlib_Ds_Finance_FinanceBase
{

    const DAOOBJ = 'Qdlib_Dao_Finance_ChannelCost';

    public function __construct()
    {
        $daoObj = self::DAOOBJ;
        $this->_tableDaoObj = new $daoObj();
    }

    protected function getFormatDbData($arrParams) {
        $arrFields = array(
            'channelId'         => isset($arrParams['channelId']) ? intval($arrParams['channelId']) : 0,
            'channelName'       => isset($arrParams['channelName']) ? strval($arrParams['channelName']) : '',
            'sourceId'          => isset($arrParams['sourceId']) ? intval($arrParams['sourceId']) : 0,
            'sourceName'        => isset($arrParams['sourceName']) ? strval($arrParams['sourceName']) : '',
            'userId'            => isset($arrParams['userId']) ? intval($arrParams['userId']) : 0,
            'userName'          => isset($arrParams['userName']) ? strval($arrParams['userName']) : '',
            'costDate'          => isset($arrParams['costDate']) ? strval($arrParams['costDate']) : '',
            'costAmount'        => isset($arrParams['costAmount']) ? intval($arrParams['costAmount']) : 0,
            'costAmountCash'    => isset($arrParams['costAmountCash']) ? intval($arrParams['costAmountCash']) : 0,
            'costAmountApi'     => isset($arrParams['costAmountApi']) ? intval($arrParams['costAmountApi']) : 0,
            'costAmountApiCash' => isset($arrParams['costAmountApiCash']) ? intval($arrParams['costAmountApiCash']) : 0,
            'business'          => isset($arrParams['business']) ? strval($arrParams['business']) : '',
            'launch'            => isset($arrParams['launch']) ? strval($arrParams['launch']) : '',
            'channel'           => isset($arrParams['channel']) ? strval($arrParams['channel']) : '',
            'agent'             => isset($arrParams['agent']) ? strval($arrParams['agent']) : '',
            'purpose'           => isset($arrParams['purpose']) ? strval($arrParams['purpose']) : '',
            'project'           => isset($arrParams['project']) ? strval($arrParams['project']) : '',
        );
        if (isset($arrParams['id']) && !empty($arrParams['id'])) {
            $arrFields['id'] = $arrParams['id'];
        }
        if (isset($arrParams['createTime']) && !empty($arrParams['createTime'])) {
            $arrFields['createTime'] = $arrParams['createTime'];
        }
        if (isset($arrParams['updateTime']) && !empty($arrParams['updateTime'])) {
            $arrFields['updateTime'] = $arrParams['updateTime'];
        }
        return $arrFields;
    }
}
