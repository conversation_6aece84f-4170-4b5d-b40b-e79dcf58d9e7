<?php
/**
 * file FinanceAccountLog.php.
 * author: <EMAIL>
 * Date: 2020/3/16
 * brief: 财务修改log表 ds
 */

class Qdlib_Ds_Finance_FinanceAccountLog extends Qdlib_Ds_Finance_FinanceBase
{

    const DAOOBJ = 'Qdlib_Dao_Finance_FinanceAccountLog';

    public function __construct()
    {
        $daoObj = self::DAOOBJ;
        $this->_tableDaoObj = new $daoObj();
    }

    protected function getFormatDbData($arrParams) {
        $arrFields = array(
            'dt'                => isset($arrParams['dt']) ? intval($arrParams['dt']) : 0,
            'channel'           => isset($arrParams['channel']) ? strval($arrParams['channel']) : '',
            'account'           => isset($arrParams['account']) ? strval($arrParams['account']) : '',
            'cost'              => isset($arrParams['cost']) ? intval($arrParams['cost']) : 0,
        );
        return $arrFields;
    }
}