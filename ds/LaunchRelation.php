<?php

class Qdlib_Ds_LaunchRelation
{
    protected $dbLaunchRelation;

    public function __construct()
    {   
        $this->dbLaunchRelation = new Qdlib_Dao_LaunchRelation();
    }

    public function getListByConds($conds, $options = NULL, $appends = NULL)
    {
        $fields = $this->dbLaunchRelation->getAllFields();
        $ret = $this->dbLaunchRelation->getListByConds($conds, $fields, $options, $appends);
        if ($ret === false) {
            Bd_Log::warning("Error:[".__CLASS__.";".__FUNCTION__."], Detail:[param: " . json_encode(func_get_args()) . "]");
            return false;
        }
        return $ret;
    }
}
