<?php
/**
 * Created by Growth AutoCode.
 * User: <EMAIL>
 * Date: 2020/07/29
 * Time: 09:43
 */
class Qdlib_Ds_AccountUname
{
    const ALL_FIELDS = 'id,aid,uname';
    const DELETE_DEL = 1;

    public $objDaoAccountUname;

    public function __construct()
    {
        $this->objDaoAccountUname = new Qdlib_Dao_AccountUname();
    }

    //新增
    public function addAccountUname($arrParams) {
        $arrFields = array(
            'id'    => isset($arrParams['id']) ? intval($arrParams['id']) : 0,
            'aid'   => isset($arrParams['aid']) ? intval($arrParams['aid']) : 0,
            'uname' => isset($arrParams['uname']) ? strval($arrParams['uname']) : '',

        );
        $result = $this->objDaoAccountUname->insertRecords($arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'AccountUname', 'addAccountUname', '数据库插入失败', json_encode($arrFields));
            return false;
        }
        $id = $this->objDaoAccountUname->getInsertId();
        return $id;
    }

    //编辑
    public function updateAccountUname($arrConds = null, $arrParams) {
        $arrAllFields = explode(',',self::ALL_FIELDS);
        foreach($arrParams as $key => $value){
            if(!in_array($key, $arrAllFields)){
                continue;
            }
            $arrFields[$key] = $value;
        }
        $result = $this->objDaoAccountUname->updateByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'AccountUname', 'updateAccountUname', '数据库更新失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //通过id编辑
    public function updateAccountUnameById($id, $arrFields) {
        $arrConds = ['id' => $id];
        return $this->updateAccountUname($arrConds, $arrFields);
    }

    //删除
    public function deleteAccountUname($arrConds = null)
    {
        $result = $this->objDaoAccountUname->deleteByConds($arrConds);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'AccountUname', 'deleteAccountUname', '数据库删除失败', json_encode(['conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //获取信息
    public function getAccountUnameInfoById($id, $arrFields = array(), $arrConds = null)
    {
        $arrConds['id'] = $id;

        if (!$arrFields) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrConds = ['id' => $id];
        $result = $this->objDaoAccountUname->getRecordByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'AccountUname', 'getAccountUnameInfoById', '查询数据库信息失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //获取列表
    public function getAccountUnameList($arrConds = null, $arrFields = array(), $arrAppends = null) {
        if (!$arrFields){
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $result = $this->objDaoAccountUname->getListByConds($arrConds, $arrFields, null, $arrAppends);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'AccountUname', 'getAccountUnameList', '查询数据库列表失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds, 'appends' => $arrAppends]));
            return false;
        }
        return $result;
    }

    //获取总数
    public function getAccountUnameTotal($arrConds = null)
    {
        $res = $this->objDaoAccountUname->getCntByConds($arrConds);
        if ($res === false) {
            Qdlib_Util_Log::warning('Service_Data', 'AccountUname', 'getAccountUnameTotal', '查询数据库总数失败', json_encode(['conds' => $arrConds]));
        }
        return $res;
    }

    // 批量新增
    public function multiAddAccountUname($fields, $arr, $onDup = null)
    {
        if (!is_array($arr[0])) {
            throw new Qdlib_Common_Exception(Qdlib_Common_ExceptionCodes::PARAM_ERROR, '批量新增参数错误');
        }
        $result = $this->objDaoAccountUname->multiInsert($fields, $arr, $onDup);
        $sql = $this->objDaoAccountUname->getLastSQL();
        if (false === $result) {
            Bd_Log::warning("Error:[AccountUname multiAddAclRoleAuth error], Detail:[param: " . json_encode($arr) . ", sql: $sql]");
            return false;
        }
        return true;
    }
}