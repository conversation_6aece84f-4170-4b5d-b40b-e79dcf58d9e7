<?php
/**
 *  Service_Data_QudaoOrderClue
 * 文件描述
 * Created on 2018/12/21  16:33
 * Create by xuxin<PERSON>@zuoyebang.com
 */

class Qdlib_Ds_QudaoOrderLog
{
    const FIELD = 'id,commitTime,uid,channel,orderId,subOrderId,clickId,orifrom,createTime,dataSource,offset,extData';

    private $_objDaoQudaoLog;

    public function __construct()
    {
        $this->_objDaoQudaoLog = new Qdlib_Dao_QudaoOrderLog();
    }

    public function addQudaoOrderLog($arrParams)
    {
        $this->_objDaoQudaoLog->reconnect();
        $ret = $this->_objDaoQudaoLog->insertRecords($arrParams);
        if ($ret === false) {
            $sql = $this->_objDaoQudaoLog->getLastSQL();
            $arrParams = json_encode($arrParams);
            Qdlib_Util_Log::error('report', 'Service_Data_QudaoOrderLog', 'addQudaoOrderLog', "sql:{$sql}", "{$arrParams}");
            return false;
        }

        return $ret;
    }

    public function getQudaoOrderLogOne($where, $field = [])
    {
        if (empty($field)) {
            $field = explode(',', self::FIELD);
        }

        $res = $this->_objDaoQudaoLog->getRecordByConds($where, $field);
        if ($res === false) {
            $sql = $this->_objDaoQudaoLog->getLastSQL();
            $where = json_encode($where);
            Qdlib_Util_Log::warning('report', 'Service_Data_QudaoOrderLog', 'getQudaoOrderLogOne', "sql:{$sql}", "{$where}");
            return false;
        }

        return $res;
    }


    public function getOrderList($where, $field = [], $arrAppends = null)
    {
        if (empty($field)) {
            $field = explode(',', self::FIELD);
        }

        $res = $this->_objDaoQudaoLog->getListByConds($where, $field, null, $arrAppends);
        if ($res === false) {
            $sql = $this->_objDaoQudaoLog->getLastSQL();
            $where = json_encode($where);
            Qdlib_Util_Log::warning('report', 'Service_Data_QudaoOrderLog', 'getOrderList', "sql:{$sql}", "{$where}");
        }

        return $res;
    }


}