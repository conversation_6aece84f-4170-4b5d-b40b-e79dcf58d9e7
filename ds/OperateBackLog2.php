<?php
/***************************************************************************
 *
 * Copyright (c) 2015 zybang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file OperateBackLog.php
 * <AUTHOR>
 * @date 2018/06/06 19:11:40
 * @brief 运营后台操作日志
 *
 **/
class Oplib_Ds_OperateBackLog2 {

    // 定义所有mid值
    const MID_TEHUIKE = 1; // 特惠课

	const ALL_FIELDS='id,mId,adId,type,status,createTime,operatorUid,extData,opUname,opEmail';

	private $objDaoOperateBackLog;

	private $orderBy = array('create_time');

    //定义type类型
    const TYPE_ADD = 1;
    const TYPE_EDIT= 2;
    const TYPE_ONLINE = 3;
    const TYPE_OFFLINE = 4;
    const TYPE_DEL     = 5;
    const TYPE_SORT    = 6;
    const TYPE_HIDE    = 7;

    //评论状态
    const TYPE_PASS         = 9;
    const TYPE_NOTPASS      = 10;
    static $TYPE_ARRAY  = array(
        self::TYPE_ADD  => '创建',
        self::TYPE_EDIT => '修改',
        self::TYPE_ONLINE => '上线',
        self::TYPE_OFFLINE=> '下线',
        self::TYPE_DEL    => '删除',
        self::TYPE_SORT   => '排序',
        self::TYPE_HIDE   => '隐藏',
        self::TYPE_PASS         => '审核通过',
        self::TYPE_NOTPASS      => '审核不通过',
    );
    //状态
    const STATUS_OK     = 0;
    const STATUS_DELETE = 1;
    static $STATUS_ARRAY = array(
        self::STATUS_OK     => '有效',
        self::STATUS_DELETE => '无效',
    );

	/**
	 * 构造函数
	 *
	 */
	public function __construct(){
		$this->objDaoOperateBackLog = new Oplib_Dao_OperateBackLog2();
	}

	/**
	 * 新增记录
	 *
	 * @param  mix  $arrParams 属性
	 * @return bool true/false
	 */

    public function addOperateBackLog($arrParams){
        if(intval($arrParams['mId']) <= 0 || intval($arrParams['type']) <= 0){
            Bd_Log::warning("Error:[param error],Detail:[".json_encode($arrParams)."]");
            return false;
        }
        $arrFields = array(
            'mId'       =>isset($arrParams['mId']) ? intval($arrParams['mId']):0,
            'adId'        =>isset($arrParams['adId']) ? intval($arrParams['adId']):0,
            'type'        =>isset($arrParams['type']) ? intval($arrParams['type']):0,
            'operatorUid' =>isset($arrParams['operatorUid']) ? intval($arrParams['operatorUid']):0,
            'createTime'  =>time(),
            'extData'     =>isset($arrParams['extData']) ? json_encode($arrParams['extData']):'[]',
            'opEmail'     =>strval($arrParams['opEmail']),
            'opUname'     =>strval($arrParams['opUname']),
        );
        $ret = $this->objDaoOperateBackLog->insertRecords($arrFields);
        return $ret;
    }

	/**
	 * 获取数量
	 * @param $arrConds
	 * @return false|int
	 */
	public function getCntBymId($mId){
        if(intval($mId) <= 0){
            Bd_Log::warning("param error Detail:mId:$mId");
        }
        $arrConds = array(
            'mId' => intval($mId),
            'status'=> self::STATUS_OK,
        );
		return $this->objDaoOperateBackLog->getCntByConds($arrConds);
	}

	/**
	 * @param array $arrConds
	 * @param array $arrFields
	 * @param string $order
	 * @param string $by
	 * @param int $offset
	 * @param int $limit
	 * @return list|false
	 */
	public function getListBymId($mId,$arrFields = array(),$order = '',$by = '',$offset = 0,$limit = 0){
        if(intval($mId) <= 0){
            Bd_Log::warning("param error Detail:mId:$mId");
        }
		if(empty($arrFields)){
			$arrFields = explode(',',self::ALL_FIELDS);
		}
        $arrConds = array(
            'mId' => intval($mId),
            'status'=> self::STATUS_OK,
        );

		$orderBy = '';
		if($order != '' && in_array($order,$this->orderBy)){
			$orderBy .= 'order by '. $order . ' ';
			$orderBy .= ($by == 'desc') ? 'desc' : 'asc';
		}
		if($offset >= 0 && $limit > 0 ){
			$orderBy .= " limit $offset,$limit";
		}else if($limit > 0){
			$orderBy .= " limit $limit";
		}
		$arrAppends = ($orderBy != '')? array($orderBy) : null;
		$ret = $this->objDaoOperateBackLog->getListByConds($arrConds,$arrFields,NULL,$arrAppends);
		return $ret;
	}
}
