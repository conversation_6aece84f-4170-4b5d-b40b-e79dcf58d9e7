<?php

class Qdlib_Ds_SeriesSkuRelations extends Qdlib_Ds_AutoLog
{
    const ALL_FIELDS = 'id,seriesId,name,channelId,thirdSkuId,lastfrom,startTime,stopTime,price,quantity,validQuantity,status,batchStatus,exportStatus,operator,createTime,updateTime';
    const LOG_SYSTEM = 'cdkey';
    const LOG_SERVICE_NAME = __CLASS__;

    const STATUS_NEW = 1;
    const STATUS_OLD = 2;
    public static $statusMap = [
        self::STATUS_NEW => '生效中',
        self::STATUS_OLD => '已失效',
    ];

    const EXPORT_STATUS_NONE = 1;
    const EXPORT_STATUS_NEW = 2;
    const EXPORT_STATUS_PROCESSING = 3;
    const EXPORT_STATUS_SUCCESS = 4;
    const EXPORT_STATUS_FAIL = 5;
    public static $exportStatusMap = [
        self::EXPORT_STATUS_NONE => '不可导',
        self::EXPORT_STATUS_NEW => '未导出',
        self::EXPORT_STATUS_PROCESSING => '导出中',
        self::EXPORT_STATUS_SUCCESS => '导出成功',
        self::EXPORT_STATUS_FAIL => '导出失败',
    ];

    public function __construct()
    {
        parent::__construct(new Qdlib_Dao_SeriesSkuRelations());
    }

    public function addRecord($fields)
    {
        return $this->insertRecords($fields, __FUNCTION__);
    }

    public function updateRecord($id, $fields)
    {
        return $this->updateByConds(['id' => $id], $fields, __FUNCTION__);
    }

    public function getRecord($conditions, $fields = null, $appends = null)
    {
        return $this->getRecordByConds($conditions, $fields, __FUNCTION__, $appends);
    }

    public function getRecords($conditions, $fields = null, $appends = null)
    {
        return $this->getListByConds($conditions, $fields, __FUNCTION__, $appends);
    }

    public function getCount($conditions)
    {
        return $this->getCntByConds($conditions, __FUNCTION__);
    }

    /**
     * 锁定并获取记录
     * 将可导出记录的导出状态标记为导出中状态
     * 导出成功状态的记录返回true
     * 其他不可导出情况返回false
     * @param $id
     * @return bool|array
     */
    public function lockAndGetRecord($id)
    {
        // 开启事务
        if (false === $this->dao->startTransaction()) {
            return false;
        }
        // 锁定记录
        $record = $this->getRecord(['id' => $id], null, ['for update']);
        if (empty($record)) {
            $this->dao->rollback();
            return false;
        }
        // 已经导出成功的记录直接返回true
        if (self::EXPORT_STATUS_SUCCESS === $record['exportStatus']) {
            $this->dao->commit();
            return true;
        }
        // 是否可导出
        if ($this->isExportable($record)) {
            // 标记为导出中
            if (false === $this->updateRecord($id, ['exportStatus' => self::EXPORT_STATUS_PROCESSING])) {
                $this->dao->rollback();
                return false;
            }
            // 提交事务
            if (false === $this->dao->commit()) {
                return false;
            }
            $record['exportStatus'] = self::EXPORT_STATUS_PROCESSING;
            return $record;
        }
        // 锁定失败
        $this->dao->rollback();
        return false;
    }

    /**
     * 记录的导出状态是否可导出
     * 导出状态为未导出和导出失败状态的可导出 导出状态为导出中但是超过1小时的记录可导出
     * @param $record
     * @return bool
     */
    public function isExportable($record)
    {
        return self::EXPORT_STATUS_NEW === $record['exportStatus'] || self::EXPORT_STATUS_FAIL === $record['exportStatus'] || (self::EXPORT_STATUS_PROCESSING === $record['exportStatus'] && time() - 3600 > $record['updateTime']);
    }

    /**
     * 添加一条兑换码绑定关系记录
     * 确保添加后同一个渠道下的有效状态的记录中thirdSkuId唯一
     * @param $fields
     * @return false|int
     */
    public function createSeriesRecord($fields)
    {
        // 开启事务
        if (false === $this->dao->startTransaction()) {
            return false;
        }
        // 锁定记录
        $record = $this->getRecord(['channelId' => $fields['channelId'], 'thirdSkuId' => $fields['thirdSkuId']], ['channelId'], ['for update']);
        if (false === $record || !empty($record)) {
            $this->dao->rollback();
            return false;
        }
        // 新增记录
        $id = $this->addRecord($fields);
        if (false === $id) {
            $this->dao->rollback();
            return false;
        }
        // 提交事务
        if (false === $this->dao->commit()) {
            return false;
        }
        return $id;
    }

    /**
     * 编辑一条兑换码绑定关系记录
     * 原记录置为已失效状态 添加一条新记录
     * 确保添加后同一个渠道下的有效状态的记录中thirdSkuId唯一
     * @param $fields
     * @return false|int
     */
    public function editSeriesRecord($id, $fields)
    {
        // 开启事务
        if (false === $this->dao->startTransaction()) {
            return false;
        }
        // 锁定记录
        $records = $this->getRecords(['channelId' => $fields['channelId'], 'thirdSkuId' => $fields['thirdSkuId']], ['channelId'], ['for update']);
        if (empty($records)) {
            $this->dao->rollback();
            return false;
        }
        // 原记录置为已失效
        if (false === $this->updateRecord($id, ['status' => self::STATUS_OLD])) {
            $this->dao->rollback();
            return false;
        }
        // 新增记录
        $id = $this->addRecord($fields);
        if (false === $id) {
            $this->dao->rollback();
            return false;
        }
        // 提交事务
        if (false === $this->dao->commit()) {
            return false;
        }
        return $id;
    }
}