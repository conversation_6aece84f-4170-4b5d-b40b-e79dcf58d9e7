<?php
/**
 * Created by Growth AutoCode.
 * User: z<PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * Date: 2020/09/17
 * Time: 15:46
 */
class Qdlib_Ds_Stock_TeamSkuStockShare
{
    const ALL_FIELDS = 'id,teamId,skuId,shareTeamId,createTime,deleted';
    const DELETE_DEL = 1;

    private $_objDaoTeamSkuStockShare;

    public function __construct()
    {
        $this->_objDaoTeamSkuStockShare = new Qdlib_Dao_Stock_TeamSkuStockShare();
    }

    //新增
    public function addTeamSkuStockShare($arrParams) {
        $arrFields = array(
            'id'          => isset($arrParams['id']) ? intval($arrParams['id']) : 0,
            'teamId'      => isset($arrParams['teamId']) ? strval($arrParams['teamId']) : '',
            'skuId'       => isset($arrParams['skuId']) ? intval($arrParams['skuId']) : 0,
            'shareTeamId' => isset($arrParams['shareTeamId']) ? strval($arrParams['shareTeamId']) : '',
            'createTime'  => time(),

        );
        $result = $this->_objDaoTeamSkuStockShare->insertRecords($arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'TeamSkuStockShare', 'addTeamSkuStockShare', '数据库插入失败', json_encode($arrFields));
            return false;
        }
        $id = $this->_objDaoTeamSkuStockShare->getInsertId();
        return $id;
    }

    //编辑
    public function updateTeamSkuStockShare($arrConds = null, $arrParams) {
        $arrAllFields = explode(',',self::ALL_FIELDS);
        foreach($arrParams as $key => $value){
            if(!in_array($key, $arrAllFields)){
                continue;
            }
            $arrFields[$key] = $value;
        }
        $result = $this->_objDaoTeamSkuStockShare->updateByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'TeamSkuStockShare', 'updateTeamSkuStockShare', '数据库更新失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //通过id编辑
    public function updateTeamSkuStockShareById($id, $arrFields) {
        $arrConds = ['id' => $id];
        return $this->updateTeamSkuStockShare($arrConds, $arrFields);
    }

    //软删除
    public function deleteTeamSkuStockShare($arrConds = null)
    {
        if (empty($arrConds)) {
            return false;
        }

        $result = $this->_objDaoTeamSkuStockShare->deleteByConds($arrConds);
        if ($result === false) {
            Qdlib_Util_Log::error('skuid', 'TeamSkuStock', 'deleteTeamSkuStockShare', '数据库更新失败', json_encode(['conds' => $arrConds]));
            return false;
        }

        return $result;
        //return $this->updateTeamSkuStockShare($arrConds, $arrFields);
    }

    //获取信息
    public function getTeamSkuStockShareInfoById($id, $arrFields = array(), $arrConds = null)
    {
        $arrConds['id'] = $id;

        if (!$arrFields) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrConds = ['id' => $id];
        $result = $this->_objDaoTeamSkuStockShare->getRecordByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'TeamSkuStockShare', 'getTeamSkuStockShareInfoById', '查询数据库信息失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }
    //获取信息
    public function getTeamSkuStockShareListBySkuId($skuId, $arrFields = array(), $arrConds = null,$arrAppends = null)
    {
        $arrConds['skuId'] = $skuId;

        if (!$arrFields) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrConds['deleted'] = 0;
        $result = $this->_objDaoTeamSkuStockShare->getListByConds($arrConds, $arrFields, null, $arrAppends);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'TeamSkuStockShare', 'getTeamSkuStockShareInfoById', '查询数据库信息失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }
    //获取列表
    public function getTeamSkuStockShareList($arrConds = null, $arrFields = array(), $arrAppends = null) {
        if (!$arrFields){
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $result = $this->_objDaoTeamSkuStockShare->getListByConds($arrConds, $arrFields, null, $arrAppends);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'TeamSkuStockShare', 'getTeamSkuStockShareList', '查询数据库列表失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds, 'appends' => $arrAppends]));
            return false;
        }
        return $result;
    }

    //获取总数
    public function getTeamSkuStockShareTotal($arrConds = null)
    {
        $res = $this->_objDaoTeamSkuStockShare->getCntByConds($arrConds);
        if ($res === false) {
            Qdlib_Util_Log::warning('Service_Data', 'TeamSkuStockShare', 'getTeamSkuStockShareTotal', '查询数据库总数失败', json_encode(['conds' => $arrConds]));
        }
        return $res;
    }
}