<?php
/**
 * @file LoginUserClueLog.php
 * <AUTHOR>
 * @version
 * @brief
 *
 **/


class Qdlib_Ds_LoginUserClueLog
{
    const ALL_FIELDS = 'id,uid,clickId,orifrom,actType,plat,loginTime,createTime,dataSource,offset,extData,callbackTime,callbackStatus';

    const NMQ_COOKIE_SOURCE = 'nmq_cookie';

    private $_objDaoLoginUserClueLog;

    public function __construct()
    {
        $this->_objDaoLoginUserClueLog = new Qdlib_Dao_LoginUserClueLog();
    }

    public function addLoginUserClueLog($arrParams){
        $callbackStatus = 0;
        $callbackTime   =0;
        if($arrParams['dataSource'] == self::NMQ_COOKIE_SOURCE ){
            $callbackStatus = 100;
            $callbackTime   = time();
        }
        $arrFields = array(
            'uid'         => isset($arrParams['uid']) ? strval($arrParams['uid']) : 0,
            'clickId'     => isset($arrParams['clickId']) ? strval($arrParams['clickId']) : '',
            'orifrom'     => isset($arrParams['orifrom']) ? strval($arrParams['orifrom']) : '',
            'actType'     => isset($arrParams['actType']) ? strval($arrParams['actType']) : '',
            'plat'        => isset($arrParams['plat']) ? strval($arrParams['plat']) : '',
            'loginTime'   => isset($arrParams['loginTime']) ? intval($arrParams['loginTime']) : 0,
            'dataSource'  => isset($arrParams['dataSource']) ? strval($arrParams['dataSource']) : '',
            'offset'      => isset($arrParams['offset'])?intval($arrParams['offset']):0,
            'createTime'  => time(),
            'callbackTime'   => $callbackTime,
            'callbackStatus' => $callbackStatus,
            'extData'     => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '[]',
        );
        $this->_objDaoLoginUserClueLog->reconnect();
        $ret = $this->_objDaoLoginUserClueLog->insertRecords($arrFields);
        if($ret === false){
            Qdlib_Util_Log::error("clue","loginClue","addLoginUserClueLog","db error",json_encode($arrFields));
            return false;

        }
        $retId = $this->_objDaoLoginUserClueLog->getInsertId();
        return $retId;
    }
    public function updateCallbackStatusByid($id = 0){
        if(empty($id)){
            return false;
        }

        $uparrConds = array('id'=>$id);
        $uparrFields['callbackStatus'] = 1;
        $uparrFields['callbackTime'] = time();
        $this->_objDaoLoginUserClueLog->reconnect();
        $ret = $this->_objDaoLoginUserClueLog->updateByConds($uparrConds, $uparrFields);
        if (false === $ret) {
            Bd_Log::warning("Error:[updateByConds error], Detail:[param: " . json_encode($uparrConds) . "]");
            return false;
        }
        return true;
    }

    public function getLoginLogList($arrConds = null, $arrFields = array(), $arrAppends = null) {
        if (!$arrFields){
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $result = $this->_objDaoLoginUserClueLog->getListByConds($arrConds, $arrFields, null, $arrAppends);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'LoginLog', 'getLoginLogList', '查询数据库列表失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds, 'appends' => $arrAppends]));
            return false;
        }
        return $result;
    }
}