<?php
/**
 * @file QudaoReport.php
 * <AUTHOR>
 * @version
 * @brief
 *
 **/


class Qdlib_Ds_QudaoReport
{
    const ALL_FIELDS = 'id,uid,channel,account,clickId,orifrom,actionType,type,transformId,realTransformId,callbackStatus,callbackCount,callbackTime,callbackErrno,callbackMsg,actionTime,createTime,updateTime,deleted,extData';
    private $_objDaoQudaoReport;

    public function __construct()
    {
        $this->_objDaoQudaoReport = new Qdlib_Dao_QudaoReport();
    }

    /**
     * @Interface addReport
     * @param $arrParams
     * @author: <EMAIL>
     * @Time: 2018/12/20   10:13
     * @brief
     */
    public function addReport($arrParams)
    {
        if (empty($arrParams)) {
            Bd_Log::warning("Error:[addReport_Fields], Detail:[param: " . json_encode($arrParams) . "]");
            return false;
        }
        $arrFields = array(
            'uid'               => isset($arrParams['uid']) ? intval($arrParams['uid']) : 0,
            'channel'           => isset($arrParams['channel']) ? strval($arrParams['channel']) : '',
            'account'           => isset($arrParams['account']) ? strval($arrParams['account']) : '',
            'clickId'           => isset($arrParams['clickId']) ? strval($arrParams['clickId']) : '',
            'orifrom'           => isset($arrParams['orifrom']) ? strval($arrParams['orifrom']) : '',
            'actionType'        => isset($arrParams['actionType']) ? strval($arrParams['actionType']) : '',
            'type'              => isset($arrParams['type']) ? intval($arrParams['type']) : 0,
            'transformId'       => isset($arrParams['transformId']) ? strval($arrParams['transformId']) : '',
            'realTransformId'   => isset($arrParams['realTransformId']) ? strval($arrParams['realTransformId']) : '',
            'callbackStatus'    => isset($arrParams['callbackStatus']) ? intval($arrParams['callbackStatus']) : 0,
            'callbackCount'     => isset($arrParams['callbackCount']) ? intval($arrParams['callbackCount']) : 0,
            'callbackTime'      => isset($arrParams['callbackTime']) ? intval($arrParams['callbackTime']) : 0,
            'callbackErrno'     => isset($arrParams['callbackErrno']) ? intval($arrParams['callbackErrno']) : 0,
            'callbackMsg'       => isset($arrParams['callbackMsg']) ? strval($arrParams['callbackMsg']) : '',
            'actionTime'        => isset($arrParams['actionTime']) ? intval($arrParams['actionTime']) : time(),
            'createTime'        => isset($arrParams['createTime']) ? intval($arrParams['createTime']) : time(),
            'deleted'           => isset($arrParams['deleted']) ? intval($arrParams['deleted']) : 0,
            'extData'           => isset($arrParams['extData']) ? strval($arrParams['extData']) : json_encode([]),
        );
        $res = $this->_objDaoQudaoReport->insertRecords($arrFields);
        if (false === $res) {
            Qdlib_Util_Log::warning('report', 'Qdlib_Ds_QudaoReport', 'addReport', 'db error',
                json_encode($arrFields));
            return false;
        }
        return $res;
    }

    public function getReportByClickId($transformId){
        if(empty($transformId)){
            Bd_Log::warning("ServiceName[report] ServiceErrorMethod:[getReportByClickId] Error:[param error] ServiceDetail:[arrInput:".json_encode(['transformId'=>$transformId])."]");
            return false;
        }
        $arrConds = [
            'transformId' => $transformId,
        ];
        if(empty($arrFields)) {
            $arrFields = explode(',',Qdlib_Ds_QudaoReport::ALL_FIELDS);
        }
        $arrAppends = [
            "order by id desc",
            "limit 1"
        ];
        $res = $this->_objDaoQudaoReport->getRecordByConds($arrConds, $arrFields, null, $arrAppends);
        if($res===false){
            Bd_Log::warning("ServiceName[report] ServiceErrorMethod:[getReportByClickId] Error:[db error] ServiceDetail:[arrConds:".json_encode($arrConds)."]");
        }
        return $res;
    }

    public function getReportByRealId($realTransformId,$type){
        if(empty($realTransformId) || empty($type)){
            Bd_Log::warning("ServiceName[report] ServiceErrorMethod:[getReportByRealId] Error:[param error] ServiceDetail:[arrInput:".json_encode(['realTransformId'=>$realTransformId,'type'=>$type])."]");
            return false;
        }
        $arrConds = [
            'realTransformId' => $realTransformId,
            'type'            => $type,
        ];
        if(empty($arrFields)) {
            $arrFields = explode(',',Qdlib_Ds_QudaoReport::ALL_FIELDS);
        }
        $arrAppends = [
            "order by id desc",
            "limit 1"
        ];
        $res = $this->_objDaoQudaoReport->getRecordByConds($arrConds, $arrFields, null, $arrAppends);
        if($res===false){
            Bd_Log::warning("ServiceName[report] ServiceErrorMethod:[getReportByRealId] Error:[db error] ServiceDetail:[arrConds:".json_encode($arrConds)."]");
        }
        return $res;
    }

    public function getReportById($id){
        if(empty($id)){
            Bd_Log::warning("ServiceName[report] ServiceErrorMethod:[getReportById] Error:[param error] Level:[warning] ServiceDetail:[arrInput:".json_encode(['id'=>$id])."]");
            return false;
        }
        $arrConds = [
            'id' => $id,
        ];
        if(empty($arrFields)) {
            $arrFields = explode(',',Qdlib_Ds_QudaoReport::ALL_FIELDS);
        }
        $arrAppends = [
            "order by id desc",
            "limit 1"
        ];
        $res = $this->_objDaoQudaoReport->getRecordByConds($arrConds, $arrFields, null, $arrAppends);
        if($res===false){
            Bd_Log::warning("ServiceName[report] ServiceErrorMethod:[getReportById] Error:[db error] Level:[fatal] ServiceDetail:[arrConds:".json_encode($arrConds)."]");
        }
        return $res;
    }



    public function updateReportStatus($id,$arrInput){
        if(empty($arrInput)){
            Bd_Log::warning("ServiceName[report] ServiceErrorMethod:[updateReportStatus] Error:[param error] ServiceDetail:[$arrInput:".json_encode($arrInput)."]");
            return false;
        }
        $arrConds = [
            'id'        => $id,
        ];
        $arrInput[] = 'update_time='.time();
        //$arrInput[] = 'callback_count=callback_count+1';

        $arrAppends = [
            "order by id desc",
            "limit 1"
        ];
        $res = $this->_objDaoQudaoReport->updateByConds($arrConds, $arrInput, null, $arrAppends);
        if($res===false){
            Bd_Log::warning("ServiceName[report] ServiceErrorMethod:[updateReportStatus] Error:[db error] ServiceDetail:[arrConds:".json_encode(array_merge($arrInput,$arrConds))."]");
        }
        return $res;
    }


    public function editReportExt($appid, $data)
    {
        if (!$appid || !$data) {
            return false;
        }
        $where = [
            'account' => $appid
        ];
        $data = json_encode($data);
        $res = $this->_objDaoQudaoReport->updateByConds($where, ["ext_data='" . $data . "'"]);

        if ($res===false) {
            Bd_Log::warning("ServiceName[report] ServiceErrorMethod:[editReportExt] Error:[db error] ServiceDetail:[arrConds:".json_encode(['account' => $appid, 'data' => $data])."]");
            return false;
        }
        return true;
    }

    /**
     * 获取自增后的id
     * @return mixed
     */
    public function getLastInsertId()
    {
        $ret = $this->_objDaoQudaoReport->getInsertId();
        if($ret===false){
            Bd_Log::warning("ServiceName[report] ServiceErrorMethod:[getLastInsertId] Error:[db error] ServiceDetail:[arrConds:]");
        }
        return $ret;
    }


    public function updateParam($arrConds, $arrFields)
    {
        if (empty($arrConds) || empty($arrFields)) {
            return false;
        }

        $arrConds = $this->_objDaoQudaoReport->updateByConds($arrConds, $arrFields);
        if ($arrConds === false) {
            $arrConds = json_encode($arrConds);
            $sql = $this->_objDaoQudaoReport->getLastSQL();
            Qdlib_Util_Log::warning('repair', 'Qdlib_Ds_QudaoReport', 'updateParam', "sql:{$sql}", "{$arrConds}");
            return false;
        }

        return $arrConds;
    }

    public function getAllReportList($where, $field = [], $arrAppends = null, $strIndex = null)
    {
        if (!$field) {
            $field = explode(',', self::ALL_FIELDS);
        }

        $res = $this->_objDaoQudaoReport->getListByConds($where, $field, null, $arrAppends, $strIndex);
        if ($res === false) {
            $sql = $this->_objDaoQudaoReport->getLastSQL();
            $where = json_encode($where);
            Qdlib_Util_Log::warning('clue', 'Qdlib_Ds_QudaoReport', 'getAllReportList', "sql:{$sql}", "{$where}");
        }

        return $res;
    }

    public function getAllReportCount($arrConds)
    {
        $res = $this->_objDaoQudaoReport->getCntByConds($arrConds);
        if ($res === false) {
            $sql = $this->_objDaoQudaoReport->getLastSQL();
            Qdlib_Util_Log::warning('clue', 'Qdlib_Ds_QudaoReport', 'getAllReportCount', "获取失败sql:{$sql}");
        }
        $res = (int) $res;
        return $res;
    }

}