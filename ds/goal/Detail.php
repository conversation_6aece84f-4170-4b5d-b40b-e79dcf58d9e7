<?php

class Qdlib_Ds_Goal_Detail
{
    private $_daoDetail;

    public function __construct()
    {
        $this->_daoDetail = new Qdlib_Dao_Goal_Detail();
    }

    //新增
    public function add($param)
    {
        if (empty($param) || !is_array($param)) {
            throw new Qdmis_Common_Exception(Qdmis_Common_ExceptionCodes::INTER_PARAM_ERROR, 'param参数错误', [
                'method' => __METHOD__,
                'arg' => $param
            ]);
        }

        $ret = $this->_daoDetail->insertRecords($param);
        if ($ret === false) {
            Bd_Log::warning("Error:[" . __CLASS__ . ";" . __FUNCTION__ . "], Detail:[param: " . json_encode(func_get_args()) . "]");
            return false;
        }

        return $this->_daoDetail->getInsertId();
    }

    //编辑
    public function update($conds, $param)
    {
        if (empty($param) || !is_array($param)) {
            throw new Qdmis_Common_Exception(Qdmis_Common_ExceptionCodes::INTER_PARAM_ERROR, 'param参数错误', [
                'method' => __METHOD__,
                'arg' => $param
            ]);
        }

        $arrAllFields = $this->_daoDetail->getAllFields();
        foreach ($param as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }
            $arrFields[$key] = $value;
        }
        $ret = $this->_daoDetail->updateByConds($conds, $arrFields);

        if ($ret === false) {
            Bd_Log::warning("Error:[" . __CLASS__ . ";" . __FUNCTION__ . "], Detail:[param: " . json_encode(func_get_args()) . "]");
            return false;
        }
        return $ret;
    }

    public function getByConds($arrConds = null, $arrFields = [], $arrOptions = NULL, $arrAppends = NULL, $strIndex = NULL)
    {
        if (!is_array($arrConds) && !empty($arrConds)) {
            throw new Qdmis_Common_Exception(Qdmis_Common_ExceptionCodes::INTER_PARAM_ERROR, 'param参数错误', [
                'method' => __METHOD__,
                'arg' => $arrConds
            ]);
        }
        $arrConds['deleted'] = 0;
        if (empty($arrFields)) {
            $arrFields = $this->_daoDetail->getAllFields();
        }
        $ret = $this->_daoDetail->getListByConds($arrConds, $arrFields, $arrOptions, $arrAppends, $strIndex);
        if ($ret === false) {
            Bd_Log::warning("Error:[" . __CLASS__ . ";" . __FUNCTION__ . "], Detail:[param: " . json_encode(func_get_args()) . "]");
            return false;
        }
        return $ret;

    }

    public function getCntByConds($arrConds = null)
    {
        $arrConds['deleted'] = 0;
        $ret = $this->_daoDetail->getCntByConds($arrConds);
        if (false === $ret) {
            Bd_Log::warning("Error:[" . __CLASS__ . ";" . __FUNCTION__ . "], Detail:[param: " . json_encode(func_get_args()) . "]");

            return false;
        }

        return $ret;
    }

    public function delByConds($arrConds)
    {
        if (!is_array($arrConds) || empty($arrConds)) {
            throw new Qdmis_Common_Exception(Qdmis_Common_ExceptionCodes::INTER_PARAM_ERROR, 'param参数错误', [
                'method' => __METHOD__,
                'arg' => $arrConds
            ]);
        }
        $ret = $this->_daoDetail->deleteByConds($arrConds);
        if ($ret === false) {
            Bd_Log::warning("Error:[" . __CLASS__ . ";" . __FUNCTION__ . "], Detail:[param: " . json_encode(func_get_args()) . "]");
            return false;
        }
        return $ret;

    }


}