<?php

/**
 * @file Month.php
 * <AUTHOR>
 * @date 2017-11-4
 * @brief 获取章节数据
 *
 * */
class Hkzb_Ds_Bluewhale_Lesson {

    const NOCONTINUETHREE = 0;
    const CONTINUETHREE = 1;

    static $CONTINUETHREE = array(
        self::NOCONTINUETHREE => '未续报',
        self::CONTINUETHREE => '已续报',
    );

    const API_TOKEN = '10594DD6D501D1EC688F8F91339216E9';
    const REFERER = '/teacherlive/';

    public function __construct() {
        // $arrConf = Bd_Conf::getConf('hk/bluewhale/bos'); 
        // $this->_bluewhale=$arrConf;
    }

    public function getLessonRate($arrConds) {
        $header = array(
            'pathinfo' => "/bluewhale/api/lessoninfo",
            'cookie' => $_COOKIE,
            'referer' => self::REFERER,
        );
        $time = time();
        // $arrConds['lessonId']=  is_array($arrConds['courseId'])?$arrConds['courseId']:array($arrConds['courseId']);
        foreach ($arrConds as $k => &$v) {
            if (is_int($v) && (($k !== "pageSize") && ($k !== "page"))) {
                $v = array($v);
            }
        }
        $arrConds['token'] = self::API_TOKEN;
        $arrConds['ip'] = self::getIp();
        $ret = ral('bluewhale', 'post', $arrConds, rand(), $header);
        if (false === $ret) {
            $errno = ral_get_errno();
            $errmsg = ral_get_error();
            $protocol_status = ral_get_protocol_code();
            Bd_Log::warning("Error:[service bluewhale connect error], Detail:[errno:$errno errmsg:$errmsg protocol_status:$protocol_status]");
            return array();
        }
        $list = json_decode($ret, true);
        $courseList = $list['data'];
        return $courseList;
    }

    public function getLessonStudent($arrConds) {
        $header = array(
            'pathinfo' => "/bluewhale/api/lessonstudent",
            'cookie' => $_COOKIE,
            'referer' => self::REFERER
        );
        $time = time();
        foreach ($arrConds as $k => &$v) {
            if (is_int($v) && (($k !== "pageSize") && ($k !== "page"))) {
                $v = array($v);
            }
        }
        $arrConds['token'] = self::API_TOKEN;
        $arrConds['ip'] = self::getIp();
        $ret = ral('bluewhale', 'post', $arrConds, rand(), $header);
        if (false === $ret) {
            $errno = ral_get_errno();
            $errmsg = ral_get_error();
            $protocol_status = ral_get_protocol_code();
            Bd_Log::warning("Error:[service bluewhale connect error], Detail:[errno:$errno errmsg:$errmsg protocol_status:$protocol_status]");
            return array();
        }
        $list = json_decode($ret, true);
        $courseList = $list['data'];
        return $courseList;
    }
    
    public function getIp()
    {
        $host = isset($_SERVER['HTTP_X_FORWARDED_HOST']) ? $_SERVER['HTTP_X_FORWARDED_HOST'] : (isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '');
        return $host;
    }

}
