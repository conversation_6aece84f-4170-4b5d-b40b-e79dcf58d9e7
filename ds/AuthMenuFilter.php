<?php
/**
 * Created by Growth AutoCode.
 * User: <EMAIL>
 * Date: 2020/07/27
 * Time: 11:44
 */
class Qdlib_Ds_AuthMenuFilter
{
    const ALL_FIELDS = 'id,menuId,systemId,controlType,filterKey,filterKeyName,operator,deleted,createTime,updateTime';
    const DELETE_DEL = 1;

    private $_objDaoAuthMenuFilter;

    public function __construct()
    {
        $this->_objDaoAuthMenuFilter = new Qdlib_Dao_AuthMenuFilter();
    }

    /**
     * 获取菜单过滤项列表
     * @param $systemId
     * @param $menuId
     * @param array $arrFields
     * @return array|false
     */
    public function getFilterListByMenuId($systemId, $menuId, $arrFields = array())
    {
        $arrConds = [];
        $arrConds['deleted'] = 0;
        $arrConds['menuId'] = $menuId;
        $arrConds['systemId'] = $systemId;

        if (!$arrFields) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $result = $this->_objDaoAuthMenuFilter->getListByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'AuthMenu', 'getFilterListByMenuId', '查询数据库信息失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }
}