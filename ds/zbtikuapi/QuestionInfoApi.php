<?php
/**
 * @file QuestionInfoApi.php
 * <AUTHOR>
 * @date: 2019-08-13
 *
 */
class Hkzb_Ds_ZbtikuApi_QuestionInfoApi extends Hkzb_Ds_ZbtikuApi_Common_Ral
{

    public function __construct($appId, $secretKey, $header = [])
    {
        parent::__construct($appId, $secretKey, $header);
    }

    /**获取试题信息
     * 接口文档：http://yapi.afpai.com/project/368/interface/api/32258
     * @param $input
     * @return mixed
     */
    public function getTidsQuestInfo($input)
    {
        $api = '/zbtikuapi/questioninfoapi/gettidsquestinfo';

        $serviceName = 'course';

        $ret = $this->call($api, $input, $serviceName);
        return $ret;
    }

    /**
     * 【此方法，未经授权使用，出问题自己负责】
     * 获取试题信息（读主库）
     * @param $input
     * @return mixed
     */
    public function questInfoReadMaster($input)
    {
        $api = '/zbtikuapi/questioninfoapi/questinforeadmaster';

        $serviceName = 'course';

        $ret = $this->call($api, $input, $serviceName);
        return $ret;
    }

    /**
     * 获取试题的短标签（组合标签）
     * 接口文档：
     * http://wiki.afpai.com/pages/viewpage.action?pageId=34152385#id-%E6%9D%A5%E6%BA%90%E6%A0%87%E7%AD%BE%E6%8E%A5%E5%8F%A3%E8%AE%BE%E8%AE%A1%E6%96%87%E6%A1%A3-%E6%8E%A5%E5%8F%A39%EF%BC%9A%E8%AE%B2%E4%B9%89%E6%8B%89%E5%8F%96%E7%9F%AD%E6%A0%87%E7%AD%BE
     * @param $input
     * @return mixed
     */
    public function getShortLabels($input)
    {
        $api = '/zbtikuapi/questioninfoapi/getshortlabels';

        $serviceName = 'course';

        $ret = $this->call($api, $input, $serviceName);
        return $ret;
    }

    /**
     * @param $input
     * @return mixed
     */
    public function getQuestSourceTid($input)
    {
        $api = '/zbtikuapi/questioninfoapi/getquestsourcetid';

        $serviceName = 'course';

        $ret = $this->call($api, $input, $serviceName);
        return $ret;
    }

    //====================供检索start==========================
    /**
     * 拼装试题信息及其相关信息
     * @param $input
     * @return mixed
     */
    public function getQuestInfoForSearch($input)
    {
        $api = '/zbtikuapi/questioninfoapi/getquestinfoforsearch';

        $serviceName = 'course';

        $ret = $this->call($api, $input, $serviceName);
        return $ret;
    }

    /**
     * 获取库里所有试题最大zbTid
     * @param $input
     * @return mixed
     */
    public function getMaxZbTid($input)
    {
        $api = '/zbtikuapi/questioninfoapi/getmaxzbtid';

        $serviceName = 'course';

        $ret = $this->call($api, $input, $serviceName);
        return $ret;
    }
    //=====================供检索end=========================


    /**
     * 录入互动课堂试题
     * @param $input
     * @return mixed
     */
    public function questionEdit($input)
    {
        $api = '/zbtikuapi/question/questionedit';

        $serviceName = 'course';

        $ret = $this->call($api, $input, $serviceName);
        return $ret;
    }

    /**
     *
     * @param $input
     * @return mixed
     */
    public function ErrorReportInfo($input)
    {
        $api = '/zbtikuapi/errorreport/errorreportinfo';

        $serviceName = 'course';

        $ret = $this->call($api, $input, $serviceName);
        return $ret;
    }

    /**
     * 添加报错信息
     * @param $input
     * @return mixed
     */
    public function ErrorReportAdd($input)
    {
        $api = '/zbtikuapi/errorreport/errorreportadd';

        $serviceName = 'course';

        $ret = $this->call($api, $input, $serviceName);
        return $ret;
    }

    public function qinfoZ($input)
    {
        $api = '/zbtikuapi/interact/qinfoz';

        $serviceName = 'course';

        $ret = $this->call($api, $input, $serviceName);
        return $ret;
    }



    public function unitTest($input)
    {
        $api = '/zbtikuapi/questioninfoapi/unittest';

        $serviceName = 'course';

        $ret = $this->call($api, $input, $serviceName);
        return $ret;
    }
}