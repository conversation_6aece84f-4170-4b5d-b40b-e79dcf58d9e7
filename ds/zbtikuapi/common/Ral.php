<?php
/**
 * @file ZbtikuApi.php
 * <AUTHOR>
 * @date: 2019-08-13
 *
 */
class Hkzb_Ds_ZbtikuApi_Common_Ral
{
    const HTTP_REQUEST_METHOD = 'post';

    private $_appId;
    private $_secretKey;
    private $_header;
    public function __construct($appId, $secretKey, $header)
    {
        $this->_appId     = intval($appId);
        $this->_secretKey = $secretKey;
        if (empty($this->_appId) || empty($this->_secretKey)){
            Bd_Log::warning("param error Detail[secretKey_{$secretKey}_appId_{$appId}");
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'appId or secretKey is empty');
        }
        $this->_header = $header;
    }

    /**
     * @param string $pathInfo 接口
     * @param array $input  入参
     * @param string $serviceName ral的服务名
     * @return mixed
     */
    protected function call($pathInfo,$input, $serviceName)
    {
        $input['appId'] = $this->_appId;

        $this->_header['pathinfo'] = $pathInfo;

        //请求时间
        $nowTime = time();
        $this->_header[Hkzb_Ds_ZbtikuApi_Common_Sign::SIGN_SEND_TIMESTAMP] = $nowTime;

        //签名
        $sign = (new Hkzb_Ds_ZbtikuApi_Common_Sign())->createSign($this->_secretKey, self::HTTP_REQUEST_METHOD, $pathInfo, $input, $this->_header);
        $this->_header[Hkzb_Ds_ZbtikuApi_Common_Sign::SIGN_SEND_AUTHORIZATION] = $sign;

        $ret = ral($serviceName, 'post', $input, rand(), $this->_header);

        return $ret;
    }


}