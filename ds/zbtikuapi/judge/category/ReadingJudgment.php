<?php
/**
 * @file ReadingJudgment.php.
 * <AUTHOR>
 * @date: 2019/03/15
 */
class Hkzb_Ds_ZbtikuApi_Judge_Category_ReadingJudgment implements Hkzb_Ds_ZbtikuApi_Judge_Category_Base_CategoryBase
{
    private $_arrStuAnswer;
    private $_arrOriStuAnswer;
    private $_dbQuestionInfo;
    private $_arrOutput;
    private $_arrDetail;

    public function Judge()
    {
        $this->_formatStuAnswer();

        foreach ($this->_arrStuAnswer as $idx=>$stuAnswer){
            $dbAnswer = $this->_dbQuestionInfo['question']['questionList'][$idx+1]['choose'];
            $corRet   = (empty($stuAnswer) || empty($dbAnswer) || $stuAnswer != $dbAnswer)
                ? Hkzb_Ds_ZbtikuApi_Const_Judge::SUBJECT_RESULT_WRONG
                : Hkzb_Ds_ZbtikuApi_Const_Judge::SUBJECT_RESULT_RIGHT;
            $this->_arrOutput[] = $corRet;
            $this->_arrDetail[] = array(
                'stuAnswer'    => $stuAnswer, //学生答题数据
                'rightAnswer'  => $dbAnswer,//正确答案
                'correctRet'   => $corRet, //批改结果
            );
        }

        return $this->_arrOutput;
    }

    public function getDetailInfo() {
        return $this->_arrDetail;
    }

    private function _formatStuAnswer()
    {
        $idx = 0;
        while($idx < count($this->_dbQuestionInfo['question']['questionList'])){
            $this->_arrStuAnswer[] = isset($this->_arrOriStuAnswer[$idx]) ? strtoupper(trim($this->_arrOriStuAnswer[$idx])) : '';
            $idx++;
        }
    }

    public function setStudentAnswer($arrStudentAnswer)
    {
        $this->_arrOriStuAnswer = $arrStudentAnswer;
        return $this;
    }

    public function setQuestionInfo($questionInfo)
    {
        $this->_dbQuestionInfo = $questionInfo;
        return $this;
    }
}
