<?php

class Qdlib_Ds_Qudao_OutAdDiagnosis extends Qdlib_Ds_Qudao_OutBase
{

    protected $_tableDaoObj;

    const FlAG_AD_DIAGNOSIS = 'ad_diagnosis';

    public static $FLAG_MAP = [
        self::FlAG_AD_DIAGNOSIS => '广告计划诊断',
    ];

    public function __construct()
    {
        parent::__construct('Qdlib_Dao_Qudao_OutAdDiagnosis');
    }

    protected function getFormatDbData($arrParams) {
        $arrFields = array(
            'channel'                   => isset($arrParams['channel']) ? strval($arrParams['channel']) : '',
            'account'                   => isset($arrParams['account']) ? strval($arrParams['account']) : '',
            'flag'                      => isset($arrParams['flag']) ? strval($arrParams['flag']) : '',
            'uniqueId'                  => isset($arrParams['uniqueId']) ? strval($arrParams['uniqueId']) : '',
            'page'                      => isset($arrParams['page']) ? intval($arrParams['page']) : 1,
            'thirdAdId'                 => isset($arrParams['thirdAdId']) ? intval($arrParams['thirdAdId']) : 0,
            'thirdCampaignId'           => isset($arrParams['thirdCampaignId']) ? intval($arrParams['thirdCampaignId']) : 0,
            'thirdAdgroupId'            => isset($arrParams['thirdAdgroupId']) ? intval($arrParams['thirdAdgroupId']) : 0,
            'ext'                       => isset($arrParams['ext']) ? strval($arrParams['ext']) : json_encode([]),
        );
        return $arrFields;
    }
}