<?php
/**
 * Created by PhpStorm.
 * User: duxiang
 * Date: 2020-02-16
 * Time: 19:15
 */
class Qdlib_Ds_Qudao_OutGroup extends Qdlib_Ds_Qudao_OutBase
{
    protected $_tableDaoObj;

    public function __construct()
    {
        parent::__construct('Qdlib_Dao_Qudao_OutGroup');
    }

    protected function getFormatDbData($arrParams) {
        $arrFields = array(
            'channel'                   => isset($arrParams['channel']) ? strval($arrParams['channel']) : '',
            'account'                   => isset($arrParams['account']) ? strval($arrParams['account']) : '',
            'campaignId'                => isset($arrParams['campaignId']) ? intval($arrParams['campaignId']) : 0,
            'configuredStatus'          => isset($arrParams['configuredStatus']) ? strval($arrParams['configuredStatus']) : 0,
            'systemStatus'              => isset($arrParams['systemStatus']) ? strval($arrParams['systemStatus']) : 0,

            'adgroupId'                => isset($arrParams['adgroupId']) ? intval($arrParams['adgroupId']) : 0,
            'adgroupName'              => isset($arrParams['adgroupName']) ? strval($arrParams['adgroupName']) : '',
            'adgroupCreatedTime'       => isset($arrParams['adgroupCreatedTime']) ? intval($arrParams['adgroupCreatedTime']) : 0,
            'adgroupLastModifiedTime'  => isset($arrParams['adgroupLastModifiedTime']) ? intval($arrParams['adgroupLastModifiedTime']) : 0,
            'ext'                       => isset($arrParams['ext']) ? strval($arrParams['ext']) : json_encode([]),
        );
        return $arrFields;
    }


}