<?php


class Qdlib_Ds_Qudao_StockQuery extends Qdlib_Ds_Qudao_OutBase
{
    protected $_tableDaoObj;

    public function __construct()
    {
        parent::__construct('Qdlib_Dao_Qudao_StockQuery');
    }

    protected function getFormatDbData($arrParams) {
        $arrFields = array(
            'channel'                   => isset($arrParams['channel']) ? strval($arrParams['channel']) : '',
            'uniqueId'                  => isset($arrParams['uniqueId']) ? strval($arrParams['uniqueId']) : '',
            'adModifiedTime'            => isset($arrParams['adModifiedTime']) ? intval($arrParams['adModifiedTime']) : 0,
            'adCreatedTime'             => isset($arrParams['adCreatedTime']) ? intval($arrParams['adCreatedTime']) : 0,
            'createTime'                => isset($arrParams['createTime']) ? intval($arrParams['createTime']):time(),
            'updateTime'                => isset($arrParams['updateTime']) ? intval($arrParams['updateTime']):time(),
            'ext'                       => isset($arrParams['ext']) ? strval($arrParams['ext']) : json_encode([]),
            'deleted'                   => isset($arrParams['deleted']) ? intval($arrParams['deleted']):0,
        );
        return $arrFields;
    }
}