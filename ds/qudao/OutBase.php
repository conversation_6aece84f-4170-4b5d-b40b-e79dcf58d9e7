<?php
/**
 * Created by PhpStorm.
 * User: duxiang
 * Date: 2020-02-16
 * Time: 21:28
 */

abstract class Qdlib_Ds_Qudao_OutBase {

    /**
     * @var Hk_Common_BaseDao
     */
    protected $_tableDaoObj = null;

    public function __construct($tableDao = '') {
        $this->_tableDaoObj = new $tableDao();
        $this->_tableSync = new Qdlib_Service_SyncTripartiteData();
    }

    abstract protected function getFormatDbData($arrParams);

    public function afterChangeData($arrFields = []) {
        return $arrFields;
    }

    //只给脚本cron更新使用
    final public function updateData($id, $arrParams)
    {
        $arrConds = array(
            'id' => intval($id),
        );

        $arrFields = $this->getFormatDbData($arrParams);

        $arrFields['updateTime'] = time();
        $arrFields['reportStatus'] = 0;
        $this->dbReconnect();
        $res = $this->_tableDaoObj->updateByConds($arrConds, $arrFields);
        $this->_debugLastSql();
        if (false === $res) {
            Qdlib_Util_Log::warning('mediaData', 'Qdlib_Dao_Qudao_OutGroup', 'updateData', 'db error',
                json_encode($arrFields));
            return false;
        }

        $arrFields['id'] = $id;
        $this->afterChangeData($arrFields);
        $this->_tableSync->sync($this->_tableDaoObj->_table, $arrFields);
        return $res;
    }


    //只给脚本cron更新使用
    final public function addData($arrParams)
    {
        if (empty($arrParams)) {
            Qdlib_Util_Log::warning('mediaData', 'Qdlib_Dao_Qudao_OutGroup', 'add', "param: empty");
            return false;
        }
        $arrFields = $this->getFormatDbData($arrParams);
        $arrFields['createTime'] = time();
        $arrFields['updateTime'] = time();
        $arrFields['reportStatus'] = 0;
        $this->dbReconnect();
        $res = $this->_tableDaoObj->insertRecords($arrFields);
        $this->_debugLastSql();
        if (false === $res) {
            Qdlib_Util_Log::warning('mediaData', 'Qdlib_Dao_Qudao_OutGroup', 'addData', 'db error',
                json_encode($arrFields));
            return false;
        }

        $arrFields['id'] = $this->_tableDaoObj->getInsertId();
        $this->afterChangeData($arrFields);
        $this->_tableSync->sync($this->_tableDaoObj->_table, $arrFields);
        return $res;
    }

    public function getDbAllFields () {
        return $this->_tableDaoObj->getAllFields();
    }

    public function getTotal($where)
    {
        return $this->_tableDaoObj->getCntByConds($where);
    }

    public function getList($where, $field = [], $arrAppends = null, $strIndex = null)
    {
        if (!$field) {
            $field = $this->_tableDaoObj->getAllFields();
        }
        $this->dbReconnect();
        $res = $this->_tableDaoObj->getListByConds($where, $field, null, $arrAppends, $strIndex);
        $this->_debugLastSql();
        if ($res === false) {
            $sql = $this->_tableDaoObj->getLastSQL();
            $where = json_encode($where);
            Qdlib_Util_Log::warning('mediaData', 'Qdlib_Ds_Qudao_OutBase', 'getList', "sql:{$sql}", "{$where}");
        }
        return $res;
    }

    public function get($where, $field = [], $arrAppends = null, $strIndex = null){
        if (!$field) {
            $field = $this->_tableDaoObj->getAllFields();
        }
        $this->dbReconnect();
        $res = $this->_tableDaoObj->getRecordByConds($where, $field, null, $arrAppends, $strIndex);
        $this->_debugLastSql();
        if ($res === false) {
            $sql = $this->_tableDaoObj->getLastSQL();
            $where = json_encode($where);
            Qdlib_Util_Log::warning('mediaData', 'Qdlib_Ds_Qudao_OutBase', 'get', "sql:{$sql}", "{$where}");
        }
        return $res;
    }

    /**
     * @brief 更新报表状态
     * @param array $ids
     * @return mix
     *  int 更新记录数量
     *  false 更新失败
     */
    public function updateReportStatus($ids = array())
    {
        if (count($ids) < 1) {
            return false;
        }
        $ids = array_map('intval', $ids);
        $arrConds = array(
            "id in (" . implode(',', $ids) . ")",
        );
        $arrFields = array(
            'report_status' => time(),
        );
        $this->dbReconnect();
        $res = $this->_tableDaoObj->updateByConds($arrConds, $arrFields);
        $this->_debugLastSql();
        if (false === $res) {
            Qdlib_Util_Log::warning('mediaData', 'Qdlib_Ds_Qudao_OutBase', '_updateReportStatus', 'upfail', json_encode($arrConds));
            return false;
        }
        return $res;
    }

    public function updateParam($arrConds, $arrFields)
    {
        if (empty($arrConds) || empty($arrFields)) {
            return false;
        }

        $this->dbReconnect();
        $arrConds = $this->_tableDaoObj->updateByConds($arrConds, $arrFields);
        $this->_debugLastSql();
        if ($arrConds === false) {
            Qdlib_Util_Log::warning('mediaData', 'Qdlib_Ds_Qudao_OutBase', '_updateParam', 'upfail', json_encode($arrConds));
            return false;
        }
        return $arrConds;
    }

    public function getListByNoSync($intLimit = 1000, $where = '')
    {
        if ($intLimit > 2000) {
            $intLimit = 2000;
        }
        $arrAppends = array(
            "LIMIT $intLimit OFFSET 0",
        );

        $arrConds = array(
            'report_status' => 0,
        );
        if ($where) {
            $arrConds[] = $where;
        }
        $arrFields = $this->_tableDaoObj->getAllFields();
        $this->dbReconnect();
        $lists = $this->_tableDaoObj->getListByConds($arrConds, $arrFields, null, $arrAppends);
        $this->_debugLastSql();
        return $lists;
    }

    public function getListByConds($arrConds, $arrFields = [], $arrOptions = NULL, $arrAppends = NULL, $strIndex = NULL)
    {
        if (!$arrFields) {
            $arrFields = $this->_tableDaoObj->getAllFields();
        }
        $this->dbReconnect();
        $res = $this->_tableDaoObj->getListByConds($arrConds, $arrFields, $arrOptions, $arrAppends, $strIndex);
        $this->_debugLastSql();
        if ($res === false) {
            $sql = $this->_tableDaoObj->getLastSQL();
            Qdlib_Util_Log::warning('mediaData', 'Qdlib_Ds_Qudao_OutBase', 'getListByConds', "sql:{$sql}");
        }
        return $res;
    }

    public function getIdsByAccount ($channel = '', $account = '', $arrFields = array(), $conds = array())
    {
        if (empty($channel) || empty($account)) {
            return false;
        }
        if($channel=='wangdian'){
            $arrConds = array(
                'channel' => $channel,
            );
        }else{
            $arrConds = array(
                'channel' => $channel,
                'account' => strval($account),
            );
        }

        if ($conds) {
            $arrConds = array_merge($arrConds, $conds);
        }
        if (empty($arrFields)) {
            $arrFields = $this->_tableDaoObj->getAllFields();

        }

        $arrAppends = null;
        $this->dbReconnect();
        $res = $this->_tableDaoObj->getListByConds($arrConds, $arrFields, null, $arrAppends, null);
        $this->_debugLastSql();
        if (false === $res) {
            Qdlib_Util_Log::warning('mediaData', 'Qdlib_Ds_Qudao_OutBase', '_getIdsByAccount', 'upfail', json_encode($arrConds));
            return false;
        }

        return $res;
    }

    protected function _debugLastSql()
    {

    }


    protected function dbReconnect()
    {
        if (method_exists($this->_tableDaoObj, 'reconnect')) {
            $this->_tableDaoObj->reconnect();
        }
    }
}