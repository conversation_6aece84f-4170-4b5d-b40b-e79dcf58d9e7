<?php

class Qdlib_Ds_Qudao_OutBalance extends Qdlib_Ds_Qudao_OutBase
{
    protected $_tableDaoObj;

    public function __construct()
    {
        parent::__construct('Qdlib_Dao_Qudao_OutBalance');
    }

    protected function getFormatDbData($arrParams) {
        $arrFields = array(
            'channel'                   => isset($arrParams['channel']) ? strval($arrParams['channel']) : '',
            'account'                   => isset($arrParams['account']) ? strval($arrParams['account']) : '',
            'balance'                   => isset($arrParams['balance']) ? intval($arrParams['balance']) : 0,
            'ext'                       => isset($arrParams['ext']) ? strval($arrParams['ext']) : json_encode([]),
        );
        return $arrFields;
    }
}