<?php


class Qdlib_Ds_Qudao_OutTask extends Qdlib_Ds_Qudao_OutBase
{
    protected $_tableDaoObj;

    public function __construct()
    {
        parent::__construct('Qdlib_Dao_Qudao_OutTask');
    }

    protected function getFormatDbData($arrParams) {
        $arrFields = array(
            'channel'       => isset($arrParams['channel']) ? strval($arrParams['channel']):'',
            'taskId'        => isset($arrParams['taskId']) ? intval($arrParams['taskId']):0,
            'actType'       => isset($arrParams['actType']) ? strval($arrParams['actType']):'',
            'ids'           => isset($arrParams['ids']) ? json_encode($arrParams['ids']):json_encode([]),
            'taskPartitions'    => isset($arrParams['taskPartitions']) ? intval($arrParams['taskPartitions']):0,
            'taskPartition'     => isset($arrParams['taskPartition']) ? intval($arrParams['taskPartition']):0,
            'taskStatus'    => isset($arrParams['taskStatus']) ? intval($arrParams['taskStatus']):0,
            'createTime'    => isset($arrParams['createTime']) ? intval($arrParams['createTime']):time(),
            'updateTime'    => isset($arrParams['updateTime']) ? intval($arrParams['updateTime']):time(),
            'taskResult'    => isset($arrParams['taskResult']) ? json_encode($arrParams['taskResult']):json_encode([]),
        );
        return $arrFields;
    }

    public function addTask($arrParams)
    {
        if (empty($arrParams)) {
            Qdlib_Util_Log::warning('mediaData', 'Qdlib_Dao_Qudao_OutGroup', 'add', "param: empty");
            return false;
        }
        $arrFields = $this->getFormatDbData($arrParams);
        $arrFields['createTime'] = time();
        $arrFields['updateTime'] = time();

        $this->dbReconnect();
        $res = $this->_tableDaoObj->insertRecords($arrFields);
        $this->_debugLastSql();
        if (false === $res) {
            Qdlib_Util_Log::warning('mediaData', 'Qdlib_Ds_Qudao_OutTask', 'addTask', 'db error',
                json_encode($arrFields));
            return false;
        }
        return $res;
    }

}