<?php

class Qdlib_Ds_Qudao_OutComment extends Qdlib_Ds_Qudao_OutBase
{

    protected $_tableDaoObj;

    public function __construct()
    {
        parent::__construct('Qdlib_Dao_Qudao_OutComment');
    }

    protected function getFormatDbData($arrParams) {
        $arrFields = array(
            'channel'                   => isset($arrParams['channel']) ? strval($arrParams['channel']) : '',
            'account'                   => isset($arrParams['account']) ? strval($arrParams['account']) : '',
            'flag'                      => isset($arrParams['flag']) ? strval($arrParams['flag']) : '',
            'uniqueId'                  => isset($arrParams['uniqueId']) ? strval($arrParams['uniqueId']) : '',
            'thirdAdId'                 => isset($arrParams['thirdAdId']) ? intval($arrParams['thirdAdId']) : 0,
            'commentId'                 => isset($arrParams['commentId']) ? intval($arrParams['commentId']) : 0,
            'appName'                   => isset($arrParams['appName']) ? strval($arrParams['appName']) : '',
            'content'                   => isset($arrParams['content']) ? strval($arrParams['content']) : '',
            'commentCreatedTime'        => isset($arrParams['commentCreatedTime']) ? intval($arrParams['commentCreatedTime']) : 0,
            'page'                      => isset($arrParams['page']) ? intval($arrParams['page']) : 1,
            'ext'                       => isset($arrParams['ext']) ? strval($arrParams['ext']) : json_encode([]),
        );
        return $arrFields;
    }
}