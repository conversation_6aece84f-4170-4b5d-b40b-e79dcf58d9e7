<?php

/**
 * @file QQGroupExport.php
 * <AUTHOR>
 * @date 2019-07-12
 * @brief 数据导出
 *
 **/
class Oplib_Ds_CourseQqGroupExport
{
    const STATUS_DOING   = 1;
	const STATUS_DONE    = 2;
	const STATUS_ERROR   = 3;
	static $STATUS_ARRAY = array(
		self::STATUS_DOING => '处理中',
		self::STATUS_DONE  => '已发送',
		self::STATUS_ERROR => '失败',
	);

	const ALL_FIELDS='taskId,taskName,errReason,status,operatorUid,operator,email,courseId,qq,createTime';

	private $objDaoQQGroupExport;

	private $orderBy = array('task_id');

	/**
	 * 构造函数
	 *
	 */
	public function __construct()
	{
		$this->objDaoQQGroupExport = new Oplib_Dao_CourseQQGroupExport();
	}

	/**
	 * 新增记录
	 *
	 * @param  mix  $arrParams 属性
	 * @return bool true/false
	 */
	public function addCourseQQGroupExport($arrParams)
	{
		$arrFields = array(
			'taskName'    =>isset($arrParams['taskName']) ? strval($arrParams['taskName']):'',
			'errReason'   =>isset($arrParams['errReason']) ? intval($arrParams['errReason']):'',
			'status'      =>isset($arrParams['status']) ? intval($arrParams['status']):self::STATUS_DOING,
			'operatorUid' =>isset($arrParams['operatorUid']) ? intval($arrParams['operatorUid']):0,
			'operator'    =>isset($arrParams['operator']) ? strval($arrParams['operator']):'',
			'email'       =>isset($arrParams['email']) ? strval($arrParams['email']):'',
            'courseId'    =>isset($arrParams['courseId']) ? strval($arrParams['courseId']):'',
            'qq'          =>isset($arrParams['qq']) ? strval($arrParams['qq']):0,
            'createTime'  =>time(),
		);

        // 必填项
        $needFields = ['taskName', 'operatorUid', 'operator', 'email', 'courseId'];
        foreach ($needFields as $field) {
            if (!isset($arrFields[$field])) {
                Bd_Log::warning("Error:[param error,{$field} not allow empty],Detail:[strParams:{$strParams},strFields:{$strFields}]");

                return false;
            }
        }

		$ret = $this->objDaoQQGroupExport->insertRecords($arrFields);
		if($ret){
			$ret = $this->objDaoQQGroupExport->getInsertId();
		}
		return $ret;
	}

    //修改任务状态
    public function setCourseQQGroupExportStatus($taskId, $status, $errReason)
    {
        if (empty($taskId) || empty($status)) {
            $paramsStr = json_encode($params);
            Bd_Log::warning("Abstract[oplib] Error:[param empty], Detail:[taskId:{$taskId},status:{$status},errReason:{$errReason}]");
            return false;
        }

        $arrConds = [
            'taskId' => $taskId
        ];

        $params = [
            'status' => $status,
            'errReason' => $errReason
        ];

        return $this->objDaoQQGroupExport->updateByConds($arrConds, $params, null, null);
    }

	public function getCourseQQGroupExportList($courseId = 0, $qq = 0,$status = 0,$arrFields = array(),$offset = 0,$limit = 0, $order = '',$by = '')
    {
        $arrConds = null;

        if(! empty($courseId)){
			$arrConds['courseId'] = intval($courseId);
		}

		if(! empty($qq)){
			$arrConds['qq'] = intval($qq);
		}

        if(! empty($status)){
			$arrConds['status'] = intval($status);
		}

		if(empty($arrFields)){
			$arrFields = explode(',',self::ALL_FIELDS);
		}

		$orderBy = '';

		if($order != '' && in_array($order,$this->orderBy)){
			$orderBy .= 'order by '. $order . ' ';
			$orderBy .= ($by == 'desc') ? 'desc' : 'asc';
		}

		if($offset >= 0 && $limit > 0 ){
			$orderBy .= " limit $offset,$limit";
		}else if($limit > 0){
			$orderBy .= " limit $limit";
		}

		$arrAppends = ($orderBy != '')? array($orderBy) : null;

		$ret = $this->objDaoQQGroupExport->getListByConds($arrConds,$arrFields,NULL,$arrAppends);
		return $ret;
	}

	/**
	 * 获取人数
	 * @return int
	 */
	public function getCourseQQGroupExportCnt($courseId=0, $qq=0)
	{
        $arrConds = null;

        if(! empty($courseId)){
            $arrConds['courseId'] = intval($courseId);
        }

		if(! empty($qq)){
			$arrConds['qq'] = intval($qq);
		}

		$cnt = $this->objDaoQQGroupExport->getCntByConds($arrConds);

		return $cnt;
	}
}

