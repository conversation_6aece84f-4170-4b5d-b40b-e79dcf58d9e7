<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * Created by PhpStorm.
 * File : TeacherSaysReport.php
 * Author: niuxiong<PERSON>@zuoyebang.com
 * Date: 2019/6/11
 * Time: 10:56
 * Desc:
 */
class Oplib_Ds_TeacherSay_TeacherSaysReport
{

    //举报者类型
    const ROLE_STUDENT = 0;
    const ROLE_TEACHER = 1;
    static $ROLE_ARRAY = array(
        self::ROLE_STUDENT => '学生',
        self::ROLE_TEACHER => '老师',
    );

    //举报类型
    const TYPE_YES = 0;
    const TYPE_NO  = 1;
    static $TYPE_ARRAY = array(
        self::TYPE_YES => '举报',
        self::TYPE_NO  => '取消举报',
    );

    const ALL_FIELDS = 'id,msgId,pubUid,pubType,type,createTime,updateTime';

    private $_objDaoTeacherSaysReport;

    /**
     * 构造函数
     *
     */
    public function __construct()
    {
        $this->_objDaoTeacherSaysReport = new Oplib_Dao_TeacherSay_TeacherSaysReport();
    }

    /**
     * 新增举报
     *
     * @param  mix $arrParams 举报属性
     * @return bool true/false
     */
    public function addTeacherSaysReport($arrParams)
    {
        if (empty($arrParams) || intval($arrParams['msgId']) <= 0 || intval($arrParams['pubUid']) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[param: " . json_encode($arrParams) . "]");
            return false;
        }

        $arrFields = array(
            'msgId'       => isset($arrParams['msgId']) ? intval($arrParams['msgId']) : 0,
            'pubUid'      => isset($arrParams['pubUid']) ? intval($arrParams['pubUid']) : 0,
            'pubType'     => isset($arrParams['pubType']) ? intval($arrParams['pubType']) : 0,
            'type'        => isset($arrParams['type']) ? intval($arrParams['type']) : 0,
            'createTime'  => time(),
            'updateTime'  => time(),
        );

        $ret = $this->_objDaoTeacherSaysReport->insertRecords($arrFields);
        if (!$ret) {
            Bd_Log::warning("Error[dbError] Detail[db insert failed]");
            return false;
        }

        return $ret;
    }


    /**
     * 更新举报信息
     *
     * @param  int $id   举报id
     * @param  array $arrParams 举报属性
     * @return bool true/false
     */
    public function updateTeacherSaysReport($id, $arrParams)
    {
        if (empty($arrParams) || intval($id) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id arrParams:$arrParams]");
            return false;
        }

        $arrConds = array(
            'id'   => intval($id),
        );

        $arrFields = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach ($arrParams as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }

            $arrFields[$key] = $value;
        }

        $arrFields['updateTime'] = time();

        $ret = $this->_objDaoTeacherSaysReport->updateByConds($arrConds, $arrFields);

        return $ret;
    }


    /**
     * 获取指定举报信息
     *
     * @param  int $id id    举报id
     * @param  array $arrFields 举报属性
     * @return mix
     */
    public function getTeacherSaysReportInfo($id, $arrFields = array())
    {
        if (intval($id) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id]");
            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'id' => intval($id),
        );

        $ret = $this->_objDaoTeacherSaysReport->getRecordByConds($arrConds, $arrFields);

        return $ret;
    }


    /**
     * 获取指定条件的举报信息
     *
     * @param  array $arrConds   筛选条件
     * @param  array $arrFields 举报属性
     * @return mix
     */
    public function getTeacherSaysReportByConds($arrConds, $arrFields = array())
    {

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $ret = $this->_objDaoTeacherSaysReport->getListByConds($arrConds, $arrFields);

        return $ret;
    }


    /**
     * 获取举报数量
     * @param $arrConds
     * @return false|int
     */
    public function getTeacherSaysReportCntByConds($arrConds)
    {

        $ret = $this->_objDaoTeacherSaysReport->getCntByConds($arrConds);

        return $ret;
    }


    /**
     * 获取指定条件的举报信息
     *
     * @param array $arrConds     条件参数
     * @param array $arrFields   访问的属性
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getTeacherSaysReportListByConds($arrConds, $arrFields = array(), $offset = 0, $limit = 20)
    {
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrAppends = array(
            "order by create_time desc",
            "limit $offset, $limit",
        );

        $ret = $this->_objDaoTeacherSaysReport->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }
}
