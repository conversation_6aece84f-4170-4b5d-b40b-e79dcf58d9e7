<?php
/**
 * @file FinishExamPaper.php.
 * <AUTHOR>
 * @date: 2018/06/02
 */
class Hkzb_Ds_TikuTar_Tiku_FinishExamPaper
{
    private $_objDaoFinishExamPaper;

    public function __construct()
    {
        $this->_objDaoFinishExamPaper = new Hkzb_Dao_TikuTar_FinishExamPaper();
    }
    
    /**
     * 获取组卷列表信息
     * @param  $arrConds
     * @param  $arrFields
     * @param  $offset
     * @param  $limit
     */
    public function fetchFinishExamPaperList($arrConds,$arrFields=array(),$offset = 0, $limit = 30){
        if (empty($arrFields)){
            $arrFields =  $this->_objDaoFinishExamPaper->arrFields;
        }
        $arrConds['status'] = 0;
        $arrAppends = [
            "order by create_time desc",
        ];
        if ($limit > 0){
            $arrAppends[] = "limit $offset, $limit";
        }
        $return = $this->_objDaoFinishExamPaper->getListByConds($arrConds,$arrFields,NULL,$arrAppends);
        if( false === $return ) {
            $return = array();
        }
        return $return;
    }

    /**
     * 
     * @param array $arrConds
     * @return type
     */
    public function getFinishExamPaperCnt($arrConds) {
        $arrConds['status'] = 0;
        $returnTmp = 0;
        $returnTmp += $this->_objDaoFinishExamPaper->getCntByConds($arrConds);
        return $returnTmp;
    }

    /**
     * @param $id
     * @param array $arrFields
     * @return array|bool|false
     */
    public function getFinishExamPaperById($tmpId, $arrFields = array())
    {
        if (intval($tmpId) <= 0){
            Bd_Log::warning("param id error Detail[id: $tmpId]");
            return false;
        }

        $arrConds = array(
            'tmpId' => intval($tmpId),
            'deleted' => 0
        );
        $arrFields = empty($arrFields) ? $this->_objDaoFinishExamPaper->arrFields : $arrFields;

        $ret = $this->_objDaoFinishExamPaper->getRecordByConds($arrConds, $arrFields);
        return $ret;
    }
    
    /**
     * 格式化组卷列表数据
     * @param array $data
     */
    public function formateFinishExamPaperList(array $data){
        $returData = array();
        foreach($data as $key => $val){
            $tmpReturn = array();
            $tmpReturn['id'] = $val['id'];
            $tmpReturn['examPaperName'] = $val['examPaperName'];
            $tmpReturn['provinceName'] = $val['provinceName'];
            $tmpReturn['downloadUrl'] = $val['downloadUrl'];
            $tmpReturn['previewUrl'] = $val['previewUrl'];
            $tmpReturn['createTime'] = $val['createTime'];
            $yearsConf = (new Hkzb_Ds_TikuTar_Tiku_ZbTikuConf())->getTagConfBySourceKey(array('YEARS_CONF'),2);
            $tmpReturn['yearsId'] = $yearsConf[$val['yearsId']];
            $examTypeConf = (new Hkzb_Ds_TikuTar_Tiku_ZbTikuConf())->getTagConfBySourceKey(array('EXAM_TYPE_CONF'),2);
            $tmpReturn['examPaperType'] = $examTypeConf[$val['examPaperType']];
            $subjectConf = (new Hkzb_Ds_TikuTar_Tiku_ZbTikuConf())->getTagConfBySourceKey(array('GLOBAL_SUBJECT'),2);
            $tmpReturn['subjectId'] = $subjectConf[$val['subjectId']];
            $gradeConf = (new Hkzb_Ds_TikuTar_Tiku_ZbTikuConf())->getTagConfBySourceKey(array('GLOBAL_GRADE'),2);
            $tmpReturn['gradeId'] = $gradeConf[$val['gradeId']];
            $returData[$key] = $tmpReturn;
            unset($tmpReturn);
        }
        return $returData;
    }


}