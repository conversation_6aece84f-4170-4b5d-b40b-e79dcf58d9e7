<?php
/**
 * @file Crypt.php.
 * <AUTHOR>
 * @date: 2018/10/23
 */
class Hkzb_Ds_TikuTar_Api_Crypt
{
    public static function encrypt($appId, $appKey)
    {
        $key = md5($appId);
        $x = 0;
        $len =   strlen($appKey);
        $l = strlen($key);
        $str = "";
        $char = "";
        for ($i = 0; $i < $len; $i++)
        {
            if ($x == $l)
            {
                $x = 0;
            }
            $char .= $key{$x};
            $x++;
        }
        for ($i = 0; $i < $len; $i++)
        {
            $str .= chr(ord($appKey{$i}) + (ord($char{$i})) % 256);
        }
        return base64_encode($str);
    }

    public static function decrypt($appId, $data)
    {
        $key = md5($appId);
        $x = 0;
        $data = base64_decode($data);
        $len = strlen($data);
        $l = strlen($key);
        $str = "";
        $char = "";
        for ($i = 0; $i < $len; $i++)
        {
            if ($x == $l)
            {
                $x = 0;
            }
            $char .= substr($key, $x, 1);
            $x++;
        }
        for ($i = 0; $i < $len; $i++)
        {
            if (ord(substr($data, $i, 1)) < ord(substr($char, $i, 1)))
            {
                $str .= chr((ord(substr($data, $i, 1)) + 256) - ord(substr($char, $i, 1)));
            }
            else
            {
                $str .= chr(ord(substr($data, $i, 1)) - ord(substr($char, $i, 1)));
            }
        }
        return $str;
    }
}