<?php
/**
 * 题目接口
 * <AUTHOR> <<EMAIL>>
 */
class Hkzb_Ds_TikuTar_Api_QuestionApi
{   
    private $_objZbHomework;
    private $_objPtHomework;
    public function __construct()
    {
        $this->_objZbHomework = new Hkzb_Ds_TikuTar_Tiku_ZbHomework();
        $this->_objPtHomework = new Hkzb_Ds_Homework_Homework();
    }

    //该接口不再维护要废弃，获取试题相关需求请联系题库RD
    public function getQuestInfoLatxByTidList($tidList,$isLatex=true)
    {
        if(empty($tidList) || !is_array($tidList)) {
            Bd_log::warning("Error[getQuestionByTidList param fail]");
            return false;
        }
        $arrFields = array("question","answer","analysis","category","tid","extContent");
        $arrQuestionInfo = $this->_objZbHomework->getQuestionByTidList($tidList,$arrFields);
        $strQuestionInfoLatix = array();
        //在直播库的题
        $zbTikuTids = array();
        if (!empty($arrQuestionInfo)){
            foreach ($arrQuestionInfo as $intQestK => $fetchQuestion) {
                $zbTikuTids[] = intval($fetchQuestion['tid']);
                if($fetchQuestion['question']['edit'] && $isLatex) {
                    $strQuestionInfoLatix[$intQestK]['questionInfo'] = $fetchQuestion['question']['edit'];
                    if (isset($strQuestionInfoLatix[$intQestK]['questionInfo']["answer"]))
                        unset($strQuestionInfoLatix[$intQestK]['questionInfo']["answer"]);
                    if (isset($strQuestionInfoLatix[$intQestK]['questionInfo']["analysis"]))
                        unset($strQuestionInfoLatix[$intQestK]['questionInfo']["analysis"]);
                    $strQuestionInfoLatix[$intQestK]['answer']   = $fetchQuestion['question']['edit']['answer'];
                    $strQuestionInfoLatix[$intQestK]['analysis'] = $fetchQuestion['question']['edit']['analysis'];
                }else
                {
                    $strQuestionInfoLatix[$intQestK]['questionInfo'] = $fetchQuestion['question'];
                    $strQuestionInfoLatix[$intQestK]['answer']   = $fetchQuestion['answer'];
                    $strQuestionInfoLatix[$intQestK]['analysis'] = $fetchQuestion['analysis'];
                }

                $strQuestionInfoLatix[$intQestK]["pointList"] = $fetchQuestion['extContent']["pointList"]?$fetchQuestion['extContent']["pointList"]:array();
                $strQuestionInfoLatix[$intQestK]["tid"]      = $fetchQuestion['tid'];
                $strQuestionInfoLatix[$intQestK]["category"] = $fetchQuestion['category'];
                $strQuestionInfoLatix[$intQestK]["labelInfo"] = $fetchQuestion['extContent'];
            }
        }

        $ptTids = empty($zbTikuTids) ? $tidList : array_diff($tidList, $zbTikuTids);
        if(!empty($ptTids)){
            foreach ($ptTids as $tid){
                $questionInfo = $this->_objPtHomework->getHomeworkDetailByTid($tid);

                if (empty($questionInfo['question']) && empty($questionInfo['answer'])){
                    continue;
                }

                $strQuestionInfoLatix[] = $this->_formatPtQuestion($questionInfo);
            }
        }
        
        return $strQuestionInfoLatix;
    }

    private function _formatPtQuestion($questionInfo)
    {
        $arrOutput = array();

        if (empty($questionInfo['question'])){
            return $arrOutput;
        }

        if ($questionInfo['question']['questionList']){
            $arrOutput['questionInfo']['content']   = $questionInfo['question']['content'];
            $arrOutput['questionInfo']['questionList'] = $questionInfo['question']['questionList'];
        }else{
            $arrOutput['questionInfo']['title']   = $questionInfo['question']['title'];
            $arrOutput['questionInfo']['options'] = $questionInfo['question']['options'];
            $arrOutput['questionInfo']['choose']  = $questionInfo['question']['choose'];

        }

        $arrOutput['tid']       = intval($questionInfo['tid']);
        $arrOutput['answer']    = isset($questionInfo['answer']['title'])?$questionInfo['answer']['title']:'';
        $arrOutput['analysis']  = isset($questionInfo['extContent']['subjectAnalysis'])?$questionInfo['extContent']['subjectAnalysis']:'';
        $arrOutput['category']  = 0;
        $arrOutput["labelInfo"] = array();
        $arrOutput["pointList"] = empty($questionInfo['extContent']["pointsList"])
            ? array() : $this->_formatPointList($questionInfo['extContent']["pointsList"]);
        return $arrOutput;
    }

    private function _formatPointList($pointList)
    {
        $arrOutput = array();
        if (empty($pointList)){
            return array();
        }

        foreach ($pointList as $key=>$val){
            $arrOutput[] = array(
                'id'   => $val['pointId'],
                'name' => $val['title'],
            );
        }

        return $arrOutput;
    }

}