<?php
/**
 * @file TaskZbTidMap.php.
 * <AUTHOR>
 * @date: 2018/07/02
 */
class Hkzb_Ds_TikuTar_TaskZbTidMap
{
    private $_objDaoTaskZbTidMap;

    public function __construct()
    {
        $this->_objDaoTaskZbTidMap = new Hkzb_Dao_TikuTar_TaskZbTidMap();
    }


    /**
     * @param $addInfo
     * @param $userInfo
     * @return int|bool
     */
    public function addTaskZbTidMap($addInfo,$userInfo)
    {
        $now = time();
        $addData = array(
            'taskId'         => $addInfo['taskId'],
            'zbTid'          => $addInfo['zbTid'],
            'status'           => 0,
            'deleted'          => 0,
            'createTime'       => $now,
            'createUid'        => $userInfo['uid'],
            'createName'       => $userInfo['nickName'],
            'updateTime'       => $now,
        );
        $ret = $this->_objDaoTaskZbTidMap->insertRecords($addData);
        if( false === $ret ) {
            Bd_Log::warning(" insert TaskZbTidMap error detail:".json_encode($addData));
            return false;
        }

        $taskId = $this->_objDaoTaskZbTidMap->getInsertId();

        return $taskId;
    }


    /**
     * @param $taskId
     * @param $arrParams
     * @return bool
     */
    public function updateTaskZbTidMap($mapId, $arrParams)
    {
        if(intval($mapId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[task_id:$mapId]");
            return false;
        }

        $arrCond = array(
            'task_id' => intval($taskId),
        );

        $arrFields = array();
        $arrAllFields = $this->_objDaoTaskZbTidMap->arrFields;
        foreach ($arrParams as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }
            $arrFields[$key] = $value;
        }

        if (empty($arrFields['updateTime'])){
            $arrFields['updateTime'] = time();
        }

        $ret = $this->_objDaoTaskZbTidMap->updateByConds($arrCond, $arrFields);

        return $ret;
    }


    /**
     * @param $taskId
     * @param $arrParams
     * @return bool
     */
    public function updateByArrConds($arrCond, $arrParams)
    {

        $arrCond['deleted'] = 0;
        $arrFields = array();
        $arrAllFields = $this->_objDaoTaskZbTidMap->arrFields;
        foreach ($arrParams as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }
            $arrFields[$key] = $value;
        }

        if (empty($arrFields['updateTime'])){
            $arrFields['updateTime'] = time();
        }

        $ret = $this->_objDaoTaskZbTidMap->updateByConds($arrCond, $arrFields);

        return $ret;
    }

    /**
     * @param $arrConds
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getTaskZbTidMapListByConds($arrConds, $arrFields = array(), $offset = 0, $limit = 0)
    {
        $arrFields = empty($arrFields) ? $this->_objDaoTaskZbTidMap->arrFields : $arrFields;

        $arrAppends = null;
        if ($limit > 0){
            $arrAppends = array();
            $arrAppends[] = 'limit ' . $offset . ',' . $limit;
        }
        $arrConds['deleted'] = 0;

        $ret = $this->_objDaoTaskZbTidMap->getListByConds($arrConds, $arrFields, null, $arrAppends);

        return $ret;
    }


    /**
     * 根据主键查询任务信息
     * @param  int $taskId     任务id
     * @param  array  $arrFields 列
     * @return array            结果集
     */
    public function getTaskZbTidMapInfoByTaskId($taskId,$arrFields=array())
    {
        if(empty($taskId)) {
            return array();
        }
        $arrFields = empty($arrFields) ? $this->_objDaoTaskZbTidMap->arrFields : $arrFields;
        $arrConds = array(
            'taskId'   => $taskId,
            'deleted' => 0,
        );
        $return = $this->_objDaoTaskZbTidMap->getRecordByConds($arrConds,$arrFields);
        if( false === $return ) {
            $return = array(); 
        }
        return $return;
    }
    
    /**
     * 
     * @param array $arrConds
     * @return type
     */
    public function getTaskZbTidMapCnt($arrConds = array()) {
        $arrConds['deleted'] = 0;
        $returnTmp = 0;
        $returnTmp += $this->_objDaoTaskZbTidMap->getCntByConds($arrConds);
        return $returnTmp;
    }


}