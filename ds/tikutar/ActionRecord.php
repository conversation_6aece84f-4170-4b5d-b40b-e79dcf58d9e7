<?php
/**
 * @file ActionRecord.php.
 * <AUTHOR>
 * @date: 2018/5/12
 */
class Hkzb_Ds_TikuTar_ActionRecord
{
    const TYPE_ZB_QUESTION_ADOPT      = 1;
    const TYPE_ZB_QUESTION_FIRST_REJECT= 2;
    const TYPE_PT_QUESTION_ADOPT      = 3;

    const TYPE_EXAM_UNBIND            = 4;
    const TYPE_EXAM_BIND              = 5;

    const TYPE_QUESTION_DELETE        = 6;
    const TYPE_QUESTION_WIPE          = 7;
    const TYPE_EXAM_DELETE            = 8;
    const TYPE_TEMPLATE_DELETE        = 9;

    const TYPE_QUESTION_CREATE        = 10;
    const TYPE_QUESTION_EDIT          = 26;
    const TYPE_EXAM_CREATE            = 11;
    const TYPE_TEMPLATE_CREATE        = 12;
    const TYPE_ERROR_CORRECTION       = 13;
    const TYPE_ERROR_CORRECTION_AUDIT = 14;
    const TYPE_ADD_TO_STORAGE         = 15;




    const TYPE_ZB_QUESTION_FIRST_PASS = 16;
    const TYPE_STORAGE_REPLACE        = 17;

    const TYPE_EXAMTIDMAP_DELETE      = 18;
    const TYPE_ZB_QUESTION_SECOND_REJECT= 19;
    const TYPE_PT_ADD_TO_STORAGE      = 20;
    
    const TYPE_TASK_CREATE            = 21;
    const TYPE_PROJECT_CREATE         = 22;
    const TYPE_TASK_UPDATE            = 23;
    const TYPE_PROJECT_UPDATE         = 24;
    const TYPE_TEMPLATE_TO_STORAGE    = 25;


    public static $TYPE_MAP = array(
        self::TYPE_ZB_QUESTION_ADOPT       => '直播试题审核通过',
        self::TYPE_ZB_QUESTION_FIRST_REJECT=> '直播试题第一次审核驳回',
        self::TYPE_PT_QUESTION_ADOPT       => '平台试题审核通过',
        self::TYPE_EXAM_UNBIND             => '试卷解绑',
        self::TYPE_EXAM_BIND               => '试卷绑定',
        self::TYPE_QUESTION_DELETE         => '试题删除',
        self::TYPE_QUESTION_WIPE           => '试题清空',
        self::TYPE_EXAM_DELETE             => '试卷删除',
        self::TYPE_TEMPLATE_DELETE         => '模板删除',
        self::TYPE_QUESTION_CREATE         => '新增试题',
        self::TYPE_QUESTION_EDIT           => '编辑试题',
        self::TYPE_EXAM_CREATE             => '新增试卷',
        self::TYPE_TEMPLATE_CREATE         => '新增模板',
        self::TYPE_ERROR_CORRECTION        => '试题纠错',
        self::TYPE_ERROR_CORRECTION_AUDIT  => '纠错审核',
        self::TYPE_ADD_TO_STORAGE          => '加入试题篮',
        self::TYPE_EXAMTIDMAP_DELETE       => '删除试卷跟题目的关系',
        self::TYPE_ZB_QUESTION_FIRST_PASS  => '试题首次审核通过',
        self::TYPE_STORAGE_REPLACE         => '试题篮试题置换',
        self::TYPE_ZB_QUESTION_SECOND_REJECT=> '直播试题第二次审核驳回',
        self::TYPE_PT_ADD_TO_STORAGE        => '平台试题加入试题篮',
        self::TYPE_TASK_CREATE              => '新增任务',
        self::TYPE_PROJECT_CREATE           => '新增项目',
        self::TYPE_TASK_UPDATE              => '编辑任务',
        self::TYPE_PROJECT_UPDATE           => '编辑项目',
        self::TYPE_TEMPLATE_TO_STORAGE      => '试卷试题加入试题篮',//再次组卷
    );

    private $_objDaoActionRecord;
    public function __construct()
    {
        $this->_objDaoActionRecord = new Hkzb_Dao_TikuTar_ActionRecord();
    }

    /**
     * 操作记录
     * @param  [int]  $acType    操作类型
     * @param  int     $auditUid  uid
     * @param  string  $auditName 用户名
     * @param  integer $zbTid     直播tid
     * @param  array   $content   操作内容
     * @param  array   $ext       扩展信息
     * @return booler             
     */
    public function actionRecord($acType,$auditUid,$auditName,$zbTid=0,$content=array(),$ext=array())
    {
        if(!$acType) {
            Bd_log::warning("Error[ actionRecord param fail]");
            return false;
        }
        $arrFields['zbTid']      = $zbTid;
        $arrFields['acType']     = $acType;
        $arrFields['auditUid']   = $auditUid;
        $arrFields['auditName']  = $auditName;
        $arrFields['createTime'] = time();
        $arrFields['ext']        = json_encode($ext,JSON_UNESCAPED_UNICODE);
        $arrFields['content']    = json_encode($content,JSON_UNESCAPED_UNICODE);
        $ret = $this->_objDaoActionRecord->insertRecords($arrFields);
        return $ret;
    }

    /**
     * 获取今日操作记录数
     * @return array 
     */
    public function getActionRecord($acType,$auditUid)
    {
        if(!$acType || !$auditUid) {
            Bd_log::warning("Error[param error]");
            return false;
        }
        $startTime = (int)strtotime(date("Y-m-d",time()));
        $arrFields = array(
            "create_time >= {$startTime}",
        );
        $arrFields['acType']   = $acType;
        $arrFields['auditUid'] = $auditUid;
        
        $ret = $this->_objDaoActionRecord->getCntByConds($arrFields);
        return $ret;
    }

}