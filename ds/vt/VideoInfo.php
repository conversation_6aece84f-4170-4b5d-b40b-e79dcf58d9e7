<?php

/**
 * @file: VideoInfo.php
 * <AUTHOR>
 * @Datetime: 2018/5/17 11:42
 * @brief : videoInfo
 */
class Hkzb_Ds_Vt_VideoInfo
{
    private $_vtDaoObj = null;
    private static $_objArray = null;

    const ALL_FIELDS = "id,videoId,videoHash,extData,createTime";

    public function __construct()
    {
        $this->_vtDaoObj = new Hkzb_Dao_Vt_VideoInfo();
    }

    public function getVideoInfoByVideoId($videoId, $arrFields = array())
    {
        if (empty($videoId)) {
            return array();
        }

        $arrConds = array(
            "videoId" => $videoId,
        );

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrAppends = array(
            "order by create_time desc",
        );

        $result = $this->_vtDaoObj->getRecordByConds($arrConds, $arrFields, NULL, $arrAppends);
        if (empty($result)) {
            return array();
        }
        $videoInfo = array();
        $videoInfo['videoId'] = $result['videoId'];
        $record = json_decode($result['extData'],true);
        if (isset($record['videoName']) && !empty($record['videoName'])) {
            if (!isset(self::$_objArray[$record['videoBkt']])) {
                self::$_objArray[$record['videoBkt']] = new Hk_Service_Bos($record['videoBkt']);
            }
            $videoInfo['oriVideo'] = self::$_objArray[$record['videoBkt']]->getObject($record['videoName']);
        }
        if (isset($record['unencodeM3u8']) && !empty($record['unencodeM3u8'])) {
            if (!isset(self::$_objArray[$record['m3u8Bkt']])) {
                self::$_objArray[$record['m3u8Bkt']] = new Hk_Service_Bos($record['m3u8Bkt']);
            }
            $videoInfo['unencodeM3u8'] = self::$_objArray[$record['m3u8Bkt']]->getObject($record['unencodeM3u8']);
        }
        if (isset($record['encodeM3u8']) && !empty($record['encodeM3u8'])) {
            if (!isset(self::$_objArray[$record['encodeM3u8Bkt']])) {
                self::$_objArray[$record['encodeM3u8Bkt']] = new Hk_Service_Bos($record['encodeM3u8Bkt']);
            }
            $videoInfo['encodeM3u8'] = self::$_objArray[$record['encodeM3u8Bkt']]->getObject($record['encodeM3u8']);
        }
        if (isset($record['encodeM3u8']) && !empty($record['encodeM3u8'])) {
            $videoInfo['playKey'] = substr($record['encodeM3u8'], 0, -5);
        }
        return $videoInfo;
    }

    public function addVideoInfo($arrParams)
    {
        $arrField = array(
            'videoId' => isset($arrParams['videoId']) ? strval($arrParams['videoId']) : '',
            'videoHash' => isset($arrParams['videoHash']) ? strval($arrParams['videoHash']) : '',
            'extData' => isset($arrParams['extData']) ? strval($arrParams['extData']) : '',
            'createTime' => isset($arrParams['createTime']) ? intval($arrParams['createTime']) : '',
        );
        if (empty($arrField['videoId']) || empty($arrField['videoHash'])) {
            Bd_Log::warning("Error:[param error], Detail:[json_encode($arrField)]");
            return false;
        }
        $res = $this->_vtDaoObj->insertRecords($arrField);
        return $res ? $this->_vtDaoObj->getInsertId() : false;
    }

    public function getVideoInfoByData($record){
        if (isset($record['videoName']) && !empty($record['videoName'])) {
            if (!isset(self::$_objArray[$record['videoBkt']])) {
                self::$_objArray[$record['videoBkt']] = new Hk_Service_Bos($record['videoBkt']);
            }
            $videoInfo['oriVideo'] = self::$_objArray[$record['videoBkt']]->getObject($record['videoName']);
        }
        if (isset($record['unencodeM3u8']) && !empty($record['unencodeM3u8'])) {
            if (!isset(self::$_objArray[$record['m3u8Bkt']])) {
                self::$_objArray[$record['m3u8Bkt']] = new Hk_Service_Bos($record['m3u8Bkt']);
            }
            $videoInfo['unencodeM3u8'] = self::$_objArray[$record['m3u8Bkt']]->getObject($record['unencodeM3u8']);
        }
        if (isset($record['encodeM3u8']) && !empty($record['encodeM3u8'])) {
            if (!isset(self::$_objArray[$record['encodeM3u8Bkt']])) {
                self::$_objArray[$record['encodeM3u8Bkt']] = new Hk_Service_Bos($record['encodeM3u8Bkt']);
            }
            $videoInfo['encodeM3u8'] = self::$_objArray[$record['encodeM3u8Bkt']]->getObject($record['encodeM3u8']);
        }
        if (isset($record['encodeM3u8']) && !empty($record['encodeM3u8'])) {
            $videoInfo['playKey'] = substr($record['encodeM3u8'], 0, -5);
        }
        return $videoInfo;
    }
}