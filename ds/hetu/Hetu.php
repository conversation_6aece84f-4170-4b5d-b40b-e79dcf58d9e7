<?php

class Qdlib_Ds_Hetu_Hetu
{
    const DELETED_YES = 1;
    const DELETED_NO = 0;

    /**
     * @var Qdlib_Dao_Hetu_Hetu
     */
    protected $dao;

    protected function __construct(Qdlib_Dao_Hetu_Hetu $dao)
    {
        $this->dao = $dao;
    }

    /**
     * @return false|int
     */
    public function insertRecords($fields)
    {
        $ret = $this->dao->insertRecords($fields);
        if (!$ret) {
            return false;
        }
        return $this->dao->getInsertId();
    }

    /**
     * @return false|int
     */
    public function multiInsert($values, $onDup = null)
    {
        return $this->dao->multiInsert(array_keys(current($values)), $values, $onDup);
    }

    /**
     * @return false|int
     */
    public function updateByConds($conditions, $fields)
    {
        return $this->dao->updateByConds($conditions, $fields);
    }

    /**
     * @return false|array
     */
    public function getRecordByConds($conditions, $fields = null, $appends = null, $options = null, $index = null)
    {
        if (!$appends) {
            $appends = 'limit 1';
        }
        return $this->dao->getRecordByConds($conditions, $fields, $options, $appends, $index);
    }

    /**
     * @return false|array
     */
    public function getListByConds($conditions, $fields = null, $appends = null, $options = null, $index = null)
    {
        return $this->dao->getListByConds($conditions, $fields, $options, $appends, $index);
    }

    /**
     * @return false|int
     */
    public function getCntByConds($conditions)
    {
        return $this->dao->getCntByConds($conditions);
    }

    //批量获取信息
    public function getInfoByIds($ids, $arrFields = null, $arrConds = null)
    {
        $arrConds[] = 'id in ('.implode(',', $ids).')';
        return $result = $this->dao->getListByConds($arrConds,$arrFields);
    }

    /**
     * @return bool
     */
    public function startTransaction()
    {
        return $this->dao->startTransaction();
    }

    /**
     * @return bool
     */
    public function commit()
    {
        return $this->dao->commit();
    }

    /**
     * @return bool
     */
    public function rollback()
    {
        return $this->dao->rollback();
    }
}