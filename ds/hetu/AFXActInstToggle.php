<?php

class Qdlib_Ds_Hetu_AFXActInstToggle extends Qdlib_Ds_Hetu_Hetu
{
    const STATE_ON = 1;
    const STATE_OFF = 2;

    const CACHE_SWITCH_STATE_TEMPLATE = 'hetu:act:inst:switch:state:%d:%d';
    const CACHE_SWITCH_STATE_TTL = 600;
    const CACHE_SWITCH_LOCK_TEMPLATE = 'hetu:act:inst:switch:lock:%d';
    const CACHE_SWITCH_LOCK_TTL = 10;

    public function __construct()
    {
        parent::__construct(new Qdlib_Dao_Hetu_AFXActInstToggle());
    }

    public function lock(int $actId): bool
    {
        if (Qdlib_Util_Cache::getQudaoRedis()->set(sprintf(self::CACHE_SWITCH_LOCK_TEMPLATE, $actId), '1', ['NX', 'EX' => self::CACHE_SWITCH_LOCK_TTL])) {
            return true;
        }
        return false;
    }

    public function unlock(int $actId): bool
    {
        return is_int((Qdlib_Util_Cache::getQudaoRedis())->del(sprintf(self::CACHE_SWITCH_LOCK_TEMPLATE, $actId)));
    }

    public function delInstStateCache(int $actId, array $instIds): bool
    {
        $keys = [];
        foreach ($instIds as $instId) {
            $keys[$instId] = $this->stateCacheKey($actId, $instId);
        }
        $tmp = Qdlib_Util_Cache::getQudaoRedis()->del($keys);
        Qdlib_Util_Log::addNotice('inst_state_cache_del', json_encode([
            'keys' => $keys,
            'result' => $tmp,
        ], JSON_UNESCAPED_UNICODE));
        return is_int($tmp);
    }

    public function getInstState(int $actId, int $instId, bool $cache = true)
    {
        if ($cache) {
            $key = $this->stateCacheKey($actId, $instId);
            $state = Qdlib_Util_Cache::getQudaoRedis()->get($key);
            if ($state) {
                Qdlib_Util_Log::addNotice('inst_state_cache_get', json_encode([
                    'key' => $key,
                    'result' => $state,
                ], JSON_UNESCAPED_UNICODE));
                return (int)$state;
            }
        }

        $lastRecord = $this->getRecordByConds(['actId' => $actId], ['currentInstIds'], ['order by id desc', 'limit 1']);
        if (false === $lastRecord) {
            return false;
        }

        $state = self::STATE_ON;
        if ($lastRecord) {
            $offedInstIds = explode(',', $lastRecord['currentInstIds']);
            if (in_array($instId, $offedInstIds)) {
                $state = self::STATE_OFF;
            }
        }

        if ($cache) {
            $tmp = Qdlib_Util_Cache::getQudaoRedis()->set($key, (string)$state, self::CACHE_SWITCH_STATE_TTL);
            Qdlib_Util_Log::addNotice('inst_state_cache_set', json_encode([
                'key' => $key,
                'value' => $state,
                'result' => $tmp,
            ], JSON_UNESCAPED_UNICODE));
        }

        return $state;
    }

    private function stateCacheKey(int $actId, int $instId): string
    {
        return sprintf(self::CACHE_SWITCH_STATE_TEMPLATE, $actId, $instId);
    }
}