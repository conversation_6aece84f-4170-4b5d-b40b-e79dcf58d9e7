<?php
/**
 * Copyright (c) 2016 zuoyebang.com, Inc. All Rights Reserved
 * @file:LessonState.php
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @date: 2016/7/13
 * @time: 11:19
 * @desc:
 */

class Zhibo_Ds_LessonState
{
    //出勤状态
    const ATTEND_NO = 0; //未出勤
    const ATTEND_YES = 1; //出勤
    //学习报告状态
    const REPORT_NO = 0;  //发送报告
    const REPORT_YES = 1; //未发送

    private $objDaoLessonState;

    public function __construct()
    {
        $this->objDaoLessonState = new Zhibo_Dao_LessonState();
    }

    /**
     * 提交课后作业
     *
     * @param  int $studentUid 学生uid
     * @param  int $courseId 课程id
     * @param  int $lessonId 课节id
     * @return bool
     */
    public function submitHomework($studentUid, $courseId, $lessonId, $status)
    {
        if (intval($studentUid) <= 0 || intval($courseId) <= 0 || intval($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId lessonId:$lessonId]");
            return false;
        }
        //将未提交作业的状态置为已提交
        $arrConds = array(
            'studentUid' => intval($studentUid),
            'courseId' => intval($courseId),
            'lessonId' => intval($lessonId),
        );
        $arrFields = array(
            'homework' => intval($status),
            'updateTime' => time(),
        );
        $ret = $this->objDaoLessonState->updateByConds($arrConds, $arrFields);
        return $ret;
    }

    /**获取提交作业数
     * @param int $courseId
     * @param int $studentUid
     * @return array|bool
     */
    public function getSubmitHomeworkNumByCourseId($courseId, $studentUid)
    {
        if (intval($studentUid) <= 0 || intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId]");
            return false;
        }

        $arrConds = array(
            'courseId' => intval($courseId),
            'studentUid' => intval($studentUid),
            'homework' => Zhibo_Ds_ExerciseDetail::STATUS_TOREVIEW,
        );
        $ret = $this->objDaoLessonState->getCntByConds($arrConds);;
        return $ret;
    }

    /**
     * 答对题目加法
     *
     * @param  int $studentUid 学生uid
     * @param  int $courseId 课程id
     * @param  int $lessonId 课节id
     * @param  int $rightNum 答对数
     * @return bool true/false
     */
    public function addRightNum($studentUid, $courseId, $lessonId, $rightNum = 1)
    {
        if (intval($courseId) <= 0 || intval($lessonId) <= 0 || intval($studentUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId lessonId:$lessonId studentUid:$studentUid]");
            return false;
        }
        $arrConds = array(
            'courseId' => intval($courseId),
            'lessonId' => intval($lessonId),
            'studentUid' => intval($studentUid),
        );
        //获取已有的答对题数
        $lessonState = $this->getLessonState($studentUid, $courseId, $lessonId);
        if (!$lessonState) {
            Bd_Log::warning("Error:[stateInfo error], Detail:[courseId:$courseId lessonId:$lessonId studentUid:$studentUid]");
            return false;
        }
        $rightNum = intval($lessonState['rightNum']) + $rightNum;
        $arrFields = array(
            'rightNum' => $rightNum,
            'updateTime' => time(),
        );
        $ret = $this->objDaoLessonState->updateByConds($arrConds, $arrFields);
        return $ret;
    }

    /**
     * 获取学生课堂信息
     *
     * @param  int $studentUid 学生uid
     * @param  int $courseId 课程id
     * @param  int $lessonId 课节id
     * @return bool|array
     */
    public function getLessonState($studentUid, $courseId, $lessonId, $arrFields = array())
    {
        if (intval($studentUid) <= 0 || intval($courseId) <= 0 || intval($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId lessonId:$lessonId]");
            return false;
        }
        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_LessonState::$allFields;
        }
        $arrConds = array(
            'studentUid' => intval($studentUid),
            'courseId' => intval($courseId),
            'lessonId' => intval($lessonId),
        );
        $ret = $this->objDaoLessonState->getRecordByConds($arrConds, $arrFields);
        return $ret;
    }

    /**获取课堂答题正确数
     * @param int $courseId
     * @param int $studentUid
     * @return array|bool
     */
    public function getRightRateByCourseId($courseId, $studentUid)
    {
        if (intval($studentUid) <= 0 || intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId]");
            return false;
        }
        $arrFields = array('rightNum', 'totalNum');
        $arrConds = array(
            'courseId' => intval($courseId),
            'studentUid' => intval($studentUid),
        );
        $ret = $this->objDaoLessonState->getListByConds($arrConds, $arrFields);
        if ($ret) {
            $right = 0;
            $total = 0;
            $rate = 0;
            foreach ($ret as $list) {
                $right += $list['rightNum'];
                $total += $list['totalNum'];
            }
            $rate = ($total > 0) ? (intval($right) * 100) / intval($total) : 0;
            $ret = $rate;
        }
        return $ret;
    }

    /**
     * 新增学生课节状态
     * @param  array $arrParams 状态属性
     * @return bool true/false
     */
    public function addLessonState($arrParams)
    {
        if (intval($arrParams['studentUid']) <= 0 || intval($arrParams['courseId']) <= 0 || intval($arrParams['lessonId']) <= 0 || intval($arrParams['teacherUid']) <= 0 || intval($arrParams['assistantUid']) <= 0 || intval($arrParams['classId']) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[param: " . json_encode($arrParams) . "]");
            return 0;
        }
        $now = time();
        $arrFields = array(
            'studentUid' => intval($arrParams['studentUid']),
            'courseId' => intval($arrParams['courseId']),
            'lessonId' => intval($arrParams['lessonId']),
            'classId' => intval($arrParams['classId']),
            'teacherUid' => intval($arrParams['teacherUid']),
            'assistantUid' => intval($arrParams['assistantUid']),
            'createTime' => $now,
            'updateTime' => $now,
            'extData' => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
        );
        $ret = $this->objDaoLessonState->insertRecords($arrFields);
        return $ret;
    }

    /**
     * 设置扩展字段
     *
     * @param  int $studentUid 学生uid
     * @param  int $courseId 课程id
     * @param  int $lessonId 课节id
     * @return bool|false
     */
    public function setExtData($studentUid, $courseId, $lessonId, $arrNewExtData)
    {
        if (intval($studentUid) <= 0 || intval($courseId) <= 0 || intval($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId lessonId:$lessonId]");
            return false;
        }
        $ret = $this->getLessonState($studentUid, $courseId, $lessonId, array('extData'));
        if (empty($ret)) {
            Bd_Log::warning("Error:[getLessonDetail error], Detail:[studentUid:$studentUid courseId:$courseId lessonId:$lessonId]");
            return false;
        }
        $arrExtData = $ret['extData'];
        foreach ($arrNewExtData as $key => $value) {
            $arrExtData[$key] = $value;
        }
        $arrFields = array(
            'extData' => json_encode($arrExtData),
            'updateTime' => time(),
        );
        $arrConds = array(
            'studentUid' => intval($studentUid),
            'courseId' => intval($courseId),
            'lessonId' => intval($lessonId),
        );
        $ret = $this->objDaoLessonState->updateByConds($arrConds, $arrFields);
        return $ret;
    }
}