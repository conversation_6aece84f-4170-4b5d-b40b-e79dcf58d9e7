<?php
/**
 * File: UpdateStgFields.php
 * User: lih<PERSON><PERSON>e
 * Date: 2018/5/10
 * Time: 下午7:08
 * Desc: 升级条件名单表
 */
class Hkzb_Ds_Update_UpdateStgFields
{
    const ALL_FIELDS = 'id,taskId,stgId,item,createTime';

    private $_objDaoUpdateStgFields;
    public function __construct()
    {
        $this->_objDaoUpdateStgFields = new Hkzb_Dao_Fudao_UpdateStgFields();
    }

    public function addStgFields($taskId, $stgId, $arrItems)
    {
        if (!$taskId || !$stgId || !$arrItems || !is_array($arrItems)) {
            return false;
        }
        $now = time();
        $arrItems = array_unique($arrItems);
        $arrItems = array_chunk($arrItems, 500);
        foreach ($arrItems as $items) {
            $sql = 'insert into tblUpdateStgFields(task_id,stg_id,item,create_time) value';
            foreach ($items as $item) {
                $sql .= "($taskId,$stgId,$item,$now),";
            }
            $sql = rtrim($sql, ',');
            $ret = $this->_objDaoUpdateStgFields->query($sql);
            if (!$ret) {
                Bd_Log::warning('Error:[insert strategy fields error] Detail:[taskId:'.$taskId.' stgId:'.$stgId.']');
                return false;
            }
        }
        return true;
    }

    public function checkHit($taskId, $stgId, $item)
    {
        $taskId = intval($taskId);
        $stgId = intval($stgId);
        $item = strval(intval($item));
        if ($taskId <= 0 || $stgId <= 0 || !$item) {
            Bd_Log::warning("Error:[param error] Detail:[taskId:$taskId,stgId:$stgId,item:$item]");
            return false;
        }
        $arrConds = array(
            'taskId' => $taskId,
            'stgId' => $stgId,
            'item' => $item,
        );
        $arrFields = array('id');
        $ret = $this->_objDaoUpdateStgFields->getRecordByConds($arrConds, $arrFields);
        return $ret ? true : false;
    }

    public function getItems($taskId, $stgId, $offset = 0, $limit = 0)
    {
        $taskId = intval($taskId);
        $stgId = intval($stgId);
        if ($taskId <= 0 || $stgId <= 0) {
            Bd_Log::warning("Error:[param error] Detail:[taskId:$taskId,stgId:$stgId]");
            return false;
        }
        $arrConds = array(
            'taskId' => $taskId,
            'stgId' => $stgId,
        );
        $arrFields = array('item');
        if ($limit > 0) {
            $arrAppends = array(
                "limit $offset,$limit",
            );
        }
        $ret = $this->_objDaoUpdateStgFields->getListByConds($arrConds, $arrFields, NULL, $arrAppends);
        if ($ret === false) {
            Bd_Log::warning("Error:[db error] Detail:[taskId:$taskId,stgId:$stgId]");
            return false;
        }
        $res = array();
        foreach ($ret as $item) {
            $res[] = $item['item'];
        }
        return $res;
    }
}
