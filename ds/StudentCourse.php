<?php
/**
 * Copyright (c) 2016 zuoyebang.com, Inc. All Rights Reserved
 * @file:StudentCourse.php
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @date: 2016/7/4
 * @time: 19:39
 * @desc:
 */

class Zhibo_Ds_StudentCourse
{
    //状态
    const STATUS_OK = 0;
    const STATUS_DELETED = 1;

    //状态
    const STATUS_NOT_FINISHED = 0; //未结束
    const STATUS_FINISHED = 1; //已结束
    const STATUS_DELETED_ = 2; //status已删除
    const STATUS_FORZEN = 3; //status冻结
    static $STATUS_ARRAY = array(
        self::STATUS_NOT_FINISHED => '未结束',
        self::STATUS_FINISHED => '已结束',
        self::STATUS_DELETED_ => '已删除',
        self::STATUS_FORZEN => '已冻结',
    );


    private $objDaoStudentCourse;

    public function __construct()
    {
        $this->objDaoStudentCourse = new Zhibo_Dao_StudentCourse();
    }

    /**
     * 获取学生课程详情
     *
     * @param  int $studentUid 学生uid
     * @param  int $courseId 课程id
     * @param  array $arrFields 指定属性
     * @return array|bool
     */
    public function getStudentCourseInfo($studentUid, $courseId, $arrFields = array())
    {
        if (intval($studentUid) <= 0 || intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId]");
            return array();
        }
        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_StudentCourse::$allFields;
        }
        $arrConds = array(
            'studentUid' => intval($studentUid),
            'courseId' => intval($courseId),
        );
        $ret = $this->objDaoStudentCourse->getRecordByConds($arrConds, $arrFields);
        return $ret;
    }

    /**
     * 批量获取某个学生的某些课程
     * @param int $studentUid
     * @param array $arrCourseId
     * @param array $arrFields
     * @return multitype:|boolean|multitype:Ambigous <array, false>
     */
    public function getStudentCourseInfoArray($studentUid, $arrCourseId, $arrFields = array())
    {
        $studentUid = intval($studentUid);
        if ($studentUid <= 0 || empty($arrCourseId)) {
            return array();
        }
        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_StudentCourse::$allFields;
        } elseif (!in_array('courseId', $arrFields)) {
            $arrFields[] = 'courseId';
        }
        $arrCourseId = array_unique($arrCourseId);
        $strCourseIds = implode(',', $arrCourseId);
        $arrConds = array(
            'studentUid' => $studentUid,
            "course_id in ($strCourseIds)",
        );
        $ret = $this->objDaoStudentCourse->getListByConds($arrConds, $arrFields);
        if (false === $ret) {
            return false;
        }
        $arrRet = array();
        foreach ($ret as $course) {
            $courseId = $course['courseId'];
            $arrRet[$courseId] = $course;
        }
        return $arrRet;
    }

    /**
     * @param $arrConds
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getCourseListByConds($arrConds, $arrFields = array(), $offset = 0, $limit = 20, $order = 'create_time', $sort = 'desc')
    {
        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_StudentCourse::$allFields;
        }
        $arrAppends = array(
            "order by $order $sort",
            "limit $offset, $limit",
        );
        $arrConds['deleted'] = self::STATUS_OK;
        $ret = $this->objDaoStudentCourse->getListByConds($arrConds, $arrFields, NULL, $arrAppends);
        return $ret;
    }

    /**
     * 获取指定学生课程列表
     *
     * @param  int $studentUid 学生uid
     * @param  array $arrFields 指定属性
     * @return array|bool
     */
    public function getCourseListAll($studentUid, $arrFields = array())
    {
        if (intval($studentUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid]");
            return false;
        }
        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_StudentCourse::$allFields;
        }
        $arrConds = array(
            'studentUid' => intval($studentUid),
            'deleted' => self::STATUS_OK,
        );
        $ret = $this->objDaoStudentCourse->getListByConds($arrConds, $arrFields);
        return $ret;
    }

    /**
     * 检查学生是否合法
     *
     * @param  int $studentUid 学生uid
     * @param  int $courseId 课程id
     * @return bool true/false
     */
    public function checkValidStudent($studentUid, $courseId)
    {
        if (intval($studentUid) <= 0 || intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId]");
            return false;
        }
        $ret = $this->getStudentCourseInfo($studentUid, $courseId, array('deleted', 'status'));
        if (empty($ret)) {
            return false;
        }
        if (self::STATUS_OK === intval($ret['deleted']) && $ret['status'] < self::STATUS_DELETED_) {
            return true;
        }
        return false;
    }

    /**
     * 根据条件获取课程数量
     * @param array $arrConds
     * @return integer|boolean
     */
    public function getCourseCntByConds($arrConds)
    {
        $arrConds['deleted'] = self::STATUS_OK;
        $ret = $this->objDaoStudentCourse->getCntByConds($arrConds);
        return $ret;
    }

    /**
     * 新增学生课程
     *
     * @param  mix $arrParams 班级属性
     * @return bool true/false
     */
    public function addStudentCourse($arrParams)
    {
        if (intval($arrParams['studentUid']) <= 0 || intval($arrParams['courseId']) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[param: " . json_encode($arrParams) . "]");
            return 0;
        }
        $now = time();
        $arrFields = array(
            'studentUid' => intval($arrParams['studentUid']),
            'courseId' => intval($arrParams['courseId']),
            'teacherUid' => isset($arrParams['teacherUid']) ? intval($arrParams['teacherUid']) : 0,
            'assistantUid' => isset($arrParams['assistantUid']) ? intval($arrParams['assistantUid']) : 0,
            'classId' => isset($arrParams['classId']) ? intval($arrParams['classId']) : 0,
            'className' => isset($arrParams['className']) ? strval($arrParams['className']) : '',
            'curLessonId' => isset($arrParams['curLessonId']) ? intval($arrParams['curLessonId']) : 0,
            'curLessonName' => isset($arrParams['curLessonName']) ? strval($arrParams['curLessonName']) : '',
            'curLessonStartTime' => isset($arrParams['curLessonStartTime']) ? intval($arrParams['curLessonStartTime']) : 0,
            'curLessonStopTime' => isset($arrParams['curLessonStopTime']) ? intval($arrParams['curLessonStopTime']) : 0,
            'deleted' => self::STATUS_OK,
            'createTime' => $now,
            'updateTime' => $now,
            'extData' => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
            'type' => isset($arrParams['type']) ? json_encode($arrParams['type']) : 0,
        );
        $ret = $this->objDaoStudentCourse->insertRecords($arrFields);
        return $ret;
    }

    /**
     * 获取学生课程详情
     *
     * @param  int $studentUid 学生uid
     * @param  int $courseId 课程id
     * @param  array $arrFields 指定属性
     * @return bool|array
     */
    public function getStudentCourseInfoArr($studentUid, $courseIds, $arrFields = array())
    {
        if (intval($studentUid) <= 0 || empty($courseIds)) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid]");
            return false;
        }
        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_StudentCourse::$allFields;
        }
        $arrConds = array(
            'studentUid' => intval($studentUid),
        );
        $strCourseIds = implode(',', $courseIds);
        $arrConds[] = "course_id in ($strCourseIds)";
        $ret = $this->objDaoStudentCourse->getListByConds($arrConds, $arrFields);
        return $ret;
    }
}