<?php


/**
 * @file    ExamAnswer.php
 * <AUTHOR>
 * @date    2018-5-23
 * @brief   测试系统 -- 答题相关
 *
 **/
class Hkzb_Ds_Inclass_ExamAnswer {

    private $_objDaoAnswer;
    private $_objDaoAnswerDetail;
    private $TEMP_CASH;

    public function __construct() {
        $this->_objDaoAnswer       = new Hkzb_Dao_Inclass_ExamAnswer();
        $this->_objDaoAnswerDetail = new Hkzb_Dao_Inclass_AnswerDetail();
    }

    // 【ByYiKe】专门针对课中测试（堂堂测），将课中测试接入到新的存储结构上，
    public function getInclassStuAnswerByExamIdByYiKe( $intUid, $intExamId ) {

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

        $obj = new Hkzb_Ds_Examrpc_ExamRpcApi();

        $arrAnswer = $obj->getInclassStuAnswerByExamIdRPC($intExamId,$intUid,10);


        if (empty( $arrAnswer )) {
            return array();
        }
        // 堂堂测只答一次，故取答题列表的第一条数据
        $arrAnswer = $arrAnswer[ 0 ];

        // 是否已经结束
        $intIsFinish = intval($arrAnswer[ 'isFinish' ]);
        // 考试得分
        $intScore = intval($arrAnswer[ 'score' ]);


        $arrExamInfo = $obj->getInclassExamInfoByExamIdRPC($intExamId,10);

        // 试卷总分
        $intTotalScore = intval($arrExamInfo['examInfo']['totalScore'] * 10);

        // 答题详情的格式转换
        $arrAnswerList = $arrAnswer[ 'answerList' ];
        $arrTidList    = array();
        foreach ($arrAnswerList as $tid => $value) {
            if (isset( $value[ 'list' ] )) {
                foreach ($value[ 'list' ] as $order => $subanswer) {
                    $key                = $tid . '_' . $order;
                    $arrTidList[ $key ] = array(
                        'c'  => self::parseChoiceFromStrToBit($subanswer[ 'answer' ]),
                        'j'  => intval($subanswer[ 'isRight' ]),
                        'du' => intval($subanswer[ 'duration' ]),
                    );
                }
            } else {
                $key                = $tid;
                $arrTidList[ $key ] = array(
                    'c'  => self::parseChoiceFromStrToBit($value[ 'answer' ]),
                    'j'  => intval($value[ 'isRight' ]),
                    'du' => intval($value[ 'duration' ]),
                );
            }
        }

        $arrStuAnswer = array(
            'uid'      => $intUid,
            'examId'   => $intExamId,
            'isFinish' => $intIsFinish,
            'score'    => $intScore,
            'totalScore' => $intTotalScore,
            'tidList'  => $arrTidList,
            'createTime'=> $arrAnswer['createTime']
        );

        // 将作答信息包一层array，目的是兼容现有代码，现有代码就是获取答题详情之后，取数组里第一个元素
        $arrOutput = array(
            $arrStuAnswer,
        );

        return $arrOutput;
    }


    // 专门针对课中测试（堂堂测），将课中测试接入到新的存储结构上
    public function getInclassStuAnswerByExamId( $intUid, $intExamId ) {

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

        // 获取答题概况
        $arrAnswerConds  = array(
            'examId'  => $intExamId,
            'uid'     => $intUid,
            'deleted' => 0,
        );
        $arrAnswerFields = $this->_objDaoAnswer->arrFields;
        // 答题列表
        $arrAnswer = $this->_objDaoAnswer->getListByConds($intExamId, $arrAnswerConds, $arrAnswerFields);

        if (empty( $arrAnswer )) {
            return array();
        }
        // 堂堂测只答一次，故取答题列表的第一条数据
        $arrAnswer = $arrAnswer[ 0 ];

        // 是否已经结束
        $intIsFinish = intval($arrAnswer[ 'isFinish' ]);
        // 考试得分
        $intScore = intval($arrAnswer[ 'score' ]);

        // 获取总分
        $objDaoExam = new Hkzb_Dao_Inclass_Exam();
        $arrExamConds  = array(
            'examId' => $intExamId,
        );
        $arrExamFields = $objDaoExam->arrFields;

        $arrExamInfo = $objDaoExam->getRecordByConds($arrExamConds, $arrExamFields);
        // 试卷总分
        $intTotalScore = intval($arrExamInfo[ 'content' ][ 'totalScore' ]);

        // 答题详情的格式转换
        $arrAnswerList = $arrAnswer[ 'answerList' ];
        $arrTidList    = array();
        foreach ($arrAnswerList as $tid => $value) {
            if (isset( $value[ 'list' ] )) {
                foreach ($value[ 'list' ] as $order => $subanswer) {
                    $key                = $tid . '_' . $order;
                    $arrTidList[ $key ] = array(
                        'c'  => self::parseChoiceFromStrToBit($subanswer[ 'answer' ]),
                        'j'  => intval($subanswer[ 'isRight' ]),
                        'du' => intval($subanswer[ 'duration' ]),
                    );
                }
            } else {
                $key                = $tid;
                $arrTidList[ $key ] = array(
                    'c'  => self::parseChoiceFromStrToBit($value[ 'answer' ]),
                    'j'  => intval($value[ 'isRight' ]),
                    'du' => intval($value[ 'duration' ]),
                );
            }
        }

        $arrStuAnswer = array(
            'uid'      => $intUid,
            'examId'   => $intExamId,
            'isFinish' => $intIsFinish,
            'score'    => $intScore,
            'totalScore' => $intTotalScore,
            'tidList'  => $arrTidList,
            'createTime'=> $arrAnswer['createTime']
        );

        // 将作答信息包一层array，目的是兼容现有代码，现有代码就是获取答题详情之后，取数组里第一个元素
        $arrOutput = array(
            $arrStuAnswer,
        );

        return $arrOutput;
    }
    // 专门针对课中测试（堂堂测），将课中测试接入到新的存储结构上
    public function getInclassStuAnswerByExamIdReport( $intUid, $intExamId ) {
        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

//rpc        // 获取答题概况
//rpc        $arrAnswerConds  = array(
//rpc            'examId'  => $intExamId,
//rpc            'uid'     => $intUid,
//rpc            'deleted' => 0,
//rpc        );
//rpc        $arrAnswerFields = $this->_objDaoAnswer->arrFields;
//rpc        // 答题列表
//rpc        $arrAnswer = $this->_objDaoAnswer->getListByConds($intExamId, $arrAnswerConds, $arrAnswerFields);

        $examRpcObj = new Hkzb_Ds_Examrpc_ExamRpcApi();
        //answerlist
        $arrAnswer = $examRpcObj->getInclassStuAnswerByExamIdRPC($intExamId,$intUid,10);

        if (empty( $arrAnswer )) {
            return array();
        }
        // 堂堂测只答一次，故取答题列表的第一条数据
        $arrAnswer = $arrAnswer[ 0 ];

        // 是否已经结束
        $intIsFinish = intval($arrAnswer[ 'isFinish' ]);
        // 考试得分
        $intScore = intval($arrAnswer[ 'score' ]);

//rpc        // 获取总分
//rpc        $objDaoExam = new Hkzb_Dao_Inclass_Exam();
//rpc        $arrExamConds  = array(
//rpc            'examId' => $intExamId,
//rpc        );
//rpc        $arrExamFields = $objDaoExam->arrFields;

//rpc        $arrExamInfo = $objDaoExam->getRecordByConds($arrExamConds, $arrExamFields);

        $examRpcObj = new Hkzb_Ds_Examrpc_ExamRpcApi();
        $arrExamInfo = $examRpcObj->getExamInfoByRpc($intExamId,10);

        // 试卷总分
        $intTotalScore = intval($arrExamInfo[ 'content' ][ 'totalScore' ]);

        // 答题详情的格式转换
        $arrAnswerList = $arrAnswer[ 'answerList' ];
        $correctList = array();#正确
        $error = array();#错误
        $notDo = array();#未做
        foreach ($arrAnswerList as $tid => $value) {
            if (isset( $value[ 'list' ] )) {
                $suError = 0;
                $num=0;
                $suCorrect=0;
                $suNodo=0;
                foreach ($value[ 'list' ] as $order => $subanswer) {
                    $num++;
                    if(intval($subanswer[ 'isRight' ]) == 1){
                        $suCorrect++;
                        continue;
                    }
                    //判断一错算错，全对算对，全空为未做
                    if(empty($subanswer[ 'answer' ]) && $subanswer[ 'answer' ] !== '0'){
                        $suNodo++;
                        continue;
                    }
                    
                    $suError++;
                }
                if($suError > 0 || ($suNodo > 0 && $suCorrect > 0)){
                    $error[]=$tid;
                    continue;
                }
                if($suError == 0 && $suCorrect == 0){
                    $notDo[] = $tid;
                    continue;
                }
                $correctList[] = $tid;
            } else {
                if(intval($value[ 'isRight' ]) == 1){
                    $correctList[] = $tid;
                    continue;
                }
                if(empty($value[ 'answer' ]) && $value[ 'answer' ] !== '0'){
                    $notDo[] = $tid;
                    continue;
                }
                
                $error[] = $tid;
            }
        }

        $arrStuAnswer = array(
            'uid'      => $intUid,
            'examId'   => $intExamId,
            'isFinish' => $intIsFinish,
            'score'    => $intScore,
            'totalScore' => $intTotalScore,
            'correctList'  => $correctList,
            'error'    => $error,
            'notDo'    => $notDo,
            'createTime'=> $arrAnswer['createTime'],
        );
        return $arrStuAnswer;
    }
    // 专门针对堂堂测
    public function addExam( $intUid, $arrData ) {

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

        // 试卷id
        $intExamId = intval($arrData[ 'examId' ]);
        // 答题是否结束
        $intIsFinish = intval($arrData[ 'isFinish' ]);
        // 本次答题得分
        $intTotalScore = intval($arrData[ 'score' ]);

        // 处理答题详情
        $arrTidList = $arrData[ 'tidList' ];

        $arrAnswerList = array();
        foreach ($arrTidList as $tid => $value) {

            if (count(explode("_", $tid)) == 2) {
                $_tid   = intval(explode("_", $tid)[ 0 ]);
                $_order = intval(explode("_", $tid)[ 1 ]);

                if (!isset( $arrAnswerList[ $_tid ] )) {
                    $arrAnswerList[ $_tid ] = array(
                        'list' => array(),
                    );
                }
                $arrAnswerList[ $_tid ][ 'list' ][ $_order ] = array(
                    "answer"   => self::parseChoiceFromBitToStr($value[ 'choice' ]),
                    'isRight'  => $value[ 'judge' ],
                    "score"    => $value[ 'score' ],
                    'duration' => $value[ 'duration' ],
                );
            } else {
                $_tid = intval($tid);

                $arrAnswerList[ $_tid ] = array(
                    "answer"   => self::parseChoiceFromBitToStr($value[ 'choice' ]),
                    'isRight'  => $value[ 'judge' ],
                    "score"    => $value[ 'score' ],
                    'duration' => $value[ 'duration' ],
                );
            }
        }

        // 获取试卷详情，用于判断题型
        $objDaoExam    = new Hkzb_Dao_Inclass_Exam();
        $arrExamConds  = array(
            'examId' => $intExamId,
        );
        $arrExamFields = $objDaoExam->arrFields;
        $arrExamInfo   = $objDaoExam->getRecordByConds($arrExamConds, $arrExamFields);

        // 获取答题详情列表
        $arrAnswerConds  = array(
            'examId'  => $intExamId,
            'uid'     => $intUid,
            'deleted' => 0,
        );
        $arrAnswerFields = $this->_objDaoAnswer->arrFields;
        $arrAnswerInfo   = $this->_objDaoAnswer->getRecordByConds($intExamId, $arrAnswerConds, $arrAnswerFields);

        // 是否需要新增答题记录
        $intInsert = 0;
        // 已经打过的题目列表（用于判断答题详情是更新还是增加）
        $arrAnswerTidList = array();
        // 如果需要新增答题记录，新的id
        $intNewAnswerId = 0;

        if (empty( $arrAnswerInfo )) {
            $intInsert = 1;
            // 分配新的答题记录id
            $objIdAlloc     = new Hk_Service_IdAlloc(Hk_Service_IdAlloc::NAME_EXAM_ANSWER);
            $intNewAnswerId = $objIdAlloc->getIdAlloc();

            if ($intNewAnswerId < 0) {
                return false;
            }
        } else {
            $intAnswerId = intval($arrAnswerInfo[ 'answerId' ]);

            $arrDetailConds = array(
                'answerId' => $intAnswerId,
            );

            $arrDetailFields = $this->_objDaoAnswerDetail->arrFields;

            $arrDetailList = $this->_objDaoAnswerDetail->getListByConds($intAnswerId, $arrDetailConds, $arrDetailFields);

            foreach ($arrDetailList as $value) {
                $intTid   = intval($value[ 'tid' ]);
                $intOrder = intval($value[ 'subOrder' ]);

                $intType = intval($arrExamInfo[ 'questionList' ][ $intTid ][ 'type' ]);
                if (14 == $intType || 15 == $intType) {
                    $tid = $intTid . '_' . $intOrder;
                } else {
                    $tid = $intTid;
                }

                if (!in_array($tid, $arrAnswerTidList)) {
                    $arrAnswerTidList[ $tid ] = array(
                        'id'       => $value[ 'id' ],
                        'answerId' => $intAnswerId,
                        'tid'      => $intTid,
                        'subOrder' => $intOrder,
                    );
                }
            }
        }

        $ret = $this->_objDaoAnswer->startTransaction();

        if (false == $ret) {
            $this->_objDaoAnswer->rollback();

            return false;
        }

        $intAnswerId = 0;

        if ($intInsert) {
            // insert 答题记录

            // 分配answer_id
            $intAnswerId = $intNewAnswerId;

            $arrAnswerInsertFields = array(
                'answerId'   => $intNewAnswerId,
                'examId'     => $intExamId,
                'uid'        => $intUid,
                'isFinish'   => $intIsFinish,
                'score'      => $intTotalScore,
                'answerList' => json_encode($arrAnswerList),
                'extData'    => '[]',
                'createTime' => time(),
                'updateTime' => time(),
                'deleted'    => 0,
            );

            $ret = $this->_objDaoAnswer->insertRecords($intExamId, $arrAnswerInsertFields);
            if (false == $ret) {
                $this->_objDaoAnswer->rollback();

                return false;
            }
        } else {
            $intAnswerId = intval($arrAnswerInfo[ 'answerId' ]);

            $arrAnswerUploadConds = array(
                'answerId' => $intAnswerId,
            );
            // upload01 答题记录
            $arrAnswerUploadFields = array(
                'isFinish'   => $intIsFinish,
                'score'      => $intTotalScore,
                'answerList' => json_encode($arrAnswerList),
                'updateTime' => time(),
            );

            $this->_objDaoAnswer->updateByConds($intExamId, $arrAnswerUploadConds, $arrAnswerUploadFields);
        }

        foreach ($arrAnswerList as $tid => $value) {

            $intType = $arrExamInfo[ 'questionList' ][ $tid ][ 'type' ];

            if (isset( $value[ 'list' ] )) {
                foreach ($value[ 'list' ] as $order => $subanwser) {
                    $key = $tid . '_' . $order;
                    if (isset( $arrAnswerTidList[ $key ] )) {
                        $arrConds  = array(
                            'id' => intval($arrAnswerTidList[ $tid ][ 'id' ]),
                        );
                        $arrUpdate = array(
                            'answer'     => json_encode($subanwser),
                            'score'      => $subanwser[ 'score' ],
                            'isRight'    => $subanwser[ 'isRight' ],
                            'updateTime' => time(),
                        );
                        $this->_objDaoAnswerDetail->updateByConds($intAnswerId, $arrConds, $arrUpdate);
                    } else {
                        $arrInsert = array(
                            'answerId'    => $intAnswerId,
                            'examId'      => $intExamId,
                            'tid'         => $tid,
                            'subOrder'    => $order,
                            'subjectType' => $intType,
                            'answer'      => json_encode($subanwser),
                            'score'       => $subanwser[ 'score' ],
                            'isRight'     => $subanwser[ 'isRight' ],
                            'extData'     => '[]',
                            'createTime'  => time(),
                            'updateTime'  => time(),
                            'deleted'     => 0,
                        );

                        $ret = $this->_objDaoAnswerDetail->insertRecords($intAnswerId, $arrInsert);

                        if (false == $ret) {
                            $this->_objDaoAnswer->rollback();

                            return false;
                        }
                    }
                }
            } else {

                // 小题得分
                $intSubScore = intval($value[ 'score' ]);
                // 是否正确
                $intIsRight = intval($value[ 'isRight' ]);
                // 答案
                $arrAnswer = array(
                    'answer'   => $value[ 'answer' ],
                    'duration' => $value[ 'duration' ],
                );

                if (isset( $arrAnswerTidList[ $tid ] )) {
                    $arrConds  = array(
                        'id' => intval($arrAnswerTidList[ $tid ][ 'id' ]),
                    );
                    $arrUpdate = array(
                        'answer'     => json_encode($arrAnswer),
                        'score'      => $intSubScore,
                        'isRight'    => $intIsRight,
                        'updateTime' => time(),
                    );
                    $this->_objDaoAnswerDetail->updateByConds($intAnswerId, $arrConds, $arrUpdate);
                } else {
                    $arrInsert = array(
                        'answerId'    => $intAnswerId,
                        'examId'      => $intExamId,
                        'tid'         => $tid,
                        'subOrder'    => 0,
                        'subjectType' => $intType,
                        'answer'      => json_encode($arrAnswer),
                        'score'       => $intSubScore,
                        'isRight'     => $intIsRight,
                        'extData'     => '[]',
                        'createTime'  => time(),
                        'updateTime'  => time(),
                        'deleted'     => 0,
                    );

                    $ret = $this->_objDaoAnswerDetail->insertRecords($intAnswerId, $arrInsert);

                    if (false == $ret) {
                        $this->_objDaoAnswer->rollback();

                        return false;
                    }
                }

            }
        }

        $ret = $this->_objDaoAnswer->commit();
        if (false == $ret) {
            $this->_objDaoAnswer->rollback();

            return false;
        }


        $arrOutput = array(
            'errno' => 0,
            'dbId'  => $intAnswerId,
        );

        return $arrOutput;
    }

    public static function parseChoiceFromStrToBit( $strChoice ) {
        if(!is_string($strChoice)) {
            if(is_array($strChoice)) {
                $strChoice=$strChoice[0];
            }
        }
        $strChoice = trim($strChoice);
        if ("" == $strChoice) {
            return 0;
        }

        $strChoice = strtoupper($strChoice);
        if (!preg_match_all("/([ABCDEF])/", strtoupper($strChoice), $match)) {
            return 0;
        }

        $int = 0;
        $arr = array_unique($match[ 1 ]);
        foreach ($arr as $key => $val) {
            if ('A' == $val) {
                $int += 1;
            } elseif ('B' == $val) {
                $int += 2;
            } elseif ('C' == $val) {
                $int += 4;
            } elseif ('D' == $val) {
                $int += 8;
            } elseif ('E' == $val) {
                $int += 16;
            } elseif ('F' == $val) {
                $int += 32;
            }
        }

        return $int;
    }

    /**
     * Choice:根据Bit转换成str("A,B,C")
     * @param  integer $intChoice
     * @return string
     */
    public static function parseChoiceFromBitToStr( $intChoice ) {
        $intChoice = intval($intChoice);
        if (0 >= $intChoice) {
            return '';
        }

        $arrChoice = array();
        if ($intChoice & 1) {
            $arrChoice[] = 'A';
        }
        if ($intChoice & 2) {
            $arrChoice[] = 'B';
        }
        if ($intChoice & 4) {
            $arrChoice[] = 'C';
        }
        if ($intChoice & 8) {
            $arrChoice[] = 'D';
        }
        if ($intChoice & 16) {
            $arrChoice[] = 'E';
        }
        if ($intChoice & 32) {
            $arrChoice[] = 'F';
        }

        return implode("", $arrChoice);
    }


    public function getAllAnswerInfoByLessonId( $intLessonId ) {

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>


        $obj = new Hkzb_Ds_Examrpc_ExamRpcApi();

        $arrRelationInfo = $obj->getExamIdByLessonIdRpc($intLessonId);

        $intExamId = intval($arrRelationInfo[ 'examId' ]);

        $arrExamInfo = $obj->getExamInfoByExamIdRpc($intExamId);

        // 试卷总分
        $intTotalScore = intval($arrExamInfo[ 'content' ][ 'totalScore' ]);
        // 试卷题目总数
        $intSubjectNum = 0;
        foreach ($arrExamInfo[ 'questionList' ] as $subject) {
            if (isset( $subject[ 'list' ] )) {
                $intSubjectNum = $intSubjectNum + count($subject[ 'list' ]);
            } else {
                $intSubjectNum++;
            }
        }
        

        $arrAnswerList = $obj->getAnswerByExamIdRpc($intExamId);

        $arrOutput = array();
        foreach ($arrAnswerList as $value) {

            // 学生得分
            $intScore = intval($value[ 'score' ]);
            // 答题时长
            $intDuration = intval($value[ 'extData' ][ 'duration' ]);
            // 正确和错误题目数量
            $intRightNum = 0;
            $intWrongNum = 0;
            foreach ($value[ 'answerList' ] as $v) {
                if (isset( $v[ 'list' ] )) {
                    foreach ($v[ 'list' ] as $sub_v) {
                        if (1 == $sub_v[ 'isRight' ]) {
                            $intRightNum++;
                        } else {
                            $intWrongNum++;
                        }
                    }
                } else {
                    if (1 == $v[ 'isRight' ]) {
                        $intRightNum++;
                    } else {
                        $intWrongNum++;
                    }
                }
            }

            // 章节ID，学员ID，试卷ID，试卷总分，题目总数，学员总得分，答题时间，答对题数，答错题数
            $temp = array(
                'lessonId'   => $intLessonId,
                'studentUid' => intval($value[ 'uid' ]),
                'examId'     => intval($value[ 'examId' ]),
                'totalScore' => $intTotalScore,
                'subjectNum' => $intSubjectNum,
                'score'      => $intScore,
                'duration'   => $intDuration,
                'rightNum'   => $intRightNum,
                'wrongNum'   => $intWrongNum,
                'createTime' => intval($value['createTime']),
            );

            $arrOutput[] = $temp;
        }

        return $arrOutput;
    }


/*    public function getAllAnswerInfoByLessonId( $intLessonId ) {

        //<<<rdtrack<<<
        $rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }
        //>>>rdtrack>>>

        $objDaoBind = new Hkzb_Dao_Inclass_ExamRelation();

        $arrBindConds = array(
            'bindType' => 10,
            'bindId'   => $intLessonId,
            'deleted'  => 0,
        );

        $arrBindFields = array(
            'examId',
        );

        $arrRelationInfo = $objDaoBind->getRecordByConds($arrBindConds, $arrBindFields);

        $intExamId = intval($arrRelationInfo[ 'examId' ]);

        // 获取堂堂测信息
        $objDaoExam = new Hkzb_Dao_Inclass_Exam();

        $arrExamConds  = array(
            'examId' => $intExamId,
        );
        $arrExamFields = $objDaoExam->arrFields;

        $arrExamInfo = $objDaoExam->getRecordByConds($arrExamConds, $arrExamFields);

        // 试卷总分
        $intTotalScore = intval($arrExamInfo[ 'content' ][ 'totalScore' ]);
        // 试卷题目总数
        $intSubjectNum = 0;
        foreach ($arrExamInfo[ 'questionList' ] as $subject) {
            if (isset( $subject[ 'list' ] )) {
                $intSubjectNum = $intSubjectNum + count($subject[ 'list' ]);
            } else {
                $intSubjectNum++;
            }
        }

        // 目前线上一个堂堂测最多有三千人答题  注意性能
        $arrConds = array(
            'examId' => $intExamId,
        );

        $arrFields = array(
            'examId',
            'uid',
            'isFinish',
            'score',
            'answerList',
            'extData',
            'createTime',
        );

        // 答题详情
        $arrAnswerList = $this->_objDaoAnswer->getListByConds($intExamId, $arrConds, $arrFields);

        $arrOutput = array();
        foreach ($arrAnswerList as $value) {

            // 学生得分
            $intScore = intval($value[ 'score' ]);
            // 答题时长
            $intDuration = intval($value[ 'extData' ][ 'duration' ]);
            // 正确和错误题目数量
            $intRightNum = 0;
            $intWrongNum = 0;
            foreach ($value[ 'answerList' ] as $v) {
                if (isset( $v[ 'list' ] )) {
                    foreach ($v[ 'list' ] as $sub_v) {
                        if (1 == $sub_v[ 'isRight' ]) {
                            $intRightNum++;
                        } else {
                            $intWrongNum++;
                        }
                    }
                } else {
                    if (1 == $v[ 'isRight' ]) {
                        $intRightNum++;
                    } else {
                        $intWrongNum++;
                    }
                }
            }

            // 章节ID，学员ID，试卷ID，试卷总分，题目总数，学员总得分，答题时间，答对题数，答错题数
            $temp = array(
                'lessonId'   => $intLessonId,
                'studentUid' => intval($value[ 'uid' ]),
                'examId'     => intval($value[ 'examId' ]),
                'totalScore' => $intTotalScore,
                'subjectNum' => $intSubjectNum,
                'score'      => $intScore,
                'duration'   => $intDuration,
                'rightNum'   => $intRightNum,
                'wrongNum'   => $intWrongNum,
                'createTime' => intval($value['createTime']),
            );

            $arrOutput[] = $temp;
        }

        return $arrOutput;
    }*/


    // 改造RPC
    public function getAllFullAnswerInfoByLessonIdByYiKe( $intLessonId ) {

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>


        $obj = new Hkzb_Ds_Examrpc_ExamRpcApi();
        $arrRelationInfo = $obj->getExamIdByLessonIdRpc($intLessonId);

        $intExamId = intval($arrRelationInfo[ 'examId' ]);

        $arrExamInfo = $obj->getExamInfoByExamIdRpc($intExamId);

        // 试卷总分
        $intTotalScore = intval($arrExamInfo[ 'content' ][ 'totalScore' ]);
        // 试卷题目总数
        $intSubjectNum = 0;

        // 上方调用RPC查出的数据只为了计算题目数量
        foreach ($arrExamInfo[ 'questionList' ] as $subject) {
            if (isset( $subject[ 'list' ] )) {
                $intSubjectNum = $intSubjectNum + count($subject[ 'list' ]);
            } else {
                $intSubjectNum++;
            }
        }

        $arrAnswerList = $obj->getAnswerByExamIdRpc($intExamId);

        $arrOutput = array();
        foreach ($arrAnswerList as $value) {
            // 学生得分
            $intScore = intval($value[ 'score' ]);
            // 答题时长
            $intDuration = intval($value[ 'extData' ][ 'duration' ]);
            // 正确和错误题目数量
            $intRightNum = 0;
            $intWrongNum = 0;

            // 章节ID，学员ID，试卷ID，试卷总分，题目总数，学员总得分，答题时间，答对题数，答错题数
            $temp = array(
                'lessonId'   => $intLessonId,
                'studentUid' => intval($value[ 'uid' ]),
                'examId'     => intval($value[ 'examId' ]),
                'totalScore' => $intTotalScore,
                'subjectNum' => $intSubjectNum,
                'score'      => $intScore,
                'duration'   => $intDuration,
                'rightNum'   => $intRightNum,
                'wrongNum'   => $intWrongNum,
                'tidList'    =>$value['answerList']
            );

            $arrOutput[] = $temp;
        }

        return $arrOutput;
    }


    public function getAllFullAnswerInfoByLessonId( $intLessonId ) {

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

        $objDaoBind = new Hkzb_Dao_Inclass_ExamRelation();

        $arrBindConds = array(
            'bindType' => 10,
            'bindId'   => $intLessonId,
            'deleted'  => 0,
        );

        $arrBindFields = array(
            'examId',
        );

        $arrRelationInfo = $objDaoBind->getRecordByConds($arrBindConds, $arrBindFields);

        $intExamId = intval($arrRelationInfo[ 'examId' ]);

        // 获取堂堂测信息
        $objDaoExam = new Hkzb_Dao_Inclass_Exam();

        $arrExamConds  = array(
            'examId' => $intExamId,
        );
        $arrExamFields = $objDaoExam->arrFields;

        $arrExamInfo = $objDaoExam->getRecordByConds($arrExamConds, $arrExamFields);

        // 试卷总分
        $intTotalScore = intval($arrExamInfo[ 'content' ][ 'totalScore' ]);
        // 试卷题目总数
        $intSubjectNum = 0;
        foreach ($arrExamInfo[ 'questionList' ] as $subject) {
            if (isset( $subject[ 'list' ] )) {
                $intSubjectNum = $intSubjectNum + count($subject[ 'list' ]);
            } else {
                $intSubjectNum++;
            }
        }

        // 目前线上一个堂堂测最多有三千人答题  注意性能
        $arrConds = array(
            'examId' => $intExamId,
        );

        $arrFields = array(
            'examId',
            'uid',
            'isFinish',
            'score',
            'answerList',
            'extData',
        );

        // 答题详情
        $arrAnswerList = $this->_objDaoAnswer->getListByConds($intExamId, $arrConds, $arrFields);

        $arrOutput = array();
        foreach ($arrAnswerList as $value) {
            // 学生得分
            $intScore = intval($value[ 'score' ]);
            // 答题时长
            $intDuration = intval($value[ 'extData' ][ 'duration' ]);
            // 正确和错误题目数量
            $intRightNum = 0;
            $intWrongNum = 0;


            // 章节ID，学员ID，试卷ID，试卷总分，题目总数，学员总得分，答题时间，答对题数，答错题数
            $temp = array(
                'lessonId'   => $intLessonId,
                'studentUid' => intval($value[ 'uid' ]),
                'examId'     => intval($value[ 'examId' ]),
                'totalScore' => $intTotalScore,
                'subjectNum' => $intSubjectNum,
                'score'      => $intScore,
                'duration'   => $intDuration,
                'rightNum'   => $intRightNum,
                'wrongNum'   => $intWrongNum,
                'tidList'    =>$value['answerList']
            );

            $arrOutput[] = $temp;
        }

        return $arrOutput;
    }



    public function getStudentInClassCondition($intLessonId,$intCourseId){

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>
        
        if(intval($intCourseId) <= 0 || intval($intLessonId) <= 0 ) {
            Bd_Log::warning('[examAnswer]--getStudentClassTestInfo--parameterError'.$intCourseId.'--'.$intLessonId.'--');
            return false;
        }
        $classTestInfo = $this->getClassTestInfo($intLessonId,$intCourseId);
        if($classTestInfo === false){
            Bd_Log::warning('[examAnswer]--getStudentClassTestInfo--DbError'.json_encode($classTestInfo));
            return false;
        }
        if(!$classTestInfo || empty($classTestInfo)){
            return array();
        }
        $examId = $classTestInfo['examId'];
        $objDsInclassAnswer = $this;

        $allUserInfo = $objDsInclassAnswer->getAllFullAnswerInfoByLessonIdByYiKe($intLessonId);
        if($allUserInfo === false){
            Bd_Log::warning('[examAnswer]--getStudentClassTestInfo--DbError2'.json_encode($allUserInfo));
            return false;
        }
        $requestion = array();
//        foreach($allUserInfo as $userInfo){
//            if(isset($userInfo['tidList'])) {
//                foreach (($userInfo['tidList']) as $tid => $answer) {
//                    if (isset($answer['list'])) {
//                        foreach($answer['list'] as $questionNum=>$answerPer){
//                            if (!empty($answerPer['answer']) && strlen($answerPer['answer']) > 1) {
//                                for ($i = 0; $i < strlen($answerPer['answer']); $i++) {
//                                    $answerKey = $answerPer['answer'][$i];
//                                    if (!isset($requestion[$tid]['list'][$questionNum]['optAnswerCnt'][$answerKey]) || empty($requestion[$tid]['list'][$questionNum]['optAnswerCnt'][$answerKey])) {
//                                        $requestion[$tid]['list'][$questionNum]['optAnswerCnt'][$answerKey] = 1;
//                                    } else {
//                                        $requestion[$tid]['list'][$questionNum]['optAnswerCnt'][$answerKey]++;
//                                    }
//                                }
//
//                            } elseif (!empty($answerPer['answer']) && strlen($answerPer['answer']) == 1) {
//                                if (!isset($requestion[$tid]['list'][$questionNum]['optAnswerCnt'][$answerPer['answer']]) || empty($requestion[$tid]['optAnswerCnt'][$answer['answer']])) {
//                                    $requestion[$tid]['optAnswerCnt'][$answer['answer']] = 1;
//                                } else {
//                                    $requestion[$tid]['list'][$questionNum]['optAnswerCnt'][$answerPer['answer']]++;
//                                }
//                            } else {
//                                if (!isset($requestion[$tid]['optAnswerCnt'][$answer['answer']]) || empty($requestion[$tid]['optAnswerCnt'][$answer['answer']])) {
//                                    $requestion[$tid]['list'][$questionNum]['optAnswerCnt']['noDo'] = 1;
//                                } else {
//                                    $requestion[$tid]['list'][$questionNum]['optAnswerCnt']['noDo']++;
//                                }
//                            }
//                            if(isset($answerPer['isRight']) && !$answerPer['isRight']){
//                                $error[$tid]['list'][$questionNum] = $userInfo['studentUid'];
//                                $requestion[$tid]['list'][$questionNum]['errorStudent'][] = $userInfo['studentUid'];
//                                continue;
//                            }
//                            if(isset($answerPer['isRight']) && $answerPer['isRight']){
//                                $correct[$tid]['list'][$questionNum] = $userInfo['studentUid'];
//                                $requestion[$tid]['list'][$questionNum]['correctStudent'][] = $userInfo['studentUid'];
//                                continue;
//                            }
//                        }
//                    }else{
//                        if (!empty($answer['answer']) && strlen($answer['answer']) > 1) {
//                            for ($i = 0; $i < strlen($answer['answer']); $i++) {
//                                $answerKey = $answer['answer'][$i];
//                                if (!isset($requestion[$tid]['optAnswerCnt'][$answerKey]) || empty($requestion[$tid]['optAnswerCnt'][$answerKey])) {
//                                    $requestion[$tid]['optAnswerCnt'][$answerKey] = 1;
//                                } else {
//                                    $requestion[$tid]['optAnswerCnt'][$answerKey]++;
//                                }
//                            }
//
//                        } elseif (!empty($answer['answer']) && strlen($answer['answer']) == 1) {
//                            if (!isset($requestion[$tid]['optAnswerCnt'][$answer['answer']]) || empty($requestion[$tid]['optAnswerCnt'][$answer['answer']])) {
//                                $requestion[$tid]['optAnswerCnt'][$answer['answer']] = 1;
//                            } else {
//                                $requestion[$tid]['optAnswerCnt'][$answer['answer']]++;
//                            }
//                        } else {
//                            if (!isset($requestion[$tid]['optAnswerCnt'][$answer['answer']]) || empty($requestion[$tid]['optAnswerCnt'][$answer['answer']])) {
//                                $requestion[$tid]['optAnswerCnt']['noDo'] = 1;
//                            } else {
//                                $requestion[$tid]['optAnswerCnt']['noDo']++;
//                            }
//                        }
//                        if(isset($answer['isRight']) && !$answer['isRight']){
//                            $error[$tid] = $userInfo['studentUid'];
//                            $requestion[$tid]['errorStudent'][] = $userInfo['studentUid'];
//                            continue;
//                        }
//                        if(isset($answer['isRight']) && $answer['isRight']){
//                            $correct[$tid] = $userInfo['studentUid'];
//                            $requestion[$tid]['correctStudent'][] = $userInfo['studentUid'];
//                            continue;
//                        }
//                    }
//
//                }
//
//            }else{
//                foreach ($classTestInfo['tidList'] as $tid=>$info) {
//                    $notDo[$tid] = $userInfo['studentUid'];
//                    $requestion[$tid]['notDoStudent'][] = $userInfo['studentUid'];
//                }
//            }
//        }
        $return = array();
        $requestion = $this->dealStudentAnswer($allUserInfo,$classTestInfo);
        foreach($requestion as $tid=>$tidStatus){
            if (isset($tidStatus['list'])) {
                foreach ($tidStatus['list'] as $idx => $item) {
                    $return[$tid]['list'][$idx] = array(
                        'answerCnt'=>(count($item['errorStudent'])+count($item['correctStudent'])),
                        'correctCnt'=>count($item['correctStudent']),
                        'errorCnt'=>count($item['errorStudent']),
                        'optAnswerCnt'=>$item['optAnswerCnt'],
                    );
                }
            } else {
                $requestion[$tid]['notDoNum'] = count($tidStatus['notDoStudent']);
                $requestion[$tid]['errorNum'] = count($tidStatus['errorStudent']);
                $requestion[$tid]['correctNum'] = count($tidStatus['correctStudent']);
                $return[$tid] = array(
                    'answerCnt'=>(count($tidStatus['errorStudent'])+count($tidStatus['correctStudent'])),
                    'correctCnt'=>count($tidStatus['correctStudent']),
                    'errorCnt'=>count($tidStatus['errorStudent']),
                    'optAnswerCnt'=>$tidStatus['optAnswerCnt'],
                );
            }

        }



        return $return;
    }
    private function dealStudentAnswer($allUserInfo,$classTestInfo){
        $requestion = [];

        //所有学生答案
        foreach ($allUserInfo as $userInfo) {
            if(isset($userInfo['tidList'])) {
                if (isset($userInfo['tidList'])) {
//                    print_r($userInfo['tidList']);
                    //单个学生答案
                    foreach (($userInfo['tidList']) as $tid => $answer) {
                        if (isset($answer['list'])) {
                            //单个题目答案
                            foreach ($answer['list'] as $qNum => $qAnswer) {
                                //处理选项和是否正确
                                $answerCondition = $this->dealAnswer($qAnswer);
                                foreach ($answerCondition['optAnswerCnt'] as $option => $value) {
                                    $requestion[$tid]['list'][$qNum]['optAnswerCnt'][$option]++;
                                }
                                if ($answerCondition['right']) {
                                    $requestion[$tid]['list'][$qNum]['correctStudent'][] = $userInfo['studentUid'];
                                } else {
                                    //过滤掉nodo的学生
                                    if ( !isset($answerCondition['optAnswerCnt']['noDo']) ) {
                                        $requestion[$tid]['list'][$qNum]['errorStudent'][] = $userInfo['studentUid'];
                                    }
                                }
                            }
                        } else {
                            $answerCondition = $this->dealAnswer($answer);
                            foreach ($answerCondition['optAnswerCnt'] as $option => $value) {
                                $requestion[$tid]['optAnswerCnt'][$option]++;
                            }
                            if ($answerCondition['right']) {
                                $requestion[$tid]['correctStudent'][] = $userInfo['studentUid'];
                            } else {
                                //过滤掉noDo
                                if ( !isset($answerCondition['optAnswerCnt']['noDo']) ) {
                                    $requestion[$tid]['errorStudent'][] = $userInfo['studentUid'];
                                }
                            }
                        }
                    }
                }
            }else{
                foreach ($classTestInfo['tidList'] as $tid=>$info) {
                    $requestion[$tid]['notDoStudent'][] = $userInfo['studentUid'];
                }
            }
        }

        return $requestion;
    }
    private function dealAnswer($answer){
        if (is_array($answer['answer']) && in_array($answer['type'], [33,34,53])) {
            foreach ($answer['answer'] as $val) {
                if (is_scalar($val) && !empty($val) && strlen($val) > 1) {
                    for ($i = 0; $i < strlen($val); $i++) {
                        $answerKey = $val[$i];
                        $return['optAnswerCnt'][$answerKey] = 1;
                    }
                } elseif (is_scalar($val) && !empty($val) && strlen($val) == 1) {
                    $return['optAnswerCnt'][$val] = 1;
                } else {
                    $return['optAnswerCnt']['noDo'] = 1;
                }
            }
        }else {
            if (is_scalar($answer['answer']) && !empty($answer['answer']) && strlen($answer['answer']) > 1) {
                for ($i = 0; $i < strlen($answer['answer']); $i++) {
                    $answerKey = $answer['answer'][$i];
                    $return['optAnswerCnt'][$answerKey] = 1;
                }
            } elseif (is_scalar($answer['answer']) && !empty($answer['answer']) && strlen($answer['answer']) == 1) {
                $return['optAnswerCnt'][$answer['answer']] = 1;
            } elseif (is_array($answer['answer']) && $answer['answer']) {
                foreach ($answer['answer'] as $idx => $item) {
                    $return['optAnswerCnt'][$idx] = 1;
                }
            } else {
                $return['optAnswerCnt']['noDo'] = 1;
            }
        }

        $return['right']= 0;
        if(isset($answer['isRight']) && $answer['isRight']){
            $return['right']= 1;
        }
        return $return;
    }

    public function getClassTestInfo($intLessonId,$intCourseId){

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>

        if(intval($intCourseId) <= 0 || intval($intLessonId) <= 0 ) {
            Bd_Log::warning('[ExamAnswer]--getClassTestInfo--parameterError'.$intCourseId.'--'.$intLessonId);
            return false;
        }
        $TempCash = isset($this->TEMP_CASH['getClassTestInfo'])?$this->TEMP_CASH['getClassTestInfo']:array();

        if($TempCash){
            if(isset($TempCash[$intLessonId])){

                return $TempCash[$intLessonId];
            }
        }
        // 课中练习(堂堂测)数据
        $objDsInclassRelation = new Hkzb_Ds_Inclass_ExamRelation();
        $examRelationInfo = $objDsInclassRelation->getInclassExamInfoByLessonId($intLessonId);
        
        if($examRelationInfo === false){
            Bd_Log::warning('[ExamAnswer]--getClassTestInfo--DbError'.json_encode($examRelationInfo));
            return false;
        }
        if(empty($examRelationInfo)){
            $this->TEMP_CASH['getClassTestInfo'][$intLessonId] = [];
            return [];
        }

        if(!in_array($examRelationInfo['status'],[1,2])){
            $this->TEMP_CASH['getClassTestInfo'][$intLessonId] = [];
            Bd_Log::addNotice("debug_getexaminfobybindid", json_encode($examRelationInfo));
        }

        $objDsInclassExam = new Hkzb_Ds_Inclass_Exam();
        // 方法已经改过RPC，所以直接更换调用就可以 ByYiKe
        $examInfo = $objDsInclassExam->getInclassExamInfoByExamIdByYiKe($examRelationInfo['examId']);

        if($examInfo === false){
            Bd_Log::warning('[ExamAnswer]--getClassTestInfo--DbError2'.json_encode($examInfo));
            return false;
        }
        if(empty($examInfo)){
            $this->TEMP_CASH['getClassTestInfo'][$intLessonId] = [];
            return [];
        }
        $totalScore = 0;
        if(isset($examInfo['tidList']) && !empty($examInfo['tidList'])){
            foreach ($examInfo['tidList'] as $tid=>$info){
                $totalScore += intval($info['s']);
            }
        }
        $examInfo['sendTime'] = $examRelationInfo['startTime'];
        $examInfo['totalScore'] = $totalScore;

        $this->TEMP_CASH['getClassTestInfo'][$intLessonId] = $examInfo;
        return $examInfo;

    }

    // MVP课程课堂报告 需要获取出门测 有多少人提交  多少人正确  本人正确几道 @xuhanqiu
    public function getAllOutdoorExamAnswerInfoByLessonId( $intLessonId,$studentUid ) {

        //<<<rdtrack<<<
        /*$rdBtInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        if ($rdBtInfo && is_array($rdBtInfo)) {
            $rdBtHashKey = $rdBtInfo[0]['file'].":".$rdBtInfo[0]['line'];
            $rdObjCache = Hk_Service_RedisClient::getInstance("zhibo");
            $cacheKey = "EXAM_RDTRACK_".__METHOD__;
            if ($rdObjCache) {
                $rdResult = $rdObjCache->hincrby($cacheKey, $rdBtHashKey, 1);
                Bd_Log::addNotice("examRdTrack", intval($rdResult));
            }
        }*/
        //>>>rdtrack>>>
        
        $objDaoBind = new Hkzb_Dao_Inclass_ExamRelation();
        $arrBindConds = array(
            'bindType' => 18,   //出门测的类型
            'bindId'   => $intLessonId,
            'deleted'  => 0,
        );
        $arrBindFields = array(
            'examId',
        );

        $arrRelationInfo = $objDaoBind->getRecordByConds($arrBindConds, $arrBindFields);
        $intExamId = intval($arrRelationInfo[ 'examId' ]);
        $arrConds = array(
            'examId' => $intExamId,
        );
        $arrFields = array(
            'examId',
            'uid',
            'answerList',
            'extData',
        );

        // 答题详情
        $stuCountRight =0;   // 本人对题总数
        $stuCountWrong = 0;    // 本人错题总数
        $stuCountNoanswer =0;  //本人未答总数
        $allCount = 0;              //所有人答题数目
        $allCountRight =0;          //所有人对题数目
        $arrAnswerList = $this->_objDaoAnswer->getListByConds($intExamId, $arrConds, $arrFields);
        foreach ($arrAnswerList as $oneStudentAnswer){
            if($oneStudentAnswer['uid'] == $studentUid)
            {
                //学生答题列表
                if(!empty($oneStudentAnswer['answerList'])){
                    foreach ($oneStudentAnswer['answerList'] as $value){
                        if(empty($value['answer']))
                            $stuCountNoanswer++;
                        else if($value['isRight'] == 0)
                            $stuCountWrong++;
                        else if($value['isRight'] == 1)
                            $stuCountRight++;
                    }
                    continue;
                }
            }
            //学生答题列表
            if(!empty($oneStudentAnswer['answerList'])){
                foreach ($oneStudentAnswer['answerList'] as $value){
                    if(!empty($value['answer'])){
                        $allCount++;
                        if($value['isRight'] == 1)
                            $allCountRight++;
                    }
                }
            }
        }
        $allCount += ($stuCountRight + $stuCountWrong );
        $allCountRight += $stuCountRight;
        $arrOutput = array(
            'allCount'        => $allCount,
            'allCountRight'  => $allCountRight,
            'stuCountRight'  => $stuCountRight,
            'stuCountWrong'  => $stuCountWrong,
            'stuCountNoanswer' =>$stuCountNoanswer

        );
        return $arrOutput;
    }

}