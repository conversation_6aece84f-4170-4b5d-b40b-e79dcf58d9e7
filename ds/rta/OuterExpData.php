<?php

class Qdlib_Ds_Rta_OuterExpData extends Qdlib_Ds_Rta_RtaBase
{
    const STATUS_ENABLED = 1;
    const STATUS_PAUSED = 2;
    const STATUS_FINISHED = 3;
    const STATUS_NAMES = [
        self::STATUS_ENABLED => '已启用',
        self::STATUS_PAUSED => '已暂停',
        self::STATUS_FINISHED => '已结束',
    ];
    const GDT_SITE_SET = [
        15 => '移动联盟',
        21 => '微信',
        25 => '移动内部站点',
        27 => '腾讯新闻流量',
        28 => '腾讯视频流量',
    ];

    public function __construct()
    {
        parent::__construct();
        $this->_tableDaoObj = new Qdlib_Dao_Rta_OuterExpData();
    }

    protected function getFormatDbData($arrParams)
    {
        return [
            'channel' => isset($arrParams['channel']) ? (string)$arrParams['channel'] : '',
            'expId' => isset($arrParams['expId']) ? (string)$arrParams['expId'] : '',
            'flag' => isset($arrParams['flag']) ? (string)$arrParams['flag'] : '',
            'advertiserId' => isset($arrParams['advertiserId']) ? (string)$arrParams['advertiserId'] : '',
            'campaignId' => isset($arrParams['campaignId']) ? (string)$arrParams['campaignId'] : '',
            'appId' => isset($arrParams['appId']) ? (string)$arrParams['appId'] : '',
            'adId' => isset($arrParams['adId']) ? (string)$arrParams['adId'] : '',
            'userWeight' => isset($arrParams['userWeight']) ? (string)$arrParams['userWeight'] : '',
            'uniqueKey' => isset($arrParams['uniqueKey']) ? (string)$arrParams['uniqueKey'] : '',
            'exposure' => isset($arrParams['exposure']) ? (int)$arrParams['exposure'] : '',
            'click' => isset($arrParams['click']) ? (int)$arrParams['click'] : '',
            'conversion' => isset($arrParams['conversion']) ? (int)$arrParams['conversion'] : '',
            'conversionSecond' => isset($arrParams['conversionSecond']) ? (int)$arrParams['conversionSecond'] : '',
            'cost' => isset($arrParams['cost']) ? (int)$arrParams['cost'] : '',
            'createTime' => isset($arrParams['createTime']) ? (int)$arrParams['createTime'] : '',
            'updateTime' => isset($arrParams['updateTime']) ? (int)$arrParams['updateTime'] : '',
            'extChecksum' => isset($arrParams['extChecksum']) ? (string)$arrParams['extChecksum'] : '',
            'ext' => isset($arrParams['ext']) ? (string)$arrParams['ext'] : '',
        ];
    }

    public function multiInsert($fields, $values)
    {
        return $this->_tableDaoObj->multiInsert($fields, $values);
    }

    public function updateRecords($conditions, $fields)
    {
        return $this->_tableDaoObj->updateByConds($conditions, $fields);
    }
}