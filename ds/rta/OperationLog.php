<?php
/**
 * file OperationLog.php.
 * author: <EMAIL>
 * Date: 2020/7/16
 * brief:
 */

class Qdlib_Ds_Rta_OperationLog extends Qdlib_Ds_Rta_RtaBase
{
    const DAOOBJ = 'Qdlib_Dao_Rta_OperationLog';


    public function __construct()
    {
        $daoObj = self::DAOOBJ;
        $this->_tableDaoObj = new $daoObj();
        $this->_system = 'rta';
    }

    protected function getFormatDbData($arrParams) {
        $arrFields = array(
            'uid'           => isset($arrParams['uid']) ? intval($arrParams['uid']) : 0,
            'courseId'      => isset($arrParams['courseId']) ? intval($arrParams['courseId']) : 0,
            'skuId'         => isset($arrParams['skuId']) ? intval($arrParams['skuId']) : 0,
            'actionType'    => isset($arrParams['actionType']) ? intval($arrParams['actionType']) : 0,
            'commitTime'    => isset($arrParams['commitTime']) ? intval($arrParams['commitTime']) : 0,
            'createTime'    => isset($arrParams['createTime']) ? intval($arrParams['createTime']) : time(),
            'updateTime'    => isset($arrParams['updateTime']) ? intval($arrParams['updateTime']) : time(),
            'extData'       => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '[]',
        );
        return $arrFields;
    }
}