<?php
/**
 * Copyright (c) 2016 zuoyebang.com, Inc. All Rights Reserved
 * @file:Teacher.php
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @date: 2016/7/4
 * @time: 16:09
 * @desc: 教师表
 */

class Zhibo_Ds_Teacher
{
    //老师职责
    const DUTY_TEACHER = 1; //主讲老师
    const DUTY_ASSISTANT = 2; //辅导老师
    static $DUTY_ARRAY = array(
        self::DUTY_TEACHER => '主讲老师',
        self::DUTY_ASSISTANT => '辅导老师',
    );

    const RECOMMEND_YES = 1; //推荐老师
    const RECOMMEND_NO = 0; //不是推荐


    //状态
    const STATUS_OK = 0; //未删除
    const STATUS_DELETED = 1; //已删除
    static $STATUS_ARRAY = array(
        self::STATUS_OK => '未删除',
        self::STATUS_DELETED => '已删除',
    );
    //老师所属类型
    const TYPE_PRIVATE = 0;
    const TYPE_PUBLIC = 1;
    const TYPE_PRIVATE_LONG = 2;
    const TYPE_PARENT_COURSE = 3;
    const TYPE_NONU = 10;
    static $TYPE_ARRAY = array(
        self::TYPE_PRIVATE => '专题课',
        self::TYPE_PUBLIC => '公开课',
        self::TYPE_PRIVATE_LONG => '班课',
        self::TYPE_PARENT_COURSE => '家长课',
        self::TYPE_NONU => '未分组',
    );

    private $objDaoTeacher;

    public function __construct()
    {
        $this->objDaoTeacher = new Zhibo_Dao_Teacher();
    }

    /**
     * 获取老师详情
     *
     * @param  int $teacherUid 老师uid
     * @param  array $arrFields 指定属性
     * @return array|bool
     */
    public function getTeacherInfo($teacherUid, $arrFields = array())
    {
        if (intval($teacherUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[teacherUid:$teacherUid]");
            return false;
        }
        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_Teacher::$allFields;
        }
        $arrConds = array(
            'teacherUid' => intval($teacherUid),
        );
        $ret = $this->objDaoTeacher->getRecordByConds($arrConds, $arrFields);
        return $ret;
    }

    /**
     * @desc 批量获取信息
     * @param $arrTeacherUids
     * @param array $arrFields
     * @return array|bool
     */
    public function getTeacherInfoArray($arrTeacherUids, $arrFields = array())
    {
        if (empty($arrTeacherUids)) {
            return array();
        }
        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_Teacher::$allFields;
        } elseif (!in_array('teacherUid', $arrFields)) {
            $arrFields[] = 'teacherUid';
        }
        $arrTeacherUids = array_unique($arrTeacherUids);
        $teacherIds = implode(',', $arrTeacherUids);
        $arrConds = array(
            "teacher_uid in ($teacherIds)",
        );
        $ret = $this->objDaoTeacher->getListByConds($arrConds, $arrFields);
        if (false === $ret) {
            return false;
        }
        $arrRes = array();
        foreach ($ret as $teacher) {
            $teacherUid = $teacher['teacherUid'];
            $arrRes[$teacherUid] = $teacher;
        }
        return $arrRes;
    }

    /**
     * @param $arrConds
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getTeacherListByConds($arrConds, $arrFields = array(), $offset = 0, $limit = 20)
    {
        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_Teacher::$allFields;
        }
        $arrAppends = array(
            "order by create_time desc",
            "limit $offset, $limit",
        );
        $ret = $this->objDaoTeacher->getListByConds($arrConds, $arrFields, NULL, $arrAppends);
        return $ret;
    }

    /**
     * 增加老师粉丝数
     * @param integer $teacherUid
     */
    public function incrTeacherFansCnt($teacherUid)
    {
        $teacherUid = intval($teacherUid);
        if ($teacherUid <= 0) {
            return 0;
        }
        $arrConds = array(
            'teacherUid' => $teacherUid,
        );
        $arrFields = array(
            'fans_cnt = fans_cnt + 1',
        );
        $ret = $this->objDaoTeacher->updateByConds($arrConds, $arrFields);
        return $ret;
    }

    /**
     * 减少老师粉丝数
     * @param integer $teacherUid
     */
    public function decrTeacherFansCnt($teacherUid)
    {
        $teacherUid = intval($teacherUid);
        if ($teacherUid <= 0) {
            return 0;
        }
        $arrConds = array(
            'teacherUid' => $teacherUid,
            'fansCnt' => array(0, '>'),
        );
        $arrFields = array(
            'fans_cnt = fans_cnt - 1',
        );
        $ret = $this->objDaoTeacher->updateByConds($arrConds, $arrFields);
        return $ret;
    }

    /**
     * 判断是否是老师
     * @param int $teacherUid
     * @return boolean
     */
    public function isTeacher($teacherUid)
    {
        $teacherUid = intval($teacherUid);
        if ($teacherUid <= 0) {
            return false;
        }
        $arrConds = array(
            'teacherUid' => $teacherUid,
            'deleted' => self::STATUS_OK,
        );
        $ret = $this->objDaoTeacher->getRecordByConds($arrConds, array('teacherUid'));
        if ($ret) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 获取老师详情
     *
     * @param  int $teacherUid 老师uid
     * @param  array $arrFields 指定属性
     * @return array|bool
     */
    public function getTeacherInfoArr($teacherUids, $arrFields = array())
    {
        if (empty($teacherUids)) {
            return array();
        }
        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_Teacher::$allFields;
        }
        $arrConds = array(
            'deleted' => self::STATUS_OK,
        );
        $strTeacherUids = implode(',', $teacherUids);
        $arrConds[] = "teacher_uid in ($strTeacherUids)";
        $ret = $this->objDaoTeacher->getListByConds($arrConds, $arrFields);
        return $ret;
    }

    /**
     * 根据姓名获取老师数量
     *
     * @param  string  $teacherName  老师姓名
     * @param  mix     $arrFields    指定属性
     * @return mix
     */
    public function getTeacherCntByName($teacherName) {
        if(strlen($teacherName) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[teacherName:$teacherName]");
            return false;
        }

        $arrConds = array(
            'deleted' => self::STATUS_OK,
            'teacherName' => strval($teacherName),
        );

        $ret = $this->objDaoTeacher->getCntByConds($arrConds);

        return $ret;
    }

    /**
     * 根据姓名获取老师列表
     *
     * @param  string  $teacherName  老师姓名
     * @param  mix     $arrFields    指定属性
     * @param  int     $offset       偏移
     * @param  int     $limit        限制
     * @return mix
     */
    public function getTeacherListByName($teacherName, $arrFields = array(), $offset = 0, $limit = 20) {
        if(strlen($teacherName) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[teacherName:$teacherName]");
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = Zhibo_Dao_Teacher::$allFields;
        }

        $arrConds = array(
            'deleted' => self::STATUS_OK,
            'teacherName' => strval($teacherName),
        );

        $arrAppends = array(
            "order by create_time",
            "limit $offset, $limit",
        );

        $ret = $this->objDaoTeacher->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }



    /**
     * 根据姓名获取老师数量（模糊）
     *
     * @param  string  $teacherName  老师姓名
     * @param  mix     $arrFields    指定属性
     * @return mix
     */
    public function getTeacherCntByKeyName($teacherName) {
        if(strlen($teacherName) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[teacherName:$teacherName]");
            return false;
        }

        $arrConds = array(
            'deleted' => self::STATUS_OK,
        );
        $binName = bin2hex("%$teacherName%");
        $arrConds[] = "teacher_name like unhex('$binName')";

        $ret = $this->objDaoTeacher->getCntByConds($arrConds);

        return $ret;
    }



    /**
     * 根据姓名获取老师列表(模糊)
     *
     * @param  string  $teacherName  老师姓名
     * @param  mix     $arrFields    指定属性
     * @param  int     $offset       偏移
     * @param  int     $limit        限制
     * @return mix
     */
    public function getTeacherListByKeyName($teacherName, $arrFields = array(), $offset = 0, $limit = 20) {
        if(strlen($teacherName) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[teacherName:$teacherName]");
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = Zhibo_Dao_Teacher::$allFields;
        }

        $arrConds = array(
            'deleted' => self::STATUS_OK,
        );

        $binName = bin2hex("%$teacherName%");
        $arrConds[] = "teacher_name like unhex('$binName')";


        $arrAppends = array(
            "order by create_time",
            "limit $offset, $limit",
        );

        $ret = $this->objDaoTeacher->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

}