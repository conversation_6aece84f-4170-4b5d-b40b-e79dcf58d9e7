<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * Created by PhpStorm.
 * File : ActTemplate.php
 * Author: <EMAIL>
 * Date: 2019/4/19
 * Time: 11:13
 * Desc: 活动模板ds层
 */
class Oplib_Ds_PageTemplate{

    private $_objDao;
    private $_objRedis;
    //状态
    const STATUS_ONLINE = 1;
    const STATUS_DEL    = 2;

    static $ARR_STATUS  = [
        self::STATUS_ONLINE => '在线',
        self::STATUS_DEL    => '删除',
    ];

    public function __construct()
    {
        $this->_objDao = new Oplib_Dao_PageTemplate();
        $this->_objRedis = Hk_Service_RedisClient::getInstance("zhibo");
    }

    /**
     * @param $arrParams
     * @return bool|int
     * @author: <EMAIL>
     * @date: 2019/4/19
     * @time: 11:29
     * @desc: 添加活动模板
     */
    public function addActTemplate($arrParams){

        if(empty($arrParams)){
            Bd_Log::warning("Abstract[opmis] Error:[param error] Detail:[".json_encode($arrParams)."]");
            return false;
        }

        $arrInserts = [
            'name'          => isset($arrParams['name']) ? trim(strval($arrParams['name'])) : '',
            'actType'       => isset($arrParams['actType']) ? intval($arrParams['actType']) : 0,
            'pid'           => isset($arrParams['pid']) ?   strval($arrParams['pid']) : 0,
            'templateData'  => isset($arrParams['templateData']) ?  $arrParams['templateData'] : [],
            'status'        => self::STATUS_ONLINE,
            'operatorUid'   => isset($arrParams['operatorUid']) ?  intval($arrParams['operatorUid']) : 0,
            'operator'      => isset($arrParams['operator']) ?  strval($arrParams['operator']) : '',
            'createTime'    => time(),
            'ext'           => isset($arrParams['ext']) ? $arrParams['ext'] : [],
        ];
        $res = $this->_objDao->insertRecords($arrInserts);

        if($res){
            $res = $this->_objDao->getInsertId();
        }
        return $res;

    }

    /**
     * @param $actType
     * @param array $arrFields
     * @param int $pn
     * @param int $rn
     * @return array|false
     * @author: <EMAIL>
     * @date: 2019/4/19
     * @time: 11:51
     * @desc: 获取活动列表
     */

    public function getActTemplateList($actType = null,$arrFields = [], $pn = 0, $rn = 10){

        $pn     = intval($pn);
        $rn     = intval($rn);

        if($actType){
            $arrConds = [
                'actType' => $actType,
                'status' => self::STATUS_ONLINE,
            ];
        }else{
            $arrConds = [
                'status' => self::STATUS_ONLINE,
            ];
        }

        empty($arrFields) && $arrFields = Oplib_Dao_PageTemplate::getAllFields();

        if ($pn >= 0  && $rn > 0) {
            $arrAppends = [
                "order by create_time desc limit $pn, $rn",
            ];
        } else {
            $arrAppends = [
                "order by create_time desc",
            ];
        }

        $res = $this->_objDao->getListByConds($arrConds,$arrFields,null,$arrAppends);

        return $res;

    }

    /**
     * @param $intId
     * @param $arrParams
     * @return bool
     * @author: <EMAIL>
     * @date: 2019/4/19
     * @time: 11:59
     * @desc: 更具id修改模板内容 | 模板json不可修改
     */

    public function updateActTemplateById($intId,$arrParams){

        $intId = intval($intId);

        if($intId <= 0 || empty($arrParams)){
            Bd_Log::warning("Abstract[opmis] Error:[param error],Detail:[id:$intId;arrParams:$arrParams]");
            return false;
        }

        $arrConds = [
            'id'  => $intId,
        ];

        $arrFields = [
            'name'          => isset($arrParams['name']) ? trim(strval($arrParams['name'])) : '',
            'actType'       => isset($arrParams['actType']) ? intval($arrParams['actType']) : 0,
            'pid'           => isset($arrParams['pid']) ?   strval($arrParams['pid']) : 0,
            'templateData'  => isset($arrParams['templateData']) ?  $arrParams['templateData'] : [],
            'operatorUid'   => isset($arrParams['operatorUid']) ?  intval($arrParams['operatorUid']) : 0,
            'operator'      => isset($arrParams['operator']) ?  strval($arrParams['operator']) : '',
            'updateTime'    => time(),
            'ext'           => isset($arrParams['ext']) ? $arrParams['ext'] : [],
        ];

        $res = $this->_objDao->updateByConds($arrConds,$arrFields);

        if($res){
            $cacheKey = sprintf(Oplib_Const_Cache::OPMIS_PAGETEMPLATE,$intId);
            $resRedis = $this->_objRedis->del($cacheKey);
            if(null === $resRedis){
                Bd_Log::warning("Error:[redis del error],Detail:[".$cacheKey."]");
                return false;
            }
        }

        return $res;

    }

    /**
     * @param $intId
     * @return bool
     * @author: <EMAIL>
     * @date: 2019/4/19
     * @time: 14:18
     * @desc: 更具模板id 删除模板
     */

    public function delActTemplateById($intId){
        $intId = intval($intId);

        if($intId <= 0){
            Bd_Log::warning("Abstract[opmis] Error:[param error],Detail:[id:$intId]");
            return false;
        }

        $arrConds = [
            'id'  => $intId,
        ];
        $arrFields = [
            'status'        => self::STATUS_DEL,
        ];
        $res = $this->_objDao->updateByConds($arrConds,$arrFields);

        if($res){
            $cacheKey = sprintf(Oplib_Const_Cache::OPMIS_PAGETEMPLATE,$intId);
            $resRedis = $this->_objRedis->del($cacheKey);
            if(null === $resRedis){
                Bd_Log::warning("Error:[redis del error],Detail:[".$cacheKey."]");
                return false;
            }
        }
        return $res;
    }

    /**
     * @param $intId
     * @param array $arrParams
     * @param bool $bCache
     * @return array|bool|false|mixed
     * @author: <EMAIL>
     * @date: 2019/5/15
     * @time: 16:43
     * @desc: 根据id获取模板信息
     */

    public function getTemplateInfoById($intId,$arrFields = [],$bCache = true){
        $id  = intval($intId);
        if($id <=0 ){
            Bd_Log::warning("Error:[param error],Detail:[id:$intId]");
            return false;
        }

        //核心字段校验,防止缓存中不存在该数据
        if( $bCache  && !empty($arrFields) && !in_array('templateData',$arrFields)){
            $arrFields[] = 'templateData';
        }


        $cacheKey = sprintf(Oplib_Const_Cache::OPMIS_PAGETEMPLATE,$intId);
        if($bCache ){
            $res   = $this->_objRedis->get($cacheKey);
            if($res){
                $arrOutPut = json_decode($res,true);
                return $arrOutPut;
            }
        }

        $arrConds = [
            'id'  => $id,
        ];
        if(empty($arrFields)){
            $arrFields =  Oplib_Dao_PageTemplate::getAllFields();
        }

        $res = $this->_objDao->getRecordByConds($arrConds,$arrFields);

        if($bCache){
            $redisRes = $this->_objRedis ->set($cacheKey,json_encode($res),3600*24);
            if($redisRes === false){
                Bd_Log::warning("Error:[redis set  error],Detail:[  ".$cacheKey."]");
            }
        }
        return $res;

    }




}
