<?php
/**
 * 运营活动收集用户信息
 * <AUTHOR>
 * @DateTime 2018-08-20
 */
class Oplib_Ds_ActivityCollectUserInfo
{
	// 数据访问对象
	private $objDao;

	/**
	 * 构造方法
	 * <AUTHOR>
	 * @DateTime 2018-08-21
	 */
	public function __construct()
	{
		$this->objDao = new Oplib_Dao_ActivityCollectUserInfo();
	}

	/**
	 * 添加运营活动用户信息数据
	 * <AUTHOR>
	 * @DateTime 2018-08-20
	 * @param    array   $arrParams 用户信息数据
	 */
	public function addActivityCollectUserInfo($arrParams)
	{
		if (empty($arrParams) || intval($arrParams['uid']) <= 0 || intval($arrParams['skuId']) <= 0 || !is_numeric($arrParams['grade'])) {
			Bd_Log::warning('Error:[param error]，Detail:[' . json_encode($arrParams) . ']');
			return false;
		}

		$arrParams = array_map('trim', $arrParams);
		$arrData = [
			'uid'        => isset($arrParams['uid']) ? intval($arrParams['uid']) : 0,
			'skuId'      => isset($arrParams['skuId']) ? intval($arrParams['skuId']) : 0,
			'city'       => isset($arrParams['city']) ? strval($arrParams['city']) : '',
			'school'     => isset($arrParams['school']) ? strval($arrParams['school']) : '',
			'grade'      => isset($arrParams['grade']) ? intval($arrParams['grade']) : 0,
			'class'      => isset($arrParams['class']) ? strval($arrParams['class']) : '',
			'createTime' => time(),
		];

		return $this->objDao->insertRecords($arrData);
	}

	/**
	 * 获取运营活动用户信息
	 * <AUTHOR>
	 * @DateTime 2018-08-22
	 * @param    int                $startTime 开始时间
	 * @param    int                $endTime   结束时间
	 * @param    array             	$arrFields 字段信息
	 * @return   array
	 */
	public function getActivityCollectUserInfoByTime($startTime, $endTime, $arrFields = [])
	{
		if (!is_numeric($startTime) || !is_numeric($endTime)) {
			Bd_Log::warning("Error:[param error],Detail:[startTime:{$startTime},endTime:{$endTime}]");
            return false;
		}

		$arrConds = [
            "create_time >= {$startTime}",
            "create_time < {$endTime}",
        ];

        empty($arrFields) && $arrFields = Oplib_Dao_ActivityCollectUserInfo::getActivityCollectUserInfoFields();
		return $this->objDao->getListByConds($arrConds, $arrFields);
	}

	/**
	 * 获取OL项目数据列表
	 * <AUTHOR>
	 * @DateTime 2019-01-15
	 * @param    array              $uids   用户id
	 * @return   array
	 */
	public function getActivityCollectUserInfoList($uids)
	{
		if (empty($uids) || !is_array($uids)) {
			Bd_Log::warning("Abstract[skyfire] Error:[" . __METHOD__ . "] Detail:[uids:" . json_encode($uids) . "]");
            return false;
		}

		$arrConds   = ['`uid` IN(' . implode(',', $uids) . ')'];
        $arrFields  = ['uid', 'city', 'school', 'grade', 'class'];

        return $this->objDao->getListByConds($arrConds, $arrFields);
	}
}