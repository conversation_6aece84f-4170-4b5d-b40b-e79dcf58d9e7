<?php
/**
 * Copyright (c) 2016 zuoyebang.com, Inc. All Rights Reserved
 * @file:RankingInClass.php
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @date: 2016/7/13
 * @time: 11:30
 * @desc:
 */

class Zhibo_Ds_RankingInClass
{

    private $_keyPrefix;
    private $_redis = null;
    //redis 做对总数  和 连错 的key
    const REDIS_KEY_RIGHTNUM = 'right';  //总做对题目数
    const REDIS_KEY_ERRORNUM = 'error';  //总做错题目数
    const REDIS_KEY_CONT_ERROR = 'cont_error';  //连错题目数   最大为 REDIS_CONT_ERROR_WARNING_NUM
    //前端需要报警的学生最大连错个数
    const REDIS_CONT_ERROR_WARNING_NUM = 3;
    //redis key ttl, 1天
    const REDIS_KEY_EXPIRE_TIME = 86400;

    public function __construct()
    {
        $path = "/hk/redis/fudao";
        $conf = Bd_Conf::getConf($path);
        if (empty($conf) || !isset($conf['service']) || empty($conf['keys']['studentrank'])) {
            Bd_Log::warning("Error[redisError] Detail[get redis conf failed, path:{$path}]");
        }
        $this->_keyPrefix = $conf['keys']['studentrank'];
        $this->_redis = new Hk_Service_Redis($conf['service']);
    }

    /**
     * 已有用户 增加做对的个数,没有会自动增加
     * @param $uid
     * @param $courseId
     * @param $lessonId
     * @param $classId
     * @param $score
     * @return bool
     */
    public function addRightScore($uid, $courseId, $lessonId, $classId, $score)
    {
        if (intval($uid) <= 0 || intval($courseId) <= 0 || intval($lessonId) <= 0 || intval($classId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[uid:$uid courseId:$courseId lessonId:$lessonId classId:$classId]");
            return false;
        }
        if (empty($this->_redis)) {
            Bd_Log::warning("Error[redisError] Detail[no redis instance]");
            return false;
        }
        $key = $this->_parseKey($courseId, $lessonId, $classId);
        $member = $this->_parseMember($uid);
        $ret = $this->_redis->zincrby($key, $score, $member);
        if ($ret) {
            $this->_redis->expire($key, self::REDIS_KEY_EXPIRE_TIME);
        }
        return $ret;
    }

    /**
     * 已有用户 增加做错的个数,没有会自动增加
     * @param $uid
     * @param $courseId
     * @param $lessonId
     * @param $classId
     * @param $score
     * @return bool
     */
    public function addErrorScore($uid, $courseId, $lessonId, $classId, $score)
    {
        if (intval($uid) <= 0 || intval($courseId) <= 0 || intval($lessonId) <= 0 || intval($classId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[uid:$uid courseId:$courseId lessonId:$lessonId classId:$classId]");
            return false;
        }
        if (empty($this->_redis)) {
            Bd_Log::warning("Error[redisError] Detail[no redis instance]");
            return false;
        }
        $key = $this->_parseKey($courseId, $lessonId, $classId, self::REDIS_KEY_ERRORNUM);
        $member = $this->_parseMember($uid);
        $ret = $this->_redis->zincrby($key, $score, $member);
        if ($ret) {
            $this->_redis->expire($key, self::REDIS_KEY_EXPIRE_TIME);
        }
        return $ret;
    }

    /**
     * 已有用户 增加连错的个数,没有会自动增加
     * @param int $uid
     * @param int $courseId
     * @param int $lessonId
     * @param int $classId
     * @param int $isRight 题目是否做对了
     * @return bool
     */
    public function addContErrorScores($uid, $courseId, $lessonId, $classId, $isRights)
    {
        if (intval($uid) <= 0 || intval($courseId) <= 0 || intval($lessonId) <= 0 || intval($classId) <= 0 || empty($isRights)) {
            Bd_Log::warning("Error:[param error], Detail:[uid:$uid courseId:$courseId lessonId:$lessonId classId:$classId isRights:" . implode(',', $isRights) . "]");
            return false;
        }
        if (empty($this->_redis)) {
            Bd_Log::warning("Error[redisError] Detail[no redis instance]");
            return false;
        }
        $key = $this->_parseKey($courseId, $lessonId, $classId, self::REDIS_KEY_CONT_ERROR);
        if ("" == $key) {
            Bd_Log::warning("Error[redisError] Detail[cannot parse key, courseId:{$courseId}]");
            return false;
        }
        $member = $this->_parseMember($uid);
        //获取之前连错数字
        $tmpScore = $this->_redis->zscore($key, $member);
        $tmpScore = intval($tmpScore);
        //预演一遍 本次更新的后果
        foreach ($isRights as $isRight) {
            //如果已经达到需要报警的连错个数了,就不继续操作redis了
            if ($tmpScore < self::REDIS_CONT_ERROR_WARNING_NUM) {
                if ($isRight) {
                    //做对了 清空连错计数
                    $tmpScore = 0;
                } else {
                    //做错了 累加连错计数
                    $tmpScore++;
                }
            }
        }
        //这里不关心事务了,默认一个同学的数据 同一时间 只有一个人会提交
        $ret = $this->_redis->zadd($key, $tmpScore, $member);
        if ($ret) {
            $this->_redis->expire($key, self::REDIS_KEY_EXPIRE_TIME);
        }
        return $ret;
    }

    /**
     * 生成keys
     * @param $courseId
     * @param $lessonId
     * @param $classId
     * @param $flag
     * @return mixed
     */
    public function _parseKey($courseId, $lessonId, $classId, $flag = self::REDIS_KEY_RIGHTNUM)
    {
        $keyIdx = $this->_keyPrefix . $courseId . '_' . $lessonId . '_' . $classId . '_' . $flag;
        return $keyIdx;
    }

    /**
     * 生成member
     * @param $uid
     * @return string
     */
    public function _parseMember($uid)
    {
        return $uid;
    }
}