<?php
/**
 * Copyright (c) 2016 zuoyebang.com, Inc. All Rights Reserved
 * @file:Lesson.php
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @date: 2016/7/4
 * @time: 19:36
 * @desc: 课节
 */

class Zhibo_Ds_Lesson
{
    //课节状态
    const STATUS_CLASS_TOSTART = 0; //未开始
    const STATUS_CLASS_IN = 1; //直播中
    const STATUS_CLASS_STOPED = 2; //已下课
    const STATUS_DELETED = 3; //已删除
    static $STATUS_ARRAY = array(
        self::STATUS_CLASS_TOSTART => '未开始',
        self::STATUS_CLASS_IN => '直播中',
        self::STATUS_CLASS_STOPED => '已下课',
        self::STATUS_DELETED => '已删除',
    );

    //课堂状态
    const STATUS_INCLASS_TOSTART = 0; //老师未开始
    const STATUS_INCLASS_TEACHERING = 1; //讲课中
    const STATUS_INCLASS_PAUSE = 4; //课间休息
    static $INCLASS_STATUS_ARRAY = array(
        self::STATUS_INCLASS_TOSTART => '老师未开始',
        self::STATUS_INCLASS_TEACHERING => '讲课中',
        self::STATUS_INCLASS_PAUSE => '课间休息',
    );

    const FILE_LECTURE = 'lecture';
    const FILE_PIC_LECTURE = 'picLecture';
    const FILE_ATTACHED = 'attached';
    static $FILE_ARRAY = array(
        self::FILE_LECTURE,
        self::FILE_ATTACHED,
        self::FILE_PIC_LECTURE,
    );

    const REDIS_LECTURE_KEY = 'lectureTransList';
    const REDIS_LECTURE_LOCK = 'lectureLock';

    private $objDaoLesson;

    public function __construct()
    {
        $this->objDaoLesson = new Zhibo_Dao_Lesson();
    }

    /**
     * 获取课节信息
     *
     * @param  int $lessonId 课节id
     * @param  array $arrFields 指定属性
     * @return
     */
    public function getLessonInfo($lessonId, $arrFields = array())
    {
        if (intval($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$lessonId]");
            return false;
        }
        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_Lesson::$allFields;
        }
        $arrConds = array(
            'lessonId' => intval($lessonId),
        );
        $ret = $this->objDaoLesson->getRecordByConds($arrConds, $arrFields);
        return $ret;
    }
    /**
     * 更新课节扩展信息
     *
     * @param  int $lessonId 课节id
     * @param  array $arrExt 课节属性
     * @return bool true/false
     */
    public function updateLessonExt($lessonId, $arrExt) {
        if (intval($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$lessonId]");
            return false;
        }
        $lessonInfo = $this->getLessonInfo($lessonId, array('extData'));
        if (empty($lessonInfo)) {
            Bd_Log::warning("Error:[lesson empty], Detail:[lessonId:$lessonId]");
            $this->objDaoLesson->rollback();
            return false;
        }
        $arrLessonExt = $lessonInfo['extData'];
        foreach ($arrExt as $key => $value) {
            $arrLessonExt[$key] = $value;
        }
        $arrConds = array(
            'lessonId' => intval($lessonId),
        );
        $arrFields = array(
            'extData' => json_encode($arrLessonExt),
        );
        $ret = $this->objDaoLesson->updateByConds($arrConds, $arrFields);
        $this->objDaoLesson->commit();
        return $ret;
    }
    /**
     * 根据课程获取课时列表
     *
     * @param  int $courseId 课程id
     * @param  array $arrFields 指定属性
     * @param  int $offset 偏移
     * @param  int $limit 限制
     * @return bool|array
     */
    public function getLessonListByCourseId($courseId, $arrFields = array(), $offset = 0, $limit = 20)
    {
        if (intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");
            return array();
        }
        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_Lesson::$allFields;
        }
        $arrConds = array(
            'courseId' => intval($courseId),
            'status' => array(self::STATUS_DELETED, '<>'),
        );
        $arrAppends = array(
            "order by start_time",
            "limit $offset, $limit",
        );
        $ret = $this->objDaoLesson->getListByConds($arrConds, $arrFields, NULL, $arrAppends);
        return $ret;
    }

    /**
     * 根据课程获取课时数量
     *
     * @param  int $courseId 课程id
     * @return int
     */
    public function getLessonCntByCourseId($courseId)
    {
        if (intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");
            return false;
        }
        $arrConds = array(
            'courseId' => intval($courseId),
            'status' => array(self::STATUS_DELETED, '<>'),
        );
        $ret = $this->objDaoLesson->getCntByConds($arrConds);
        return $ret;
    }

    /**
     * 根据课程获取课时列表
     *
     * @param  int $arrCourseId 课程id
     * @param  array $arrFields 指定属性
     * @param  int $offset 偏移
     * @param  int $limit 限制
     * @return array|bool
     */
    public function getLessonListByCourseArr($arrCourseIds, $arrFields = array(), $offset = 0, $limit = 20)
    {
        if (empty($arrCourseIds)) {
            Bd_Log::warning("Error:[param error], Detail:[arrCourseIds:json_encode($arrCourseIds)]");
            return false;
        }
        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_Lesson::$allFields;
        }
        $arrConds = array(
            'status' => array(self::STATUS_DELETED, '<>'),
        );
        $strCourseIds = implode(',', $arrCourseIds);
        $arrConds[] = "course_id in ($strCourseIds)";
        $arrAppends = array(
            "order by course_id,start_time",
            "limit $offset, $limit",
        );
        $ret = $this->objDaoLesson->getListByConds($arrConds, $arrFields, NULL, $arrAppends);
        return $ret;
    }

    /**
     * 根据课程获取课时数量
     *
     * @param  array $courseId 课程ids
     * @return int
     */
    public function getLessonCntByCourseArr($arrCourseIds)
    {
        if (empty($arrCourseIds)) {
            Bd_Log::warning("Error:[param error], Detail:[arrCourseIds:json_encode($arrCourseIds)]");
            return false;
        }
        $arrConds = array(
            'status' => array(self::STATUS_DELETED, '<>'),
        );
        $strCourseIds = implode(',', $arrCourseIds);
        $arrConds[] = "course_id in ($strCourseIds)";
        $ret = $this->objDaoLesson->getCntByConds($arrConds);
        return $ret;
    }

    /**
     * 批量获取课节详情(不包含已删除)
     *
     * @param  array $arrLessonId 课节ids
     * @param  array $arrFields 指定属性
     * @return array|bool
     */
    public function getLessonInfoArray($arrLessonId, $arrFields = array())
    {
        if (empty($arrLessonId)) {
            return array();
        }
        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_Lesson::$allFields;
        } elseif (!in_array('lessonId', $arrFields)) {
            $arrFields[] = 'lessonId';
        }
        $arrLessonId = array_unique($arrLessonId);
        $strLessonIds = implode(',', $arrLessonId);
        $arrConds = array(
            'status' => array(self::STATUS_DELETED, '<>'),
            "lesson_id in ($strLessonIds)",
        );
        $arrAppends = array(
            'order by start_time asc',
        );
        $ret = $this->objDaoLesson->getListByConds($arrConds, $arrFields, null, $arrAppends);
        if (false === $ret) {
            return false;
        }
        $arrRes = array();
        foreach ($ret as $lesson) {
            $lessonId = $lesson['lessonId'];
            $arrRes[$lessonId] = $lesson;
        }
        return $arrRes;
    }

    /**
     * 获取数量
     * @param $arrConds
     * @return false|int
     */
    public function getLessonCntByConds($arrConds)
    {
        $ret = $this->objDaoLesson->getCntByConds($arrConds);
        return $ret;
    }

    /**
     * @param $arrConds
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getLessonListByConds($arrConds, $arrFields = array(), $offset = 0, $limit = 20)
    {
        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_Lesson::$allFields;
        }
        $arrAppends = array(
            "order by start_time asc",
            "limit $offset, $limit",
        );
        $ret = $this->objDaoLesson->getListByConds($arrConds, $arrFields, NULL, $arrAppends);
        return $ret;
    }

    /**
     * 根据课程获取课时列表
     * @param  array $arrCourseId 课程id
     * @param  array $arrFields 指定属性
     * @return array
     */
    public function getOnlineLessonListByCourseArr($arrCourseIds, $arrFields = array())
    {
        if (empty($arrCourseIds)) {
            return array();
        }
        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_Lesson::$allFields;
        }
        $arrConds = array(
            'status' => self::STATUS_CLASS_IN,
        );
        $strCourseIds = implode(',', $arrCourseIds);
        $arrConds[] = "course_id in ($strCourseIds)";
        $ret = $this->objDaoLesson->getListByConds($arrConds, $arrFields);
        return $ret;
    }

    /**
     * 获取最近一个已完成课节详情
     *
     * @param  int $courseId 课程id
     * @param  array $arrFields 指定属性
     * @return bool|array
     */
    public function getLessonInfoLatelyFinish($courseId, $arrFields = array())
    {
        if (intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$courseId]");
            return false;
        }
        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_Lesson::$allFields;
        }
        $arrConds = array(
            'courseId' => intval($courseId),
            'status' => array(self::STATUS_CLASS_STOPED, "="),
        );
        $arrAppends = array(
            " order by start_time desc limit 1",
        );
        $ret = $this->objDaoLesson->getRecordByConds($arrConds, $arrFields, null, $arrAppends);
        return $ret;
    }

    /**
     * 获取最近课节详情
     *
     * @param  int $courseId 课程id
     * @param  int $studentUid 学生id
     * @param  array $arrFields 指定属性
     * @return array
     */
    public function getLessonInfoLately($arrLessonIds, $arrFields = array())
    {
        if (empty($arrLessonIds)) {
            return array();
        }
        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_Lesson::$allFields;
        }
        $arrConds = array(
            'status' => array(self::STATUS_CLASS_STOPED, "<"),
        );
        $strLessonIds = implode(',', $arrLessonIds);
        $arrConds[] = "lesson_id in ($strLessonIds)";
        $arrAppends = array(
            " order by start_time limit 1",
        );
        $ret = $this->objDaoLesson->getRecordByConds($arrConds, $arrFields, null, $arrAppends);
        return $ret;
    }

    /**
     * 获取最近课节详情
     *
     * @param  int $courseId 课程id
     * @param  int $studentUid 学生id
     * @param  array $arrFields 指定属性
     * @return array
     */
    public function getLessonInfoLatelyFinishByLessonId($arrLessonIds, $arrFields = array())
    {
        if (empty($arrLessonIds)) {
            return array();
        }
        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_Lesson::$allFields;
        }
        $arrConds = array(
            'status' => array(self::STATUS_CLASS_STOPED, "="),
        );
        $strLessonIds = implode(',', $arrLessonIds);
        $arrConds[] = "lesson_id in ($strLessonIds)";
        $arrAppends = array(
            " order by start_time desc limit 1",
        );
        $ret = $this->objDaoLesson->getRecordByConds($arrConds, $arrFields, null, $arrAppends);
        return $ret;
    }
}