<?php
/**************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   OperationWeiKeUser.php
 * <AUTHOR>
 * @date   2018/11/20 9:53
 * @brief  运营-名师微课0.2-用户数据表
 **/
class Oplib_Ds_OperationWeiKeUser
{
    const ALL_FIELDS       = 'id,uid,remainCnt,integralCnt,lastRewardsTime,createTime,updateTime,ext';
    const SHOW_BASE_FIELDS = 'id,uid,remainCnt,integralCnt,lastRewardsTime';

    private $_objDaoWeiKeUser;

    public function __construct()
    {
        $this->_objDaoWeiKeUser = new Oplib_Dao_OperationWeiKeUser();
    }

    /** 更新剩余学习次数 （step > 0 增加剩余时间，step < 0 减少剩余时间）
     * @param int    $id
     * @param int    $step
     * @param  array $extraCondition 额外条件
     * @return array|bool|false
     */
    public function incrRemainCntById($id = 0, $step = 1, $extraCondition = [])
    {
        $id   = intval($id);
        $step = intval($step);
        if ($id <= 0 || $step == 0) {
            Bd_Log::warning("Error:[param error],Detail:[id:$id,step:$step]");
            return false;
        }

        $arrConds = ['id' => $id];

        $arrFields               = ['remain_cnt = remain_cnt + ' . $step];
        $arrFields['updateTime'] = time();
        if ($step > 0) {
            // step大于0 更新奖励时间
            $arrFields['lastRewardsTime'] = time();
        } else {
            $arrConds['remainCnt'] = [-$step, '>=']; // 防止剩余次数为负数的情况
        }
        if (!empty($extraCondition)) {
            $arrConds = array_merge($extraCondition, $arrConds);
        }

        $ret = $this->_objDaoWeiKeUser->updateByConds($arrConds, $arrFields, null, ['LIMIT 1']);
        if ($ret) {
            $ret = $this->_objDaoWeiKeUser->getAffectedRows();
        }

        return $ret;
    }

    /**更新积分 （step > 0 增加积分，step < 0 减少积分）
     * @param int $id
     * @param int $step
     * @return array|bool|false
     */
    public function incrIntegralCntById($id = 0, $step = 1)
    {
        $id   = intval($id);
        $step = intval($step);
        if ($id <= 0 || $step == 0) {
            Bd_Log::warning("Error:[param error],Detail:[id:$id,step:$step]");
            return false;
        }

        $arrConds = ['id' => $id];
        if ($step < 0) {
            $arrConds['integralCnt'] = [-$step, '>=']; // 防止剩余次数为负数的情况
        }

        $arrFields               = ['integral_cnt = integral_cnt + ' . $step];
        $arrFields['updateTime'] = time();

        $ret = $this->_objDaoWeiKeUser->updateByConds($arrConds, $arrFields, null, ['LIMIT 1']);
        if ($ret) {
            $ret = $this->_objDaoWeiKeUser->getAffectedRows();
        }

        return $ret;
    }

    /** 根据uid查询数据
     * @param $uid
     * @param $strFields
     * @return array|bool|false
     */
    public function getWeikeUserByUid($uid, $strFields = self::SHOW_BASE_FIELDS)
    {
        $arrConds = ['uid' => intval($uid)];
        if ($arrConds['uid'] <= 0) {
            Bd_Log::warning("Error:[param error],Detail:[uid:$uid]");
            return false;
        }

        if (empty($strFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        } else {
            $arrFields = explode(',', $strFields);
        }

        $ret = $this->_objDaoWeiKeUser->getRecordByConds($arrConds, $arrFields, null, ['LIMIT 1']);

        return $ret;
    }

    /**添加录播课
     * @param $arrParams
     * @return bool|int
     */
    public function addWeikeUser($arrParams)
    {
        $arrFields = [
            'uid'             => isset($arrParams['uid']) ? intval($arrParams['uid']) : 0,
            'remainCnt'       => isset($arrParams['remainCnt']) ? intval($arrParams['remainCnt']) : 0,
            'integralCnt'     => isset($arrParams['integralCnt']) ? intval($arrParams['integralCnt']) : 0,
            'lastRewardsTime' => time(),
            'createTime'      => time(),
            'updateTime'      => time(),
            'ext'             => isset($arrParams['ext']) ? $arrParams['ext'] : '[]',
        ];

        if ($arrFields['uid'] <= 0) {
            $strParams = json_encode($arrParams);
            Bd_Log::warning("Error:[param error],Detail:[strParams:{$strParams}]");
            return false;
        }
        if (is_array($arrFields['ext'])) {
            $arrFields['ext'] = json_encode($arrFields['ext']);
        }

        $ret = $this->_objDaoWeiKeUser->insertRecords($arrFields);
        if ($ret) {
            $ret = $this->_objDaoWeiKeUser->getInsertId();
        }

        return $ret;
    }

    /** 更新微课用户的扩展信息
     * @param       $id
     * @param array $ext
     * @return bool
     */
    public function updateWeikeUserExtById($id, $ext = [])
    {
        if (intval($id) <= 0) {
            Bd_Log::warning("Error:[param error],Detail:[id:{$id}]");

            return false;
        }
        $arrConds  = ['id' => intval($id)];
        $arrFields = ['ext' => json_encode($ext), 'updateTime' => time()];
        $ret       = $this->_objDaoWeiKeUser->updateByConds($arrConds, $arrFields, null, ['LIMIT 1']);

        return $ret;
    }
}