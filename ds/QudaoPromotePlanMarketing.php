<?php
/**
 * @file QudaoPromotePlan.php
 * <AUTHOR>
 * @version
 * @brief
 *
 **/


class Qdlib_Ds_QudaoPromotePlanMarketing
{
    const FLAG_MATERIAL = 1; // 素材
    const FLAG_RESOURCE = 2; // 资源位
    const FLAG_CREATIVE = 4; // 创意
    const FLAG_FILE = 8; // 文件

    public $allFields;

    private $_objDaoQudaoPromotePlanMarketing;
    private $objRedis;

    public function __construct()
    {
        $this->objRedis = Qdlib_Util_Cache::getQudaoRedis();
        $this->_objDaoQudaoPromotePlanMarketing = new Qdlib_Dao_QudaoPromotePlanMarketing();
        $this->allFields = $this->_objDaoQudaoPromotePlanMarketing->getFields();
    }

    public function searchStateName($fields, $conds, $append)
    {
        return $this->_objDaoQudaoPromotePlanMarketing->getListByConds($conds, $fields, null, $append);
    }
}