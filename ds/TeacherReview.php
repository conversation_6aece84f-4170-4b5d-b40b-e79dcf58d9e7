<?php
/**
 * Copyright (c) 2016 zuoyebang.com, Inc. All Rights Reserved
 * @file:TeacherReview.php
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @date: 2016/7/5
 * @time: 11:35
 * @desc: 评价
 */

class Zhibo_Ds_TeacherReview
{
    //星级
    public static $starInfo = array(
        1 => '极不满意',
        2 => '勉强可以',
        3 => '感觉一般',
        4 => '比较满意',
        5 => '非常满意',
    );
    const STATUS_GOOD = 0;//可展示信息
    const STATUS_BAD = 1;//不可展示信息

    public static $starTag = array(//星级标签
        1 => array('态度冷淡', '缺少互动', '没有耐心', '不文明用语', '没听懂'),
        2 => array('互动较少', '讲解不细致', '逻辑混乱', '有口音', '耐心不够'),
        3 => array('互动较少', '耐心不够', '讲解不细致', '表达欠佳', '讲得一般'),
        4 => array('思路清晰', '讲解细致', '态度热情', '板书美观', '互动欠缺'),
        5 => array('思路清晰', '方法独特', '知识渊博', '态度热情', '板书美观'),
    );

    private $objDaoTeacherReview;

    /**
     * 构造函数
     *
     */
    public function __construct()
    {
        $this->objDaoTeacherReview = new Zhibo_Dao_TeacherReview();
    }

    /**
     * 获取学生课节评价信息
     *
     * @param  int $studentUid 学生uid
     * @param  int $courseId 课程id
     * @param  int $lessonId 课节id
     * @return bool|array
     */
    public function getTeacherReviewByLesson($studentUid, $courseId, $lessonId, $teacherUid, $arrFields = array())
    {
        if (intval($studentUid) <= 0 || intval($courseId) <= 0 || intval($lessonId) <= 0 || intval($teacherUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId lessonId:$lessonId teacherUid:$teacherUid]");
            return false;
        }
        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_TeacherReview::$allFields;
        }
        $arrConds = array(
            'studentUid' => intval($studentUid),
            'courseId' => intval($courseId),
            'lessonId' => intval($lessonId),
            'teacherUid' => intval($teacherUid),
        );
        $ret = $this->objDaoTeacherReview->getRecordByConds($arrConds, $arrFields);
        return $ret;
    }

    /**
     * 新增学生评价
     *
     * @param  array $arrParams 状态属性
     * @return bool true/false
     */
    public function addTeacherReview($arrParams)
    {
        if (intval($arrParams['studentUid']) <= 0 || intval($arrParams['courseId']) <= 0 || intval($arrParams['teacherUid']) <= 0 || intval($arrParams['lessonId']) <= 0 || intval($arrParams['star']) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[param: " . json_encode($arrParams) . "]");
            return false;
        }
        $arrFields = array(
            'studentUid' => intval($arrParams['studentUid']),
            'courseId' => intval($arrParams['courseId']),
            'lessonId' => intval($arrParams['lessonId']),
            'teacherUid' => intval($arrParams['teacherUid']),
            'star' => intval($arrParams['star']),
            'tag' => strval($arrParams['tag']),
            'content' => strval($arrParams['content']),
            'createTime' => time(),
            'updateTime' => time(),
            'rank' => intval($arrParams['rank']),
            'status' => intval($arrParams['status']),
            'extData' => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
        );
        $ret = $this->objDaoTeacherReview->insertRecords($arrFields);
        return $ret;
    }

    /**
     * @param $arrConds
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getTeacherReviewByConds($arrConds, $arrFields = array(), $offset = 0, $limit = 20)
    {
        if (empty($arrFields)) {
            $arrFields = Zhibo_Dao_TeacherReview::$allFields;
        }
        if (!isset($arrConds['status'])) {
            $arrConds['status'] = self::STATUS_GOOD;
        }
        $arrAppends = array(
            "order by rank asc, create_time desc",
            "limit $offset, $limit",
        );
        $ret = $this->objDaoTeacherReview->getListByConds($arrConds, $arrFields, NULL, $arrAppends);
        return $ret;
    }
}