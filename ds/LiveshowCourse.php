<?php
/**
 * 直播问答 - 课程相关
 * <AUTHOR>
 * @DateTime 2018-09-03
 */
class Oplib_Ds_LiveshowCourse
{
	// 已结束
	const LESSON_FINSH = 1;
	// 未结束
	const LESSON_UNFINISHED = 0;

	// 已结束
	const API_LESSON_FINSH = 3;
	// 进行中
	const API_LESSON_STARTED = 2;
	// 即将开始
	const API_LESSON_STARTING = 1;
	// 未知
	const API_LESSON_UNKNOWN = 0;
	// 章节课程状态
	private static $lessonStatus = [
		self::API_LESSON_FINSH    => '已结束',
		self::API_LESSON_STARTED  => '直播中',
		self::API_LESSON_STARTING => '即将开始',
		self::API_LESSON_UNKNOWN  => '未知',
	];

    /**
     * 构造方法
     * <AUTHOR>
     * @DateTime 2018-08-21
     */
    public function __construct()
    {
		$this->objDauInterface = new  Zb_Core_Ds_Dau_Interface();
    }

	/**
	 * 批量获取直播课程信息
	 * <AUTHOR>
	 * @DateTime 2018-09-05
	 * @param    array                 $courseIds    课程id
	 * @param    array                 $courseFields 课程字段
	 * @param    array                 $lessonFields 章节字段
	 * @return   array
	 */
	public static function getBatchLiveshowCourse($courseIds = [], $courseFields = [], $lessonFields = [])
	{
		if (empty($courseIds) || empty($courseFields)) {
			return [];
		}

		// 如果需要查对应的Lesson数据，那么$courseFields变量必须包含courseId字段
		if (!empty($lessonFields) && !in_array('courseId', $courseFields)) {
			$courseFields[] = 'courseId';
		}

		$courseData = Zb_Service_Dal_Course::getKVByCourseId($courseIds, $courseFields, $lessonFields);

		if (!isset($courseData['errNo']) || $courseData['errNo'] != 0 || !isset($courseData['data']) || !($courseInfo = $courseData['data'])) {
			return [];
		}

		if (!empty($lessonFields)) {
			// 保证一个Course里只对应一个Lesson
			foreach ($courseInfo as &$course) {
				isset($course['lessonList']) && $course['lessonList'] = array_shift($course['lessonList']);
			}
		}

		return $courseInfo;
	}

	/**
	 * 格式化课程信息
	 * <AUTHOR>
	 * @DateTime 2018-09-05
	 * @param    array                 $courseInfo 课程信息
	 * @return   array
	 */
	public static function formatCourseInfo(&$courseInfo = [])
	{
		if (empty($courseInfo)) {
			return [];
		}

		foreach ($courseInfo as &$course) {
			if (isset($course['courseName'])) {
				$course['title'] = $course['courseName'];
				unset($course['courseName']);
			}

			// Status=1是已结束 0是未结束
			if (isset($course['lessonList']['status']) && isset($course['lessonList']['startTime'])) {
				$course['status'] = self::makeLessonStatus($course['lessonList']['status'], $course['lessonList']['startTime']);
				unset($course['lessonList']['status']);
			}

			if (isset($course['lessonList']['startTime'])) {
				$course['createTime'] = date('Y.m.d H:i', $course['lessonList']['startTime']);
				unset($course['lessonList']['startTime']);
			}

			if (isset($course['lessonList']['stopTime'])) {
				$course['endTime'] = date('Y.m.d H:i', $course['lessonList']['stopTime']);
				unset($course['lessonList']['stopTime']);
			}

			if (isset($course['lessonList']) && empty($course['lessonList'])) {
				unset($course['lessonList']);
			}
		}
	}

	/**
	 * 生成Lesson状态
	 * <AUTHOR>
	 * @DateTime 2018-09-05
	 * @param    int                $status    Lesson状态
	 * @param    int                $startTime Lesson开始时间
	 * @param    int 				$isInt     是否返回整数
	 * @return   string
	 */
	public static function makeLessonStatus($status, $startTime = 0, $isInt = false)
	{
		if ($status == self::LESSON_FINSH) {
			$code = self::API_LESSON_FINSH;
		} elseif ($status == self::LESSON_UNFINISHED) {
			// 直播中，即将开始
			$code = ($startTime > time()) ? self::API_LESSON_STARTING : self::API_LESSON_STARTED;
		} else {
			$code = self::API_LESSON_UNKNOWN;
		}

		return ($isInt === true) ? $code : self::$lessonStatus[$code];
	}

	/**
	 * 批量获取直播章节信息
	 * <AUTHOR>
	 * @DateTime 2018-09-12
	 * @param    array                 $lessonIds 章节id
	 * @param    array                 $arrFields 字段信息
	 * @return   array
	 */
	public static function getBatchLiveshowLesson($lessonIds = [], $arrFields = [])
	{
		if (empty($lessonIds) || empty($arrFields)) {
			return [];
		}

		$lessonData = Zb_Service_dal_Lesson::getKVByLessonId($lessonIds, $arrFields);

		if (!isset($lessonData['errNo']) || $lessonData['errNo'] != 0 || !isset($lessonData['data']) || !($lessonInfo = $lessonData['data'])) {
			return [];
		}

		return $lessonInfo;
	}

	/**
	 * 格式化课程信息
	 * <AUTHOR>
	 * @DateTime 2018-09-12
	 * @param    array                 $lessonInfo 章节信息
	 * @return   array
	 */
	public static function formatLessonInfo(&$lessonInfo = [])
	{
		if (empty($lessonInfo)) {
			return [];
		}

		foreach ($lessonInfo as &$lesson) {
			// Status=1是已结束 0是未结束
			if (isset($lesson['status']) && isset($lesson['startTime'])) {
				$lesson['status'] = self::makeLessonStatus($lesson['status'], $lesson['startTime']);
			}

			if (isset($lesson['startTime'])) {
				$lesson['startTime'] = date('Y.m.d H:i', $lesson['startTime']);
			}

			if (isset($lesson['stopTime'])) {
				$lesson['endTime'] = date('Y.m.d H:i', $lesson['stopTime']);
				unset($lesson['stopTime']);
			}
		}
	}
}