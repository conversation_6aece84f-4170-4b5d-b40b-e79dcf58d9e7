<?php

/**
 * @file    AfterSaleOperation.php
 * <AUTHOR> <<EMAIL>>
 * @date    2018/5/21 11:00
 * @brief   客服后台 - 售后操作流
 *
 * */
class Hkzb_Ds_Gnmis_AfterSaleOperation
{
    //操作流状态
    const OPERATION_START  = 1;
    const OPERATION_END    = 2;
    const OPERATION_REFUSE = 3;
    static $operationMap = [
        self::OPERATION_START  => '待处理',
        self::OPERATION_END    => '已完成',
        self::OPERATION_REFUSE => '用户拒收',
    ];

    const ALL_FIELDS = 'id,aftersaleId,createUser,createTime,createIdentity,status,deleted,extData';

    private $objDaoAfterSaleOperation;

    public function __construct()
    {
        $this->objDaoAfterSaleOperation = new Hkzb_Dao_Gnmis_AfterSaleOperation();
    }

    public function addAfterSaleOperation($arrParams)
    {
        if (!intval($arrParams['aftersaleId'])) {
            Bd_Log::warning("Error:[param error], Detail:[param: " . json_encode($arrParams) . "]");

            return false;
        }
        //插入数据
        $ts        = time();
        $arrFields = [
            'aftersaleId'    => isset($arrParams['aftersaleId']) ? $arrParams['aftersaleId'] : 0,
            'createUser'     => isset($arrParams['createUser']) ? $arrParams['createUser'] : 0,
            'createTime'     => time(),
            'createIdentity' => isset($arrParams['createIdentity']) ? $arrParams['createIdentity'] : 0,
            'status'         => self::OPERATION_START,
            'extData'        => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '[]',
        ];

        $ret = $this->objDaoAfterSaleOperation->insertRecords($arrFields);
        if ($ret === false) {
            return false;
        }

        return $this->objDaoAfterSaleOperation->getInsertId();
    }

    /**
     * 更新&新增操作流
     *
     * @datetime 2018/5/21 13:59
     * <AUTHOR> <<EMAIL>>
     * @param int   $id             售后父id
     * @param array $flows          操作流
     * @param int   $nextStep       下一步操作
     * @param int   $createUser     创建人 [123123123 代表用户自身]
     * @param int   $createIdentity 创建人身份
     *
     * @return bool|int
     */
    public function updateAndAddFlowByafterSaleId($id, $flows, $nextStep, $createUser = 123123123, $createIdentity = 0)
    {
        if (intval($id) <= 0) {
            Bd_Log::warning("[error] param is error [Detail:afterSaleId-{$id}]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(",", self::ALL_FIELDS);
        }

        // 更新最后一步操作流为完成
        $lastOperation = $this->objDaoAfterSaleOperation->getRecordByConds(['aftersaleId' => $id], $arrFields, null, ['order by id desc']);
        if (!$lastOperation) {
            Bd_Log::warning("[error] get afterSale last operation flow is error [Detail:afterSaleId-{$id}]");

            return false;
        }
        $updateRet = $this->objDaoAfterSaleOperation->updateByConds(['id' => $lastOperation['id']], ['status' => self::OPERATION_END]);
        if ($updateRet) {
            if ($nextStep === count($flows)) {
                return true;
            }
            $flowInfo  = $flows[$nextStep];
            $arrParams = [
                'aftersaleId'    => $id,
                'createUser'     => $createUser,
                'createTime'     => time(),
                'createIdentity' => $createIdentity,
                'status'         => self::OPERATION_START,
                'extData'        => ['name' => Hkzb_Ds_Gnmis_AfterSale::$operationNameMap[$flowInfo['name']], 'giftOpInfo' => ''],
            ];

            return self::addAfterSaleOperation($arrParams);
        }
        return true;
    }

    // 根据指定条件获取售后列表
    public function getListByConds($arrParam,$allFields = array()){
        if ($allFields == array()){
            $allFields = Hkzb_Dao_Gnmis_AfterSaleOperation::$allFields;
        }
        $arrParam = array_intersect_key($arrParam,array_flip(Hkzb_Dao_Gnmis_AfterSaleOperation::$allFields));
        $re = $this->objDaoAfterSaleOperation->getListByConds($arrParam,$allFields);
        if (!$re){
            Bd_Log::warning("[error] select error [Detail:arrParam - ".var_export($arrParam,true)."]");
        }
        return $re;
    }
}
