<?php
/**************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @filename:      StuConsultTaskClue
 * @author:        <EMAIL>
 * @desc:
 * @create:        2017-09-07 20:43:20
 * @last modified: 2017-09-07 20:43:20
 */

class Hkzb_Ds_Gnmis_StuConsultClue
{

    const SEX_DEFAULT = 0;
    const SEX_MALE    = 1;
    const SEX_FEMALE  = 2;

    static $SEX_ARRAY = [
        self::SEX_DEFAULT => '无',
        self::SEX_MALE    => '男',
        self::SEX_FEMALE  => '女',
    ];

    const TYPE_NORMAL       = 1;
    const TYPE_CLUE_SEA     = 2;
    const TYPE_INTRODUCE    = 3;
    const TYPE_POOL         = 4;

    public static $TYPE_ARRAY = array(
        self::TYPE_NORMAL           => '正常购课例子',
        self::TYPE_CLUE_SEA         => '公海分配',
        self::TYPE_INTRODUCE        => '转介绍例子',
        self::TYPE_POOL             => '公海池',
    );


    const TRANS_NOT         = 0;
    const TRANS_HADTRAN     = 1;
    const TRANS_REFUDNCLASS = 2;
    const TRANS_REFUDN      = 3;

    public static $TRANS_STATUS = array(
        self::TRANS_NOT             => '未购班课',
        self::TRANS_HADTRAN         => '已购班课',
        self::TRANS_REFUDNCLASS     => '开课后才退班课',
        self::TRANS_REFUDN          => '开课前就退班课',
        self::TRANS_REFUDN          => '开课前就退班课',
    );

    const INTENTION_DEFAULT = 5;
    const INTENTION_NONE    = 0;
    const INTENTION_TOPAY   = 1;
    const INTENTION_HIGH    = 2;
    const INTENTION_GENERAL = 3;
    const INTENTION_LOW     = 4;

    // 意向
    static $INTENTION_ARRAY = [
        self::INTENTION_DEFAULT => '未接通',
        self::INTENTION_NONE    => '默认(未编辑)',
        self::INTENTION_TOPAY   => '高意向-待付款',
        self::INTENTION_HIGH    => '高意向',
        self::INTENTION_GENERAL => '一般意向',
        self::INTENTION_LOW     => '无意向',
    ];

    const STATUS_TOASSIGN     = 0;
    const STATUS_BOUND        = 1;
    const STATUS_FINISH       = 2;
    const STATUS_UNBOUND      = 3;
    const STATUS_INVALID      = 4;

    // 学生状态
    static $STATUS_ARRAY = [
        self::STATUS_TOASSIGN     => '待分配',
        self::STATUS_BOUND        => '待跟进',
        self::STATUS_FINISH       => '已完成',
        self::STATUS_UNBOUND      => '已解绑',
        self::STATUS_INVALID      => '无效',
    ];

    // 到课
    const CLASS_ATTEND_TODO   = 0;    // 课程未结束
    const CLASS_ATTEND_NO     = 10;   // 未到课，在课堂时间5min以内
    const CLASS_ATTEND_YES    = 20;   // 到课 在课堂时间5min以上
    const CLASS_ATTEND_FINISH = 30;   // 完课 在课堂时间/课程时长>75%

    static $ATTEND_ARRAY = [
        self::CLASS_ATTEND_TODO   => '课程未结束',
        self::CLASS_ATTEND_NO     => '未到课',
        self::CLASS_ATTEND_YES    => '到课',
        self::CLASS_ATTEND_FINISH => '完课'
    ];

    static $ALL_FIELDS_ARRAY = [
        'id',
        'stu_phone',
        'stu_touch_phone',
        'stu_uid',
        'type',
        'course_id',
        'sale_uid',
        'intention',
        'status',
        'trans_time',
        'trans_money',
        'trans_order',
        'trans_status',
        'grade_id',
        'create_time',
        'bind_time',
        'unbind_time',
        'expire_time',
        'confirm_time',
        'first_touch_time',
        'next_touch_time',
        'last_touch_time',
        'bit',
        'ext',
        'flow_id',
        'cvt_value',
        'cvt_time',
        'class_attend',
        'cvt_quota',
        'course_stime',
        'course_etime',
    ];

    public function __construct($db = null) {
        $this->_objStuConsultClue = new Hkzb_Dao_Gnmis_StuConsultClue();
    }


    public function addClue($arrParams)
    {
        $arrFields = [
            'stu_phone'        => (string) $arrParams['stu_phone'],
            'stu_touch_phone'  => (string) $arrParams['stu_touch_phone'],
            'stu_uid'          => (int) $arrParams['stu_uid'],
            'type'             => (int) $arrParams['type'],
            'course_id'        => (int) $arrParams['course_id'],
            'sale_uid'         => (int) $arrParams['sale_uid'],
            'intention'        => (int) $arrParams['intention'],
            'status'           => (int) $arrParams['status'],
            'grade_id'         => (int) $arrParams['grade_id'],
            'confirm_time'     => (int) $arrParams['confirm_time'],
            'bind_time'        => (int) $arrParams['bind_time'],
            'create_time'      => (int) time(),
            'flow_id'          => (int) $arrParams['flow_id'],
        ];


        $arrConds = [
            'stuUid' => (int) $arrParams['stuUid'],
            'status < 4',
        ];
        $exists = $this->_objStuConsultClue->getRecordByConds($arrConds, ['id']);
        if (!empty($exists)) {
            Bd_Log::warning('Error:[addTaskClue error], Detail:[stu exists]');
            return false;
        }
        $res = $this->_objStuConsultClue->insertRecords($arrFields);
        if (false === $res) {
            Bd_Log::warning('Error:[addSCClue error], Detail:[role]');
        }
        return $res;
    }

    /**
     * reAddTaskClue
     *
     * 二次流转添加
     * @param Array $arrParams
     * @access public
     * @return Bool
     */
    public function reAddTaskClue($arrParams)
    {
        $arrFields = [
            'stu_phone'        => (string) $arrParams['stu_phone'],
            'stu_touch_phone'  => (string) $arrParams['stu_touch_phone'],
            'stu_uid'          => (int) $arrParams['stu_uid'],
            'stu_utype'        => (int) $arrParams['stu_utype'],
            'stu_channel'      => (string) $arrParams['stu_channel'],
            'course_id'        => (int) $arrParams['course_id'],
            'sale_uid'         => (int) $arrParams['sale_uid'],
            'intention'        => (int) $arrParams['intention'],
            'status'           => (int) $arrParams['status'],
            'grade_id'         => (int) $arrParams['grade_id'],
            'sex'              => (int) $arrParams['sex'],
            'province'         => (string) $arrParams['province'],
            'city'             => (string) $arrParams['city'],
            'area'             => (string) $arrParams['area'],
            'school'           => (string) $arrParams['school'],
            'book_version'     => (string) $arrParams['book_version'],
            'stu_name'         => (string) $arrParams['stu_name'],
            'qq'               => (string) $arrParams['qq'],
            'create_time'      => (int) $arrParams['create_time'],
            'flow_id'          => (int) $arrParams['flow_id'],
            'bind_time'        => time(),
        ];

        $res = $this->_objStuConsultClue->insertRecords($arrFields);
        if (false === $res) {
            Bd_Log::warning('Error:[addTaskClue error], Detail:[arrFields]');
        }
        return $res;
    }

    /**
     * getTaskClueListByConds
     *
     * 获取列表
     * @param mixed $arrConds
     * @param mixed $arrFields
     * @param int $pn
     * @param int $rn
     * @access public
     * @return Array
     */
    public function getTaskClueListByConds($arrConds = null, $arrFields = [], $pn = 0, $rn = 0)
    {
        $arrAppends = null;

        if ($rn > 0) {
            $arrAppends = [
                'ORDER BY id desc ',
                "limit $pn,$rn",
            ];
        }
        if (empty($arrFields)) {
            $arrFields = self::$ALL_FIELDS_ARRAY;
        }
        $res = $this->_objStuConsultClue->getListByConds($arrConds,
            $arrFields,
            null,
            $arrAppends
        );

        if (false === $res) {
            Bd_Log::warning('Error:[getcluelist  error], Detail:[role]');
        }
        if ($res) {
            //格式化bitpack
            foreach ($res as $k => $v) {
                if (isset($v['bit'])) {
                    $res[$k]['arrBit'] = $this->parseBitPack($v['bit']);
                }
            }
        }
        return $res;
    }

    public function getTaskClueListByCondsAppend($arrConds, $arrFields, $arrOptions = null, $arrAppends = null)
    {
        if (empty($arrFields)) {
            $arrFields = self::$ALL_FIELDS_ARRAY;
        }
        $res = $this->_objStuConsultClue->getListByConds($arrConds,
            $arrFields,
            $arrOptions,
            $arrAppends
        );

        if (false === $res) {
            Bd_Log::warning('Error:[gettaskclue  error], Detail:[role]');
        }
        if ($res) {
            //格式化bitpack
            foreach ($res as $k => $v) {
                if (isset($v['bit'])) {
                    $res[$k]['arrBit'] = $this->parseBitPack($v['bit']);
                }
            }
        }
        return $res;
    }

    /**
     * @解析bit位
     * @param int $bit
     * @return multitype:number
     */
    public function parseBitPack($bit) {
        $bitMap = array();
        foreach (Dao_StuConsultClue::$bitPackMap as $key => $v) {
            if ($v & $bit) {
                $bitMap[$key] = 1;
            } else {
                $bitMap[$key] = 0;
            }
        }
        return $bitMap;
    }

    /**
     * @根据流转粒子id来批量线索
     * @param array $flowId 粒子流转id
     * @param unknown $arrFields
     * @return boolean| array
     */
    public function getClueByFlowIds($flowIds, $arrFields = [])
    {
        $flowIds = implode(',', $flowIds);
        $arrConds = [
            "flow_id in ({$flowIds})",
        ];
        if (empty($arrFields)) {
            $arrFields = self::$ALL_FIELDS_ARRAY;
        }
        $res = $this->_objStuConsultClue->getListByConds($arrConds, $arrFields);
        if (false === $res) {
            Bd_Log::warning('Error[getClueByFlowIds] Abstract[dbError] Detail[db error]');
            return false;
        }
        return $res;
    }

}
