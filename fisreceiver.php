<?php

@error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);

if(getenv('RUN_ENV') !== 'test')
{
    echo 'no';
    exit;
}

function mkdirs($path, $mod = 0777) {
    if (is_dir($path)) {
        return chmod($path, $mod);
    } else {
        $old = umask(0);
        if(mkdir($path, $mod, true) && is_dir($path)){
            umask($old);
            return true;
        } else {
            umask($old);
        }
    }
    return false;
}

function makeCurlFile($file){
    if (version_compare(PHP_VERSION, '5.5.0', 'ge')) {
        $mime = mime_content_type($file);
        $info = pathinfo($file);
        $name = $info['basename'];
        $output = new CURLFile($file, $mime, $name);
    } else {
        $output = "@$file";
    }
    return $output;
}

if($_POST['to']){
    $to = urldecode($_POST['to']);
    $is_save_local = TRUE;
    if(is_dir($to) || $_FILES["file"]["error"] > 0){
        header("Status: 500 Internal Server Error");
        exit();
    } else if (strpos($to, '/home/<USER>/webroot/static/') === 0){
        if (strpos($to, '.html') === FALSE) {
            $is_save_local = FALSE;
        }
        # try to proxy to router.test.suanshubang.com
        $url = "http://router.test.suanshubang.com:8080/fisreceiver.php";
        $curl = curl_init();
        $post_data = [
            'to' => $to,
            'file' => makeCurlFile($_FILES["file"]["tmp_name"]),
        ];

        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => 1,
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POST => 1,
            CURLOPT_POSTFIELDS => $post_data,
        ));
        $response = curl_exec($curl);
        if (!isset($response) || $response !== '0') {
            # 出错了， 返回1
            echo 1;
            exit();
        }

        curl_close($curl);
        if(!$is_save_local) {
            echo 0;
            exit();
        }
    }
    if($is_save_local) {
        if(file_exists($to)){
            unlink($to);
        } else {
            $dir = dirname($to);
            if(!file_exists($dir)){
                mkdirs($dir, $mode = 0755);
            }
        }
        echo move_uploaded_file($_FILES["file"]["tmp_name"], $to) ? 0 : 1;
        exit();
    }
} else {
    echo 'I\'m ready for that, you know.';
}
