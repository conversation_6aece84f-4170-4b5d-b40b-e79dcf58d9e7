# 日志级别
#  1：打印FATAL
#  2：打印FATAL和WARNING
#  4：打印FATAL、WARNING、NOTICE（线上程序正常运行时的配置）
#  8：打印FATAL、WARNING、NOTICE、TRACE（线上程序异常时使用该配置）
# 16：打印FATAL、WARNING、NOTICE、TRACE、DEBUG（测试环境配置） 
level: 4

# 是否按小时自动分日志，设置为1时，日志被打在some-app.log.2011010101
auto_rotate: 0

# 日志文件路径是否增加一个基于app名称的子目录，例如：log/some-app/some-app.log
# 该配置对于unknown-app同样生效
use_sub_dir: 1

format: %L: %t [%f:%N] errno[%E] logId[%l] uri[%U] refer[%{referer}i] cookie[%{cookie}i] %S %M

# 提供绝对路径，日志存放的根目录，只有非odp环境下有效
log_path: /mnt/homework/log/
# # 提供绝对路径，日志格式数据存放的根目录，只有非odp环境下有效
data_path: /mnt/homework/data/
# 是否开启Omp日志, 0默认值（两个日志文件都开启），1，只打印omp的new日志，2只打印老日志
is_omp: 0

[OMP]
# 配置是否在.new的NOTICE日志中打印cookie字段的信息，默认为0不打印，开启设置为1
cookie : 0
