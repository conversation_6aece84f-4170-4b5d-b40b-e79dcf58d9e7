# 需要在zb sdk里进行超时控制的上下游模块
# from：上游，目前只限于交易组的模块
# to：下游，需要调用的下游模块
[zbsdkExtraFromAndTo]
@from : trade
@from : zbcore
@from : aya
@from : listing
@from : ois
@from : one
@to : zbcore
@to : zborder
@to : zbstock
@to : newgoodsplatform
@to : zbuserpersonas
@to : archimedes 

# zb sdk中zbcore的超时重试控制
[zbcore]
#重试次数
retry : 1
#连接超时，根据后端服务状态配置，单位为毫秒。
ctimeout:200
#读超时，同上
wtimeout:1000
#写超时，同上
rtimeout:1000

# zb sdk中zborder的超时重试控制
[zborder]
#重试次数
retry : 1 
#连接超时，根据后端服务状态配置，单位为毫秒。
ctimeout:200
#读超时，同上
wtimeout:1000
#写超时，同上
rtimeout:1000

# zb sdk中zbstock的超时重试控制
[zbstock]
#重试次数
retry : 1 
#连接超时，根据后端服务状态配置，单位为毫秒。
ctimeout:200
#读超时，同上
wtimeout:1000
#写超时，同上
rtimeout:1000

# zb sdk中newgoodsplatform的超时重试控制
[newgoodsplatform]
#重试次数
retry : 1 
#连接超时，根据后端服务状态配置，单位为毫秒。
ctimeout:200
#读超时，同上
wtimeout:1000
#写超时，同上
rtimeout:1000

# zb sdk中zbuserpersonas的超时重试控制
[zbuserpersonas]
#重试次数
retry : 1 
#连接超时，根据后端服务状态配置，单位为毫秒。
ctimeout:200
#读超时，同上
wtimeout:1000
#写超时，同上
rtimeout:1000

# zb sdk中zbbiz的超时重试控制
[zbbiz]
#重试次数
retry : 1 
#连接超时，根据后端服务状态配置，单位为毫秒。
ctimeout:200
#读超时，同上
wtimeout:1000
#写超时，同上
rtimeout:1000

# zb sdk中archimedes的超时重试控制
[archimedes]
#重试次数
retry : 1
#连接超时，根据后端服务状态配置，单位为毫秒。
ctimeout:200
#读超时，同上
wtimeout:1000
#写超时，同上
rtimeout:1000

