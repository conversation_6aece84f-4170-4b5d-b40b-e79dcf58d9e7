#kpstaff 反确认发送MQ
[command_no]
type : int
must : 1
remark : 命令号
[role]
type : string
must : 1
remark : 角色
[type]
type : string
must : 1
remark : 自动反确认类型  autoAcceptFriend  |  sendPhone
[staffUid]
type : string
must : 1
remark : staffUid
[remoteId]
type : string
must : 1
remark : remoteId
[userId]
type : string
must : 1
remark : userId
[corpId]
type : string
must : 1
remark : corpId
[phone]
type : string
must : 1
remark : 手机号
[studentUid]
type : string
must : 1
remark : 反确认Uid
[externalUserid]
type : string
must : 1
remark : externalUserid
