#章节履约
[skuSourceId]
type:int
must:1
remark:商品业务线
[brandId]
type:int
must:0
remark:商品品牌
[groupKey]
type:int
must:1
remark:时序保证
[studentUid]
type : int
must : 1
remark : 学生id
[courseId]
type : int
must : 1
remark : 课程id
[lessonIds]
type:string
must:0
remark:补充履约章节
[isGift]
type : int
must : 1
remark : 是否赠送课程
[skuId]
type : int
must : 1
remark : 商品id
[changeFrom]
type:int
must:1
remark:调课旧课程id
[commitTime]
type:int
must:1
remark:提交时间
[lastFrom]
type : string
must : 0
remark : 下单打点
[flowPond]
type : string
must : 0
remark : abTest打点
[subTradeId]
type : int
must : 0
remark : 子订单id
[fromSubTradeId]
type : int
must : 0
remark : 转班来源子订单ID
[tradeId]
type : int
must : 0
remark : 订单id
[orderSource]
type : int
must : 0
remark : 订单来源
[callBackParam]
type:map
must:0
remark:供回调ofc使用
[.map]
[..ofcId]
type : int
must : 0
remark : 履约id
[..behaviorId]
type : int
must : 0
remark : 履约行为id
[originalCourseId]
type : int
must : 0
remark : 最初购买课程id
[originalPayTime]
type : int
must : 0
remark : 最初购买课程支付时间
[saleChannelId]
type : int
must : 0
remark : 渠道id
