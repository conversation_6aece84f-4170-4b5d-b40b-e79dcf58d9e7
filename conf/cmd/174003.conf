#交易3.0支付成功
[userId]
type:int
must:1
remark:用户ID
[orderId]
type:int
must:1
remark:订单ID
[groupKey]
type:int
must:1
remark:时序保证,userId
[businessType]
type:int
must:1
remark:订单业务类型
[totalAmount]
type:int
must:1
remark:订单应付
[payableAmount]
type:int
must:1
remark :订单实付
[payChannel]
type:int
must:1
remark:支付渠道
[skuIdMap]
type:string
must:1
remark:下单sku集合
[isVirtual]
type:int
must:1
remark:是否虚拟订单
[shopId]
type:int
must:1
remark:店铺ID,联运之前shopId=source
[source]
type:int
must:0
remark:业务线
[logInfo]
type:string
must:1
remark:订单标准统计信息,如lastfrom等
[commitTime]
type:int
must:1
remark:支付时间