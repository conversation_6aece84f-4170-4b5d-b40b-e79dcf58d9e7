#修改课程--miscourse
[groupKey]
type:int
must:1
remark:时序保证
[courseId]
type:int
must:1
remark:课程id
[brandId]
type:int
must:0
remark:品牌id
[courseName]
type:string
must:0
remark:课程名称
[courseType]
type:int
must:0
remark:课程类型（枚举值）
[numberPeriods]
type:string
must:0
remark:期数
[onlineStrategyId]
type:int
must:0
remark:上架策略
[entityList]
type:list
must:0
remark:教材list
[.list]
type:map
must:0
remark:教材内容
[..map]
[...entityId]
type:int
must:0
remark:实物id
[...entityPrice]
type:int
must:0
remark:实物价格（成本价）
[gradeIdList]
type:list
must:0
remark:年级id列表（枚举值数组集合）
[.list]
type:int
must:1
remark:年级
[subjectIdList]
type:list
must:0
remark:学科id列表（枚举值数组集合）
[.list]
type:int
must:1
remark:学科id
[year]
type:int
must:0
remark:年份
[learnSeason]
type:int
must:0
remark:学季 (枚举值)
[content]
type:map
must:0
remark:课程内容
[.map]
[..courseForPeople]
type:string
must:0
remark:适合人群
[..courseTarget]
type:string
must:0
remark:学习目标
[..courseSpecial]
type:string
must:0
remark:课程特色
[..courseLecture]
type:string
must:0
remark:赠送讲义
[..courseService]
type:string
must:0
remark:课程服务
[..courseLength]
type:string
must:0
remark:课程时长
[..courseAbstract]
type:string
must:0
remark:课程简介
[..courseContent]
type:string
must:0
remark:教学内容
[resource]
type:map
must:0
remark:课程相关的资源
[.map]
[..detailImgApp]
type:list
must:0
remark:课程详情页图片（APP）
[...list]
type:map
must:0
remark:课程详情页图片列表
[....map]
[.....res]
type:string
must:1
remark:资源pid或者URL
[.....desc]
type:string
must:1
remark:资源描述
[.....type]
type:int
must:1
remark:资源类型
[.....size]
type:int
must:1
remark:资源大小
[.....len]
type:int
must:1
remark:资源长度（通常用于视频）
[..coverImgApp]
type:list
must:0
remark:课程封面图片（APP）
[...list]
type:map
must:0
remark:课程封面图片列表
[....map]
[.....res]
type:string
must:1
remark:资源pid或者URL
[.....desc]
type:string
must:1
remark:资源描述
[.....type]
type:int
must:1
remark:资源类型
[.....size]
type:int
must:1
remark:资源大小
[.....len]
type:int
must:1
remark:资源长度（通常用于视频）
[..detailVideo]
type:list
must:0
remark:课程详情页视频介绍
[...list]
type:map
must:0
remark:课程详情页视频介绍列表
[....map]
[.....res]
type:string
must:1
remark:资源pid或者URL
[.....desc]
type:string
must:1
remark:资源描述
[.....type]
type:int
must:1
remark:资源类型
[.....size]
type:int
must:1
remark:资源大小
[.....len]
type:int
must:1
remark:资源长度（通常用于视频）
[..detailVideoPic]
type:list
must:0
remark:课程详情页视频封面
[...list]
type:map
must:0
remark:课程详情页视频封面列表
[....map]
[.....res]
type:string
must:1
remark:资源pid或者URL
[.....desc]
type:string
must:1
remark:资源描述
[.....type]
type:int
must:1
remark:资源类型
[.....size]
type:int
must:1
remark:资源大小
[.....len]
type:int
must:1
remark:资源长度（通常用于视频）
[registerStartTime]
type:int
must:0
remark:报名开始时间
[registerStopTime]
type:int
must:0
remark:报名结束时间
[startTime]
type:int
must:0
remark:课程开始时间
[stopTime]
type:int
must:0
remark:课程结束时间
[price]
type:int
must:0
remark:价格（单位：分）
[originPrice]
type:int
must:0
remark:原价（单位：分）
[courseFrom]
type:int
must:0
remark:课程来源
[servicesList]
type:list
must:0
remark:课程服务项列表
[.list]
type:int
must:0
remark:课程服务项列表
[studentMaxCnt]
type:int
must:0
remark:最大报名人数（满班人数）
[tags]
type:map
must:0
remark:课程标签
[.map]
[..sale]
type:list
must:0
remark: 售卖或者打折标签列表
[...list]
type:map
must:0
remark:售卖或者打折标签
[....map]
[.....title]
type:string
must:1
remark:标签标题
[.....desc]
type:string
must:1
remark:标签自定义描述
[..feature]
type:list
must:0
remark: 课程特色标签列表
[...list]
type:map
must:0
remark:课程特色标签
[....map]
[.....title]
type:string
must:1
remark:标签标题
[.....desc]
type:string
must:1
remark:标签自定义描述
[..basic]
type:list
must:0
remark: 课程基础标签列表
[...list]
type:map
must:0
remark:课程基础标签
[....map]
[.....title]
type:string
must:1
remark:标签标题
[.....desc]
type:string
must:1
remark:标签自定义描述
[..module]
type:list
must:0
remark: 课程模块标签列表
[...list]
type:map
must:0
remark:课程模块标签
[....map]
[.....title]
type:string
must:1
remark:标签标题
[.....desc]
type:string
must:1
remark:标签自定义描述
[isShow]
type:int
must:0
remark:课程是否展示
[isInner]
type:int
must:0
remark:是否是内部课
[formatShow]
type:int
must:0
remark:是否格式化展示
[isVipClass]
type:int
must:0
remark:是否是VIP班
[lectureSendTime]
type:int
must:0
remark:教材寄送时间
[pullNewDuty]
type:list
must:0
remark:拉新属性
[.list]
type:int
must:0
remark:拉新属性id
[isClosing]
type:int
must:0
remark:预关班状态
[materialList]
type:string
must:0
remark:教辅数据