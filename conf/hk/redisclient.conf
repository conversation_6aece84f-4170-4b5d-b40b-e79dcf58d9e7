[services]
  # 通用业务集群，严禁随意调用
  [.push]
  instance : redis-push
  prefix   : push
  timeout  : 50
  conn_retry : 1
  [.badge]
  instance : redis-push
  prefix   : badge
  timeout  : 100
  conn_retry : 1
  [.sysmsg]
  instance : redis-push
  prefix   : sysmsg
  timeout  : 100
  conn_retry : 1
  [.antispam]
  instance : redis-antispam
  prefix   : antispam
  timeout  : 100
  conn_retry : 1
  [.session]
  instance : redis-session
  timeout  : 100
  conn_retry : 1
  [.common]
  instance : redis-common
  timeout  : 100
  conn_retry : 1
  [.userprofile]
  instance : redis-userprofile
  timeout  : 50
  conn_retry : 1
  [.ucloud]
  instance : redis-ucloud
  timeout  : 30
  conn_retry : 1
  [.adplatform]
  instance : redis-adplatform
  timeout  : 30
  conn_retry : 1
  [.dataprotect]
  instance : redis-dataprotect
  timeout  : 30
  conn_retry : 1


  # 以下为业务集群，请使用各自业务的集群，严禁跨业务使用
  # 平台业务
  [.banner]
  instance : redis-banner
  prefix   : banner
  timeout  : 100
  conn_retry : 1
  [.search]
  instance : redis-search
  prefix   : search
  timeout  : 100
  conn_retry : 1
  [.vip]
  instance : redis-vip
  prefix   : vip
  timeout  : 50
  conn_retry : 1
  [.plat]
  instance : redis-plat
  timeout  : 100
  conn_retry : 1
  [.ugc]
  instance : redis-ugc
  timeout  : 100
  conn_retry : 1
  [.practice]
  instance : redis-practice
  timeout  : 100
  conn_retry : 1
  # 缓存集群
  [.searchrecord]
  instance : redis-searchrecord
  timeout  : 30
  conn_retry : 1
  [.question]
  instance : redis-question
  timeout  : 30
  conn_retry : 1
  [.homework]
  instance : redis-homework
  timeout  : 30
  conn_retry : 1
  [.viplib]
  instance : redis-viplib
  timeout  : 100
  conn_retry : 1
  [.dxucloud]
  instance : redis-dxucloud
  timeout  : 100
  conn_retry : 1
  [.impusergrade]
  instance : redis-usergrade
  timeout  : 50
  conn_retry : 1

  # 拉新
  [.offline]
  instance : redis-offline
  timeout  : 100
  conn_retry : 1

  # 投放
  [.toufang]
  instance : redis-toufang
  timeout  : 100
  conn_retry : 1

  # 题库
  [.tiku]
  instance : redis-tiku
  timeout  : 100
  conn_retry : 1

  # 检索业务
  [.searchjudge]
  instance : redis-searchjudge
  timeout  : 100
  conn_retry : 1

  # 家长端
  [.parent]
  instance : redis-parent
  timeout  : 100
  conn_retry : 1
  [.kousuan]
  instance : redis-kousuan
  timeout  : 100
  conn_retry : 1

  # 答疑业务
  [.dayi]
  instance : redis-dayi
  timeout  : 100
  conn_retry : 1

  # saas
  [.spam]
  instance : redis-spam
  timeout  : 100
  conn_retry : 1
  [.saascommon]
  instance : redis-saascommon
  timeout  : 100
  conn_retry : 1
  [.saascore]
  instance : redis-saascore
  timeout  : 100
  conn_retry : 1

  # 大数据
  [.bw]
  instance : redis-bw
  timeout  : 100
  conn_retry : 1

  # 直播业务
  [.userpersonas]
  instance : redis-userpersonas
  timeout  : 100
  conn_retry : 1
  read_timeout: 100
  [.zhibo]
  instance : redis-zhibo
  timeout  : 100
  conn_retry : 1
  [.zbsell]
  instance : redis-zbsell
  timeout  : 100
  conn_retry : 1
  read_timeout: 50
  [.zbsellfront]
  instance : redis-zbsellfront
  timeout  : 100
  conn_retry : 1
  read_timeout: 200
  [.zbcourse]
  instance : redis-zbcourse
  timeout  : 100
  conn_retry : 1
  [.zbexam]
  instance : redis-zbcss
  timeout  : 100
  conn_retry : 1
  [.zblec]
  instance : redis-dianxiao
  timeout  : 100
  conn_retry : 1
  [.zbjx]
  instance : redis-zbjx
  timeout  : 100
  conn_retry : 1
  [.bbactive]
  instance : redis-bbactive
  timeout  : 100
  conn_retry : 1
  [.huanxiong]
  instance : redis-huanxiong
  timeout  : 100
  conn_retry : 1
  [.bzrfd]
  instance : redis-bzrfd
  timeout  : 100
  conn_retry : 1
  [.userbuy]
  instance : redis-userbuy
  timeout  : 100
  conn_retry : 1
  read_timeout: 50
  [.wechat]
  instance : redis-wechat
  timeout  : 100
  conn_retry : 1
  [.kidtutor]
  instance : redis-kidtutor
  timeout  : 100
  conn_retry : 1
  [.writing]
  instance : redis-writing
  timeout  : 100
  conn_retry : 1
  [.tcs]
  instance : redis-tcs
  timeout  : 100
  conn_retry : 1
  read_timeout: 50
  [.hdjw]
  instance : redis-hdjw
  timeout  : 100
  conn_retry : 1
  [.business]
  instance : redis-business
  timeout  : 100
  conn_retry : 1
  [.fwyy]
  instance : redis-fwyy
  timeout  : 100
  conn_retry : 1
  [.spcache]
  instance : redis-spcache
  timeout  : 50
  conn_retry : 1
  read_timeout: 50
  [.zbnmq]
  instance : redis-zbnmq
  timeout  : 50
  conn_retry : 1
  read_timeout: 300
  [.userrole]
  instance : redis-userrole
  timeout  : 50
  conn_retry : 1
  [.dascache]
  instance : redis-dascache
  timeout  : 50
  conn_retry : 1
  read_timeout: 50
  [.s3]
  instance : redis-s3
  timeout  : 50
  conn_retry : 1
  [.dystatistics]
  instance : redis-dystatistics
  timeout  : 50
  conn_retry : 1
  [.toufangrta]
  instance : redis-toufangrta
  timeout  : 50
  conn_retry : 1
  [.qkcl]
  instance : redis-qkcl
  timeout  : 50
  conn_retry : 1

  # stored集群
  [.spdata]
  instance : redis-spdata
  timeout  : 100
  conn_retry : 1
  [.spexam]
  instance : redis-spexam
  timeout  : 100
  conn_retry : 1
  [.spfront]
  instance : redis-spfront
  timeout  : 100
  conn_retry : 1
  [.saassns]
  instance : redis-saassns
  timeout  : 100
  conn_retry : 1
  [.viprights]
  instance : redis-viprights
  timeout  : 100
  conn_retry : 1
  [.toufangdata]
  instance : redis-toufangdata
  timeout  : 100
  conn_retry : 1
  [.lpc]
  instance : redis-lpc
  timeout  : 100
  conn_retry : 1
  [.zbapp]
  instance : redis-zbapp
  timeout  : 100
  conn_retry : 1
  [.billing]
  instance : redis-billing
  timeout  : 100
  conn_retry : 1
  read_timeout: 50
  [.archimedes]
  instance : redis-archimedes
  timeout  : 100
  conn_retry : 1
  [.goodscache]
  instance : redis-goodscache
  timeout  : 100
  conn_retry : 1
  read_timeout: 200
  [.wms]
  instance : redis-wms
  timeout  : 100
  conn_retry : 1
  read_timeout: 500
  [.biz]
  instance : redis-biz
  timeout  : 100
  conn_retry : 1
  read_timeout: 200
  [.kunpeng]
  instance : redis-kunpeng
  timeout  : 100
  conn_retry : 1
  read_timeout: 200
  [.fengniao]
  instance : redis-fengniao
  timeout  : 100
  conn_retry : 1
  [.vipqatest]
  instance : redis-vip
  prefix   : shadow
  timeout  : 50
  conn_retry : 1
  [.wenda]
  instance : redis-wenda
  timeout  : 100
  conn_retry : 1
  [.aied]
  instance : redis-aied
  timeout  : 100
  conn_retry : 1
  [.toufangcpa]
  instance : codis-toufangcpa
  timeout  : 50
  conn_retry : 1
  [.smallflow]
  instance : redis-smallflow
  timeout  : 50
  conn_retry : 1
  [.commonrpc]
  instance : redis-commonrpc
  timeout  : 50
  conn_retry : 1
  read_timeout: 300
  [.implec]
  instance : codis-implec
  timeout  : 50
  conn_retry : 1
  [.dataimp]
  instance : redis-dataimp
  timeout  : 100
  conn_retry : 1
  [.mpacls]
  instance : redis-mpacls
  timeout : 100
  conn_retry : 1
  [.wendaask]
  instance : redis-wendaask
  timeout : 50
  conn_retry : 1
  read_timeout: 300
  prefix : wendaask
  [.seven]
  instance : redis-seven
  timeout  : 100
  conn_retry : 1
  [.plato]
  instance : redis-plato
  timeout : 100
  conn_retry : 1
  [.hkdc]
  instance : redis-hkdc
  timeout : 30
  conn_retry : 1
  [.pigai]
  instance : redis-pigai
  timeout  : 50
  conn_retry : 1
  [.adult]
  instance : redis-adult
  timeout  : 100
  conn_retry : 1
  [.lec]
  instance : mixcodis-lec
  timeout : 50
  conn_retry : 1
  [.ipstest]
  instance : redis-ipstest
  timeout : 100
  conn_retry : 1
  [.upsgt]
  instance : redis-upsgt
  timeout : 100
  conn_retry : 1
  [.ipstestcommon]
  instance : redis-ipstestcommon
  timeout : 100
  conn_retry : 1
  [.actplat]
  instance : redis-actplat
  timeout : 100
  conn_retry : 1
  [.storedkidtutor]
  instance : redis-storedkidtutor
  timeout : 1000
  [.duxuesc]
  instance : redis-duxuesc
  timeout : 100
  conn_retry : 1
  [.sanxia]
  instance : redis-sanxia
  timeout : 100
  conn_retry : 1
  [.spinfra]
  instance : redis-spinfra
  timeout : 50
  conn_retry : 1
  read_timeout: 50
  [.studyroom]
  instance : redis-studyroom
  timeout : 100
  conn_retry : 1
  [.ipsupply]
  instance : redis-ipsupply
  timeout : 100
  conn_retry : 1
  [.wechatcluster]
  instance : redis-wechatcluster
  timeout  : 50
  conn_retry : 1
  [.wechatcluster2]
  instance : redis-wechatcluster2
  timeout  : 50
  conn_retry : 1
  [.wechatbak]
  instance : redis-wechatbak
  timeout  : 50
  conn_retry : 1
  [.muse]
  instance : redis-muse
  timeout  : 100
  conn_retry : 1
  [.hkbucloud]
  instance : stored-hkbucloud
  timeout : 100
  conn_retry : 1
  [.address]
  instance : redis-address
  timeout  : 50
  conn_retry : 1
  [.payment]
  instance : redis-payment
  timeout  : 100
  conn_retry : 1
  [.paycommon]
  instance : redis-paycommon
  timeout  : 100
  conn_retry : 1
  read_timeout: 50
  [.abtest]
  instance : redis-abtest
  timeout  : 100
  conn_retry : 1
  [.viplib2]
  instance : redis-viplib2
  timeout  : 100
  conn_retry : 1
  [.zbcoupon]
  instance : redis-zbcoupon
  timeout  : 100
  conn_retry : 1
  read_timeout: 50
  [.lpclaxinmis]
  instance : redis-lpclaxinmis
  timeout  : 100
  conn_retry : 1
  [.peoplepack]
  instance : redis-peoplepack
  timeout  : 100
  conn_retry : 1