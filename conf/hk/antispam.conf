[common]
# rand过期时间(秒)
# 默认配置90天
expire : 7776000

# 不需要反作弊接口
@except : /user/antispam
@except : /ajax/picture
@except : /ajax/video
@except : /activity/cardhome
@except : /question/webview
@except : /question/relatedview
@except : /stat/appconfigdelay
@except : /activity/activityinfo
@except : /patch/hotfix
@except : /stat/checkappinfo
@except : /stat/checkappupdate
@except : /stat/updatereport
# 迁移到pluto模块后的不需校验签名的接口
@except : /app/antispam
@except : /app/hotfix
@except : /publish/checkappupdate
@except : /publish/updatereport
# 新push接口
@except : /user/tokenbind
@except : /user/tokenunbind

#允许的版本hash
#android-keystore-online
@hash : 0f3c509eef614432e414ce9d37f00c80
#android-keystore-debug
@hash : 00732d9383e592cbe3e69abe2850a887

# 是否开启反作弊检测
[.switch]
ios     : 1
android : 1
other   : 1

[.rejectVersion]
switch : 1
minAndroidVersion : 25
minIosVersion : 28
@rejectUri : /search/submit/textsearch
@rejectUri : /search/submit/voicesearch
@rejectUri : /search/submit/picsearch
@rejectUri : /search/browse/searchresult
@rejectUri : /search/submit/picask
@rejectUri : /search/submit/synpicask
@rejectUri : /napi/question/list
@rejectUri : /napi/question/list
@rejectUri : /napi/question/view
@rejectUri : /napi/question/talk
@rejectUri : /napi/question/picask
@rejectUri : /napi/question/voiceask
@rejectUri : /napi/question/textask
@rejectUri : /napi/question/textaskresult
@rejectUri : /napi/question/picaskresult
@rejectUri : /napi/question/picasksearch
@rejectUri : /napi/question/picaskevaluate
@rejectUri : /napi/question/picasksyn
@rejectUri : /napi/question/webview


[.timeControl]
switch  : 1
maxTime : 60

[keystore]
#上传使用的密钥
key1 : @fG2SuLA
#下载使用的密钥(前部分拼接rand)
key2 : #G4
#前缀
key3 : 8&%d*
