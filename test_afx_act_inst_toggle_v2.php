<?php
/**
 * 测试脚本：验证 AFXActInstToggle V2新方法
 * 
 * 使用说明：
 * 1. 确保新表 tblAFXInstActToggleConfig 已创建
 * 2. 确保新的DAO类 Qdlib_Dao_Hetu_AFXInstActToggleConfig 已部署
 * 3. 插入一些测试数据
 * 4. 运行此脚本验证V2方法功能
 */

// 引入必要的类文件（根据实际项目结构调整路径）
require_once 'path/to/your/autoloader.php'; // 替换为实际的自动加载文件

class AFXActInstToggleV2Test
{
    private $afxActInstToggle;
    
    public function __construct()
    {
        $this->afxActInstToggle = new Qdlib_Ds_Hetu_AFXActInstToggle();
    }
    
    /**
     * 测试原有方法是否正常工作
     */
    public function testOriginalMethod()
    {
        echo "=== 测试原有的 getInstState 方法（确保未被破坏）===\n";
        
        $actId = 1001;
        $instId = 2001;
        
        echo "测试原有方法：查询机构 {$instId} 在活动 {$actId} 中的状态\n";
        try {
            $state = $this->afxActInstToggle->getInstState($actId, $instId, false);
            echo "✓ 原有方法正常工作，返回状态：{$state}\n";
        } catch (Exception $e) {
            echo "✗ 原有方法出错：" . $e->getMessage() . "\n";
        }
        echo "\n";
    }

    /**
     * 测试新的V2版本获取机构状态功能
     */
    public function testGetInstStateV2()
    {
        echo "=== 测试新的 getInstStateV2 方法（使用新表逻辑）===\n";
        
        // 测试用例1：查询存在的配置记录
        $actId = 1001;
        $instId = 2001;
        
        echo "测试用例1：查询机构 {$instId} 在活动 {$actId} 中的状态\n";
        try {
            $state = $this->afxActInstToggle->getInstStateV2($actId, $instId, false); // 不使用缓存
            if ($state === false) {
                echo "✗ 查询失败\n";
            } else {
                echo "✓ V2方法返回状态：{$state} (" . ($state == 1 ? '开启' : '关停') . ")\n";
            }
        } catch (Exception $e) {
            echo "✗ V2方法出错：" . $e->getMessage() . "\n";
        }
        echo "\n";
        
        // 测试用例2：查询不存在的配置记录（默认开启状态）
        $actId2 = 9999;
        $instId2 = 9999;
        
        echo "测试用例2：查询不存在的机构 {$instId2} 在活动 {$actId2} 中的状态\n";
        try {
            $state2 = $this->afxActInstToggle->getInstStateV2($actId2, $instId2, false);
            if ($state2 === false) {
                echo "✗ 查询失败\n";
            } else {
                echo "✓ V2方法返回状态：{$state2} (" . ($state2 == 1 ? '开启（默认）' : '关停') . ")\n";
            }
        } catch (Exception $e) {
            echo "✗ V2方法出错：" . $e->getMessage() . "\n";
        }
        echo "\n";
        
        // 测试用例3：测试缓存功能
        echo "测试用例3：测试V2方法的缓存功能\n";
        try {
            $startTime = microtime(true);
            $state3 = $this->afxActInstToggle->getInstStateV2($actId, $instId, true); // 使用缓存
            $firstCallTime = microtime(true) - $startTime;
            
            $startTime = microtime(true);
            $state4 = $this->afxActInstToggle->getInstStateV2($actId, $instId, true); // 第二次调用，应该命中缓存
            $secondCallTime = microtime(true) - $startTime;
            
            echo "第一次调用耗时：{$firstCallTime}秒，状态：{$state3}\n";
            echo "第二次调用耗时：{$secondCallTime}秒，状态：{$state4}\n";
            echo "缓存是否生效：" . ($secondCallTime < $firstCallTime ? '✓ 是' : '✗ 否') . "\n";
        } catch (Exception $e) {
            echo "✗ 缓存测试出错：" . $e->getMessage() . "\n";
        }
        echo "\n";
    }

    /**
     * 测试批量获取机构状态功能
     */
    public function testGetBatchInstStateV2()
    {
        echo "=== 测试批量获取机构状态 getBatchInstStateV2 方法 ===\n";
        
        $actId = 1001;
        $instIds = [2001, 2002, 2003, 9999]; // 包含存在和不存在的机构ID
        
        echo "批量查询机构状态，活动ID：{$actId}，机构IDs：" . implode(',', $instIds) . "\n";
        try {
            $states = $this->afxActInstToggle->getBatchInstStateV2($actId, $instIds, false);
            
            if (empty($states)) {
                echo "✗ 批量查询返回空结果\n";
            } else {
                echo "✓ 批量查询成功：\n";
                foreach ($states as $instId => $state) {
                    echo "  机构 {$instId}：状态 {$state} (" . ($state == 1 ? '开启' : '关停') . ")\n";
                }
            }
        } catch (Exception $e) {
            echo "✗ 批量查询出错：" . $e->getMessage() . "\n";
        }
        echo "\n";
    }

    /**
     * 测试V2版本的缓存删除功能
     */
    public function testDelInstStateCacheV2()
    {
        echo "=== 测试 delInstStateCacheV2 方法 ===\n";
        
        $actId = 1001;
        $instIds = [2001, 2002, 2003];
        
        echo "先设置缓存...\n";
        // 先设置缓存
        foreach ($instIds as $instId) {
            try {
                $this->afxActInstToggle->getInstStateV2($actId, $instId, true);
                echo "  机构 {$instId} 缓存已设置\n";
            } catch (Exception $e) {
                echo "  机构 {$instId} 缓存设置失败：" . $e->getMessage() . "\n";
            }
        }
        
        echo "删除缓存...\n";
        // 删除缓存
        try {
            $result = $this->afxActInstToggle->delInstStateCacheV2($actId, $instIds);
            echo "✓ 删除缓存结果：" . ($result ? '成功' : '失败') . "\n";
        } catch (Exception $e) {
            echo "✗ 删除缓存出错：" . $e->getMessage() . "\n";
        }
        echo "\n";
    }

    /**
     * 创建测试数据的SQL语句
     */
    public function getTestDataSQL()
    {
        echo "=== 测试数据SQL ===\n";
        echo "请在数据库中执行以下SQL来创建测试数据：\n\n";
        
        $sql = "
-- 创建新表（如果还没创建）
CREATE TABLE IF NOT EXISTS `tblAFXInstActToggleConfig` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键任务id',
  `inst_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '机构id',
  `act_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '活动id',
  `status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '1 开启, 2 关停',
  `operator` varchar(64) NOT NULL DEFAULT '' COMMENT '操作人员用户名',
  `is_new` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否新配置',
  `deleted` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_inst_act_id` (`inst_id`,`act_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销机构下活动直接关停配置记录表';

-- 插入测试数据
INSERT INTO tblAFXInstActToggleConfig (inst_id, act_id, status, operator, is_new, deleted, create_time, update_time) VALUES
(2001, 1001, 2, 'test_user', 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2002, 1001, 1, 'test_user', 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2003, 1002, 2, 'test_user', 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
ON DUPLICATE KEY UPDATE 
  status = VALUES(status),
  update_time = UNIX_TIMESTAMP();

-- 查询测试数据
SELECT * FROM tblAFXInstActToggleConfig WHERE deleted = 0;
        ";
        
        echo $sql . "\n";
    }
    
    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        echo "开始测试 AFXActInstToggle V2新方法...\n\n";
        
        $this->getTestDataSQL();
        $this->testOriginalMethod();
        $this->testGetInstStateV2();
        $this->testGetBatchInstStateV2();
        $this->testDelInstStateCacheV2();
        
        echo "测试完成！\n";
        echo "\n=== 使用建议 ===\n";
        echo "1. 原有的 getInstState() 方法保持不变，继续使用旧表逻辑\n";
        echo "2. 新的 getInstStateV2() 方法使用新表逻辑，可以逐步迁移\n";
        echo "3. 新增的 getBatchInstStateV2() 方法支持批量查询，提高性能\n";
        echo "4. V2方法使用独立的缓存键，不会与原有缓存冲突\n";
    }
}

// 运行测试
if (php_sapi_name() === 'cli') {
    $test = new AFXActInstToggleV2Test();
    $test->runAllTests();
} else {
    echo "请在命令行环境下运行此测试脚本\n";
}
