<?php
/**
 * Created by Growth AutoCode.
 * User: <EMAIL>
 * Date: 2021/05/25
 * Time: 11:38
 */
class Service_Page_OrderReport_V1_DownloadFile
{
    protected $objOrderReportTask;
    private $csv;
    const FILE_URL_PREFIX = 'https://zyb-toufang-1253445850.cos.ap-beijing.myqcloud.com';

    public function  __construct()
    {
        $this->objOrderReportTask = new Service_Data_AFXOrderReportTask();
        $this->csv = new Oplib_Util_Actplat_Csv();
    }

    public function  execute($arrInput)
    {
        $type = (int)$arrInput['type'] ? (int)$arrInput['type'] : 0;
        $taskID = (int)$arrInput['taskId'] ? (int)$arrInput['taskId'] : 0;
        $uid = $arrInput['uid'];
        if ($type == 1) {
            //下载低价课订单模板
            $this->csv->setCsvHeader($this->getDJKTemplateHeader());
            $this->downloadFile();
            exit(0);
        }elseif ($type == 2) {
            //下载正价课订单模板
            $this->csv->setCsvHeader($this->getZJKTemplateHeader());
            $this->downloadFile();
            exit(0);
        }
        else {
            $arrOutput = ["url" => ""];
            //检索任务id对应的fail_fileid;
            $info = $this->objOrderReportTask->getAFXOrderReportTaskInfoById($taskID);
            if (empty($info)) {
                throw new Afxmis_Exception(Afxmis_ExceptionCodes::PARAM_ERROR, "不存在该任务id");
            }
            if ($uid != $info["uid"]) {
                throw new Afxmis_Exception(Afxmis_ExceptionCodes::DATA_MATCHING_ERROR, "该任务不属于该代理商");
            }

            $failFileID = $info["failFileId"];
            if (empty($failFileID)) {
                throw new Afxmis_Exception(Afxmis_ExceptionCodes::DATA_MATCHING_ERROR, "不存在校验失败文件id");
            }
            //http 替换https
            $urlArr = parse_url($failFileID);
            $arrOutput["url"] = self::FILE_URL_PREFIX.$urlArr['path'];
            return $arrOutput;
        }
    }

    private function downloadFile()
    {
        $file = $this->csv->getFullPath();

        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream,  charset=utf-8');
        header('Content-Transfer-Encoding: binary');
        header('Accept-Ranges: bytes');
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');
        header('Content-Length: ' . filesize($file));
        header('Content-Disposition: attachment; filename=' . basename($file));

        ob_end_clean();
        readfile($file);
    }

    private function getDJKTemplateHeader()
    {
        $header = [];
        $header[] = "第三方订单ID";
        $header[] = "开通作业帮手机号";
        $header[] = "收件人姓名";
        $header[] = "收件人手机号";
        $header[] = "收件人地址（省市区镇详细地址）";
        $header[] = "用户购买课程年级";
        $header[] = "用户实付金额";
        return $header;
    }

    private function getZJKTemplateHeader()
    {
        $header = [];
        $header[] = "第三方订单ID";
        $header[] = "开通作业帮手机号";
        $header[] = "收件人姓名";
        $header[] = "收件人手机号";
        $header[] = "收件人地址（省市区镇详细地址）";
        $header[] = "用户购买课程年级";
        $header[] = "用户实付金额";
        $header[] = "用户购买课程学季";
        $header[] = "用户购买课程学科";
        $header[] = "用户购买课程班型";
        return $header;
    }

}


