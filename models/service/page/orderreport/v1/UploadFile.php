<?php
/**
 * Created by Growth AutoCode.
 * User: <EMAIL>
 * Date: 2021/05/25
 * Time: 11:38
 */
class Service_Page_OrderReport_V1_UploadFile
{
    const FILE_URL_PREFIX = 'https://zyb-toufang-1253445850.cos.ap-beijing.myqcloud.com';
    protected $objOrderReportTask;

    public function  __construct()
    {
        $this->objOrderReportTask = new Service_Data_AFXOrderReportTask();
        $this->objAct = new Service_Data_AFXAct();
        $this->objUser = new Service_Data_AFXUser();
        $this->objAfxOrgan = new Service_Data_AFXOrgan();
    }

    public function  execute($arrInput)
    {
        //写入任务
        $actId = (int)$arrInput['actId'] ? (int)$arrInput['actId'] : 0;
        $channelLabel = (int)$arrInput['channelLabel'] ? (int)$arrInput['channelLabel'] : 0;
        $commodityType = (int)$arrInput['commodityType'] ? (int)$arrInput['commodityType'] : 0;
        if ($actId == 0) {
            throw new Afxmis_Exception(Afxmis_ExceptionCodes::PARAM_ERROR, "活动为空");
        }
        if ($channelLabel == 0 || $commodityType == 0) {
            throw new Afxmis_Exception(Afxmis_ExceptionCodes::PARAM_ERROR, "系统更新，请刷新链接后重试");
        }
        //检索该活动id是否存在并且已开始
        $arrConds['id'] = $actId;
        $arrConds[] = 'start_time <='.time();
        $arrConds[] = 'end_time >'.time();
        $total = $this->objAct->getCntByConds($arrConds);
        if (empty($total)) {
            throw new Afxmis_Exception(Afxmis_ExceptionCodes::PARAM_ERROR, "当前不是已开始的活动");
        }
        $arrConds = [];
        //判断用户目前是否还有正在上传的文件
        $arrConds[] = sprintf("status = %d or status = %d", Afxmis_Const::ORDER_REPORT_INIT, Afxmis_Const::ORDER_REPORT_REVIEW);
        $arrConds['uid'] = $arrInput['uid'];

        $total = $this->objOrderReportTask->getAFXOrderReportTaskTotal($arrConds);
        //TODO:暂时屏蔽
//        if (! empty($total)) {
//            throw new Afxmis_Exception(Afxmis_ExceptionCodes::DATA_MATCHING_ERROR, "当前代理商有正在进行的任务");
//        }

        $line = $this->checkFile();
        $url = $this->uploadFile();

        if ($line == 0) {
            throw new Afxmis_Exception(Afxmis_ExceptionCodes::PARAM_ERROR, "导入的文件内容为空");
        }

        if (empty($url)) {
            throw new Afxmis_Exception(Afxmis_ExceptionCodes::FILE_UNKNOWN_UPLOAD_ERROR, "文件上传cos服务失败");
        }

        //获取代理商对应的应用方
        $userInfo = $this->objUser->getUserInfo($arrInput['uid']);
        $organInfo = $this->getOrganInfoByLv1Agent($arrInput['uid']);
        //插入任务
        $arrParams = [
            'uid'           => $arrInput['uid'],
            'appId'         => $userInfo['appId'],
            'cadre'         => $userInfo['cadre'],
            'organId'       => $organInfo['id'],
            'actId'         => $arrInput['actId'],
            'channelLabel'  => $arrInput['channelLabel'],
            'commodityType' => $arrInput['commodityType'],
            'status'        => Afxmis_Const::ORDER_REPORT_INIT,
            'fileId'        => $url,
            'totalLines'    => $line,
            'createTime'    => time(),
            'updateTime'    => time(),
        ];
        $result = $this->objOrderReportTask->addAFXOrderReportTask($arrParams);
        if ($result === false) {
            Oplib_Util_Log::warning('Service_Data', 'AFXOrderReportTask', 'addAFXOrderReportTask', '数据库插入失败', json_encode($arrParams));
            return false;
        }
        return ['taskId' => $result];
    }


    private function checkFile()
    {
        if (empty($_FILES)) {
            throw new Afxmis_Exception(Afxmis_ExceptionCodes::PARAM_ERROR, "未包含上传文件");
        }

        if(empty($_FILES['file'])) {
            throw new Afxmis_Exception(Afxmis_ExceptionCodes::PARAM_ERROR, "未收到指定文件名");
        }

        $fileFormat = pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION);

        if (!in_array($fileFormat, array("csv"))) {
            throw new Afxmis_Exception(Afxmis_ExceptionCodes::PARAM_ERROR, "上传文件格式只支持csv");
        }

        $tmpFile = $_FILES['file']['tmp_name'];
        $fp = fopen($tmpFile, "r");

        if (empty($fp)) {
            throw new Afxmis_Exception(Afxmis_ExceptionCodes::PARAM_ERROR, "文件读取失败");
        }

        $fileLine = 0;
        while ($line = fgets($fp)) {
            $line = trim($line);
            if (empty($line)) continue;
            if (!strstr($line,',')) {
                throw new Afxmis_Exception(Afxmis_ExceptionCodes::PARAM_ERROR, "上传文件格式只支持csv");
            }
            $fileLine++;
        }

        fclose($fp);

        if($fileLine - 1 <= 0){
            throw new Afxmis_Exception(Afxmis_ExceptionCodes::PARAM_ERROR, "文件内容为空");
        }
        return $fileLine - 1;
    }

    private function uploadFile()
    {
        $path = $_FILES['file']['tmp_name'];
        $ret =    Hk_Service_Tcos::uploadLocalFile("toufang", $path, 'csv');

        if(empty($ret)){
            Qdlib_Util_Log::fatal(APP, __CLASS__, __FUNCTION__, '文件上传cos服务失败', json_encode(['path' => $path], JSON_UNESCAPED_UNICODE));
            throw new Afxmis_Exception(Afxmis_ExceptionCodes::PARAM_ERROR, "文件上传cos服务失败");
        }

        $urlArr = parse_url($ret);

        return self::FILE_URL_PREFIX.$urlArr['path'];
    }

    //获取一级代理商对应的代理商机构信息
    private function getOrganInfoByLv1Agent($uid)
    {
        $userInfo = $this->objUser->getUserInfo($uid);
        $roleId = $userInfo['roleId'];
        if ($roleId == Afxmis_User_Authority::ROLE_LV1_AGENT) {
            //一级代理商
            $organId = $userInfo['instId'];
        }else {
            $agentPIds = explode('|', trim($userInfo['relation'], '|'));
            // 查询代理商信息,角色必须是代理商角色
            $userInfo = $this->objUser->getInfoById($agentPIds[0]);
            $organId = $userInfo['instId'];
        }
        return $this->objAfxOrgan->getInfoById($organId);
    }
}