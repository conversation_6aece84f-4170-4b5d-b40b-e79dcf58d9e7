<?php
/**************************************************************************
 *
 * Copyright (c) 2020 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   ActGenLink.php
 * <AUTHOR>
 * @date   2020/4/26 14:46
 * @brief
 **/
class Service_Page_Admin_V1_ActGenLink
{
    // 分销链接旧域名列表
    const OLD_HOSTS = ['www.zuoyebang.com', 'zouyebang.com'];
    // 分销链接新域名
    const NEW_HOST = 'tf.zuoyebang.com';

    private $objDsApp;
    private $objDsAct;
    private $objDsUser;
    private $objDsSCode;
    private $objDsUcloud;

    public function __construct()
    {
        $this->objDsApp    = new Service_Data_AFXApp();
        $this->objDsAct    = new Service_Data_AFXAct();
        $this->objDsUser   = new Service_Data_AFXUser();
        $this->objDsSCode  = new Service_Data_AFXSCode();
        $this->objDsUcloud = new Hk_Ds_User_Ucloud();
    }

    public function execute($arrInput)
    {
        try {
            return $this->_execute($arrInput);
        } catch (Exception $e) {
            Qdlib_Util_Log::warning(APP, __CLASS__, __FUNCTION__, '生成分销活动短链失败:' . $e->getMessage(), json_encode($arrInput, JSON_UNESCAPED_UNICODE));
            throw $e;
        }
    }

    public function _execute($arrInput)
    {
        $arrOutput = [
            'type'  => 0,
            'isCSV' => 0,
            'list'  => [],
        ];

        $ret = $this->checkAndFormatParams($arrInput);
        $cadre = $ret['cadre'];
        $appId = $ret['appId'];
        $actId = $ret['actId'];
        $type  = $ret['type'];
        $isCSV = $ret['isCSV'];
        $uid   = $ret['uid'];
        $ids   = $ret['ids'];
        $instId = $ret['instId'];
        $roleId = $ret['roleId'];
        $isExportExcel   = $ret['isExportExcel']; // 是否导出为excel文件
        $channelLabel   = $ret['channelLabel'];


        $arrOutput['type']  = $type;
        $arrOutput['isCSV'] = $isCSV;

        $actInfo = $this->objDsAct->getInfoById($actId);
        if ($actInfo === false) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::SELECT_ERROR);
        } elseif ($actInfo['appId'] != $appId) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'actId不存在', $actInfo);
        }

        // 获取售卖渠道ID
        //$saleChannelId = $this->objDsApp->getSaleChannelIdById($appId);
        $saleChannelId = $this->objDsAct->getSaleChannelIdById($actId);
        if (false === $saleChannelId) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::SELECT_ERROR);
        } elseif (is_null($saleChannelId) || !isset(Afxmis_Const::$saleChannelMap[$saleChannelId])) {
            $errMsg = is_null($saleChannelId) ? 'saleChannelId未配置' : 'saleChannelId不支持';
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, $errMsg);
        }

        $userMap = []; // 存储当前用户或者下级用户的信息:(k=uid,v:nickname),查询分销码使用
        // 获取用户信息
        $userInfo = $this->objDsUser->getUserInfo($uid, $appId, 0, 0, $instId, $roleId);
        if ($userInfo === false) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::SELECT_ERROR);
        } elseif (empty($userInfo) || Afxmis_User_Authority::STATUS_DISABLE == $userInfo['status']) {
            $errMsg = empty($userInfo) ? '当前用户不存在' : '当前用户已失效';
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, $errMsg, $userInfo);
        }

        // 获取当前用户的下级用户
        if ($type == 1) {
            $arrConds  = [
                'appId'  => $appId,                                 // 当前应用方下
                sprintf('id IN (%s)', implode(',', $ids)),   // 查询下级指定id
                'status' => Afxmis_User_Authority::STATUS_ENABLE, // 状态有效
                // 必须是下级的
                sprintf("relation LIKE '|%s%d|%%' ", ltrim($userInfo['relation'], ' |'), $userInfo['id']),
            ];
            $arrFields = ['id', 'uid', 'nickname', 'roleId', 'relation'];
            $userList  = $this->objDsUser->getListByConds($arrConds, $arrFields);
            if ($userList === false) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::SELECT_ERROR, '', ['arrConds' => json_encode($arrConds)]);
            }
            foreach ($userList as $v) {
                $userMap[$v['uid']] = ['id' => (int)$v['id'], 'nickname' => $v['nickname'], 'roleId' => $v['roleId'], 'relation' => explode('|', $v['relation'])];
            }
        } else {
            // 给自己生成链接
            $userMap[$uid] = ['id' => $userInfo['id'], 'nickname' => $userInfo['nickname'],'roleId' => $userInfo['roleId'], 'relation' => explode('|', $userInfo['relation'])];
        }

        $uids = array_keys($userMap);
        // 获取用户分销码信息
        $arrConds  = [
            'appId' => $appId,
            'instId' => $instId,
            sprintf('uid IN (%s)', implode(',', $uids)),
        ];
        $arrFields = ['uid', 'shareCode'];
        $ret       = $this->objDsSCode->getListByConds($arrConds, $arrFields);
        if ($ret === false) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::SELECT_ERROR, '', ['arrConds' => json_encode($arrConds)]);
        }
        $shareCodeMap = array_column($ret, 'shareCode', 'uid');

        // 获取用户手机号信息
        $usersPhoneMap = Afxmis_User_User::getInstance()->batchGetUcloudUserInfo($uids);
        if ($usersPhoneMap === false) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::SELECT_ERROR, '', ['uids' => json_encode($uids)]);
        }

        // 生成链接
        $apiSource = $actInfo['apiInfo']['apiSource'] ?? Oplib_Const_AFX::API_SOURCE_WU;
        switch ($apiSource) {
            case Oplib_Const_AFX::API_SOURCE_WU:  // 直接拼接链接
                foreach ($userMap as $uid => $mapEntity) {
                    $item = [
                        'textUrl'  => $this->genLink($actInfo['url'], $shareCodeMap[$uid] ?? '', $actInfo['id'], $saleChannelId),
                        'filename' => sprintf('%s-%s-%s', $mapEntity['nickname'], $usersPhoneMap[$uid]['phone'] ?? 0, $actInfo['name']),
                        'actType'  => $actInfo['type'], // 活动类型
                    ];

//                    if (stripos($item['textUrl'], '/fengniao/') !== false) {
//                        // 蜂鸟过去的活动，需要额外配置参数，$item['textUrl']已经包含了多个参数&
//                        // PRD: http://wiki.zuoyebang.cc/pages/viewpage.action?pageId=124974560
//                        $item['textUrl'] .= '&businessType=10';
//                    }

                    if (stripos($item['textUrl'], '/fengniao/') !== false && in_array($saleChannelId,[103,102])) {
                        // 蜂鸟过去的活动，需要额外配置参数，$item['textUrl']已经包含了多个参数&
                        // PRD: http://wiki.zuoyebang.cc/pages/viewpage.action?pageId=124974560
                        $item['textUrl'] .= '&saleChannelId=1';
                    }

                    if (0 >= strlen($item['textUrl'])) {
                        $data = [
                            'actInfo' => json_encode($actInfo, JSON_UNESCAPED_UNICODE),
                            'uid'     => $uid, 'shareCode' => $shareCodeMap[$uid] ?? '', 'saleChannelId' => $saleChannelId,
                        ];
                        throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::OTHER_ERROR, '生成落地页素材链接失败', $data);
                    }
                    $arrOutput['list'][] = $item;
                }
                break;
            case Oplib_Const_AFX::API_SOURCE_DYZT: // 低幼中台:小程序
                $arrParams = [
                    'courseType'    => $actInfo['apiInfo']['apiPValue'] ?? '',
                    'flowPonds'     => $this->genFlowPonds($shareCodeMap, $actId),
                    'saleChannelId' => $saleChannelId,
                    'channelLabel'  => $channelLabel, //渠道标签
                    'afxActId'      => $actId, // 河图活动id
                ];
                $ret       = Afxmis_ApiSource::getLPageUrlList($apiSource, $arrParams);
                if ($ret['errNo'] != 0) {
                    $data = ['apiSourceRet' => json_encode($ret, JSON_UNESCAPED_UNICODE)];
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::OTHER_ERROR, $ret['errStr'] ?? $ret['errstr'] ?? '', $data);
                }
                // 组装数据
                foreach ($userMap as $uid => $mapEntity) {
                    $item = [
                        'textUrl'  => $ret['data'][$uid] ?? '',
                        'filename' => sprintf('%s-%s-%s', $mapEntity['nickname'], $usersPhoneMap[$uid]['phone'] ?? 0, $actInfo['name']),
                        'actType'  => $actInfo['type'], // 活动类型
                    ];
                    if (0 >= strlen($item['textUrl'])) {
                        $data = ['apiSourceRet' => json_encode($ret, JSON_UNESCAPED_UNICODE), 'uid' => $uid];
                        throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::OTHER_ERROR, '生成落地页素材链接失败', $data);
                    }
                    $arrOutput['list'][] = $item;
                }
                break;
            case Oplib_Const_AFX::API_SOURCE_TF: // 投放：落地页添加lasfrom
                // 一级代理商信息
                $agentInfo = $this->getRoleLv1AgentInfo($userInfo);
                $agentIds = [];
                if($userInfo['relation']){
                    $agentIds = explode('|', $userInfo['relation']);
                }
                $arrParams = [
                    'agentOrgId'       => $userInfo['instId'],
                    'businessLine'     => $actInfo['businessLine'],
                    'agentModeType'    => $agentInfo['modelType'],
                    'agentCooperation' => $agentInfo['cooperationType'],
                    'agentName'        => $agentInfo['nickname'],
                    'agentId'          => $agentInfo['id'],
                    'projectLv1'       => $actInfo['apiInfo']['projectLv1'] ?? '',
                    'projectLv2'       => $actInfo['apiInfo']['projectLv2'] ?? '',
                    'owner'            => $cadre,
                    'mark'             => $actInfo['mark'],
                    'url'              => $actInfo['url'],
                    'flowPonds'        => $this->genFlowPonds($shareCodeMap, $actId, $actInfo['url']),
                    'saleChannelId'    => $saleChannelId,
                    'channelLabel'      => $channelLabel, //渠道标签
                    'linkNum'           => $arrInput['linkNum'], //分销链接最大数
                    // 'cpsLv1'           => $userInfo['roleId'] == Afxmis_User_Authority::ROLE_LV1_AGENT ? $userInfo['id'] : intval($agentIds[1]),
                    // 'cpsLv2'           => $userInfo['roleId'] == Afxmis_User_Authority::ROLE_LV2_AGENT ? $userInfo['id'] : intval($agentIds[2]),
                    // 'cpsLv3'           => $userInfo['roleId'] == Afxmis_User_Authority::ROLE_LV3_AGENT ? $userInfo['id'] : intval($agentIds[3]),
                    'afxActId'          => $actId, // 河图活动id
                ];
                if($saleChannelId==103){
                    unset($arrParams['saleChannelId']);
                }

                // 组装数据
                foreach ($userMap as $uid => $mapEntity) {
                    if ($mapEntity['roleId'] == Afxmis_User_Authority::ROLE_LV1_AGENT) {
                        // 一级
                        $arrParams['cpsLv1'] = intval($mapEntity['id']);
                        $arrParams['cpsLv2'] = 0;
                        $arrParams['cpsLv3'] = 0;
                    } else if ($mapEntity['roleId'] == Afxmis_User_Authority::ROLE_LV2_AGENT) {
                        // 二级
                        $arrParams['cpsLv1'] = $mapEntity['relation'][1];
                        $arrParams['cpsLv2'] = intval($mapEntity['id']);
                        $arrParams['cpsLv3'] = 0;
                    } else if ($mapEntity['roleId'] == Afxmis_User_Authority::ROLE_LV3_AGENT) {
                        // 三级
                        $arrParams['cpsLv1'] = $mapEntity['relation'][1];
                        $arrParams['cpsLv2'] = $mapEntity['relation'][2];
                        $arrParams['cpsLv3'] = intval($mapEntity['id']);
                    }

                    Bd_Log::addNotice('ActGenLinkTFCommand', json_encode($arrParams, JSON_UNESCAPED_UNICODE));
                    $ret = Afxmis_ApiSource::getLPageUrlList($apiSource, $arrParams);
                    if ($ret['errNo'] != 0) {
                        $data = ['apiSourceRet' => json_encode($ret, JSON_UNESCAPED_UNICODE)];
                        throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::OTHER_ERROR, $ret['errStr'] ?? $ret['errstr'] ?? '', $data);
                    }

                    $item = [
                        'textUrl'  => $ret['data'][$uid] ?? '',
                        'filename' => sprintf('%s-%s-%s', $mapEntity['nickname'], $usersPhoneMap[$uid]['phone'] ?? 0, $actInfo['name']),
                        'actType'  => $actInfo['type'], // 活动类型
                    ];
                    if (0 >= strlen($item['textUrl'])) {
                        $data = ['apiSourceRet' => json_encode($ret, JSON_UNESCAPED_UNICODE), 'uid' => $uid];
                        throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::OTHER_ERROR, '生成落地页素材链接失败', $data);
                    }

                    // 将洛书页面域名全量切换为二级域名
                    // 测试代理商UID: 小猪趣学(2516710040), 王珊珊(2395557579), 魏智妮(2322694073)
                    $oldTextUrl = $item['textUrl'];
                    $item['textUrl'] = self::changeHost($oldTextUrl, self::NEW_HOST, self::OLD_HOSTS);
                    $logData = [
                        'actId' => $actInfo['id'],
                        'agentId' => $agentInfo['id'],
                        'agentUid' => $agentInfo['uid'],
                        'apiSource' => $apiSource,
                        'oldTextUrl' => $oldTextUrl,
                        'newTextUrl' => $item['textUrl']
                    ];
                    Bd_Log::addNotice('ActGenLinkChangeHost', json_encode($logData));
                    if($arrInput['type']==0 && !empty($arrInput['linkGroup'])){
                        $fileName = $item['filename'];
                        foreach (explode(',',$arrInput['linkGroup']) as $num){
                            $num = str_pad($num,2,"0",STR_PAD_LEFT);
                            $item['textUrl'] = $this->changeLastfrom($item['textUrl'],$num);
                            $item['lastfrom'] = $this->getLastfromFromUrl($item['textUrl']);
                            $item['filename'] = $fileName.'-'.$num;
                            $arrOutput['list'][] = $item;

                        }
                    }else{
                        $item['lastfrom'] = $this->getLastfromFromUrl($item['textUrl']);
                        $arrOutput['list'][] = $item;
                    }
                }
                break;
        }
        Bd_Log::notice("arrOutput:", json_encode($arrOutput));
        //生成短链
        $actLinkLogData = [];
        foreach($arrOutput['list'] as $k=>$v){
            // 该应用方，不需在分销链接中带上salechannelID参数
            // https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=341710635
            if (Afxmis_Const::SALE_CHANNEL_XBHMKT === $saleChannelId) {
                $v['textUrl'] = trim(preg_replace('/([?&])saleChannelId=[^&]*(&|$)/', '$1', $v['textUrl']), '?&');
            }

            $shotUrl = Qdlib_Service_Moat_Su::retrunShortUrlOrFalse($v['textUrl']);
            if($shotUrl==false){
                Oplib_Util_Log::fatal('afxmis',__CLASS__,__FUNCTION__,"分销链接短链生成异常,失败链接【{$v['textUrl']}】", '', [18600051881]);
                continue;
            }
            //替换host
            $shotUrl = str_replace('zyburl.com','fengniaourl.com',$shotUrl);
            $actLinkLogData[] =[
                'cadre' => $cadre,
                'actId' => $actId,
                'url'   => $v['textUrl'],
                'shortUrl' => $shotUrl,
                'urlName'  => $v['filename'],
                'saleChannel' => $saleChannelId,
                'ext'         => json_encode($arrInput),
            ];

            $arrOutput['list'][$k]['textUrl'] = $shotUrl;

        }
        $this->addActLinkLog($actLinkLogData);

        if ($isExportExcel) {
            // 是否导出成为excel,如果是直接下载输出为excel文件
            $name     = sprintf('分销链接-%s', date('Y-m-d'));
            $dataList = [
                ['分销链接', '分销细节'],
            ];
            foreach ($arrOutput['list'] as $v) {
                $dataList[] = [$v['textUrl'], $v['filename']];
            }
            Oplib_Util_Helper::setDownloadHeader($name . '.xlsx', 0);
            Oplib_Util_Helper::arrayToExcel2007($dataList, 'php://output', $name);
            exit;
        }

        if($isCSV){
            // 需要csv的数据，会对数据按csv规则，对数据转义处理
            foreach ($arrOutput['list'] as $k => $v){
                $arrOutput['list'][$k] = array_map('Afxmis_Download::encodeCSVField', $v);
            }

        }

        return $arrOutput;
    }

    /**
     * 替换URL域名
     * @param $url
     * @param $newHost
     * @param $oldHosts
     * @return string
     */
    private function changeHost($url, $newHost, $oldHosts)
    {
        $parts = parse_url($url);
        if (empty($parts['host']) || !in_array($parts['host'], $oldHosts)) {
            return $url;
        }
        $parts['host'] = $newHost;
        return Afxmis_Tools_UrlUtil::buildUrl($parts);
    }

    private function changeLastfrom($url,$num){
        /*$url = 'http://wzn1-docker.suanshubang.com/static/hy/cornucopia/kmubxqoy.html?lastfrom=yk_zmt_rwtf_cps2xfj5t417c1311c2554c304_01&flowPond=%7B%22fissionId%22%3A%22AFX_dn9xHbip%22%2C%22afxActId%22%3A132%7D&saleChannelId=24';*/
        $pattern = '|lastfrom=(\w+)_(\w+)_(\w+)_(\w+)_(\d+)|';
        return preg_replace($pattern, 'lastfrom=$1_$2_$3_$4'.'_'.$num, $url);

    }

    private function getLastfromFromUrl($url){
//        $url = 'http://wzn1-docker.suanshubang.com/static/hy/cornucopia/kmubxqoy.html?lastfrom=yk_zmt_rwtf_cps2xfj5t417c1311c2554c304_01&flowPond=%7B%22fissionId%22%3A%22AFX_dn9xHbip%22%2C%22afxActId%22%3A132%7D&saleChannelId=24';
        $pattern = '|lastfrom=\w+|';
        $ret = preg_match($pattern, $url, $matches);
        if (!empty($matches)){
            return trim($matches[0], "lastfrom=");
        }
        return "";
    }



    private function checkAndFormatParams($arrInput)
    {
        $cadre = trim(strval($arrInput['founderCadre'] ?? ''));

        $appId = intval(trim(strval($arrInput['appId'] ?? '0')));
        $actId = intval(trim(strval($arrInput['actId']) ?? '0'));
        $type  = intval(trim(strval($arrInput['type']) ?? '0'));
        $isCSV = intval(trim(strval($arrInput['isCSV']) ?? '0'));
        $uid   = intval(trim(strval($arrInput['uid']) ?? '0'));
        $instId = intval(trim(strval($arrInput['instId']) ?? '0'));
        $roleId = intval(trim(strval($arrInput['roleId']) ?? '0'));
        $isExportExcel   = intval(trim(strval($arrInput['isExportExcel']) ?? '0'));
        $ids   = $arrInput['ids'];
        $channelLabel   = trim($arrInput['channelLabel'] ?? '');
        $linkNum = intval($arrInput['linkNum']);
        $linkGroup = strval($arrInput['linkGroup']);

        // 验证参数
        foreach (['appId', 'actId', 'uid', 'instId', 'roleId'] as $v) {
            if (0 >= ${$v}) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, $v . '不合法');
            }
        }
        if (!in_array($type, [0, 1])) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'type不合法');
        }
        if($type==0 && $linkNum==0 ){
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, '生成数量不合法');
        }
        if($type==0 && $linkNum>200){
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, '生成数量超过200');
        }
        if($type==0 && empty($linkGroup)){
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, '生成多分销链接区段不合法');
        }
        if (!in_array($isCSV, [0, 1])) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'isCSV不合法');
        }
        if (!in_array($isExportExcel, [0, 1])) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'isExportExcel不合法');
        }

        $channelLabels = array_keys(Afxmis_LastfromAttr::getChannelLabel());
        if (!in_array($channelLabel, $channelLabels)) {
            //Bd_Log::addNotice('InvalidChannelLabel', json_encode(['channelLabel' => $channelLabel]));
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, '系统更新，请刷新链接后重试！');
        }
        // 判断ids
        if ($type == 1) {
            $ids = is_array($ids) ? $ids : [];
            $ids = array_map('intval', $ids);
            $ids = array_unique($ids);
            $ids = array_filter($ids);
        } else {
            $ids = [];
        }
        if ($type == 1 && (empty($ids) || !is_array($ids))) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'ids不合法', $arrInput);
        }

        return compact('appId', 'actId', 'type', 'uid', 'ids', 'isCSV', 'isExportExcel', 'cadre', 'channelLabel','linkNum','linkGroup', 'instId', 'roleId');
    }

    private function genLink($url, $shareCode, $actId, $saleChannelId)
    {
        $targerUrl = '';
        $url       = trim($url);
        // 带来了参数
        if (false !== ($pos = strpos($url, '?'))) {
            $targerUrl = substr($url, 0, $pos);
            $urlQuery  = substr($url, $pos + 1);

            if(in_array($saleChannelId,[102,103])){
                $urlParams = [
                    0 => $this->generateFlowPond($shareCode, $actId, $url),
                ];
            }else{
                $urlParams = [
                    0 => $this->generateFlowPond($shareCode, $actId, $url),
                    1 => 'saleChannelId=' . $saleChannelId,
                ];
            }

            if (0 >= strlen($urlQuery)) {
                $urlParams[0] = sprintf('flowPond=%s', rawurlencode(json_encode($urlParams[0])));
                $targerUrl    = sprintf('%s?%s', $targerUrl, implode('&', $urlParams));
                return $targerUrl;
            }
            $ret      = explode('#', $urlQuery, 2);
            $urlQuery = $ret[0];
            $anchor   = $ret[1] ?? '';
            foreach (explode('&', $urlQuery) as $chunk) {
                $param = explode("=", $chunk, 2);
                if ($param === false) {
                    continue;
                }
                switch ($param[0]) {
                    case 'saleChannelId':// no break
                    case 'saleChannel':
                        continue; // 参数删除掉
                        break;
                    case 'flowPond':
                        if (isset($param[1]) && $param[1]) {
                            $flowPond = json_decode($param[1], true);
                            is_array($flowPond) || $flowPond = json_decode(urldecode($param[1]), true);
                            is_array($flowPond) || $flowPond = [];
                            $urlParams[0] = $urlParams[0] + $flowPond; // flowPond参数合并
                        }
                        break;
                    default:
                        $urlParams[] = $chunk;
                }
            }
            $urlParams[0] = sprintf('flowPond=%s', rawurlencode(json_encode($urlParams[0])));
            if ($anchor) {
                $anchor = '#' . $anchor;
            }
            $targerUrl = sprintf('%s?%s%s', $targerUrl, implode('&', $urlParams), $anchor);
            return $targerUrl;
        } else {
            $ret      = explode('#', $url, 2);
            $url      = $ret[0];
            $anchor   = $ret[1] ?? '';
            if ($anchor) {
                $anchor = '#' . $anchor;
            }
            if(in_array($saleChannelId,[102,103])){
                $targerUrl = sprintf('%s?flowPond=%s%s', $url, rawurlencode(json_encode($this->generateFlowPond($shareCode, $actId, $url))),  $anchor);
            }else{
                $targerUrl = sprintf('%s?flowPond=%s&saleChannelId=%s%s', $url, rawurlencode(json_encode($this->generateFlowPond($shareCode, $actId, $url))), $saleChannelId, $anchor);
            }
        }
        return $targerUrl;
    }

    // 调用接口方传递flowPonds参数
    private function genFlowPonds($shareCodeMap, $actId, $url = '')
    {
        $flowPonds = [];
        $actId     = intval($actId);
        if (0 >= $actId) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'actId不合法', ['actId' => $actId]);
        }
        foreach ($shareCodeMap as $uid => $shareCode) {
            $shareCode = trim(strval($shareCode));
            if (0 >= strlen($shareCode)) {
                $data = ['shareCodeMap' => json_encode($shareCodeMap, JSON_UNESCAPED_UNICODE)];
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::OTHER_ERROR, sprintf('用户(uid:%s)分销码为空', $uid), $data);
            }
            $flowPonds[] = [
                'key'      => $uid,
                'flowPond' => json_encode($this->generateFlowPond($shareCode, $actId, $url)),
            ];
        }
        return $flowPonds;
    }

    // 根据当前用户，获取他的1一级代理商信息，如果他自己使用一级代理商则返回他自己
    private function getRoleLv1AgentInfo($userInfo, $flag = false)
    {
        if ($userInfo['roleId'] == Afxmis_User_Authority::ROLE_LV1_AGENT) {
            return $userInfo;
        } elseif (isset(Afxmis_User_Authority::ROLE_AGENT_MAP[$userInfo['roleId']])) {
            // 如果是代理商则，查询对应的一级代理商
            $relation = trim($userInfo['relation'], ' |');
            $agentId  = empty($relation) ? 0 : explode('|', $relation)[0];
            if (!is_numeric($agentId) || 0 >= $agentId) {
                $data = ['userInfo' => json_encode($userInfo, JSON_UNESCAPED_UNICODE)];
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::OTHER_ERROR, sprintf('代理商(uid:%s)relation错误', $userInfo['uid']), $data);
            }
            $ret = $this->objDsUser->getInfoById($agentId);
            if (false === $ret) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::SELECT_ERROR, '', ['agentId' => $agentId]);
            }
            if (empty($ret) || $ret['appId'] != $userInfo['appId']) {
                $data = ['agentInfo' => json_encode($ret, JSON_UNESCAPED_UNICODE), 'userInfo' => json_encode($userInfo, JSON_UNESCAPED_UNICODE)];
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::OTHER_ERROR, '一级代理商未找到', $data);
            }
            return $ret;
        }
        // 是分销员,查询代理商信息
        $ret = $this->objDsUser->getInfoById($userInfo['aId']);
        if (false === $ret) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::SELECT_ERROR, '', ['agentId' => $userInfo['aId']]);
        }
        if (empty($ret) || $ret['appId'] != $userInfo['appId']) {
            $data = ['agentInfo' => json_encode($ret, JSON_UNESCAPED_UNICODE), 'userInfo' => json_encode($userInfo, JSON_UNESCAPED_UNICODE)];
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::OTHER_ERROR, '代理商未找到', $data);
        }

        if ($flag) {
            $data = ['agentInfo' => json_encode($ret, JSON_UNESCAPED_UNICODE), 'userInfo' => json_encode($userInfo, JSON_UNESCAPED_UNICODE)];
            // 查询不到直接抛异常,防止死循环
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::OTHER_ERROR, '代理商未找到', $data);
        }

        // 继续查询，直到查询到一级代理商位置
        $ret = $this->getRoleLv1AgentInfo($ret, true);
        return $ret;
    }

    //记录分销链接日志
    private function addActLinkLog($data){
        $objLinkLog = new Service_Data_AFXActLinkLog();

        if (empty($data)) {
            return true;
        }

        foreach ($data as &$item){
            $urlParse = parse_url($item['url']);
            $item['lastfrom'] = '';
            if(!empty($urlParse['query'])){
                $urlParams = Afxmis_Tools_UrlUtil::convertUrlQuery($urlParse['query']);
                $item['lastfrom'] = $urlParams['lastfrom'] ?? '';
            }
            $item['createTime'] = time();
            $item['updateTime'] = time();
        }

        $objLinkLog->batchAdd($data);

        return true;
    }

    private function generateFlowPond($shareCode, $actId, $url): array
    {
        $flowPond = [
            'fissionId' => $shareCode,
            'afxActId' => $actId,
        ];
        if ($this->isCuboUrl($url)) {
            $flowPond['cubeOrder'] = 1;
        }
        return $flowPond;
    }

    private function isCuboUrl($url): bool
    {
        if (!is_string($url) || '' == $url) {
            return false;
        }
        if (false === strpos($url, '/static/cube-extend/')) {
            return false;
        }
        return true;
    }
}
