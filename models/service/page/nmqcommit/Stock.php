<?php

class Service_Page_NmqCommit_Stock extends Zb_Common_BaseApiCommitPage
{
    private $qdlibDsAct;
    private $qdlibDsSCode;
    private $qdlibDsUser;
    private $qdlibDsInstToggle;
    private $redis;
    private $logInfo;
    private $orderInfo;

    protected $qdlibDsSaleRule;

    public function __construct()
    {
        parent::__construct();
        $this->qdlibDsAct = new Qdlib_Ds_Hetu_AFXAct();
        $this->qdlibDsSCode = new Qdlib_Ds_Hetu_AFXSCode();
        $this->qdlibDsUser = new Qdlib_Ds_Hetu_AFXUser();
        $this->qdlibDsInstToggle = new Qdlib_Ds_Hetu_AFXActInstToggle();
        $this->qdlibDsSaleRule = new Qdlib_Ds_Hetu_AFXSaleRuleV2();
        $this->redis = Qdlib_Util_Cache::getQudaoRedis();
    }

    public function process()
    {
        Bd_Log::addNotice('nmqCommand_' . (string)$this->intCmdNo, json_encode($this->arrCommand, JSON_UNESCAPED_UNICODE));

        // 压测订单过滤
        if (isset($this->arrCommand['_press_mark']) && 1 === $this->arrCommand['_press_mark']) {
            return;
        }

        if (empty($this->arrCommand['userId']) || empty($this->arrCommand['orderId'])) {
            Qdlib_Util_Log::error('afxmis', get_called_class(), 'process', 'invalid params', json_encode($this->arrCommand));
            return;
        }

        // 处理分销库存逻辑
        $error = $this->stock();
        if (!empty($error)) {
            Qdlib_Util_Log::error('afxmis', get_called_class(), 'stock', $error, json_encode($this->arrCommand));
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, $error);
        }

        // 转发500021 处理订单明细逻辑
        $error = $this->sendMQ();
        if (!empty($error)) {
            Qdlib_Util_Log::error('afxmis', get_called_class(), 'sendMQ', $error, json_encode($this->arrCommand));
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, $error);
        }
    }

    protected function stock(): string
    {
        // 获取flowPond
        $logInfo = $this->getLogInfo();
        if (false === $logInfo) {
            return '获取logInfo失败';
        }
        if (empty($logInfo['flowPond'])) {
            Qdlib_Util_Log::addNotice('stock_skip', 'flowPond empty');
            return '';
        }

        // 解析flowPond
        $arrFlowPond = Qdlib_Util_Tool::flowPondParser($logInfo['flowPond']);
//        $arrFlowPond = json_decode('{"fissionId":"ghghgfhg","afxActId":543}', true);
        if (empty($arrFlowPond['afxActId'])) {
            Qdlib_Util_Log::addNotice('stock_skip', 'afxActId empty');
            return '';
        }
        if (empty($arrFlowPond['fissionId'])) {
            Qdlib_Util_Log::addNotice('stock_skip', 'fissionId empty');
            return '';
        }
        $actId = (int)$arrFlowPond['afxActId'];
        $shareCode = (string)$arrFlowPond['fissionId'];

        // 活动
        $actInfo = $this->qdlibDsAct->getInfo($actId);

        // h5+投放才可以设置分销库存
        if (empty($actInfo['id']) || empty($actInfo['apiInfo']['apiSource']) || Oplib_Const_AFX::API_SOURCE_TF != $actInfo['apiInfo']['apiSource'] || Oplib_Const_AFX::ACT_TYPE_H5 != $actInfo['type']) {
            return '';
        }

        // 分销码
        $shareCodeInfo = $this->qdlibDsSCode->getInfo($shareCode);
        if (false === $shareCodeInfo) {
            return '查询活动信息失败';
        }
        if ($shareCodeInfo['userId'] == 0) {
            Qdlib_Util_Log::addNotice('stock_skip', 'invalid shareCode');
            return '';
        }

        // 代理
        $userInfo = $this->qdlibDsUser->getInfo($actInfo['appId'], $shareCodeInfo['userId']);
        if (false === $userInfo) {
            return '查询代理信息失败';
        }
        if ($userInfo['instId'] == 0) {
            return '查询代理所属机构失败';
        }

        // 机构ID
        $instId = $shareCodeInfo['instId'];
        $agentId = $userInfo['id'];

        // 获取支付时间
        $orderInfo = $this->getOrderInfo();
        if (false === $orderInfo) {
            return '查询订单信息失败';
        }
        $originalOrderId = 0;
        foreach ($orderInfo['skuList'] as $skuOrderInfo) {
            if (!empty($skuOrderInfo['changeInfo']['originalOrderId'])) {
                $originalOrderId = $skuOrderInfo['changeInfo']['originalOrderId'];
                break;
            }
        }
        // 转班取原始订单的支付时间
        if ($originalOrderId) {
            $orderInfo = Qdlib_Service_Moat_One::returnDetailOrFalse($this->arrCommand['userId'], $originalOrderId);
            if (false === $orderInfo) {
                return '查询订单信息失败';
            }
        }
        $payTime = $orderInfo['payInfo']['payTime'] ?? 0;
//        $payTime = time();

        // 计算数量
        $skuIdQuantityMap = $this->skuIdQuantityMap();
        if (false === $skuIdQuantityMap) {
            return '计算数量失败';
        }
        if (empty($skuIdQuantityMap)) {
            Qdlib_Util_Log::addNotice('stock_skip', 'quantity empty');
            return '';
        }
        Qdlib_Util_Log::addNotice('stock_quantity', json_encode($skuIdQuantityMap));

        // 时段机构库存配置
        $options = [];
        $LS = false;
        if (Qdlib_Const_LastFrom::BUSINESS_NUM_YK == $actInfo['businessLine'] && isset($actInfo['apiInfo']['pageType']) && Qdlib_Ds_Hetu_AFXAct::PAGE_TYPE_LS === $actInfo['apiInfo']['pageType'] && !empty($actInfo['apiInfo']['param'])) {
            $LS = true;
            $skuList = self::getSkuList(array_keys($skuIdQuantityMap));
            foreach ($skuList as $skuInfo) {
                $options = self::options($this->qdlibDsSaleRule, $skuInfo, true, $agentId);
            }
        } else {
            foreach ($skuIdQuantityMap as $skuId => $quantity) {
                $options = self::options($this->qdlibDsSaleRule, ['skuId' => $skuId], false, $agentId);
            }
        }
        $saleRuleConfigs = $this->qdlibDsSaleRule->getConfigs($actId, $instId, $options, time());
        if (false === $saleRuleConfigs) {
            return '查询时段机构库存配置失败';
        }
        Qdlib_Util_Log::addNotice('allSaleRuleConfigs', json_encode($saleRuleConfigs, JSON_UNESCAPED_UNICODE));
        foreach ($saleRuleConfigs as $option => $saleRuleConfig) {
            if (Qdlib_Ds_Hetu_AFXSaleRuleV2::NOLIMIT === $saleRuleConfig['threshold']) {
                unset($saleRuleConfigs[$option]);
                Qdlib_Util_Log::addNotice('stock_skip_' . (string)$option, 'nolimit');
                continue;
            }

            if ($payTime < $saleRuleConfig['beginTime']) {
                unset($saleRuleConfigs[$option]);
                Qdlib_Util_Log::addNotice('stock_skip_' . (string)$option, 'expired');
            }
        }
        Qdlib_Util_Log::addNotice('saleRuleConfigs', json_encode($saleRuleConfigs, JSON_UNESCAPED_UNICODE));
        if (empty($saleRuleConfigs)) {
            return '';
        }

        // sku映射到分销库存规则id
        $configIdQuantityMap = [];
        if ($LS) {
            foreach ($skuIdQuantityMap as $skuId => $quantity) {
                if (isset($skuList[$skuId])) {
                    $options = self::options($this->qdlibDsSaleRule, $skuList[$skuId], true, $agentId);
                    foreach ($options as $option) {
                        if (isset($saleRuleConfigs[$option])) {
                            $configIdQuantityMap[$saleRuleConfigs[$option]['id']] += $quantity;
                        }
                    }
                }
            }
        } else {
            foreach ($skuIdQuantityMap as $skuId => $quantity) {
                $options = self::options($this->qdlibDsSaleRule, ['skuId' => $skuId], false, $agentId);
                foreach ($options as $option) {
                    if (isset($saleRuleConfigs[$option])) {
                        $configIdQuantityMap[$saleRuleConfigs[$option]['id']] += $quantity;
                    }
                }
            }
        }

        // 消息去重复
        $key = sprintf('%d:%d:%d', $this->intCmdNo, $this->arrCommand['orderId'], $this->arrCommand['refundBatchId'] ?? 0);
        if ($this->redis->exists($key)) {
            Qdlib_Util_Log::addNotice('stock_skip', 'exists');
            return '';
        }

        // 计数
        $error = $this->counter($configIdQuantityMap);
        if (!empty($error)) {
            return $error;
        }

        // 消息去重复
        Qdlib_Util_Log::addNotice('stock_cache_set', $this->redis->set($key, '1', 86400));

        return '';
    }

    protected function skuIdQuantityMap()
    {
        $courseSkuIdQuantityMap = [];
        $otherSkuIdQuantityMap = [];
        foreach ($this->orderInfo['skuList'] as $skuOrderInfo) {
            // 赠品不计算
            if (isset($skuOrderInfo['flags']['isGift']) && 0 !== $skuOrderInfo['flags']['isGift']) {
                continue;
            }

            // 组合sku的子sku不计算
            if (isset($skuOrderInfo['flags']['isAssembly']) && 2 === $skuOrderInfo['flags']['isAssembly']) {
                continue;
            }

            if (1 === $skuOrderInfo['productType']) {
                $courseSkuIdQuantityMap[$skuOrderInfo['skuId']] += $skuOrderInfo['quantity'];
            } else {
                $otherSkuIdQuantityMap[$skuOrderInfo['skuId']] += $skuOrderInfo['quantity'];
            }
        }
        // 如果有课程sku 则只计算课程sku
        return empty($courseSkuIdQuantityMap) ? $otherSkuIdQuantityMap : $courseSkuIdQuantityMap;
    }

    protected function counter(array $configIdQuantityMap): string
    {
        return '';
    }

    private function getLogInfo()
    {
        if (is_null($this->logInfo)) {
            if (isset($this->arrCommand['logInfo'])) {
                $this->logInfo = json_decode($this->arrCommand['logInfo'], true);
                if (!is_array($this->logInfo)) {
                    $this->logInfo = [];
                }
            } else {
                $orderInfo = $this->getOrderInfo();
                if (false === $orderInfo) {
                    return false;
                }
                $this->logInfo = $orderInfo['logInfo'] ?? [];
            }
        }
        return $this->logInfo;
    }

    protected function getOrderInfo()
    {
        if (is_null($this->orderInfo)) {
            $this->orderInfo = Qdlib_Service_Moat_One::returnDetailOrFalse($this->arrCommand['userId'], $this->arrCommand['orderId']);
            if (false === $this->orderInfo) {
                return false;
            }
        }
        return $this->orderInfo;
    }

    private function sendMQ(): string
    {
        $cmdNo = 500021;
        $key = 'sendMQ_' . (string)$cmdNo;
        Hk_Util_Log::start($key);
        $payload = [
            'customPayload' => [
                'dbName' => $this->dbName,
                'autoTrans' => 'off',
                'intCmdNo' => $this->intCmdNo,
                'arrCommand' => [
                    'userId' => (int)$this->arrCommand['userId'],
                    'tradeId' => (int)$this->arrCommand['orderId'],
                ],
                'strModName' => $this->strModName,
                'transId' => $this->transId,
            ],
        ];
        $result = Qdlib_Util_MQ::sendCmd($cmdNo, $payload);
        Hk_Util_Log::stop($key);
        if (false === $result) {
            return '发送mq失败';
        }
        Qdlib_Util_Log::addNotice($key, json_encode(['payload' => $payload, 'result' => $result], JSON_UNESCAPED_UNICODE));
        return '';
    }

    public static function getSkuList(array $skuIds)
    {
        $redis = Qdlib_Util_Cache::getQudaoRedis();
        $skuIds = array_values($skuIds);
        $keyPrefix = 'hetu:sku:info:';
        $keys = [];
        $result = [];
        foreach ($skuIds as $skuId) {
            $keys[] = $keyPrefix . (string)$skuId;
        }

        // redis mget
        $values = $redis->mget($keys);
        Qdlib_Util_Log::addNotice('hetu_sku_info_cache_mget', json_encode([
            'keys' => $keys,
            'values' => $values,
        ], JSON_UNESCAPED_UNICODE));

        $dbSkuIds = [];
        if (is_array($values) && count($values) == count($keys)) {
            foreach ($values as $index => $value) {
                if ($value && $value = json_decode($value, true)) {
                    $result[$skuIds[$index]] = $value;
                } else {
                    $dbSkuIds[$index] = $skuIds[$index];
                }
            }
        } else {
            $dbSkuIds = $skuIds;
        }

        // ral + redis set
        if (!empty($dbSkuIds)) {
            $ralResult = Qdlib_Service_Moat_Common::returnDataOrFalse(Qdlib_Service_Moat_Newgoodsplatform::getSkuList($dbSkuIds));
            if (false === $ralResult) {
                return false;
            }
            foreach ($ralResult as $item) {
                $result[$item['skuId']] = $item;

                $tmp = $redis->set($keyPrefix . (string)$item['skuId'], json_encode($item), 600);
                Qdlib_Util_Log::addNotice('sale_rule_conf_cache_set_' . (string)$index, json_encode([
                    'value' => $item,
                    'result' => $tmp,
                ], JSON_UNESCAPED_UNICODE));
            }
        }

        return $result;
    }

    public static function options(Qdlib_Ds_Hetu_AFXSaleRuleV2 $dsSaleRuleV2, array $skuInfo, $isLS = true, $agentId = 0): array
    {
        $options = [];
        $option = $dsSaleRuleV2->option($skuInfo['skuId'], -1, -1, 0);
        $options[$option] = $option;
        $option = $dsSaleRuleV2->option(-1, -1, -1, 0);
        $options[$option] = $option;
        if ($agentId != 0) {
            $option = $dsSaleRuleV2->option($skuInfo['skuId'], -1, -1, $agentId);
            $options[$option] = $option;
            $option = $dsSaleRuleV2->option(-1, -1, -1, $agentId);
            $options[$option] = $option;
        }

        if ($isLS) {
            $option = $dsSaleRuleV2->option(-1, $skuInfo['gradeId'], $skuInfo['seasonId'], 0);
            $options[$option] = $option;
            $option = $dsSaleRuleV2->option(-1, -1, $skuInfo['seasonId'], 0);
            $options[$option] = $option;
            $option = $dsSaleRuleV2->option(-1, $skuInfo['gradeId'], -1, 0);
            $options[$option] = $option;
            if ($agentId != 0) {
                $option = $dsSaleRuleV2->option(-1, $skuInfo['gradeId'], $skuInfo['seasonId'], $agentId);
                $options[$option] = $option;
                $option = $dsSaleRuleV2->option(-1, -1, $skuInfo['seasonId'], $agentId);
                $options[$option] = $option;
                $option = $dsSaleRuleV2->option(-1, $skuInfo['gradeId'], -1, $agentId);
                $options[$option] = $option;
            }
        }
        return $options;
    }
}