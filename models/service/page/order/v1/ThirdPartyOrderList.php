<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * <AUTHOR>
 * @version     2019-08-26 17:57:41
 * @brief       第三方查询订单
 **/
class Service_Page_Order_V1_ThirdPartyOrderList
{
    const ID_REQUEST_KEY = 'Afxmis::ThirdPartyOrderList::IdRequestAccess::%s';
    const ID_REQUEST_LIMIT = 10;
    const IP_REQUEST_KEY = 'Afxmis::ThirdPartyOrderList::IdRequestAccess::%s';
    const IP_REQUEST_LIMIT = 50;
    private $accessTimeOut = 60;//超时时间

    public function __construct()
    {
        $this->objOrder = new Service_Data_AFXOrder();
        $this->objSku = new Service_Data_AFXSku();
        $this->objUser = new Service_Data_AFXUser();
        $this->objUserCommon = new Afxmis_User_Common();
        $this->objDsAFXSkuCategory = new Service_Data_AFXSkuCategory();
        $this->objAct = new Service_Data_AFXAct();
        $this->objAccessCode = new Service_Data_AFXAccessCode();
        $this->objDorisOrder = new Service_Data_AFXDorisOrder();
    }

    public function execute($arrInput)
    {
        //鉴权参数
        $accessId = (int)$arrInput['accessId'] ? (int)$arrInput['accessId'] : 0;
        $encryptionTime = (int)$arrInput['encryptionTime'] ? (int)$arrInput['encryptionTime'] : 0;
        $accessKey = (string)$arrInput['accessKey'] ? (string)$arrInput['accessKey'] : '';

        //获取参数
        $agentUid = (int)$arrInput['agentUid'] ? (int)$arrInput['agentUid'] : 0;
        $sTime = (int)$arrInput['sTime'] ? (int)$arrInput['sTime'] : 0;
        $eTime = (int)$arrInput['eTime'] ? (int)$arrInput['eTime'] : 0;
        $pn = (int)$arrInput['pn'] ? (int)$arrInput['pn'] : 1;
        $rn = (int)$arrInput['rn'] ? (int)$arrInput['rn'] : 10;
        $offset = intval($rn*($pn-1));
        $userIp = Bd_Ip::getUserIp();

        //接口请求频次限制
        //ip频次限制
        $ipLimitKey = $this->questKey(self::ID_REQUEST_KEY, $userIp);
        $ipLimitType = $this->questLimit($ipLimitKey, self::IP_REQUEST_LIMIT);
        if(!$ipLimitType){
            throw new Afxmis_Exception(Afxmis_ExceptionCodes::PARAM_ERROR, '该ip超出每分钟请求上限');
        }
        //id频次限制
        $idLimitKey = $this->questKey(self::IP_REQUEST_KEY, $accessId);
        $idLimitType = $this->questLimit($idLimitKey, self::ID_REQUEST_LIMIT);
        if(!$idLimitType){
            throw new Afxmis_Exception(Afxmis_ExceptionCodes::PARAM_ERROR, '该机构超出每分钟请求上限');
        }

        //获取鉴权信息
        $accessCodeInfo = $this->objAccessCode->getInfoById($accessId);
        if(empty($accessCodeInfo)){
            throw new Afxmis_Exception(Afxmis_ExceptionCodes::PARAM_ERROR, '鉴权信息获取失败');
        }
        //鉴权
        $accessKeyMd5 = md5($accessId.$encryptionTime.$accessCodeInfo['secretKey']);
        if(empty($accessId) || empty($accessKey) || $accessKeyMd5 !== $accessKey){
            throw new Afxmis_Exception(Afxmis_ExceptionCodes::PARAM_ERROR, '鉴权失败');
        }
        if(time()-$encryptionTime>$this->accessTimeOut){
            throw new Afxmis_Exception(Afxmis_ExceptionCodes::PARAM_ERROR, '请求超时');
        }

        //ip白名单
        $checkIp = $this->checkIp($userIp, $accessCodeInfo['accessIp']);
        if(!$checkIp){
            Oplib_Util_Log::addNotice('checkIp', 'userIp:【'.$userIp.'】,accessIp:【'.json_encode($accessCodeInfo['accessIp']).'】');
            throw new Afxmis_Exception(Afxmis_ExceptionCodes::PARAM_ERROR, '不在ip白名单内');
        }

        //查询用户授权
        if(empty($accessCodeInfo['accessUids']) || !in_array($agentUid, $accessCodeInfo['accessUids'])){
            Oplib_Util_Log::addNotice('checkAccessUids', 'agentUid:【'.$agentUid.'】,accessUids:【'.json_encode($accessCodeInfo['accessUids']).'】');
            throw new Afxmis_Exception(Afxmis_ExceptionCodes::PARAM_ERROR, '没有该代理商售卖信息查询授权');
        }

        // 返回值
        $arrOutput = [
            'pn'    => $pn,
            'total' => 0,
            'list'  => [],
        ];

        //获取用户信息
        $userInfo = $this->objUser->getUserInfo($agentUid);
        if(empty($userInfo)){
            return $arrOutput;
        }

        //只展示自己和下级分销信息
        $uids[] = $userInfo['uid'];
        $nextLevelList = $this->objUserCommon->nextLevelList($userInfo['appId'], $userInfo['id'], $userInfo['relation'], 0, 0, '', 0, 0, 0, 10000);
        $orderUids[] = $founderUid;
        if(!empty($nextLevelList['list'])){
            foreach($nextLevelList['list'] as $val){
                $uids[] = $val['uid'];
            }
        }

        //拼装查询数据
        $arrConds['type'] = Service_Data_AFXOrder::TYPE_FX_11;
        $arrConds[] = 'status !='.Service_Data_AFXOrder::STATUS_GONGHAI;
        $arrConds[] = 'uid in('.implode(',',$uids).')';
        if($sTime){
            $arrConds[] = "buy_time >= {$sTime}";
        }
        if($eTime){
            $arrConds[] = "buy_time <= {$eTime}";
        }
        // 查询数量
        //$total = $this->objDorisOrder->getCnt($arrConds);
        $total = $this->objOrder->getCnt($arrConds);
        if (empty($total)) {
            return $arrOutput;
        }
        $arrOutput['total'] = $total;

        //查询列表
        $order = ' id desc ';
        //$list = $this->objDorisOrder->getList($arrConds, [], $order, '', $offset, $rn);
        $list = $this->objOrder->getList($arrConds, [], $order, $offset, $rn);
        if (empty($list)) {
            return $arrOutput;
        }

        //抽出代理商id和课程id
        $times = 0;
        $uids = $aUids = $userInfo = [];
        $ucloud = new Hk_Ds_User_Ucloud();
        foreach($list as $val){
            if(!empty($uids[$times]) && count($uids[$times])>=20){
                $times++;
            }
            $uids[$times][] = $val['uid'];
            $uids[$times][] = $val['buyerUid'];

            if(!empty($val['uid']) && !in_array($val['uid'], $aUids)){
                $aUids[] = $val['uid'];
            }
            if(!empty($val['snapshootInfo']['roleLv1AgentInfo']['uid']) && !in_array($val['snapshootInfo']['roleLv1AgentInfo']['uid'], $aUids)){
                $aUids[] = $val['snapshootInfo']['roleLv1AgentInfo']['uid'];
            }
        }
        //通过用户id查出手机号
        foreach($uids as $times=>$val){
            $uInfo = $ucloud->getUserInfo($uids[$times], true);
            $userInfo = !empty($userInfo) ? $userInfo+$uInfo : $uInfo;
        }

        //查询代理商信息
        $agentList = [];
        $userConds[] = 'uid in ('.implode(',',$aUids).')';
        $uInfo = $this->objUser->getList($userConds);
        foreach($uInfo as $val){
            $agentList[$val['uid']] = $val;
        }

        //课程类型
        $goodsType = $this->objDsAFXSkuCategory->getAllCategory();

        $orderList = [];
        foreach($list as $val){
            $tmp = [
                'id'=>$val['id'],
                'orderId'=>(string)$val['orderId'],
                'skuId'=>(int)$val['skuId'],
                'actId'=>(int)$val['actId'],
                'actName'=>(string)$val['snapshootInfo']['actInfo']['name'],
                'status'=>$val['refundTime']>0 ? '已退款' : '已支付',
                'skuName'=>(string)$val['skuInfo']['skuName'],
                'skuType'=>(string)$goodsType['goodsCategoryRoot'][$val['skuInfo']['categoryPid']].'-'.(string)$goodsType['goodsCategoryTree'][$val['skuInfo']['categoryPid']][$val['skuInfo']['categoryId']],
                'price'=> sprintf('%.2f',($val['snapshootInfo']['orderInfo']['amountTrade'] ?? $val['skuInfo']['price']) / 100), // 实际支付的金额
                'skuOriginPrice'=> sprintf('%.2f',((int)$val['skuInfo']['skuOriginPrice'] / 100)), // 商品标价
                'buyerMobile'=>(string)Afxmis_Util::hidtel($userInfo[$val['buyerUid']]['phone']),
                'buyerUid'=>(int)$val['buyerUid'],
                'agentName'=>(string)$agentList[$val['uid']]['nickname'],
                'agentMobile'=>!empty($userInfo[$val['uid']]['phone']) ? (string)Afxmis_Util::hidtel($userInfo[$val['uid']]['phone']) : '',
                'agentRole'=>(string)Afxmis_User_Authority::getRoleName($agentList[$val['uid']]['roleId'], $appId),
                'buyTime'=>date('Y-m-d H:i:s', $val['buyTime']),
                'generalAgent'=>(string)$agentList[$val['snapshootInfo']['roleLv1AgentInfo']['uid']]['nickname'],
                'generalAgentUid'=>(string)$val['snapshootInfo']['roleLv1AgentInfo']['uid'],
                'agentUid'=>(string)$val['uid'],
            ];
            $orderList[] = $tmp;
        }

        $arrOutput['total'] = $total;
        $arrOutput['list'] = $orderList;

        return $arrOutput;
    }

    //请求限制key
    private function questKey($prefixKey, $value)
    {
        $_cachekey   = sprintf($prefixKey, $value);
        return $_cachekey;
    }

    //请求限制
    private function questLimit($key, $limit, $isFatal=false)
    {
        $objRedis = Qdlib_Util_Cache::getQudaoRedis();
        $check = $objRedis->exists($key);
        if($check){
            $objRedis->incr($key);
            $count = $objRedis->get($key);
            $objRedis->expire($key,$this->accessTimeOut);
            if($count > $limit){
                if($isFatal){
                    //Oplib_Util_Log::fatal('afxmis',__CLASS__,__FUNCTION__,"线下分销三方接口触发ip频次限制,触发ip为【{$userIp}】,触发者【{$accessId}】", '', [18600051881]);
                }
                return false;
            }
        }else{
            $objRedis->set($key,1,$this->accessTimeOut);
        }

        return true;
    }

    private function checkIp($ip, $allowIpList) {
        if(empty($ip) || empty($allowIpList)){
            return false;
        }
        $y = explode('.', $ip);
        foreach ($allowIpList as $allow) {
            $x = count(explode('.', $allow));
            if (implode('.', array_slice($y, 0, $x)) == $allow) {
                return true;
            }
        }
        return false;
    }
}
