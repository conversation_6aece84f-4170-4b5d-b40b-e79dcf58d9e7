<?php
/**************************************************************************
 *
 * Copyright (c) 2021 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * <AUTHOR>
 * @version     2021-08-02 10:45
 * @brief       兑换码修改注释
 **/
class Service_Page_Act_V1_CouponCodeRemark
{
    public function __construct()
    {
        $this->objCouponCode = new Qdlib_Ds_Hetu_CouponCode();
    }

    public function execute($arrInput)
    {
        //如果uid为0，直接退出
        if($arrInput['uid'] == 0)
        {
            throw new Qdlib_Common_Exception(Qdlib_Common_ExceptionCodes::USER_NOT_LOGIN);
        }
        //更新数据
        $affectedRows = $this->objCouponCode->updateCouponCode(['codeName' => $arrInput['codeName'], 'agentStaffId' => $arrInput['uid']], ['remark' => $arrInput['remark']]);
        if ($affectedRows === false) {
            throw new Qdlib_Common_Exception(Qdlib_Common_ExceptionCodes::DB_ERROR, ' 修改备注失败');
        }
        return ['affectedRows' => $affectedRows];
    }
}

