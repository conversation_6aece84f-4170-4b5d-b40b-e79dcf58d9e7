<?php
/**************************************************************************
 *
 * Copyright (c) 2021 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * <AUTHOR>
 * @version     2021-08-04 15:58
 * @brief       已认领兑换码查询列表
 **/
class Service_Page_Act_V1_CouponCodeConfirmedList
{
    public function __construct()
    {
        $this->objCouponCode = new Qdlib_Ds_Hetu_CouponCode();
    }

    public function execute($arrInput)
    {
        $page = (intval($arrInput['page']) >= 1) ? intval($arrInput['page']) : 1;
        $pageSize = (intval($arrInput['pageSize']) > 0) ? intval($arrInput['pageSize']) : 20;
        $start = ($page - 1) * $pageSize;
        // 返回值
        $arrOutput = [
            'total' => 0,
            'page'  => $page,
            'consumedNum'  => 0,
            'list'  => [],
        ];
        if($arrInput['uid'] == 0){
            throw new Qdlib_Common_Exception(Qdlib_Common_ExceptionCodes::USER_NOT_LOGIN);
        }

        $arrConds = [];
        if (isset($arrInput['codeName']) && strlen($arrInput['codeName']) > 0) {
            $arrConds['codeName'] = $arrInput['codeName'];
        }
        if (isset($arrInput['discount']) && strlen($arrInput['discount']) > 0) {
            $arrConds['discount'] = intval($arrInput['discount'] * 100);
        }

        if (isset($arrInput['remark']) && strlen($arrInput['remark']) > 0) {
            $arrConds['remark'] = ['%' . $arrInput['remark'] . '%', 'like'];
        }
        if (isset($arrInput['status']) && $arrInput['status'] > 0) {
            $arrConds['status'] = $arrInput['status'];
        }
        //金丝雀里是能看到的已认领过的列表（不是已认领，是广义的 已认领过的）
        $arrConds['agentStaffId'] = $arrInput['uid'];
        $arrConds['confirmTime'] = [0, '>'];
        // 查询数量
        $total = $this->objCouponCode->getCouponCodeTotal($arrConds);
        if (empty($total)) {
            return $arrOutput;
        }
        $arrOutput['total'] = $total;
        //查询列表
        $arrAppends = array(
            'order by confirm_time desc',
            "limit $start, $pageSize",
        );
        $list = $this->objCouponCode->getCouponCodeList($arrConds,[],$arrAppends);
        $list = array_map(function($coupon) {
            $row = [];
            $row['codeName'] = $coupon['codeName'];
            $row['codeEndtime'] = date("Y-m-d H:i:s", $coupon['codeEndtime']);
            $row['couponUid'] = $coupon['couponUid'];
            $row['couponId'] = $coupon['couponId'];
            $row['status'] = $this->objCouponCode->status($coupon);
            $row['statusStr'] = Qdlib_Const_Hetu_CouponCode::$statusDict[$row['status']];
            $row['discount'] = $coupon['discount'] / 100;
            $row['confirmTime'] = date("Y-m-d H:i:s", $coupon['confirmTime']);
            $row['remark'] = $coupon['remark'];
            return $row;}, $list
        );
        $arrOutput['list'] = $list;
        //查询已核销数量
        $arrConds['status'] = Qdlib_Const_Hetu_CouponCode::STATUS_COUPON_USED;
        $consumedNum = $this->objCouponCode->getCouponCodeTotal($arrConds);
        $arrOutput['consumedNum'] = $consumedNum;

        return $arrOutput;
    }
}
