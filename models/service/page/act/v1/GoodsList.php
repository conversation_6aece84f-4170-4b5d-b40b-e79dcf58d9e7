<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * <AUTHOR>
 * @version     2019-08-26 17:57:41
 * @brief       活动商品列表
 **/
class Service_Page_Act_V1_GoodsList
{
    public function __construct()
    {
        $this->objDsAFXApp = new Service_Data_AFXApp();
        $this->objDsAFXSku = new Service_Data_AFXSku();
        $this->objGoods = new Service_Data_AFXGoods();
        $this->objDsAFXOrder = new Service_Data_AFXOrder();
        $this->objDsSaleThreshold = new Service_Data_AFXSaleThreshold();
        $this->objDsSaleThresholdAgent = new Service_Data_AFXSaleThresholdAgent();
    }

    public function execute($arrInput)
    {
        $founderId = (int)$arrInput['founderId'];
        $founderUid = (int)$arrInput['founderUid'];
        $relation = (string)$arrInput['founderRelation'];
        $appId = (int)$arrInput['appId'];
        $actId = (int)$arrInput['actId'];
        $pn = (int)$arrInput['pn'];
        $rn = (int)$arrInput['rn'];
        $offset = intval($rn*($pn-1));

        // 返回值
        $arrOutput = [
            'pn'    => $pn,
            'total' => 0,
            'list'  => [],
        ];

        //获取该活动下售卖与退课总量
        $buyRefundCnt = $this->objDsAFXOrder->getSkuBuyRefundCnt($appId, $actId, 0, $founderUid);

        $arrOutput['saleNum'] = (int)$buyRefundCnt[0]['buyCnt'];
        $arrOutput['returnNum'] = (int)$buyRefundCnt[0]['refundCnt'];

        //拼装查询条件
        $arrConds['actId'] = $actId;

        // 查询数量
        $total = $this->objGoods->getCnt($arrConds);
        if (empty($total)) {
            return $arrOutput;
        }
        //查询列表
        $list = $this->objGoods->getList($arrConds, [], $order, '', $rn);
        if (empty($list)) {
            return $arrOutput;
        }
        $skuIds = [];
        foreach($list as $val){
            $skuIds[] = $val['skuId'];
        }

        $appInfo = $this->objDsAFXApp->getInfoById($appId);
        $stockList = $this->objDsAFXSku->getStockBySkuId($skuIds, $appInfo['saleChannelId']);

        //获取一级代理商自增id
        $agentId = 0;
        if(empty($relation)){
            $agentId = $founderId;
        }else{
            $relation = explode('|', $relation);
            $agentId = $relation[1];
        }
        //根据自增id获得是否在该活动设置规则
        $threshold = 0;
        $thresholdAgentInfo = $this->objDsSaleThresholdAgent->getInfoByAgentId($actId, $agentId);
        if(!empty($thresholdAgentInfo) && !empty($thresholdAgentInfo['stId'])){
            $thresholdInfo = $this->objDsSaleThreshold->getInfoByStId($thresholdAgentInfo['stId']);
            if(!empty($thresholdInfo)){
                $threshold = (int)$thresholdInfo['saleThreshold']-(int)$thresholdInfo['saleNum']-(int)$thresholdInfo['lockNum'];
            }
        }

        //售卖量和退课量统计
        $skuCnt = [];
        $skuBuyRefundCnt = $this->objDsAFXOrder->getSkuBuyRefundCnt($appId, $actId, $skuIds, $founderUid);
        if($skuBuyRefundCnt){
            foreach($skuBuyRefundCnt as $info){
                $skuCnt[$info['skuId']]['buyCnt'] = $info['buyCnt'];
                $skuCnt[$info['skuId']]['refundCnt'] = $info['refundCnt'];
            }
        }
        foreach($list as &$val){
            $stockCnt = (int)$stockList['stockCnt'][$val['skuId']]-(int)$stockList['saleCnt'][$val['skuId']];
            $val['saleChannel'] = $appInfo['name'];
            $val['stock'] = !empty($thresholdInfo['saleThreshold']) ? $threshold : $stockCnt;
            $val['saleNum'] = (int)$skuCnt[$val['skuId']]['buyCnt'];
            $val['returnNum'] = (int)$skuCnt[$val['skuId']]['refundCnt'];
        }
        $arrOutput['total'] = $total;
        $arrOutput['list'] = $list;

        return $arrOutput;
    }
}
