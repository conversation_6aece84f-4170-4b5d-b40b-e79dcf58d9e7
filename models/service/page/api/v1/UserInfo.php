<?php
/**************************************************************************
 *
 * Copyright (c) 2020 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   UserInfo.php
 * <AUTHOR>
 * @date   2020/4/2 15:26
 * @brief  【代理商或分销员】基本信息
 **/
class Service_Page_Api_V1_UserInfo
{
    public function __construct()
    {
        $this->objDsUser = new Service_Data_AFXUser();
    }

    public function execute($arrInput)
    {
        $ret       = $this->checkAndFormatParams($arrInput);
        $appId     = $ret['appId'];
        $arrConds  = $ret['arrConds'];
        $arrFields = $ret['arrFields'];

        $arrAppends = ['LIMIT 1'];
        $userInfo   = $this->objDsUser->getRecordByConds($arrConds, $arrFields, null, $arrAppends);
        if (false === $ret) {
            $errMsg = json_encode(compact('arrConds', 'arrFields', 'arrAppends'));
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::SELECT_ERROR, '', $errMsg);
        }
        if ($userInfo) {
            $userInfo['roleName'] = Afxmis_User_Authority::getRoleName($userInfo['roleId'], $appId);
        }

        return $userInfo;
    }

    private function checkAndFormatParams($arrInput)
    {
        // $arrConds, $arrFields
        $arrConds  = [];
        $arrFields = ['id', 'pid', 'appId', 'uid', 'nickname', 'roleId', 'status']; // 展示字段

        $appId = intval(trim(strval($arrInput['appId'])));
        $id    = strval($arrInput['id'] ?? '');
        $uid   = strval($arrInput['uid'] ?? '');
        // if (0 >= $appId) {
        //     throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'appId不合法');
        // }
        foreach (['id', 'uid'] as $k) {
            // id处理
            if (0 < strlen(${$k}) && 0 >= intval(${$k})) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, $k . '不合法');
            } else {
                ${$k} = (0 < strlen(${$k})) ? intval(${$k}) : null;
            }
        }
        if (is_null($id) && is_null($uid)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'uid,id不能同时为空');
        }
        if (!empty($appId)) {
            $arrConds['appId'] = $appId;
        }
        if (!is_null($id)) {
            $arrConds['id'] = $id;
        }
        if (!is_null($uid)) {
            $arrConds['uid'] = $uid;
        }

        return compact('arrConds', 'arrFields', 'appId');
    }
}
