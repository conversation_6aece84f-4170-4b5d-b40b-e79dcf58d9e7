<?php
/**************************************************************************
 *
 * Copyright (c) 2020 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   UserList.php
 * <AUTHOR>
 * @date   2020/4/2 14:17
 * @brief  【代理商或分销员】列表
 **/
class Service_Page_Api_V1_UserList
{
    public function __construct()
    {
        $this->objDsUser = new Service_Data_AFXUser();
    }

    public function execute($arrInput)
    {
        $ret        = $this->checkAndFormatParams($arrInput);
        $arrConds   = $ret['arrConds'];
        $arrFields  = $ret['arrFields'];
        $pn         = $ret['pn'];
        $rn         = $ret['rn'];
        $arrAppends = ['ORDER BY id',sprintf('LIMIT %d,%d', ($pn - 1) * $rn, $rn + 1)];
        $list       = $this->objDsUser->getListByConds($arrConds, $arrFields, null, $arrAppends);
        if (false === $list) {
            $errMsg = json_encode(compact('arrConds', 'arrFields', 'arrAppends'));
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::SELECT_ERROR, '', $errMsg);
        }
        $cnt   = count($list);
        $total = ($pn - 1) * $rn + $cnt; // 默认这个数
        if ($cnt > $rn || (0 == $cnt && $pn > 1)) {
            // 此时需要查询数据库总共多少记录
            $total = $this->objDsUser->getCntByConds($arrConds);
        }
        if (false === $total) {
            $errMsg = json_encode(compact('arrConds'));
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::SELECT_ERROR, '', $errMsg);
        }
        // 如果返回个数大于rn,则删除最后一个
        $cnt > $rn && array_pop($list);
        if ($list) {
            $list = array_map(function ($v) {
                $v['roleName'] = Afxmis_User_Authority::getRoleName($v['roleId'], $v['appId']);
                return $v;
            }, $list);
        }

        $arrOutput['total'] = $total;
        $arrOutput['list']  = $list;

        return $arrOutput;
    }

    private function checkAndFormatParams($arrInput)
    {
        // $arrConds, $arrFields
        $arrConds  = [];
        $arrFields = ['id', 'pid', 'appId', 'uid', 'nickname', 'roleId', 'status', 'updateTime']; // 展示字段

        $appId   = intval(trim(strval($arrInput['appId'])));
        $roleIds = trim(strval($arrInput['roleIds'] ?? '[]'));
        $pid     = trim(strval($arrInput['pid'] ?? ''));
        $status  = trim(strval($arrInput['status'] ?? ''));
        $pn      = trim(strval($arrInput['pn'] ?? ''));
        $rn      = trim(strval($arrInput['rn'] ?? ''));
        // if (0 >= $appId) {
        //     throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'appId不合法');
        // }
        // $arrConds['appId'] = $appId;
        $arrConds[] = '1=1';
        if (0 < $appId) {
            $arrConds['appId'] = $appId;
        }

        // 需要json_decode的值
        $keys = ['roleIds'];
        foreach ($keys as $k) {
            if (0 >= strlen(${$k})) {
                ${$k} = [];
            } else {
                ${$k} = json_decode(${$k}, true);
            }
            // json_decode 不是数组数据数据
            if (!is_array(${$k})) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, $k . '不合法');
            }
        }
        if ($roleIds) {
            $roleIds    = array_unique(array_map('intval', $roleIds));
            $arrConds[] = sprintf('role_id IN (%s)', implode(',', $roleIds));
        }
        if ($roleIds && !Afxmis_User_Authority::checkRoleIds($roleIds)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'roleIds不合法,有不存在的roleId');
        }

        // pid处理
        if (0 < strlen($pid) && 0 > intval($pid)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'pid不合法');
        } else {
            $pid = (0 < strlen($pid)) ? intval($pid) : null;
        }
        if (!is_null($pid)) {
            $arrConds['pid'] = $pid;
        }

        // status处理
        if (0 < strlen($status) && !in_array($status, array_keys(Afxmis_User_Authority::$Status))) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'status不合法');
        } else {
            $status = (0 < strlen($status)) ? intval($status) : null;
        }
        if (!is_null($status)) {
            $arrConds['status'] = $status;
        }

        if (strlen($pn) > 0) {
            $pn = intval($pn);
        } else {
            $pn = 1; // 默认1
        }
        if (strlen($rn) > 0) {
            $rn = intval($rn);
        } else {
            $rn = 20; // 默认20
        }
        // 判断页面分页
        if (0 >= $pn) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'pn不合法，必须为大于0的整数');
        }
        if (0 >= $rn || 200 < $rn) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'rn不合法，必须为大于0且不大于200的整数');
        }

        if ($arrInput['updateTime']) {
            $arrConds[] = 'update_time >= ' . $arrInput['updateTime'];
        }

        return compact('arrConds', 'arrFields', 'pn', 'rn');
    }
}
