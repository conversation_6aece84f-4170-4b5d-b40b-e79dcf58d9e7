<?php
/**
 * <AUTHOR>
 * @copyright   zybang.com
 **/
class Service_Data_AFXSaleThreshold
{
    /**
     * 表中所有字段
     */
    const  ARR_ALL_FIELDS = Dao_AFXSaleThreshold::ARR_ALL_FIELDS;

    //状态
    const STATUS_ON  = 1;//生效
    const STATUS_OFF = 2;//失效

    public static $STATUS = array(
        self::STATUS_ON => '有效',
        self::STATUS_OFF => '失效',
    );

    private $_objDaoGoods;

    public function __construct()
    {
        $this->_objDaoSaleThreshold = new Dao_AFXSaleThreshold();
    }

    //获取总数
    public function getCnt($arrConds = null)
    {
        $res = $this->_objDaoSaleThreshold->getCntByConds($arrConds);
        if ($res === false) {
            Oplib_Util_Log::warning('afxmis', __CLASS__, __FUNCTION__, '查询数据库总数失败', json_encode(['conds' => $arrConds]));
        }
        return $res;
    }

    /**
     * 列表查询，根据限制条件获取结果数组
     * @param  [array]  $arrConds  [限制条件]
     * @param  [array]  $arrFields [需要查询的字段]
     * @param  string   $order      [排序字段 id desc等]
     * @param  integer  $offset     [offset]
     * @param  integer  $limit      [limit]
     * @return array|false        返回查询的结果，连接DB失败返回false
     */
    public function getList($arrConds, $arrFields=[], $order = '', $offset = 0, $limit = 0)
    {
        if (!$arrFields){
            $arrFields = self::ARR_ALL_FIELDS;
        }
        $strAppend = '';
        if( $order ){
            $strAppend .= 'order by '. $order . ' ';
        }
        $offset = (int)$offset;
        $limit = (int)$limit;
        if($offset >= 0 && $limit > 0 ){
            $strAppend .= " limit $offset,$limit";
        }else if($limit > 0){
            $strAppend .= " limit $limit";
        }
        $arrAppends = (!empty($strAppend)) ? array($strAppend) : NULL;
        $ret = $this->_objDaoSaleThreshold->getListByConds($arrConds,$arrFields,NULL,$arrAppends);
        return $ret;
    }

    //获取规则
    public function getInfoByStId($stId, $arrFields = self::ARR_ALL_FIELDS)
    {
        $arrConds = [
            'id' => $stId,
            'status' => self::STATUS_ON,
        ];

        $ret = $this->_objDaoSaleThreshold->getRecordByConds($arrConds, $arrFields, null, ['LIMIT 1', 'for update']);
        return $ret;
    }

    //锁库存
    public function lockStock($actId, $stId)
    {
        $arrConds['actId'] = $actId;
        $arrConds['id'] = $stId;
        $arrFields = [
            '`lock_num` = `lock_num`+1',
        ];
        $result = $this->_objDaoSaleThreshold->updateByConds($arrConds, $arrFields);
        if ($result === false) {
            Oplib_Util_Log::warning('afxmis', __CLASS__, __FUNCTION__, '数据库更新失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //释放库存
    public function unlockStock($actId, $stId)
    {
        $arrConds['actId'] = $actId;
        $arrConds['id'] = $stId;
        $arrFields = [
            '`lock_num` = `lock_num`-1',
        ];
        $result = $this->_objDaoSaleThreshold->updateByConds($arrConds, $arrFields);
        if ($result === false) {
            Oplib_Util_Log::warning('afxmis', __CLASS__, __FUNCTION__, '数据库更新失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //增加购买数
    public function reduceStock($actId, $stId)
    {
        $arrConds['actId'] = $actId;
        $arrConds['id'] = $stId;
        $arrFields = [
            '`sale_num` = `sale_num`+1',
        ];
        $result = $this->_objDaoSaleThreshold->updateByConds($arrConds, $arrFields);
        if ($result === false) {
            Oplib_Util_Log::warning('afxmis', __CLASS__, __FUNCTION__, '数据库更新失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }
}
