<?php
/**
 * <AUTHOR>
 * @copyright   zybang.com
 * @brief       订单表
 **/
class Service_Data_AFXDorisOrder
{
    /**
     * 表中所有字段
     */
    const  ARR_ALL_FIELDS = Dao_AFXDorisOrder::ARR_ALL_FIELDS;

    private $_objDaoDorisLeadsDetail;

    public function __construct()
    {
        $this->_objDaoDorisOrder = new Dao_AFXDorisOrder();
    }

    /**
     * 列表查询，根据限制条件获取结果数组
     * @param  [array]  $arrConds  [限制条件]
     * @param  [array]  $arrFields [需要查询的字段]
     * @param  string   $order      [排序字段 id desc等]
     * @param  integer  $offset     [offset]
     * @param  integer  $limit      [limit]
     * @return array|false        返回查询的结果，连接DB失败返回false
     */
    public function getList($arrConds, $arrFields=[], $order = '', $group = '', $offset = 0, $limit = 0)
    {
        if (!$arrFields){
            $arrFields = self::ARR_ALL_FIELDS;
        }
        $strAppend = '';
        if( $group ){
            $strAppend .= 'group by '. $group . ' ';
        }
        if( $order ){
            $strAppend .= 'order by '. $order . ' ';
        }
        $offset = (int)$offset;
        $limit = (int)$limit;
        if($offset >= 0 && $limit > 0 ){
            $strAppend .= " limit $offset,$limit";
        }else if($limit > 0){
            $strAppend .= " limit $limit";
        }
        $arrConds[] = "sys_del_s = 0";
        $arrConds[] = "sys_del_h = 0";
        $arrAppends = (!empty($strAppend)) ? array($strAppend) : NULL;
        $ret = $this->_objDaoDorisOrder->getListByConds($arrConds,$arrFields,$arrAppends);
        return $ret;
    }

    //获取总数
    public function getCnt($arrConds = null)
    {
        $arrConds[] = "sys_del_s = 0";
        $arrConds[] = "sys_del_h = 0";
        $res = $this->_objDaoDorisOrder->getCntByConds($arrConds);
        if ($res === false) {
            Oplib_Util_Log::warning('afxmis', __CLASS__, __FUNCTION__, '查询数据库总数失败', json_encode(['conds' => $arrConds]));
        }
        return $res;
    }

    public function getCntByQuerySql($querySql)
    {
        $res = $this->_objDaoDorisOrder->getCntByQuerySql($querySql);
        if ($res === false) {
            Oplib_Util_Log::warning('afxmis', __CLASS__, __FUNCTION__, '查询数据库总数失败', json_encode(['sql' => $querySql]));
        }
        return $res;
    }

    public function getListByQuerySql($querySql)
    {
        $res = $this->_objDaoDorisOrder->getListByQuerySql($querySql);
        if ($res === false) {
            Oplib_Util_Log::warning('afxmis', __CLASS__, __FUNCTION__, '查询数据库失败', json_encode(['sql' => $querySql]));
        }
        return $res;
    }
}

