<?php
/**
 * User: <EMAIL>
 */

class Dao_AFXDorisOrder extends Afxmis_Doris
{
    const ARR_ALL_FIELDS = [
        'id',
        'appId',
        'uid',
        'actId',
        'buyerUid',
        'type',
        'orderId',
        'skuId',
        'buyTime',
        'refundTime',
        'status',
        'snapshootInfo',
        'skuInfo',
        'createTime',
        'updateTime',
        'extData',
    ];

    public function __construct()
    {
        $this->_table       = 'zhibo_laxin.ods_fenxiao_afx_order_v6';
        $this->arrFieldsMap = array(
            'id'            => 'id',
            'appId'         => 'app_id',
            'uid'           => 'uid',
            'actId'         => 'act_id',
            'buyerUid'      => 'buyer_uid',
            'type'          => 'type',
            'orderId'       => 'order_id',
            'skuId'         => 'sku_id',
            'buyTime'       => 'buy_time',
            'refundTime'    => 'refund_time',
            'status'        => 'status',
            'snapshootInfo' => 'snapshoot_info',
            'createTime'    => 'create_time',
            'skuInfo'       => 'sku_info',
            'updateTime'    => 'update_time',
            'extData'       => 'ext_data',
            'courseId'      => 'course_id'
        );

        $this->arrTypesMap = array(
            'id'            => Hk_Service_Db::TYPE_INT,
            'appId'         => Hk_Service_Db::TYPE_INT,
            'uid'           => Hk_Service_Db::TYPE_INT,
            'actId'         => Hk_Service_Db::TYPE_INT,
            'buyerUid'      => Hk_Service_Db::TYPE_INT,
            'type'          => Hk_Service_Db::TYPE_INT,
            'orderId'       => Hk_Service_Db::TYPE_INT,
            'skuId'         => Hk_Service_Db::TYPE_INT,
            'buyTime'       => Hk_Service_Db::TYPE_INT,
            'refundTime'    => Hk_Service_Db::TYPE_INT,
            'status'        => Hk_Service_Db::TYPE_INT,
            'snapshootInfo' => Hk_Service_Db::TYPE_JSON,
            'skuInfo'       => Hk_Service_Db::TYPE_JSON,
            'createTime'    => Hk_Service_Db::TYPE_INT,
            'updateTime'    => Hk_Service_Db::TYPE_INT,
            'extData'       => Hk_Service_Db::TYPE_JSON,
            'courseId'      => Hk_Service_Db::TYPE_INT,
        );
    }
}

