<?php
/**************************************************************************
 *
 * Copyright (c) 2020 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   AFXApp.php
 * <AUTHOR>
 * @date   2020/3/27 19:45
 * @brief  【代理+分销】应用方信息
 **/
class Dao_AFXApp extends Hk_Common_BaseDao
{
    const ARR_ALL_FIELDS = [
        'id',
        'name',
        'saleChannelId',
        'opName',
        'createTime',
        'updateTime',
        'roleAlias',
        'extData',
    ];

    public function __construct()
    {
        $this->_dbName  = 'zyb_yk_actplat/zhibo_fenxiao';
        $this->_logFile = Zb_Const_Db::DBLOG_ZB;
        $this->_table   = 'tblAFXApp';

        $this->arrFieldsMap = [
            'id'            => 'id',
            'name'          => 'name',
            'saleChannelId' => 'sale_channel_id',
            'opName'        => 'op_name',
            'createTime'    => 'create_time',
            'updateTime'    => 'update_time',
            'roleAlias'     => 'role_alias',
            'extData'       => 'ext_data',
        ];
        $this->arrTypesMap  = [
            'id'            => Hk_Service_Db::TYPE_INT,
            'name'          => Hk_Service_Db::TYPE_STR,
            'saleChannelId' => Hk_Service_Db::TYPE_INT,
            'roleAlias'     => Hk_Service_Db::TYPE_JSON,
            'opName'        => Hk_Service_Db::TYPE_STR,
            'createTime'    => Hk_Service_Db::TYPE_INT,
            'updateTime'    => Hk_Service_Db::TYPE_INT,
            'extData'       => Hk_Service_Db::TYPE_JSON,
        ];
    }
}