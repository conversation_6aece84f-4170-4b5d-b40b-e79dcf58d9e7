<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/8/18
 * Time: 19:42
 */

/**
 * Class Dao_AFXVideoDataNote
 * @file AFXVideoDataNote.php
 * <AUTHOR>
 * @date 2020/9/1
 */
class Dao_AFXVideoDataNote extends HK_Common_BaseDao
{
    //定义所有字段
    const ARR_ALL_FIELDS = [
        'id',
        'app_id', //应用方ID
        'agent_uid', //代理商uid
        'uid', //用户ID
        'afx_act_id', //线下分销活动ID
        'fengniao_element_id', //蜂鸟元素ID
        'note',//备注信息
        'create_time', //创建时间
    ];

    /**
     * 构造函数
     */
    public function __construct()
    {
        //设置数据库集群
        $this -> _dbName = 'zyb_yk_actplat/zhibo_fenxiao'; //集群名称

        //mysql日志文件
        $this->_logFile = Zb_Const_Db::DBLOG_ZB;

        //设置表名
        $this -> _table = 'tblAFXVideoDataNote';

        //设置字段映射
        $this -> arrFieldsMap = [
            'id' => 'id',
            'appId'=> 'app_id', //应用方ID
            'agentUid' => 'agent_uid', //代理商uid
            'uid' =>  'uid', //用户ID
            'afxActId' => 'afx_act_id', //线下分销活动ID
            'fengniaoElementId' => 'fengniao_element_id', //蜂鸟元素ID
            'note' => 'note',//备注信息
            'createTime' => 'create_time', //创建时间
        ];

        //设置字段验证
        $this -> arrTypesMap = [
            'id' => Hk_Service_Db::TYPE_INT,
            'appId'=> Hk_Service_Db::TYPE_INT,
            'agentUid' => Hk_Service_Db::TYPE_INT,
            'uid' =>  Hk_Service_Db::TYPE_INT,
            'afxActId' => Hk_Service_Db::TYPE_INT,
            'fengniaoElementId' => Hk_Service_Db::TYPE_STR,
            'note' => Hk_Service_Db::TYPE_STR,
            'createTime' => Hk_Service_Db::TYPE_INT,

        ];

    }


}