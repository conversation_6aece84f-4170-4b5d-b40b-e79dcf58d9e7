<?php
/**************************************************************************
 *
 * Copyright (c) 2020 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   AFXSaleThresholdAgent.php
 * <AUTHOR>
 * @brief 限制售卖代理商表
 **/
class Dao_AFXSaleThresholdAgent extends Hk_Common_BaseDao
{
    const ARR_ALL_FIELDS = [
        'id',
        'actId',
        'stId',
        'version',
        'agentId',
        'saleNum',
        'lockNum',
        'createTime',
        'updateTime',
    ];

    public function __construct()
    {
        $this->_dbName  = 'zyb_yk_actplat/zhibo_fenxiao';
        $this->_logFile = Zb_Const_Db::DBLOG_ZB;
        $this->_table   = 'tblAFXSaleThresholdAgent';

        $this->arrFieldsMap = [
            'id'            => 'id',
            'actId'         => 'act_id',
            'stId'          => 'st_id',
            'version'       => 'version',
            'agentId'       => 'agent_id',
            'saleNum'       => 'sale_num',
            'lockNum'       => 'lock_num',
            'createTime'    => 'create_time',
            'updateTime'    => 'update_time',
        ];
        $this->arrTypesMap  = [
            'id'            => Hk_Service_Db::TYPE_INT,
            'actId'         => Hk_Service_Db::TYPE_INT,
            'stId'          => Hk_Service_Db::TYPE_INT,
            'version'       => Hk_Service_Db::TYPE_INT,
            'agentId'       => Hk_Service_Db::TYPE_INT,
            'saleNum'       => Hk_Service_Db::TYPE_INT,
            'lockNum'       => Hk_Service_Db::TYPE_INT,
            'createTime'    => Hk_Service_Db::TYPE_INT,
            'updateTime'    => Hk_Service_Db::TYPE_INT,
        ];
    }
}

