<?php
/**************************************************************************
 *
 * Copyright (c) 2020 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   AFXRole.php
 * <AUTHOR>
 * @brief  【代理+分销】金丝雀角色
 **/
class Dao_AFXRole extends Hk_Common_BaseDao
{
    const ARR_ALL_FIELDS = [
        'id',
        'roleId',
        'name',
        'auth',
        'banAuth',
        'status',
        'createTime',
        'updateTime',
    ];

    public function __construct()
    {
        $this->_dbName  = 'zyb_yk_actplat/zhibo_fenxiao';
        $this->_logFile = Zb_Const_Db::DBLOG_ZB;
        $this->_table   = 'tblAFXRole';

        $this->arrFieldsMap = [
            'id'        => 'id',
            'roleId'    => 'role_id',
            'name'      => 'name',
            'auth'      => 'auth',
            'banAuth'   => 'ban_auth',
            'status'    => 'status',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
        ];
        $this->arrTypesMap  = [
            'id'        => Hk_Service_Db::TYPE_INT,
            'roleId'    => Hk_Service_Db::TYPE_INT,
            'name'      => Hk_Service_Db::TYPE_STR,
            'auth'      => Hk_Service_Db::TYPE_JSON,
            'banAuth'   => Hk_Service_Db::TYPE_JSON,
            'status'    => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
        ];
    }
}
