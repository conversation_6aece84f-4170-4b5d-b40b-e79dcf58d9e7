<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   Download.php
 * <AUTHOR>
 * @date   2019/12/25 16:17
 * @brief  下载工具
 **/
class Afxmis_Download
{
    /**
     * @brief  将data数组 下载保存为csv格式文件
     * <AUTHOR>
     * @date   2019/12/25 16:35
     * @param       $saveFileName 保存文件名
     * @param array $data
     * @param       $header       [['k'=> '要获取的data中的字段','v'=> '显示的首行字段值']]
     *
     *   demo:  $data = [['id'=>1,'name'=> '张三']];
     *          $header = [ ['k'=> 'id','v' => '序号'],['k'=> 'name','v'=> '姓名']]
     *  output: 序号,姓名
     *            1,张三
     */
    public static function saveAsCSV($saveFileName, array $data, array $header = [])
    {
        self::setDownloadHeader($saveFileName);
        // 输出UTF-8 BOM，不然用excel打开文件会乱码
        $output = pack('H*','EFBBBF');
        echo $output;
        if ($header) {
            // 输入字段头
            echo implode(',', array_map('Afxmis_Download::encodeCSVField', array_column($header, 'v'))) . PHP_EOL;
            $keys = array_column($header, 'k');
            foreach ($data as $kk => $v) {
                $values = [];
                foreach ($keys as $k) {
                    $values[] = self::encodeCSVField($v[$k] ?? '');
                }
                // 直接输出
                echo implode(',', $values) . PHP_EOL;
                unset($data[$kk]);
            }
        } else {
            // 不带字段头的
            foreach ($data as $kk => $v) {
                $values = array_map('Afxmis_Download::encodeCSVField', $v);
                // 直接输出
                echo implode(',', $values) . PHP_EOL;
                unset($data[$kk]);
            }
        }
        exit;
    }

    public static function encodeCSVField($value)
    {
        //字段包含特殊字符(逗号，换行符，或双引号)，必须以双引号括住
        //行内包含一个项目是空字符串，可以以双引号括住
        //字段的值包含双引号时，要双写这个双引号（就像把一个双引号当做转义符一样）
        if (strpos($value, '"') !== false) {
            $value = preg_replace('/"/', '""', $value);
            return '"' . $value . '"';
        }
        if (strpos($value, ',') !== false || strpos($value, '\n') !== false) {
            return '"' . $value . '"';
        }
        return $value;
    }

    // 设置下载header
    public static function setDownloadHeader($filename, $fileSize = 0)
    {
        //设置头信息
        //声明浏览器输出的是字节流
        header('Content-Type: application/octet-stream');
        //声明浏览器返回大小是按字节进行计算
        header('Accept-Ranges:bytes');
        if ($fileSize) {
            //告诉浏览器文件的总大小;注意是'Content-Length:' 非Accept-Length
            header('Content-Length:' . $fileSize);
        }
        $ua = $_SERVER["HTTP_USER_AGENT"] ?? '';
        //声明作为附件处理和下载后文件的名称
        if (preg_match("/MSIE/", $ua)) {
            $encodedFilename = rawurlencode($filename);
            header('Content-Disposition: attachment; filename="' . $encodedFilename . '"');
        } elseif (preg_match("/Firefox/", $ua)) {
            header('Content-Disposition: attachment; filename*="utf8\'\'' . $filename . '"');
        } else {
            header('Content-Disposition: attachment; filename="' . $filename . '"');
        }
    }
}