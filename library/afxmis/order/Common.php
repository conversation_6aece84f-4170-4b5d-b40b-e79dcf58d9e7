<?php

/**
 * 分销记录通用方法
 */
class Afxmis_Order_Common
{
    private $_userInfo  = [];
    private $_goodsType = [];
    //不发送数据appid
    private $_notSendAppList = [1, 2, 3, 6];

    //状态
    const STATUS_ON  = 10;//生效
    const STATUS_OFF = 30;//失效
    const STATUS_GONGHAI = 20;//公海数据

    public static $STATUS = array(
        self::STATUS_ON => '有效',
        self::STATUS_OFF => '失效',
        self::STATUS_GONGHAI => '公海数据',
    );

    public function sendOrderForMq($dataList = [], $conds = []){
        if (empty($dataList) && empty($conds)){
            return [];
        }

        if (empty($dataList)){
            $objOrder = new Service_Data_AFXOrder();
            $dataList = $objOrder->getList($conds);
        }

        if (!empty($dataList)){
            foreach ($dataList as $item){
                //自己的appid的数据不广播
                if (in_array($item['appId'],$this->_notSendAppList)){
                    continue;
                }

                if ($item['type'] != Service_Data_AFXOrder::TYPE_FX_11){
                    continue;
                }

                $data = $this->formatData($item);
                $this->sendToMq($data);
            }
        }

        return true;
    }

    public function formatData($data){
        $reData = [];

        if (empty($data)){
            return $reData;
        }

        $organInfo = $data['snapshootInfo']['organInfo'] ?? [];
        $agentLv2Info = $data['snapshootInfo']['roleLv2AgentInfo'] ?? [];

        if (empty($data['snapshootInfo']['organInfo']['id'])){
            list($organInfo, $agentLv1Info, $agentLv2Info) = (new Afxmis_User_Common())->getParentInfoById(0,$data['snapshootInfo']['userInfo'] );
        }

        $reData = [
            'appId'                 => (int)$data['snapshootInfo']['userInfo']['appId'] ?? 0,
            'buyerMobile'           => (string)$this->getUserInfo($data['buyerUid'])['phone'] ?? '',
            'buyerUid'              => (int)$data['buyerUid'],
            'orderId'               => (int)$data['orderId'],
            'status'                => $data['refundTime'] > 0 ? '已退款' : (string)self::$STATUS[$data['status']],
            'buyTime'               => (int)$data['buyTime'],
            'refundTime'            => (int)$data['refundTime'],
            'skuName'               => (string)$data['skuInfo']['skuName'],
            'skuid'                 => (int)$data['skuId'],
            'categoryPid'           => (string)$this->getGoodsType()['goodsCategoryRoot'][$data['skuInfo']['categoryPid']] ?? '',
            'categoryId'            => (string)$this->getGoodsType()['goodsCategoryTree'][$data['skuInfo']['categoryPid']][$data['skuInfo']['categoryId']] ?? '',
            'skuOriginPrice'        => (int)$data['skuInfo']['skuOriginPrice'],
            'price'                 => (int)$data['snapshootInfo']['orderInfo']['amountTrade'] ?? $data['skuInfo']['price'],
            'actId'                 => (int)$data['snapshootInfo']['actInfo']['id'] ?? 0,
            'actName'               => $data['snapshootInfo']['actInfo']['name'] ?? '',
            'curAgentUid'           => (int)$data['snapshootInfo']['userInfo']['uid'] ?? 0,
            'curAgentName'          => $data['snapshootInfo']['userInfo']['nickname'],
            'curAgentMobile'        => (string)$this->getUserInfo((int)$data['snapshootInfo']['userInfo']['uid'] ?? 0)['phone'] ?? '',
            'organId'               => (int)$organInfo['id'] ?? 0,
            'organName'             => $organInfo['name'] ?? '',
            'organCadre'            => $organInfo['cadre'] ?? '',
            'agentLv1Uid'           => (int)$data['snapshootInfo']['roleLv1AgentInfo']['uid'] ?? 0,
            'agentLv1Name'          => $data['snapshootInfo']['roleLv1AgentInfo']['nickname'] ?? '',
            'agentLv1Mobile'        => (string)$this->getUserInfo((int)$data['snapshootInfo']['roleLv1AgentInfo']['uid'] ?? 0)['phone'] ?? '',
            'agentLv1Cadre'         => $data['snapshootInfo']['roleLv1AgentInfo']['cadre'] ?? '',
            'agentLv2Uid'           => (int)$agentLv2Info['uid'] ?? 0,
            'agentLv2Name'          => $agentLv2Info['nickname'] ?? '',
            'agentLv2Mobile'        => (string)$this->getUserInfo((int)$agentLv2Info['uid'] ?? 0)['phone'] ?? '',
            'agentLv3Uid'           => 0,
            'agentLv3Name'          => '',
            'agentLv3Mobile'        => '',
        ];
        $currentRoleId = intval($data['snapshootInfo']['userInfo']['roleId'] ?? 0);

        if ($currentRoleId === Afxmis_User_Authority::ROLE_LV2_AGENT){
            $reData['agentLv2Uid']     = $data['snapshootInfo']['userInfo']['uid'] ?? 0;
            $reData['agentLv2Name']    = $data['snapshootInfo']['userInfo']['nickname'] ?? '';
            $reData['agentLv2Mobile']  = (string)$this->getUserInfo($reData['agentLv2Uid'])['phone'] ?? '';
        }

        if ($currentRoleId === Afxmis_User_Authority::ROLE_LV3_AGENT){
            $reData['agentLv3Uid']     = $data['snapshootInfo']['userInfo']['uid'] ?? 0;
            $reData['agentLv3Name']    = $data['snapshootInfo']['userInfo']['nickname'] ?? '';
            $reData['agentLv3Mobile']  = (string)$this->getUserInfo($reData['agentLv3Uid'])['phone'] ?? '';
        }

        return $reData;
    }

    private function getUserInfo($uid){
        if (empty($uid)){
            return [];
        }
        if (empty($this->_userInfo[$uid])){
            $ucloud = new Hk_Ds_User_Ucloud();
            $this->_userInfo[$uid] = $ucloud->getUserInfo($uid, true);
        }
        return $this->_userInfo[$uid];
    }

    private function getGoodsType(){

        if (empty($this->_goodsType)){
            $obj = new Service_Data_AFXSkuCategory();
            $this->_goodsType = $obj->getAllCategory();
        }

        return $this->_goodsType;
    }

    private function sendToMq($data){
        Qdlib_Util_MQ::sendCmd(500020, $data);
        Oplib_Util_Log::addNotice('laxin_nmq_500020_output', json_encode($data));
        return true;
    }
}
