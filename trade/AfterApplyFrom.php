<?php
/**
 * @file: AfterApplyFrom.php
 *        售后申请来源 类文件
 * @author: dds(<EMAIL>)
 * @date: 2021-10-12 17:00:07
 * @brief: 售后申请来源
 *         使用中
 */


class Sp_Dict_Trade_AfterApplyFrom
{
   //*Const
   const APP = 1;//用户 - 使用中
   const CALLCENTER = 2;//客服 - 使用中
   const SYSTEM = 3;//系统自动退款 - 使用中
   const EXPIRE = 4;//过期退 - 使用中
   const HUANXIONG = 5;//浣熊客服 - 使用中
   const DINGJICE = 6;//定级测 - 使用中
   const TUAN = 7;//拼团未成团系统退 - 使用中
   const OMS = 8;//三方平台 - 使用中
   const EMPTYADDR = 9;//地址后置 - 使用中
   const FUDAOPEIXUN = 10;//辅导培训 - 使用中
   const LPC = 11;//lpc - 使用中
   const LPCSERVER = 12;//lpcserver - 使用中
   const TOUFANG = 13;//投放 - 使用中
   const TOOLS_PLATFORM = 14;//工具平台 - 使用中
   const IMCBATCHTOOL = 15;//IMC工具平台 - 使用中
   const NT = 16;//NT服务 - 使用中

   //*Map
   public static $map = [
       self::APP => "用户",
       self::CALLCENTER => "客服",
       self::SYSTEM => "系统自动退款",
       self::EXPIRE => "过期退",
       self::HUANXIONG => "浣熊客服",
       self::DINGJICE => "定级测",
       self::TUAN => "拼团未成团系统退",
       self::OMS => "三方平台",
       self::EMPTYADDR => "地址后置",
       self::FUDAOPEIXUN => "辅导培训",
       self::LPC => "lpc",
       self::LPCSERVER => "lpcserver",
       self::TOUFANG => "投放",
       self::TOOLS_PLATFORM => "工具平台",
       self::IMCBATCHTOOL => "IMC工具平台",
       self::NT => "NT服务",
   ];

   //*KeyMap - 字符串map
   public static $keyMap = [
       self::APP => "app",
       self::CALLCENTER => "callcenter",
       self::SYSTEM => "system",
       self::EXPIRE => "expire",
       self::HUANXIONG => "huanxiong",
       self::DINGJICE => "dingjice",
       self::TUAN => "tuan",
       self::OMS => "oms",
       self::EMPTYADDR => "emptyaddr",
       self::FUDAOPEIXUN => "fudaopeixun",
       self::LPC => "lpc",
       self::LPCSERVER => "lpcserver",
       self::TOUFANG => "toufang",
       self::TOOLS_PLATFORM => "tools_platform",
       self::IMCBATCHTOOL => "imcbatchtool",
       self::NT => "nt",
   ];
}
