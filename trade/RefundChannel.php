<?php
/**
 * @file: RefundChannel.php
 *        退款退回渠道 类文件
 * @author: dds(<EMAIL>)
 * @date: 2021-10-12 17:00:07
 * @brief: 退款退回渠道
 *         使用中
 */


class Sp_Dict_Trade_RefundChannel
{
   //*Const
   const ORIGINAL = 1;//原路退回 - 使用中
   const BALANCE = 2;//退到余额 - 使用中
   const OFFLINE = 3;//线下转账 - 使用中
   const VIRTUAL = 4;//虚拟退款 - 使用中

   //*Map
   public static $map = [
       self::ORIGINAL => "原路退回",
       self::BALANCE => "退到余额",
       self::OFFLINE => "线下转账",
       self::VIRTUAL => "虚拟退款",
   ];

   //*KeyMap - 字符串map
   public static $keyMap = [
       self::ORIGINAL => "original",
       self::BALANCE => "balance",
       self::OFFLINE => "offline",
       self::VIRTUAL => "virtual",
   ];
}
