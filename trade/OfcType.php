<?php
/**
 * @file: OfcType.php
 *        履约类型 类文件
 * @author: dds(liuxia<PERSON>@zuoyebang.com)
 * @date: 2021-10-12 17:00:07
 * @brief: 不同履约场景下，细分履约输入方
 *         使用中
 */


class Sp_Dict_Trade_OfcType
{
   //*Const
   const INIT_ORDER = 1;//下单 - 使用中
   const REFUND = 2;//退款 - 使用中
   const SUPPLEMENT = 3;//补寄 - 使用中
   const EXCHANGE = 4;//换货 - 使用中
   const TRANSFER = 5;//转班 - 使用中

   //*Map
   public static $map = [
       self::INIT_ORDER => "下单",
       self::REFUND => "退款",
       self::SUPPLEMENT => "补寄",
       self::EXCHANGE => "换货",
       self::TRANSFER => "转班",
   ];

   //*KeyMap - 字符串map
   public static $keyMap = [
       self::INIT_ORDER => "init_order",
       self::REFUND => "refund",
       self::SUPPLEMENT => "supplement",
       self::EXCHANGE => "exchange",
       self::TRANSFER => "transfer",
   ];
}
