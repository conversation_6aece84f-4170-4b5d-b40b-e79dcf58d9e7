<?php
/**
 * @file: AfterStatus.php
 *        售后服务单状态 类文件
 * @author: dds(liu<PERSON><PERSON>@zuoyebang.com)
 * @date: 2021-10-12 17:00:07
 * @brief: 售后服务单所具有的直接状态
 *         使用中
 */


class Sp_Dict_Trade_AfterStatus
{
   //*Const
   const TO_CHECK = 1;//待审核 - 使用中
   const DOING = 2;//进行中 - 使用中
   const DONE = 3;//已完成 - 使用中
   const CLOSE = 4;//已关闭 - 使用中

   //*Map
   public static $map = [
       self::TO_CHECK => "待审核",
       self::DOING => "进行中",
       self::DONE => "已完成",
       self::CLOSE => "已关闭",
   ];

   //*KeyMap - 字符串map
   public static $keyMap = [
       self::TO_CHECK => "to_check",
       self::DOING => "doing",
       self::DONE => "done",
       self::CLOSE => "close",
   ];
}
