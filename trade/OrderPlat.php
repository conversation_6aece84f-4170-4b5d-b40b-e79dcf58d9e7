<?php
/**
 * @file: OrderPlat.php
 *        下单平台来源 类文件
 * @author: dds(<EMAIL>)
 * @date: 2021-10-12 17:00:07
 * @brief: 标记不同的下单流量来源的具体应用平台
 *         使用中
 */


class Sp_Dict_Trade_OrderPlat
{
   //*Const
   const ZYB = 1;//作业帮用户端 - 使用中
   const YIKE = 2;//作业帮直播课用户端 - 使用中
   const LARK = 3;//客服后台 - 使用中
   const TOUFANG = 4;//端外投放平台 - 使用中
   const OMS = 5;//zoms - 使用中
   const MOFANG = 6;//魔方 - 使用中
   const FENGNIAO = 8;//蜂鸟 - 使用中
   const BFKT = 7;//不凡课堂 - 使用中
   const KOUSUAN = 9;//作业帮口算 - 使用中
   const IM = 10;//智能IM系统 - 使用中

   //*Map
   public static $map = [
       self::ZYB => "作业帮用户端",
       self::YIKE => "作业帮直播课用户端",
       self::LARK => "客服后台",
       self::TOUFANG => "端外投放平台",
       self::OMS => "zoms后台",
       self::MOFANG => "魔方建站平台",
       self::FENGNIAO => "蜂鸟建站平台",
       self::BFKT => "不凡课堂用户端",
       self::KOUSUAN => "作业帮口算用户端",
       self::IM => "智能IM系统",
   ];

   //*KeyMap - 字符串map
   public static $keyMap = [
       self::ZYB => "zyb",
       self::YIKE => "yike",
       self::LARK => "lark",
       self::TOUFANG => "toufang",
       self::OMS => "oms",
       self::MOFANG => "mofang",
       self::FENGNIAO => "fengniao",
       self::BFKT => "bfkt",
       self::KOUSUAN => "kousuan",
       self::IM => "im",
   ];
}
