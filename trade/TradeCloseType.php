<?php
/**
 * @file: TradeCloseType.php
 *        交易关单类型 类文件
 * @author: dds(liu<PERSON><PERSON>@zuoyebang.com)
 * @date: 2021-10-12 17:00:07
 * @brief: 未支付订单更新为已取消状态的具体关单触发条件
 *         使用中
 */


class Sp_Dict_Trade_TradeCloseType
{
   //*Const
   const USER = 0;//用户关单 - 使用中
   const ATTEMPT = 1;//下单失败关单 - 使用中
   const EXPIRED = 2;//过期关单 - 使用中
   const BUSINESS = 3;//内部业务方关单 - 使用中

   //*Map
   public static $map = [
       self::USER => "用户关单",
       self::ATTEMPT => "下单失败关单",
       self::EXPIRED => "过期关单",
       self::BUSINESS => "内部业务方关单",
   ];

   //*KeyMap - 字符串map
   public static $keyMap = [
       self::USER => "user",
       self::ATTEMPT => "attempt",
       self::EXPIRED => "expired",
       self::BUSINESS => "business",
   ];
}
