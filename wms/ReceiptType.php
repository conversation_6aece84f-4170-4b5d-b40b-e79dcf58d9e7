<?php
/**
 * @file: ReceiptType.php
 *        库存流水单据类型 类文件
 * @author: dds(<EMAIL>)
 * @date: 2021-10-12 17:00:07
 * @brief: 库存流水单据类型定义
 *         使用中
 */


class Sp_Dict_Wms_ReceiptType
{
   //*Const
   const CGRK = 101;//采购入库 - 使用中
   const SDRK = 102;//手动入库 - 使用中
   const PYRK = 103;//盘盈入库 - 使用中
   const DBRK = 104;//调拨入库 - 使用中
   const XTRK = 105;//销退入库 - 使用中
   const JQRK = 106;//拒签入库 - 使用中
   const SDCK = 107;//手动出库 - 使用中
   const PKCK = 108;//盘亏出库 - 使用中
   const CNBS = 109;//仓内报损 - 使用中
   const THBS = 110;//退货报损 - 使用中
   const DBCK = 111;//调拨出库 - 使用中
   const FHCK = 112;//发货出库 - 使用中
   const BMLY = 113;//部门领用 - 使用中
   const WLLY = 114;//物流履约单 - 使用中
   const WLSC = 115;//物流生产单 - 使用中
   const RGJHD = 116;//人工计划单 - 使用中
   const KCCSHD = 117;//库存初始化单 - 使用中
   const TGCK = 118;//退供出库 - 使用中
   const JYDD = 201;//交易订单 - 使用中
   const CPTH = 119;//仓配退货 - 使用中
   const THCK = 120;//退货出库 - 使用中

   //*Map
   public static $map = [
       self::CGRK => "采购入库",
       self::SDRK => "手动入库",
       self::PYRK => "盘盈入库",
       self::DBRK => "调拨入库",
       self::XTRK => "销退入库",
       self::JQRK => "拒签入库",
       self::SDCK => "手动出库",
       self::PKCK => "盘亏出库",
       self::CNBS => "仓内报损",
       self::THBS => "退货报损",
       self::DBCK => "调拨出库",
       self::FHCK => "发货出库",
       self::BMLY => "部门领用",
       self::WLLY => "物流履约单",
       self::WLSC => "物流生产单",
       self::RGJHD => "人工计划单",
       self::KCCSHD => "库存初始化单",
       self::TGCK => "退供出库",
       self::JYDD => "交易订单",
       self::CPTH => "仓配退货",
       self::THCK => "退货出库",
   ];

   //*KeyMap - 字符串map
   public static $keyMap = [
       self::CGRK => "cgrk",
       self::SDRK => "sdrk",
       self::PYRK => "pyrk",
       self::DBRK => "dbrk",
       self::XTRK => "xtrk",
       self::JQRK => "jqrk",
       self::SDCK => "sdck",
       self::PKCK => "pkck",
       self::CNBS => "cnbs",
       self::THBS => "thbs",
       self::DBCK => "dbck",
       self::FHCK => "fhck",
       self::BMLY => "bmly",
       self::WLLY => "wlly",
       self::WLSC => "wlsc",
       self::RGJHD => "rgjhd",
       self::KCCSHD => "kccshd",
       self::TGCK => "tgck",
       self::JYDD => "jydd",
       self::CPTH => "cpth",
       self::THCK => "thck",
   ];
}
