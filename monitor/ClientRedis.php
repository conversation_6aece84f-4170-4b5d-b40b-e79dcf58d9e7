<?php
/**
 * Created by PhpStorm.
 * @file ClientRedis.php
 * <AUTHOR>
 * @date 19-6-4
 * @version
 * @brief
 *
 **/


class Qdlib_Monitor_ClientRedis implements Qdlib_Monitor_ClientInterface
{
    private $namespace = '';
    private $listSize = 0;
    private $connection = NULL;
    private $expire = 1;
    private $len = 0;
    private $timings = [];
    private $memoryProfiles = [];


    public function __construct($namespace = '')
    {
        if (isset(Qdlib_Monitor_Const_RecorderMap::$namespaceConf[$namespace])) {
            $this->namespace = (string)$namespace;
            $this->connection = Qdlib_Util_Cache::getQudaoRedis();
            $this->listSize = Qdlib_Monitor_Const_RecorderMap::$namespaceConf[$namespace]['len'];
            $this->expire = Qdlib_Monitor_Const_RecorderMap::$namespaceConf[$namespace]['expire'];
            $this->len = intval($this->listSize) - intval($this->getLen());
        };
    }

    /**
     * increments the key by 1
     *
     * @param string $key
     * @param float $sampleRate
     * @param array $tags
     */
    public function increment($key, $sampleRate = 1.0, $tags = [])
    {
        if (!$this->connection && $this->len > 0 && $this->len--) {
            return false;
        }

        $value = ['key' => $key, 'sampleRate' => $sampleRate, 'tags' => $tags, 'method' => 'increment'];
        return $this->connection->lpush($this->namespace, json_encode($value));
    }


    public function clear()
    {
        if (!$this->connection) {
            return false;
        }
        return $this->connection->del($this->namespace);

    }


    public function getLastMsg()
    {
        if (!$this->connection) {
            return false;
        }
        return $this->connection->rpop($this->namespace);

    }

    public function getLen()
    {
        if (!$this->connection) {
            return false;
        }
        return $this->connection->llen($this->namespace);
    }

    /**
     * 消耗
     * decrements the key by 1
     *
     * @param string $key
     * @param float $sampleRate
     * @param array $tags
     */
    public function decrement($key, $sampleRate = 1.0, $tags = [])
    {
        if (!$this->connection && $this->len > 0 && $this->len--) {
            return false;
        }

        $value = ['key' => $key, 'sampleRate' => $sampleRate, 'tags' => $tags, 'method' => 'decrement'];
        return $this->connection->lpush($this->namespace, json_encode($value));
    }

    /**
     * sends a count to statsd
     *
     * @param string $key
     * @param int|float $value
     * @param float $sampleRate
     * @param array $tags
     */
    public function count($key, $value, $sampleRate = 1.0, $tags = [])
    {
        if (!$this->connection && $this->len > 0 && $this->len--) {
            return false;
        }

        $cvalue = ['key' => $key, 'value' => $value, 'sampleRate' => $sampleRate, 'tags' => $tags, 'method' => 'count'];
        return $this->connection->lpush($this->namespace, json_encode($cvalue));
    }

    /**
     * sends a timing to statsd (in ms)
     *
     * @param string $key
     * @param int $value the timing in ms
     * @param float $sampleRate
     * @param array $tags
     */
    public function timing($key, $value, $sampleRate = 1.0, $tags = [])
    {
        if (!$this->connection && $this->len > 0 && $this->len--) {
            return false;
        }

        $cvalue = ['key' => $key, 'value' => $value, 'sampleRate' => $sampleRate, 'tags' => $tags, 'method' => 'timing'];
        return $this->connection->lpush($this->namespace, json_encode($cvalue));
    }

    /**
     * starts the timing for a key
     *
     * @param string $key
     */
    public function startTiming($key)
    {
        $this->timings[$key] = gettimeofday(true);
    }

    /**
     * ends the timing for a key and sends it to statsd
     *
     * @param string $key
     * @param float $sampleRate
     * @param array $tags
     *
     * @return float|null
     */
    public function endTiming($key, $sampleRate = 1.0, $tags = [])
    {
        $end = gettimeofday(true);

        if (isset($this->timings[$key])) {
            $timing = ($end - $this->timings[$key]) * 1000;
            $this->timing($key, $timing, $sampleRate, $tags);
            unset($this->timings[$key]);

            return $timing;
        }

        return null;
    }

    /**
     * start memory "profiling"
     *
     * @param string $key
     */
    public function startMemoryProfile($key)
    {
        $this->memoryProfiles[$key] = memory_get_usage();
    }

    /**
     * ends the memory profiling and sends the value to the server
     *
     * @param string $key
     * @param float $sampleRate
     * @param array $tags
     */
    public function endMemoryProfile($key, $sampleRate = 1.0, $tags = [])
    {
        $end = memory_get_usage();

        if (array_key_exists($key, $this->memoryProfiles)) {
            $memory = ($end - $this->memoryProfiles[$key]);
            $this->memory($key, $memory, $sampleRate, $tags);

            unset($this->memoryProfiles[$key]);
        }
    }

    /**
     * report memory usage to statsd. if memory was not given report peak usage
     *
     * @param string $key
     * @param int $memory
     * @param float $sampleRate
     * @param array $tags
     */
    public function memory($key, $memory = null, $sampleRate = 1.0, $tags = [])
    {
        if ($memory === null) {
            $memory = memory_get_peak_usage();
        }

        $this->count($key, $memory, $sampleRate, $tags);
    }

    /**
     * executes a Closure and records it's execution time and sends it to statsd
     * returns the value the Closure returned
     *
     * @param string $key
     * @param \Closure $block
     * @param float $sampleRate
     * @param array $tags
     *
     * @return mixed
     */
    public function time($key, \Closure $block, $sampleRate = 1.0, $tags = [])
    {
        return false;
    }

    /**
     * sends a gauge, an arbitrary value to StatsD
     *
     * @param string $key
     * @param string|int $value
     * @param array $tags
     */
    public function gauge($key, $value, $tags = [])
    {
        if (!$this->connection && $this->len > 0 && $this->len--) {
            return false;
        }

        $cvalue = ['key' => $key, 'value' => $value, 'tags' => $tags, 'method' => 'gauge'];
        return $this->connection->lpush($this->namespace, json_encode($cvalue));
    }

    /**
     * sends a set member
     *
     * @param string $key
     * @param int $value
     * @param array $tags
     */
    public function set($key, $value, $tags = [])
    {
        if (!$this->connection && $this->len > 0 && $this->len--) {
            return false;
        }

        $cvalue = ['key' => $key, 'value' => $value, 'tags' => $tags, 'method' => 'set'];
        return $this->connection->lpush($this->namespace, json_encode($cvalue));
    }

    /**
     * changes the global key namespace
     *
     * @param string $namespace
     */
    public function setNamespace($namespace)
    {

        $this->namespace = (string)$namespace;
    }

    /**
     * gets the global key namespace
     *
     * @return string
     */
    public function getNamespace()
    {
        return $this->namespace;
    }

    /**
     * is batch processing running?
     *
     * @return boolean
     */
    public function isBatch()
    {
        return false;
    }

    /**
     * start batch-send-recording
     */
    public function startBatch()
    {
        return false;
    }

    /**
     * ends batch-send-recording and sends the recorded messages to the connection
     */
    public function endBatch()
    {
        return false;
    }

    /**
     * stops batch-recording and resets the batch
     */
    public function cancelBatch()
    {
        return false;
    }
}