FROM image-docker.zuoyebang.cc/base/odp:1.8

#RUN apt update && apt install -y file

ARG APP_NAME
ENV APP_NAME $APP_NAME

#COPY ./library/phplibcommon/ $HOMEWORK_LIBRARY/
#COPY ./library/phplibcommon/conf/  $HOMEWORK/conf/
#COPY ./library/phplibcommon/hk/conf $HOMEWORK/conf/hk
#COPY ./library/phplibcommon/saf/conf/ $HOMEWORK/conf/

COPY ./library/zb $HOMEWORK_LIBRARY/zb
COPY ./library/zb/conf  $HOMEWORK/conf/zb

COPY ./library/zhibo $HOMEWORK_LIBRARY/zhibo
COPY ./library/dict $HOMEWORK_LIBRARY/sp/dict

COPY ./library/oplib $HOMEWORK_LIBRARY/oplib

COPY ./library/hkzb $HOMEWORK_LIBRARY/hkzb
COPY ./library/hkzb/conf $HOMEWORK/conf/hkzb

COPY ./library/qdlib $HOMEWORK_LIBRARY/qdlib

COPY ./ $HOMEWORK/app/${APP_NAME}/
COPY ./index.php $HOMEWORK/webroot/${APP_NAME}/index.php
COPY ./conf $HOMEWORK/conf/app/${APP_NAME}/

COPY ./library/phplibcommon/ $HOMEWORK_LIBRARY/
COPY ./library/phplibcommon/conf/idc.conf $HOMEWORK/conf/idc.conf
COPY ./library/phplibcommon/conf/log.conf $HOMEWORK/conf/log.conf

