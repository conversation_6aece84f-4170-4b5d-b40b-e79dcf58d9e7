<?php

class Zb_Service_Stock_Archimedes {

    const RAL_SERVICE_ARCHIMEDES = "archimedes";
    const ARCHIMEDES_COMMON_CHANNEL_ID = 99;
    //TCC 统一key 有效时间
    const TCC_EXPIRE_TIME = 43200;
    const MAX_TRY_TIMES = 2;


    public static function createEntityStock($arrInput) {
        //TODO
    }

    public static function updateEntityStock($arrInput) {
        //TODO
    }

    public static function getMarkUseArchimedesKey($tccId) {
        return "dak_stock_tcc_use_archimedes_" . $tccId;
    }

    public static function tryWithReturnKey($tccId){
        return "dak_stock_tcc_use_archimedes_try_with_return_" . $tccId;
    }

    public static function requestWithTry($arrParams, $arrHeader) {
        $tryTimes = 0;
        $data = [];
        $paramsJson = json_encode($arrParams);
        $headerJson = json_encode($arrHeader);
        while ($tryTimes < self::MAX_TRY_TIMES) {
            $data = Zb_Util_ZbServiceTools::post(self::RAL_SERVICE_ARCHIMEDES, $arrParams, $arrHeader);
            //出现网络失败重试一次
            if ($data['errNo'] != Zb_Const_ServiceError::NETWORK_ERROR) {
                break;
            }
            Bd_Log::warning("rpc call archimedes Try {$tryTimes} Params:{$paramsJson} Header:{$headerJson}");
            $tryTimes++;
        }
        return $data;
    }

    /**
     * @param $tccId
     * @param $objRedis
     * @return int
     */
    public static function tccIsUseArchimedes($tccId, $objRedis = null) {
        if (empty($objRedis)) {
            $objRedis = Hk_Service_RedisClient::getInstance("zbsell");
        }
        $key = self::getMarkUseArchimedesKey($tccId);
        $val = $objRedis->get($key);
        if (null === $val) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::NETWORK_ERROR,"redis connect failed redisKey：{$key}");
        }
        return $val;
    }

    public static function tccTryIsWithReturn($tccId, $objRedis = null) {
        if (empty($objRedis)) {
            $objRedis = Hk_Service_RedisClient::getInstance("zbsell");
        }
        $key = self::tryWithReturnKey($tccId);
        $val = $objRedis->get($key);
        if (null === $val) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::NETWORK_ERROR,"redis connect failed redisKey：{$key}");
        }
        return $val == 1;
    }

    /**
     * @param $tccId
     * @param $val
     * @param $objRedis
     */
    public static function logTccIsUseArchimedes($tccId, $val, $objRedis = null) {
        if (empty($objRedis)) {
            $objRedis = Hk_Service_RedisClient::getInstance("zbsell");
        }
        $key = self::getMarkUseArchimedesKey($tccId);
        $res = $objRedis->set($key, $val, self::TCC_EXPIRE_TIME);
        if (null === $res) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::NETWORK_ERROR,"redis connect failed redisKey：{$key}");
        }
    }

    /**
     * 记录 try 的时候同时做了退货操作
     * @param $tccId
     * @param $objRedis
     */
    public static function logTccTryWithReturn($tccId, $objRedis = null){
        if (empty($objRedis)) {
            $objRedis = Hk_Service_RedisClient::getInstance("zbsell");
        }
        $key = self::tryWithReturnKey($tccId);
        $res = $objRedis->set($key, 1, self::TCC_EXPIRE_TIME);
        if (null === $res) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::NETWORK_ERROR,"redis connect failed redisKey：{$key}");
        }
    }

    public static function getEntityStock($arrInput) {
        $arrHeader = [
            "pathinfo" => "/archimedes/gateway/good/stock/list",
            "refer" => $_SERVER['REQUEST_URI'],
        ];
        $channelId = isset($arrInput['channelId']) ? intval($arrInput['channelId']) : self::ARCHIMEDES_COMMON_CHANNEL_ID;
        $entityIds = $arrInput['entityIds'];
        $arrParams = [
            'channelId' => $channelId,
            'entityIdList' => json_encode($entityIds)
        ];
        $data = Zb_Service_Stock_Archimedes::requestWithTry($arrParams, $arrHeader);
        return $data;
    }

    /**
     * @param $arrInput
     * @param bool $isTransfer 是否是转班
     * @return array
     */
    public static function tccTryEntityStock($arrInput, $isTransfer = false) {
        $arrHeader = [
            "pathinfo" => "/archimedes/sell/trade/try",
            "refer" => $_SERVER['REQUEST_URI'],
        ];
        if ($isTransfer) {
            $arrHeader['pathinfo'] = '/archimedes/sell/trade/transfer/try';
        }
        $txId = strval($arrInput['uniqueTccId']);
        $entityList = is_array($arrInput['entityList']) && !empty($arrInput['entityList']) ? $arrInput['entityList'] : json_decode($arrInput['entityList'], true);
        $channelId = isset($arrInput['channelId']) ? intval($arrInput['channelId']) : 0;
        $lockEntityList = [];
        foreach ($entityList as $item) {
            $lockEntityList[] = [
                'entityId' => intval($item['entityId']),
                'cnt' => intval($item['cnt']),
                'channelId' => intval($item['channelId'])
            ];
        }
        $arrParams = [
            'txId' => $txId,
            'entityList' => json_encode($lockEntityList),
            'channelId' => $channelId,
            'defaultChannelId' => $arrInput['defaultSaleChannelId'],
        ];
        return Zb_Service_Stock_Archimedes::requestWithTry($arrParams, $arrHeader);
    }

    public static function tccPureTryEntityStock($arrInput) {
        $arrHeader = [
            "pathinfo" => "/archimedes/sell/trade/pure/try",
            "refer" => $_SERVER['REQUEST_URI'],
        ];
        $entityList = is_array($arrInput['entityList']) && !empty($arrInput['entityList']) ? $arrInput['entityList'] : json_decode($arrInput['entityList'], true);
        $channelId = isset($arrInput['channelId']) ? intval($arrInput['channelId']) : 0;
        $lockEntityList = [];
        foreach ($entityList as $item) {
            $lockEntityList[] = [
                'entityId' => intval($item['entityId']),
                'cnt' => intval($item['cnt']),
                'channelId' => intval($item['channelId'])
            ];
        }
        $txId = strval($arrInput['uniqueTccId']);
        $arrParams = [
            'txId' => $txId,
            'entityList' => json_encode($lockEntityList),
            'channelId' => $channelId,
            'defaultChannelId' => $arrInput['defaultSaleChannelId']
        ];
        return  Zb_Service_Stock_Archimedes::requestWithTry($arrParams, $arrHeader);
    }

    public static function tccConfirmEntityStock($arrInput) {
        $arrHeader = [
            "pathinfo" => "/archimedes/sell/trade/confirm",
            "refer" => $_SERVER['REQUEST_URI'],
        ];
        $arrParams = [
            'txId' => strval($arrInput['uniqueTccId'])
        ];
        return Zb_Service_Stock_Archimedes::requestWithTry($arrParams, $arrHeader);
    }

    public static function tccPureConfirmEntityStock($arrInput) {
        $arrHeader = [
            "pathinfo" => "/archimedes/sell/trade/pure/confirm",
            "refer" => $_SERVER['REQUEST_URI'],
        ];
        $arrParams = [
            'txId' => strval($arrInput['uniqueTccId'])
        ];
        return Zb_Service_Stock_Archimedes::requestWithTry($arrParams, $arrHeader);;
    }

    public static function tccCancelEntityStock($arrInput) {
        $arrHeader = [
            "pathinfo" => "/archimedes/sell/trade/cancel",
            "refer" => $_SERVER['REQUEST_URI'],
        ];
        $arrParams = [
            'txId' => strval($arrInput['uniqueTccId'])
        ];
        return Zb_Service_Stock_Archimedes::requestWithTry($arrParams, $arrHeader);
    }

    public static function tccPureCancelEntityStock($arrInput) {
        $arrHeader = [
            "pathinfo" => "/archimedes/sell/trade/pure/cancel",
            "refer" => $_SERVER['REQUEST_URI'],
        ];
        $arrParams = [
            'txId' => strval($arrInput['uniqueTccId'])
        ];
        return Zb_Service_Stock_Archimedes::requestWithTry($arrParams, $arrHeader);
    }

    /**
     * 直接退库存操作 没有cancel操作
     * @param $arrInput
     * @return array
     */
    public static function returnEntityStock($arrInput) {
        $arrHeader = [
            "pathinfo" => "/archimedes/sell/trade/return",
            "refer" => $_SERVER['REQUEST_URI'],
        ];
        $returnEntityList = $arrInput['entityList'];
        $arrParams = [
            'txId' => strval($arrInput['uniqueTccId']),
            'entityList' => json_encode($returnEntityList)
        ];
        $data = Zb_Service_Stock_Archimedes::requestWithTry($arrParams, $arrHeader);
        return $data;
    }

    /**
     * 退库存 try 操作，confirm阶段完成操作
     * @param $arrInput
     * @return array
     */
    public static function tryReturnEntityStock($arrInput) {
        $arrHeader = [
            "pathinfo" => "/archimedes/sell/trade/try/return",
            "refer" => $_SERVER['REQUEST_URI'],
        ];
        $returnEntityList = $arrInput['entityList'];
        $arrParams = [
            'txId' => strval($arrInput['uniqueTccId']),
            'entityList' => json_encode($returnEntityList)
        ];
        $data = Zb_Service_Stock_Archimedes::requestWithTry($arrParams, $arrHeader);
        return $data;
    }

    /**
     * 退库存 try 操作，confirm阶段完成操作
     * @param $arrInput
     * @return array
     */
    public static function tryPureReturnEntityStock($arrInput) {
        $arrHeader = [
            "pathinfo" => "/archimedes/sell/trade/try/pure/return",
            "refer" => $_SERVER['REQUEST_URI'],
        ];
        $returnEntityList = $arrInput['entityList'];
        $arrParams = [
            'txId' => strval($arrInput['uniqueTccId']),
            'entityList' => json_encode($returnEntityList)
        ];
        return Zb_Service_Stock_Archimedes::requestWithTry($arrParams, $arrHeader);
    }

    /**
     *
     * @param $arrInput
     * @return array
     */
    public static function confirmReturnEntityStock($arrInput) {
        $arrHeader = [
            "pathinfo" => "/archimedes/sell/trade/confirm/return",
            "refer" => $_SERVER['REQUEST_URI'],
        ];
        $arrParams = [
            'txId' => strval($arrInput['uniqueTccId']),
        ];
        $data = Zb_Service_Stock_Archimedes::requestWithTry($arrParams, $arrHeader);
        return $data;
    }

    /**
     *
     * @param $arrInput
     * @return array
     */
    public static function confirmPureReturnEntityStock($arrInput) {
        $arrHeader = [
            "pathinfo" => "/archimedes/sell/trade/confirm/pure/return",
            "refer" => $_SERVER['REQUEST_URI'],
        ];
        $arrParams = [
            'txId' => strval($arrInput['uniqueTccId']),
        ];
        return Zb_Service_Stock_Archimedes::requestWithTry($arrParams, $arrHeader);
    }

    /**
     *
     * @param $arrInput
     * @return array
     */
    public static function cancelReturnEntityStock($arrInput) {
        $arrHeader = [
            "pathinfo" => "/archimedes/sell/trade/cancel/return",
            "refer" => $_SERVER['REQUEST_URI'],
        ];
        $arrParams = [
            'txId' => strval($arrInput['uniqueTccId']),
        ];
        $data = Zb_Service_Stock_Archimedes::requestWithTry($arrParams, $arrHeader);
        return $data;
    }

    /**
     *
     * @param $arrInput
     * @return array
     */
    public static function cancelPureReturnEntityStock($arrInput) {
        $arrHeader = [
            "pathinfo" => "/archimedes/sell/trade/cancel/pure/return",
            "refer" => $_SERVER['REQUEST_URI'],
        ];
        $arrParams = [
            'txId' => strval($arrInput['uniqueTccId']),
        ];
        return Zb_Service_Stock_Archimedes::requestWithTry($arrParams, $arrHeader);
    }

    /**
     * 商城只支持 Try
     * @param $inParams
     * @return array
     * @throws Exception
     */
    public static function tryAdapter($inParams) {
        $lockStockType = $inParams['lockStockType'];
        //下单
        if ($lockStockType == Zb_Const_Dak::LOCK_STOCK_COMMON_TYPE) {
            $tryEntityList = [];
            $typeEntityList = $inParams['typeEntityList'];
            $paramChannelId = $inParams['channelId'];
            foreach ($typeEntityList as $item) {
                $itemCnt = $item['cnt'];
                $isTry = $itemCnt > 0;
                $itemChannelId = isset($item['channelId']) ? $item['channelId'] : $paramChannelId;
                $entityItem = [
                    'entityId' => $item['entityId'],
                    'cnt' => abs($itemCnt),
                    'channelId' => intval($itemChannelId)
                ];
                if ($isTry) {
                    $paramChannelId = $itemChannelId;
                    $tryEntityList[] = $entityItem;
                }
            }
            $tccId = $inParams['uniqueTccId'];
            $tryTccId = strval($tccId);
            $tryInput = [
                'uniqueTccId' => $tryTccId,
                'channelId' => $paramChannelId,
                'entityList' => $tryEntityList,
                'defaultSaleChannelId' => $inParams['defaultSaleChannelId'],
            ];
            $tryResult = Zb_Service_Stock_Archimedes::tccPureTryEntityStock($tryInput);
            Bd_Log::addNotice("Archimedes tryAdapter Transfer_Try_entityStockReturnResult", "content:[" . json_encode($tryResult) . "]");
            return $tryResult;
        } else if ($lockStockType == Zb_Const_Dak::LOCK_STOCK_REFUND_TYPE) { //退款
            $typeEntityList = $inParams['typeEntityList'];
            $returnEntityList = [];
            $paramChannelId = $inParams['channelId'];
            foreach ($typeEntityList as $item) {
                $itemChannelId = isset($item['channelId']) ? $item['channelId'] : $paramChannelId;
                $returnEntityList[] = [
                    'entityId' => $item['entityId'],
                    'cnt' => abs($item['cnt']),
                    'channelId' => intval($itemChannelId)
                ];
            }
            $returnTccId = intval($inParams['uniqueTccId']);
            $input = [
                'uniqueTccId' => $returnTccId,
                'entityList' => $returnEntityList
            ];
            $returnResult = Zb_Service_Stock_Archimedes::tryPureReturnEntityStock($input);
            Bd_Log::addNotice("Archimedes Return_entityStockReturnResult", "content:[" . json_encode($returnResult) . "]");
            return $returnResult;
        } else {
            throw new Exception("lockStockType 参数错误", Zb_Const_ServiceError::PARAM_ERROR);
        }
    }

    /**
     * lockStockType 1 => 普通锁定 2 => 退款锁定 3 => 转班 [优先普通库存转班，如普通满班 进行预留库存转班]
     * @param $inParams
     * @return mixed
     * @throws Exception
     */
    public static function zbStockTryAdapter($inParams) {
        $lockStockType = $inParams['lockStockType'];
        self::logTccIsUseArchimedes($inParams['uniqueTccId'], $lockStockType);
        if ($lockStockType == Zb_Const_Dak::LOCK_STOCK_COMMON_TYPE || $lockStockType == Zb_Const_Dak::LOCK_STOCK_RESERVE_TYPE) {
            //下单商品
            $tryEntityList = [];
            //退款商品
            $returnEntityList = [];
            $typeEntityList = $inParams['typeEntityList'];
            $paramChannelId = $inParams['channelId'];
            foreach ($typeEntityList as $item) {
                $itemCnt = $item['cnt'];
                $isTry = $itemCnt > 0;
                $itemChannelId = isset($item['channelId']) ? $item['channelId'] : $paramChannelId;
                $entityItem = [
                    'entityId' => $item['entityId'],
                    'cnt' => abs($itemCnt),
                    'channelId' => intval($itemChannelId)
                ];
                if ($isTry) {
                    //下单只有一个渠道信息
                    //转班 会有渠道信息吗
                    //archimedes 支持 EntityList 传入渠道ID
                    $paramChannelId = $itemChannelId;
                    $tryEntityList[] = $entityItem;
                } else {
                    $returnEntityList[] = $entityItem;
                }
            }
            $tccId = $inParams['uniqueTccId'];
            $tryTccId = $tccId;
            $tryInput = [
                'uniqueTccId' => $tryTccId,
                'channelId' => $paramChannelId,
                'entityList' => $tryEntityList,
                'defaultSaleChannelId' => $inParams['defaultSaleChannelId'],
            ];
            //转班使用预留库存
            $isTransfer = $lockStockType == Zb_Const_Dak::LOCK_STOCK_RESERVE_TYPE;
            $tryResult = Zb_Service_Stock_Archimedes::tccTryEntityStock($tryInput, $isTransfer);
            Bd_Log::addNotice("Archimedes Transfer_Try_entityStockReturnResult", "content:[" . json_encode($tryResult) . "]");
            if ($tryResult['errNo'] == 0) {
                //下单成功之后做退款逻辑
                if (count($returnEntityList) > 0) {
                    //同步archimedes tcc-id 会幂等，生成新的TCC ID
                    $returnTccId = self::getTccId($tccId, 2);
                    $returnInput = [
                        'uniqueTccId' => $returnTccId,
                        'entityList' => $returnEntityList
                    ];
                    $returnResult = Zb_Service_Stock_Archimedes::tryReturnEntityStock($returnInput);
                    Bd_Log::addNotice("Archimedes Transfer_Return_entityStockReturnResult", "content:[" . json_encode($returnResult) . "]");
                    self::logTccTryWithReturn($tccId);
                }
            }
            return $tryResult;
        } elseif ($lockStockType == Zb_Const_Dak::LOCK_STOCK_REFUND_TYPE) {
            $typeEntityList = $inParams['typeEntityList'];
            $returnEntityList = [];
            $paramChannelId = $inParams['channelId'];
            foreach ($typeEntityList as $item) {
                $itemChannelId = isset($item['channelId']) ? $item['channelId'] : $paramChannelId;
                $returnEntityList[] = [
                    'entityId' => $item['entityId'],
                    'cnt' => abs($item['cnt']),
                    'channelId' => intval($itemChannelId)
                ];
            }
            $returnTccId = $inParams['uniqueTccId'];
            $input = [
                'uniqueTccId' => $returnTccId,
                'entityList' => $returnEntityList
            ];
            $returnResult = Zb_Service_Stock_Archimedes::tryReturnEntityStock($input);
            Bd_Log::addNotice("Archimedes Return_entityStockReturnResult", "content:[" . json_encode($returnResult) . "]");
            return $returnResult;
        } else {
            throw new Exception("lockStockType 参数错误", Zb_Const_ServiceError::PARAM_ERROR);
        }
    }

    public static function zbStockConfirmAdapter($inParams, $lockType) {
        $tccId = intval($inParams['uniqueTccId']);
        $inParams = [
            'uniqueTccId' => $tccId,
        ];
        if ($lockType == Zb_Const_Dak::LOCK_STOCK_COMMON_TYPE || $lockType == Zb_Const_Dak::LOCK_STOCK_RESERVE_TYPE) {
            $confirmResult = Zb_Service_Stock_Archimedes::tccConfirmEntityStock($inParams);
            Bd_Log::addNotice("Archimedes Confirm_Try_entityStockReturnResult", "content:[" . json_encode($confirmResult) . "]");
            $tryWithReturn = self::tccTryIsWithReturn($tccId);
            if($tryWithReturn){
                $returnTccId = self::getTccId($tccId, 2);
                $returnInParams = [
                    'uniqueTccId' => $returnTccId,
                ];
                Zb_Service_Stock_Archimedes::confirmReturnEntityStock($returnInParams);
            }
        } else {
            $confirmResult = Zb_Service_Stock_Archimedes::confirmReturnEntityStock($inParams);
            Bd_Log::addNotice("Archimedes Confirm_Return_entityStockReturnResult", "content:[" . json_encode($confirmResult) . "]");
        }
        return $confirmResult;
    }

    public static function zbStockCancelAdapter($inParams, $lockType) {
        $tccId = intval($inParams['uniqueTccId']);
        $inParams = [
            'uniqueTccId' => $tccId,
        ];
        if ($lockType == Zb_Const_Dak::LOCK_STOCK_COMMON_TYPE || $lockType == Zb_Const_Dak::LOCK_STOCK_RESERVE_TYPE) {
            $cancelResult = Zb_Service_Stock_Archimedes::tccCancelEntityStock($inParams);
            Bd_Log::addNotice("Archimedes Cancel_Try_entityStockReturnResult", "content:[" . json_encode($cancelResult) . "]");
            $tryWithReturn = self::tccTryIsWithReturn($tccId);
            if ($tryWithReturn) {
                $returnTccId = self::getTccId($tccId, 2);
                $returnInParams = [
                    'uniqueTccId' => $returnTccId,
                ];
                Zb_Service_Stock_Archimedes::cancelReturnEntityStock($returnInParams);
            }
        } else {
            $cancelResult = Zb_Service_Stock_Archimedes::cancelReturnEntityStock($inParams);
            Bd_Log::addNotice("Archimedes Cancel_Return_entityStockReturnResult", "content:[" . json_encode($cancelResult) . "]");
        }
        return $cancelResult;
    }

    public static function cancelAdapter($inParams, $type) {
        $tccId = $inParams['tccId'];
        $inParams = [
            'uniqueTccId' => $tccId,
        ];
        if ($type == Zb_Const_Dak::LOCK_STOCK_COMMON_TYPE) {
            return Zb_Service_Stock_Archimedes::tccPureCancelEntityStock($inParams);
        } else if ($type == Zb_Const_Dak::LOCK_STOCK_REFUND_TYPE) {
            return Zb_Service_Stock_Archimedes::cancelPureReturnEntityStock($inParams);
        } else {
            throw new Exception("lockStockType 参数错误", Zb_Const_ServiceError::PARAM_ERROR);
        }
    }

    public static function confirmAdapter($inParams, $type) {
        $tccId = $inParams['tccId'];
        $inParams = [
            'uniqueTccId' => $tccId,
        ];
        if ($type == Zb_Const_Dak::LOCK_STOCK_COMMON_TYPE) {
            return Zb_Service_Stock_Archimedes::tccPureConfirmEntityStock($inParams);
        } else if ($type == Zb_Const_Dak::LOCK_STOCK_REFUND_TYPE) {
            return Zb_Service_Stock_Archimedes::confirmPureReturnEntityStock($inParams);
        } else {
            throw new Exception("lockStockType 参数错误", Zb_Const_ServiceError::PARAM_ERROR);
        }
    }

    private static function getTccId($inTccId, $mod) {
        return intval($inTccId) * 100 + $mod;
    }

    public static function cdKeyCheckStockByArchimedes($skuInfo) {
        $arrHeader = [
            "pathinfo" => "archimedes/sell/stock/cdkey/checkstock",
            "refer" => $_SERVER['REQUEST_URI'],
        ];
        if (empty($skuInfo)) {
            return [];
        }

        $data = Zb_Service_Stock_Archimedes::requestWithTry($skuInfo, $arrHeader);
        if ($data['errNo'] != 0 || !is_array($data['data'])) {
            $paramsJson = json_encode($skuInfo);
            $resJson = json_encode($data);
            Bd_Log::warning("getSkuStockInfoByArchimedes Params:{$paramsJson} Detail:{$resJson}");
        }
        return $data;
    }

    public static function getSkuStockInfoByArchimedes($skuIdsList = [], $channelId = 0, $withReserve = 1, $defaultSaleChannelId = 0){
        $arrHeader = [
            "pathinfo" => "/archimedes/sell/stock/entities/stock/details",
            "refer" => $_SERVER['REQUEST_URI'],
        ];
        if (empty($skuIdsList)) {
            return [];
        }
        $skuIds = [];
        foreach ($skuIdsList as $skuId) {
            $skuIds[] = intval($skuId);
        }
        $params = [
            'withReserve' => $withReserve,
            'channelId' => $channelId,
            'entityIds' => json_encode($skuIds),
            'defaultChannelId' => (int)$defaultSaleChannelId,
        ];
        $data = Zb_Service_Stock_Archimedes::requestWithTry($params, $arrHeader);
        if ($data['errNo'] != 0 || !is_array($data['data'])) {
            $paramsJson = json_encode($params);
            $resJson = json_encode($data);
            Bd_Log::warning("getSkuStockInfoByArchimedes Params:{$paramsJson} Detail:{$resJson}");
            return [];
        }
        $skuStockList = [];
        $skuSaleStockList = [];
        $skuPreSellStockList = [];
        $skuPreSellSoldStockList = [];
        $reserveStockCntList = [];
        $reserveSaleStockCntList = [];
        $skuPreSaleStockList = []; //兼容旧的dak 和 zbstock 预售逻辑
        $stockList = $data['data'];
        foreach ($stockList as $stock) {
            $sEntityId = $stock['entityId'];
            $sChannelId = $stock['channelId'];
            $stockCnt = $stock['availableCnt'];
            $soldCnt = $stock['soldCnt'] + $stock['lockCnt'] - $stock['returnCnt'];
            if ($sChannelId == Zb_Const_GoodsPlatform::RESERVE_CHANNEL_TYPE) {//预留渠道库存
                $reserveStockCntList[$sEntityId] = $stockCnt;
                $reserveSaleStockCntList[$sEntityId] = $soldCnt;
            } elseif ($sChannelId == Zb_Const_GoodsPlatform::PRE_SELL_CHANNEL_TYPE) {//预售渠道库存
                $skuPreSellStockList[$sEntityId] = $stockCnt;
                $skuPreSellSoldStockList[$sEntityId] = $soldCnt;
            } else {
                $skuStockList[$sEntityId] = $stockCnt;
                $skuSaleStockList[$sEntityId] = $soldCnt;
            }
            $skuPreSaleStockList[$sEntityId] = 0;
        }
        return [
            'stockCnt' => $skuStockList,
            'saleCnt' => $skuSaleStockList,
            'preSaleCnt' => $skuPreSaleStockList,
            'reserveSaleCnt' => $reserveSaleStockCntList,
            'reserveStockCnt' => $reserveStockCntList,
            'preSellStockCnt' => $skuPreSaleStockList,
            'preSellSoldStockCnt' => $skuPreSellSoldStockList,
        ];
    }

    public static function getEntityStockSummary($skuIdList = []) {
        $arrHeader = [
            "pathinfo" => "/archimedes/gateway/good/entity/stock/summary",
            "refer" => $_SERVER['REQUEST_URI'],
        ];
        if (empty($skuIdList)) {
            return [];
        }
        $skuIds = [];
        foreach ($skuIdList as $skuId) {
            $skuIds[] = intval($skuId);
        }
        $params = [
            'entityIdList' => json_encode($skuIds)
        ];
        return Zb_Service_Stock_Archimedes::requestWithTry($params, $arrHeader);
    }
}