<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Date: 7/15/17
 * Time: 4:00 PM
 */

class Qdlib_Service_Clickhouse_Client
{
    /** @var  Qdlib_Service_Clickhouse_Config */
    private $config;

    /** @var  Qdlib_Service_Clickhouse_Connector */
    private $connector;

    /** @var  Qdlib_Service_Clickhouse_Format_FormatInterface */
    private $format;

    /**
     * Client constructor.
     * @param Qdlib_Service_Clickhouse_Config $config
     * @param string $defaultFormatClass
     * @throws \Exception
     */
    public function __construct(Qdlib_Service_Clickhouse_Config $config, string $defaultFormatClass = Qdlib_Service_Clickhouse_Format_JsonFormat::class)
    {
        $this->config = $config;
        $this->connector = new Qdlib_Service_Clickhouse_Connector($this->config);

        $this->format = null;
        if ($defaultFormatClass) {
            $this->isValidFormatClass($defaultFormatClass);
            $this->format = new $defaultFormatClass();
        }
    }

    /**
     * @return Qdlib_Service_Clickhouse_Config
     */
    public function config()
    {
        return $this->config;
    }

    /**
     * @param string $formatClass
     * @throws \RuntimeException
     */
    private function isValidFormatClass(string $formatClass)
    {
        if (!in_array(Qdlib_Service_Clickhouse_Format_FormatInterface::class, class_implements($formatClass))) {
            throw new \RuntimeException('Default format class received (' . $formatClass . ') does not implement ' . Qdlib_Service_Clickhouse_Format_FormatInterface::class);
        }
    }

    /**
     * @param string|null $formatClass
     * @return Qdlib_Service_Clickhouse_Format_FormatInterface
     */
    private function defineFormat(string $formatClass = null)
    {
        if ($formatClass) {
            $this->isValidFormatClass($formatClass);
            return new $formatClass;
        }
        return new $this->format;
    }

    /**
     * @return Qdlib_Service_Clickhouse_Response
     * @throws Qdlib_Service_Clickhouse_Exception
     */
    public function ping()
    {
        $response = $this->connector->performRequest(
            $this->connector->createResource()
        );
        return $response;
    }

    /**
     * @param string $sql
     * @param string|null $formatClass
     * @return Qdlib_Service_Clickhouse_Response
     * @throws Qdlib_Service_Clickhouse_Exception
     */
    public function query(string $sql, string $formatClass = null)
    {
        $format = $this->defineFormat($formatClass);

        // add format to SQL
        $sql = rtrim(trim($sql), ';');
        $sql .= ' FORMAT ' . $format->queryFormat();

        $response = $this->connector->performRequest(
            $this->connector->createResource(
                array_merge(
                    $this->config->getSettings(),
                    ['query' => $sql]
                )
            )
        );

        $response->setFormat($format);

        return $response;
    }

    /**
     * @param string $sql
     * @param $stream
     * @param string|null $formatClass
     * @return Qdlib_Service_Clickhouse_Response
     * @throws Qdlib_Service_Clickhouse_Exception
     */
    public function queryStream(string $sql, $stream, string $formatClass = null)
    {
        $format = $this->defineFormat($formatClass);

        // add format to SQL
        $sql = rtrim(trim($sql), ';');
        $sql .= ' FORMAT ' . $format->queryFormat();

        $ch = $this->connector->createResource(
            array_merge(
                $this->config->getSettings(),
                ['query' => $sql]
            )
        );

        //give curl the file pointer so that it can write to it
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, false);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_FILE, $stream);

        $response = $this->connector->performRequest($ch);

        return $response;

    }

    /**
     * @param string $sql
     * @param \Closure $closure
     * @param string|null $formatClass
     * @return Qdlib_Service_Clickhouse_Response
     * @throws Qdlib_Service_Clickhouse_Exception
     */
    public function queryClosure(string $sql, \Closure $closure, string $formatClass = null)
    {
        $format = $this->defineFormat($formatClass);

        // add format to SQL
        $sql = rtrim(trim($sql), ';');
        $sql .= ' FORMAT ' . $format->queryFormat();

        $ch = $this->connector->createResource(
            array_merge(
                $this->config->getSettings(),
                ['query' => $sql]
            )
        );

        $buffer = '';

        //give curl the file pointer so that it can write to it
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, false);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_WRITEFUNCTION, function($curl, $str) use ($closure, &$buffer) {

            // explode lines into array
            $lines = explode("\n", $buffer . $str);

            // get last element
            $lastLine = end($lines);

            // check if is normal string
            if (substr($lastLine, -1) !== "\n") {
                // remove from lines array
                array_pop($lines);
                // add to buffer
                $buffer = $lastLine;
            } else {
                // clear buffer
                $buffer = '';
            }

            foreach($lines as $line) {
                $closure($line);
            }

            return strlen($str);
        });

        $response = $this->connector->performRequest($ch);

        return $response;
    }

    /**
     * @param string $sql
     * @return Qdlib_Service_Clickhouse_Response
     * @throws Qdlib_Service_Clickhouse_Exception
     */
    public function write(string $sql)
    {
        $response = $this->connector->performRequest(
            $this->connector->createPostRawResource(
                $this->config->getSettings(),
                $sql
            )
        );
        return $response;
    }

    /**
     * @param string $sql
     * @param array $rows
     * @param string|null $formatClass
     * @return Qdlib_Service_Clickhouse_Response
     * @throws Qdlib_Service_Clickhouse_Exception
     */
    public function writeRows(string $sql, array $rows, string $formatClass = null)
    {
        // set default format
        $format = $this->defineFormat($formatClass);

        // add format to SQL
        $sql = rtrim(trim($sql), ';');
        $sql .= ' FORMAT ' . $format->insertFormat();

        // encode rows
        $rowsEncoded = '';
        foreach($rows as $row) {
            $rowsEncoded .= $format->encode($row);
        }

        $response = $this->connector->performRequest(
            $this->connector->createPostRawResource(
                array_merge(
                    $this->config->getSettings(),
                    ['query' => $sql]
                ),
                $rowsEncoded
            )
        );
        return $response;
    }

    /**
     * @param string $sql
     * @param $resource
     * @param string|null $formatClass
     * @return Qdlib_Service_Clickhouse_Response
     * @throws Qdlib_Service_Clickhouse_Exception
     */
    public function writeStream(string $sql, $resource, string $formatClass = null)
    {
        // set default format
        $format = $this->defineFormat($formatClass);

        // add format to SQL
        $sql = rtrim(trim($sql), ';');
        $sql .= ' FORMAT ' . $format->insertFormat();

        $response = $this->connector->performRequest(
            $this->connector->createPostStreamResource(
                array_merge(
                    $this->config->getSettings(),
                    ['query' => $sql]
                ),
                $resource
            )
        );

        return $response;
    }

    /**
     * @param string $sql
     * @return Qdlib_Service_Clickhouse_Response
     * @throws Qdlib_Service_Clickhouse_Exception
     */
    public function system(string $sql)
    {
        $response = $this->connector->performRequest(
            $this->connector->createPostRawResource(
                $this->config->getSettings(),
                $sql
            )
        );
        return $response;
    }
}
