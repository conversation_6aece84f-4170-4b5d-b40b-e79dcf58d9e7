<?php

/**
 * 黑蝙蝠运营位排序（寒春署秋）
 * Class Oplib_Service_PosRank
 */
class Oplib_Service_PosRank
{
    //goods前台首页展示默认为"寒春署秋"
    public static $homepagePos = [
        Oplib_Const_Operation::POS_CLASS_CARD_H,//首页长期班（寒）
        Oplib_Const_Operation::POS_CLASS_CARD_C,//首页长期班（春）
        Oplib_Const_Operation::POS_CLASS_CARD_S,//首页长期班（暑）
        Oplib_Const_Operation::POS_CLASS_CARD_Q,//首页长期班（秋）
    ];

    //goods前台学科展示默认为"寒春署秋"
    public static $subjectPos = [
        Oplib_Const_Operation::POS_SUBJECT_BANKE_H,//学科页班课卡片(长期班寒)
        Oplib_Const_Operation::POS_SUBJECT_BANKE_C,//学科页班课卡片（长期班春）
        Oplib_Const_Operation::POS_SUBJECT_BANKE_S,//学科页班课卡片（长期班暑）
        Oplib_Const_Operation::POS_SUBJECT_BANKE_Q,//学科页班课卡片(长期班秋)
    ];


    //获取排序
    public static function getPostListRank($needSortList, $subject = -1, $up2Down = true)
    {
        if (empty($needSortList)) {
            return [];
        }

        $ranked = true;

        foreach ($needSortList as &$value) {
            //有一个没排过序（新增），则整体认为没排过序
            if ($value['rank'] <= 0) {
                $ranked = false;
            }

            if ($up2Down) {
                $value['up'] = 1;
                $value['down'] = 1;
            }
        }

        //从没排过序，以默认为准
        if (!$ranked) {
            $needSortList = array_reduce($needSortList, function ($res, $v) {
                if ($v['moduleId']) {
                    $res[$v['moduleId']] = $v;
                } else {
                    $moduleId = $v['ext']['moduleId'];
                    $v['moduleId'] = $moduleId;
                    $res[$moduleId] = $v;
                }
                return $res;
            });
            //首页
            if ($subject == -1 || $subject == '') {
                $rankPos = self::$homepagePos;
            } else {
                $rankPos = self::$subjectPos;
            }
            //过滤掉不存在的pos
            foreach ($rankPos as $k => $v) {
                if (!in_array($v, array_keys($needSortList))) {
                    unset($rankPos[$k]);
                }
            }
            $needSortList = array_replace(array_flip($rankPos), $needSortList);
        } else {//否则，以rank为准
            $needSortList = array_reduce($needSortList, function ($res, $v) {
                if (!$v['moduleId']) {
                    $v['moduleId'] = $v['ext']['moduleId'];
                }
                $res[$v['rank']] = $v;
                return $res;
            });
            ksort($needSortList);
        }

        $needSortList = array_values($needSortList);
        //第一个和最后一个，上移、下移比较特殊
        if ($up2Down) {
            $needSortList[0]['up'] = 0;
            $needSortList[count($needSortList) - 1]['down'] = 0;
        }

        return $needSortList;
    }


    public static function setHcsqRank($posId, $grade, $subject)
    {
        //取库中最新的排序
        if ($subject == -1) {//首页
            $moduleIds = self::$homepagePos;
        } else {//学科
            $moduleIds = self::$subjectPos;
        }

        $arrFields = ['id', 'rank', 'ext'];

        $obj = new Oplib_Ds_OperationPosAd();
        $moduleInfo = $obj->getOperationPosAdListByPosId($posId, $grade, null, $arrFields, 'rank', 'asc', 0, 0, $subject, false);

        $rankList = [];
        foreach ($moduleInfo as $each) {
            if (in_array($each['ext']['moduleId'], $moduleIds)) {
                $rankList[] = $each;
            }
        }
        if (!$rankList) {
            return null;
        }


        $rankList = self::getPostListRank($rankList, $subject, false);
        $rankList = array_reduce($rankList, function ($res, $v) {
            $res[$v['moduleId']] = $v;
            return $res;
        });


        //更新缓存
        $cacheKey = Oplib_Common_Cache::getHcsqCacheKey($posId, $grade, $subject);
        $cacheData = array_keys($rankList);
        $res = Oplib_Common_Cache::setHcsqRank($cacheKey, json_encode($cacheData));
        if (false === $res) {
            Bd_Log::warning("update cache error Detail[key:" . $cacheKey . "]");
        }

        return $cacheData;
    }

}