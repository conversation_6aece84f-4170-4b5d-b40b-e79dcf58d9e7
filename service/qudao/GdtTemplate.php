<?php

class Qdlib_Service_Qudao_GdtTemplate
{
    const CHANNEL = 'gdt';

    /**
     * @var array[]
     * @desc 动态词包
     */
    public $dynamic_name = [
        [
            'name' => '城市',
            'value' => '{{city}}',
            'maxLength' => 4,
        ],
        [
            'name' => '性别',
            'value' => '{{gender}}',
            'maxLength' => 2,
        ], [
            'name' => '日期',
            'value' => '{{day}}',
            'maxLength' => 6,
        ], [
            'name' => '星期',
            'value' => '{{week}}',
            'maxLength' => 3,
        ], [
            'name' => '星座',
            'value' => '{{constellation}}',
            'maxLength' => 3,
        ], [
            'name' => '星座日期',
            'value' => '{{constellation_date}}',
            'maxLength' => 6,
        ]
    ];

    public function __construct()
    {
        $this->dsCreativeTemplate = new Qdlib_Ds_Qudao_OutCreativeTemplate();
        $this->dsOutFile = new Qdlib_Ds_Qudao_OutFile();
    }

    /**
     * @param $params
     * @throws Hk_Util_Exception
     */
    public function checkParam($params)
    {
        //自动版位
        if (!$params['automaticSiteEnabled'] && empty($params['siteSet'])) {
            $errParam[] = "投放版位";
        }
        if ($params['automaticSiteEnabled'] == true && !empty($params['siteSet'])) {
            $errParam[] = "投放版位";
        }
        if (!empty($params['siteSet'])) {
            $siteSet = $params['siteSet'];
            if (is_array($params['siteSet'])) {
                $siteSet = $params['siteSet'][0];
            }
            if (!QdLib_Const_AdsEnum_Lv2_Gdt::AD_SITE_SET[$siteSet]) {
                $errParam[] = "投放版位";
            }
        }
        if (!is_numeric($params['adcreativeTemplateId'])) {
            $errParam[] = "	创意形式 id";
        }
        if (empty($params['promotedObjectType']) || !QdLib_Const_AdsEnum_Lv1_Gdt::PROMOTED_OBJECT_TYPE_SHOW[$params['promotedObjectType']]) {
            $errParam[] = "推广目标类型";
        }
        if (!empty($errParam)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, implode(",", $errParam));
        }
    }

    /**
     * @param $params
     * @return array|mixed
     * @throws Hk_Util_Exception
     */
    public function getTemplate($params)
    {
        $this->checkParam($params);

        $conds = [
            "template_id" => $params['adcreativeTemplateId'],
            "promoted_object_type" => $params['promotedObjectType'],
            "account" => $params['account'],
        ];
        $ret = [];
        $adcreativeElements = $supportPageType = [];
        if ($params['automaticSiteEnabled'] == true) {
            $conds[] = "site_set not in ('SITE_SET_MOMENTS','SITE_SET_WECHAT')";
            $info = $this->dsCreativeTemplate->getList($conds, ['ext']);
            //不同版位 获取结构可能不全，补全所有数据
            if (!empty($info)) {
                $info1 = array_column($info, 'ext');
                $info2 = array_column($info1, 'adcreative_elements');
                foreach ($info1 as $item) {
                    $supportPageType = array_merge($supportPageType, $item['support_page_type']);
                }
                foreach ($info2 as $item) {
                    $tmpInfo = array_column($item, null, 'name');
                    $adcreativeElements = array_merge($adcreativeElements, $tmpInfo);
                }
                $ret = $info[0]['ext'];
                $ret['adcreative_elements'] = $adcreativeElements;
                $ret['support_page_type'] = $supportPageType;
            }
            return $ret;
        } else {
            $conds['site_set'] = is_array($params['siteSet']) ? $params['siteSet'][0] : $params['siteSet'];
            $info = $this->dsCreativeTemplate->getList($conds, ['ext'])[0];
            $ret = $info['ext'];
            return $ret;
        }
    }

    /**
     * @param null $string
     * @return false|float
     */
    public function getTextLen($string = null)
    {
        $extra = 0;
        if (empty($string)) {
            return $extra;
        }
        //正则匹配表情 一个表情占1个字节
        $pattern = '/\[[\x{4e00}-\x{9fa5}a-zA-Z0-9]+\]/use';
        preg_match_all($pattern, $string, $matches);
        if (!empty($matches[0])) {
            foreach ($matches[0] as $item) {
                if (QdLib_Const_AdsEnum_Lv3_Gdt::EMOJIS[$item]) {
                    $string = str_replace($item, '', $string);
                    $extra += 1;
                }
            }
        }
        // 动态词包 长度
        foreach ($this->dynamic_name as $item) {
            if (strpos($string, $item['value']) !== false) {
                $string = str_replace($item['value'], '', $string);
                $extra += $item['maxLength'];
            }
        }
        // 将字符串分解为单元 英文字符占半个
        preg_match_all("/./us", $string, $match);
        $match = $match[0];
        foreach ($match as $item) {
            if (strlen($item) == 1) {
                $extra += 0.5;
            } else {
                $extra += 1;
            }
        }
        // 返回单元个数
        return ceil($extra);
    }

    public function checkInputByType(&$errParam, $account, $field, $param, $pField = [], $pParam = [])
    {
        $restriction = $field['restriction'];
        //单独处理 image_list
        if ($field['name'] == 'image_list') {
            if (empty($param['value'])) {
                $errParam[$field['name']] = $field['description'] . "空";
                return false;
            }
            if ($field['array_property']['min_number'] > count($param['value']) || $field['array_property']['max_number'] < count($param['value'])) {
                $errParam[$field['name']] = $field['description'] . "元素个数错误";
                return false;
            }
            $where = [
                'channel' => self::CHANNEL,
                'account' => $account,
                'flag' => 'image'
            ];
            $imgIds = array_unique($param['value']);
            $where[] = Qdlib_Util_DB::whereIn('unique_id', $imgIds);
            $fileInfo = $this->dsOutFile->getList($where, ['ext', 'unique_id']);
            $fileInfo = empty($fileInfo) ? [] : array_column($fileInfo, 'ext', 'unique_id');
            foreach ($imgIds as $imgId) {
                if (empty($fileInfo[$imgId])) {
                    $errParam[$field['name']] = $field['description'] . "数据获取错误";
                    return false;
                }
                if (!in_array($fileInfo[$imgId]['type'], $restriction['image_restriction']['file_format'])
                    || $fileInfo[$imgId]['width'] != $restriction['image_restriction']['width']
                    || $fileInfo[$imgId]['height'] != $restriction['image_restriction']['height']
                    || $fileInfo[$imgId]['file_size'] > $restriction['image_restriction']['file_size'] * 1024
                ) {
                    $errParam[$field['name']] = $field['description'] . "格式错误";
                    return false;
                }
            }

            return true;
        }
        //有父结构
        if (!empty($pField)) {
            if (empty($pParam) || $pField['field_type'] != $pParam['field_type']) {
                $errParam[$field['name']] = $field['description'] . "父结构错误";
                return false;
            }
            //个数限制
            if (!empty($pField['array_property'])) {
                if (empty($param['value'])
                    || count($param['value']) < $pField['array_property']['min_number']
                    || count($param['value']) > $pField['array_property']['max_number']
                ) {
                    $errParam[$field['name']] = $field['description'] . "元素个数错误";
                    return false;
                }
            }
        }
        //枚举类型
        if ($field['element_type'] == 'ELEMENT_TYPE_ENUM') {
            if (!in_array($param['value'], array_column($field['enum_property']['enumeration'], 'value'))) {
                $errParam[$field['name']] = $field['description'] . "枚举值错误";
                return false;
            }
            return true;
        }
        //图片类型
        if ($field['element_type'] == 'ELEMENT_TYPE_IMAGE') {
            $where = [
                'channel' => self::CHANNEL,
                'account' => $account,
                'flag' => 'image'
            ];
            $imgIds = is_array($param['value']) ? $param['value'] : [$param['value']];
            if (empty($imgIds)) {
                $errParam[$field['name']] = $field['description'] . "数据获取错误";
                return false;
            }
            $imgIds = array_unique($imgIds);
            $where[] = Qdlib_Util_DB::whereIn('unique_id', $imgIds);
            $fileInfo = $this->dsOutFile->getList($where, ['ext', 'unique_id']);
            $fileInfo = empty($fileInfo) ? [] : array_column($fileInfo, 'ext', 'unique_id');
            foreach ($imgIds as $imgId) {
                if (empty($fileInfo[$imgId])) {
                    $errParam[$field['name']] = $field['description'] . "数据获取错误";
                    return false;
                }
                if (!in_array($fileInfo[$imgId]['type'], $restriction['image_restriction']['file_format'])
                    || $fileInfo[$imgId]['width'] != $restriction['image_restriction']['width']
                    || $fileInfo[$imgId]['height'] != $restriction['image_restriction']['height']
                    || $fileInfo[$imgId]['file_size'] > $restriction['image_restriction']['file_size'] * 1024) {
                    $errParam[$field['name']] = $field['description'] . "格式错误";
                    return false;
                }
            }
            return true;
        }
        //视频类型
        if ($field['element_type'] == 'ELEMENT_TYPE_VIDEO') {
            $where = [
                'channel' => self::CHANNEL,
                'account' => $account,
                'unique_id' => $param['value'],
                'flag' => 'video'
            ];
            $fileInfo = $this->dsOutFile->getList($where, ['ext'], ['limit 1'])[0]['ext'];
            //1s = 1000ms
            if (empty($fileInfo)) {
                $errParam[$field['name']] = $field['description'] . "数据获取错误";
                return false;
            }
            if (!in_array($fileInfo['type'], $restriction['video_restriction']['file_format'])) {
                $errParam[$field['name']] = $field['description'] . "格式错误";
                return false;
            }
            if ($fileInfo['file_size'] > $restriction['video_restriction']['file_size'] * 1024) {
                $errParam[$field['name']] = $field['description'] . "文件大小错误";
                return false;
            }
            //|| $fileInfo['audio_duration_millisecond'] < $restriction['video_restriction']['min_duration'] * 1000
            //|| $fileInfo['audio_duration_millisecond'] > $restriction['video_restriction']['max_duration'] * 1000
            if ($fileInfo['image_duration_millisecond'] < $restriction['video_restriction']['min_duration'] * 1000 || $fileInfo['image_duration_millisecond'] > $restriction['video_restriction']['max_duration'] * 1000) {
                $errParam[$field['name']] = $field['description'] . "长度错误";
                return false;
            }
            //ratio_width、ratio_height、min_width、min_height 与 width、height 不同时返回
            if (!empty($restriction['video_restriction']['width']) &&
                ($fileInfo['width'] != $restriction['video_restriction']['width'] || $fileInfo['height'] < $restriction['video_restriction']['height'])) {
                $errParam[$field['name']] = $field['description'] . "宽高错误";
                return false;
            }
            if (!empty($restriction['video_restriction']['min_width']) &&
                ($fileInfo['width'] < $restriction['video_restriction']['min_width'] || $fileInfo['height'] < $restriction['video_restriction']['min_height'])) {
                $errParam[$field['name']] = $field['description'] . "最小宽高错误";
                return false;
            }
            if (!empty($restriction['video_restriction']['ratio_width']) && ($fileInfo['width'] / $fileInfo['height'] != ($restriction['video_restriction']['ratio_width'] / $restriction['video_restriction']['ratio_height']))) {
                $errParam[$field['name']] = $field['description'] . "宽高比错误";
                return false;
            }
            return true;
        }
        //文本框
        if ($field['element_type'] == 'ELEMENT_TYPE_TEXT') {
            $pattern = '{&|<|>|\'|"|\x08\x09\x0A\x0D/}';
            if (is_array($param['value'])) {
                foreach ($param['value'] as $item) {
                    if ($restriction['text_restriction']['min_length'] > $this->getTextLen($item) || $restriction['text_restriction']['max_length'] < $this->getTextLen($item)) {
                        $errParam[$field['name']] = $field['description'] . "格式错误";
                        return false;
                    }
                    if (!empty($restriction['text_restriction']['text_pattern'])) {
                        if (preg_match($pattern, $item)) {
                            $errParam[$field['name']] = $field['description'] . "格式错误";
                            return false;
                        }
                    }
                }
            } else {
                if ($restriction['text_restriction']['min_length'] > $this->getTextLen($param['value']) || $restriction['text_restriction']['max_length'] < $this->getTextLen($param['value'])) {
                    return false;
                }
                if (!empty($restriction['text_restriction']['text_pattern'])) {
                    if (preg_match($pattern, $param['value'])) {
                        $errParam[$field['name']] = $field['description'] . "格式错误";
                        return false;
                    }
                }
            }
        }

        //单独处理 标签内容，限制总长度 16
        if ($field['name'] == 'content' && $field['parent_name'] == 'label') {
            $tmpText = implode('', $param['value']);
            if ($this->getTextLen($tmpText) > 16) {
                $errParam[$field['name']] = $field['description'] . "元素字长至多16";
                return false;
            }
        }
        return true;
    }

    /**
     * @param $usualElements
     * @return array
     * @desc 反结构化
     */
    public function destructured($usualElements)
    {
        $res = [];
        foreach ($usualElements as $key => $item) {
            //特殊formate
            if ($key == 'image_list') {
                $res['image_list'] = [
                    'field_type' => 'FIELD_TYPE_STRING_ARRAY',
                    'value' => $item,
                ];
                continue;
            }
            //数组类型
            if (is_array($item)) {
                foreach ($item as $cKey => $cItem) {
                    if (is_array($cItem)) {
                        $res[$key]['field_type'] = 'FIELD_TYPE_STRUCT_ARRAY';
                        foreach ($cItem as $ccKey => $ccItem) {
                            $res[$ccKey]['value'][$cKey] = $ccItem;
                            $res[$ccKey]['field_type'] = 'FIELD_TYPE_STRING';
                            $res[$ccKey]['parent_name'] = $key;
                        }
                    } else {
                        $res[$key]['field_type'] = 'FIELD_TYPE_STRUCT';
                        $res[$cKey]['value'] = $cItem;
                        $res[$cKey]['parent_name'] = $key;
                        $res[$cKey]['field_type'] = 'FIELD_TYPE_STRING';
                    }
                }
            } else {
                $res[$key] = [
                    'value' => $item,
                    'field_type' => 'FIELD_TYPE_STRING',
                ];
            }

        }
        return $res;
    }

    /**
     * @param $camelCaps
     * @param string $separator
     * @return string
     * 驼峰命名转下划线命名
     * 思路:
     * 小写和大写紧挨一起的地方,加上分隔符,然后全部转小写
     */
    public function uncamelize($camelCaps, $separator = '_')
    {
        return strtolower(preg_replace('/([a-z])([A-Z])/', "$1" . $separator . "$2", $camelCaps));
    }

    /**
     * 驼峰转下划线变量格式
     */
    public function transToUnderLine(&$res)
    {
        if (!$res || !is_array($res)) {
            return false;
        }

        foreach ($res as $fields => &$value) {
            $strFields = $this->uncamelize($fields);
            $res[$strFields] = $value;
            if ($strFields != $fields)
                unset($res[$fields]);
            if (is_array($value) && isset($value[0])) {
                foreach ($value as $index => &$itemValue) {
                    $this->transToUnderLine($itemValue);
                }
            }
            if (is_array($value) && !isset($value[0])) {
                $this->transToUnderLine($value);
            }
        }
        return true;
    }

    /**
     * @param $data
     * @return array
     * @throws Hk_Util_Exception
     */
    public function checkInput($data)
    {
        $ret = [
            'result' => true,
            'errMsg' => [],
        ];

        $info = $this->getTemplate($data);
        if (empty($info)) {
            $ret['result'] = false;
            $ret['errMsg'] = [];
            return $ret;
        }
        if (empty($data['adcreativeElements'])) {
            $ret['result'] = false;
            $ret['errMsg'] = [];
            return $ret;
        }

        $usualElements = $data['adcreativeElements'];
        //驼峰命名转下划线
        $this->transToUnderLine($usualElements);
        //去结构化
        $usualElements = $this->destructured($usualElements);
        $errParam = [];
        $elements = array_column($info['adcreative_elements'], null, 'name');

        //过滤误传字段
        $fieldUsual = array_keys($usualElements);
        $fieldDb = array_keys($elements);
        $fieldExtra = array_diff($fieldUsual, $fieldDb);
        if (!empty($fieldExtra)) {
            foreach ($fieldExtra as $item) {
                $errParam[$item] = "多传" . $item;
            }
        }

        //最多两层父子关系
        foreach ($elements as $key => $item) {
            //校验必填项
            if ($item['required'] == true && empty($usualElements[$key])) {
                if (empty($item['parent_name'])) {
                    $errParam[$key] = $item['description'] . "空";
                    continue;
                } else {
                    //有父结构
                    if (isset($usualElements[$item['parent_name']])) {
                        $errParam[$key] = $item['description'] . "空";
                        continue;
                    }
                }
            }
            //校验结构 类型 数据
            if (!empty($usualElements[$key])) {
                if (($item['field_type'] != $usualElements[$key]['field_type']) && $item['field_type'] != 'FIELD_TYPE_INTEGER') {
                    $errParam[$key] = $item['description'] . "数据结构错误";
                    continue;
                }
                $pField = $elements[$item['parent_name']] ?? [];
                $pParam = $usualElements[$item['parent_name']] ?? [];

                if (!$this->checkInputByType($errParam, $data['account'], $item, $usualElements[$key], $pField, $pParam)) {
                    continue;
                }
            }
        }
        if (!empty($errParam)) {
            $ret['result'] = false;
            $ret['errMsg'] = $errParam;
        }

        return $ret;
    }

    /**
     * @return array[]
     */
    public function getDynamicName()
    {
        return $this->dynamic_name;
    }

    /**
     * @return mixed
     */
    public function getLabel()
    {
        $dsLabel = new Qdlib_Ds_Qudao_OutMediasConf();
        $info = $dsLabel->getList(["unique_id" => 'gdt_importaccount__1_channel', 'channel' => self::CHANNEL], ['ext'], ["limit 1"])[0];

        return $info['ext'];
    }

    /**
     * @param $account
     * @return array|false|mixed
     */
    public function getBrand($param)
    {
        $dsBrand = new Qdlib_Ds_Qudao_OutAdConf();
        $conds = [
            "channel" => self::CHANNEL,
            'flag' => 'ad_brandlist',
            'account' => $param['account'],
        ];
        if (!empty($param['width'])) {
            $conds[] = "json_extract(ext,'$.width') = " . $param['width'];
        }
        if (!empty($param['height'])) {
            $conds[] = "json_extract(ext,'$.height') = " . $param['height'];
        }
        $info = $dsBrand->getList($conds, ['ext']);
        return $info;
    }
}