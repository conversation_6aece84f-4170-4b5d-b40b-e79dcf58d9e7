<?php
/**
 * file OutTask.php.
 * author: <EMAIL>
 * Date: 2020/2/17
 * brief:
 */

class  Qdlib_Service_Qudao_OutTask
{
    private $_objDsQudaoOutTask;
    static $dsObj = null;
    const MAXIDSNUM = 10000;

    static $taskMaps =[
        'ad'        => ['syncFile'=>'QudaoOutAdSync','ds'=>'Qdlib_Ds_Qudao_OutAd','field' => 'ad_id','syncField'=>'adId', 'forceIndex'=>'force index (idx_ad_id)'],
        'campaign'  => ['syncFile'=>'QudaoOutCampaignSync','ds'=>'Qdlib_Ds_Qudao_OutCampaign','field'=>'campaign_id','syncField'=>'campaignId', 'forceIndex'=>'force index (idx_campaign_id)'],
        'creative'  => ['syncFile'=>'QudaoOutCreativeSync','ds'=>'Qdlib_Ds_Qudao_OutCreative','field'=>'creative_id','syncField'=>'creativeId', 'forceIndex'=>'force index (idx_creative_id)'],
        'group'     => ['syncFile'=>'QudaoOutGroupSync','ds'=>'Qdlib_Ds_Qudao_OutGroup','field'=>'adgroup_id','syncField'=>'adgroupId', 'forceIndex'=>'force index (idx_adgroup_id)'],
    ];

    public function __construct()
    {
        $this->_objDsQudaoOutTask = new Qdlib_Ds_Qudao_OutTask();
    }

    public function syncTask($taskId)
    {
        Qdlib_Util_Log::addNotice("syncTask_请求同步状态",$taskId);
        $ret = $this->getTaskInfo($taskId);
        if($ret===false){
            Qdlib_Util_Log::warning('syncTask', 'Qdlib_Service_Qudao_OutTask', 'syncTask', 'getTaskInfo empty', $taskId);
            return false;
        }

        $actType = $this->_getSyncAct($ret['flag']);
        if(empty($actType)){
            Qdlib_Util_Log::warning('syncTask', 'Qdlib_Service_Qudao_OutTask', 'syncTask', '_getSyncAct empty', json_encode($ret));
            return false;
        }

        //这里要调用定时脚本跑数据
        $partitions = 3;
        $partition = 0;
        //这个插入到outtask表中
        for ($i=0;$i<$partitions;$i++){
            $arrField = array(
                'channel'       => $ret['channel'],
                'taskId'        => intval($taskId),
                'actType'       => $actType,
                'ids'           => $ret['ids'],
                'taskPartitions'    => $partitions,
                'taskPartition'     => $i,
            );
            $res = $this->_objDsQudaoOutTask->addTask($arrField);
            if($res===false){
                Qdlib_Util_Log::warning('syncTask', 'Qdlib_Service_Qudao_OutTask', 'syncTask', 'addData false', json_encode($arrField));
            }
        }
        $scriptFile = self::$taskMaps[$actType]['syncFile'].'.php';
        for ($j=0;$j<$partitions;$j++){
            $exec = sprintf("cd /home/<USER>/app/qudao/script/marketing; php %s dispatchExecTaskId %s %d %d %d",$scriptFile,$ret['channel'],$taskId,$partitions,$j);
            Qdlib_Util_Log::addNotice("syncTask_exec",$exec);
            exec($exec);
        }
    }


    public function notifyTask($taskId, $partitions=0, $partition=0, $counter=[])
    {
        Qdlib_Util_Log::addNotice("notifyTask",json_encode(['taskId'=>$taskId,'partitions'=>$partitions,
            'partition'=>$partition,'counter'=>$counter]));
        if(empty($taskId) || !$partitions){
            return false;
        }
        $partitions = intval($partitions);
        $partition = intval($partition);

        $ret = $this->_objDsQudaoOutTask->getList(['taskId'=>$taskId]);
        if($ret===false){
            Qdlib_Util_Log::warning('syncTask', 'Qdlib_Service_Qudao_OutTask', 'notifyTask', 'getListsByTaskId false', json_encode(['taskId'=>$taskId]));
            return false;
        }
        if(empty($ret)){
            Qdlib_Util_Log::warning('syncTask', 'Qdlib_Service_Qudao_OutTask', 'notifyTask', 'getListsByTaskId empty', json_encode(['taskId'=>$taskId]));
            return false;
        }
        $i = 0;
        $count = count($ret);
        foreach ($ret as $row){
            if($row['taskPartitions']==$partitions && $row['taskPartition']==$partition){
                $i++;
                $res = $this->_objDsQudaoOutTask->updateParam(['id'=>$row['id']],['taskStatus'=>1,'taskResult'=>json_encode
                ($counter)]);
                if($res===false){
                    Qdlib_Util_Log::warning('syncTask', 'Qdlib_Service_Qudao_OutTask', 'notifyTask', 'updateData false', json_encode(['id'=>$row['id'],'taskStatus'=>1,'taskResult'=>$counter]));
                }
            }
            if($row['taskStatus']==1){
                $i++;
            }
        }
        if($i==$count){
            //通知task任务更新完毕
            Qdlib_Util_Log::addNotice("notifyTaskToupdateBatchTaskStatus",json_encode(['taskId'=>$taskId,'status'=>Qdlib_Const_Ads::BATCH_TASK_STATUS_SYNC_DATA]));
            $ret = Qdlib_Const_Ads::updateBatchTaskStatus($taskId, Qdlib_Const_Ads::BATCH_TASK_STATUS_SYNC_DATA);
            if ($ret) {
                Qdlib_Const_Ads::handleBatchTask($taskId);
            }
        }
        return true;
    }

    public function getTaskInfo($taskId)
    {
        if(empty($taskId)){
            return false;
        }
        //这里调用根据taskid获取详情方法
        $ret = Qdlib_Const_Ads::getAdBatchTaskById($taskId);
        if($ret===false){
            Qdlib_Util_Log::warning('syncTask', 'Qdlib_Service_Qudao_OutTask', 'syncTask', 'getTaskInfo false', json_encode(['taskId'=>$taskId]));
            return false;
        }
        return $ret;
    }


    private function _getSyncAct($flag)
    {
        $actType = '';
        switch ($flag){
            case Qdlib_Const_Ads::JRTT_AD:
            case Qdlib_Const_Ads::GDT_AD:
            case Qdlib_Const_Ads::KUAISHOU_AD:
                $actType = 'ad';
                break;
            case Qdlib_Const_Ads::JRTT_CAMPAIGN:
            case Qdlib_Const_Ads::GDT_CAMPAIGN:
            case Qdlib_Const_Ads::KUAISHOU_CAMPAIGN:
                $actType = 'campaign';
                break;
            case Qdlib_Const_Ads::JRTT_CREATIVE:
            case Qdlib_Const_Ads::KUAISHOU_CREATIVE:
                $actType = 'creative';
                break;
            case Qdlib_Const_Ads::GDT_AD_GROUP:
                $actType = 'group';
                break;
            default:
                $actType = '';
                break;
        }
        return $actType;
    }

    public function getAccountByTaskId($taskId)
    {
        Qdlib_Util_Log::addNotice("getAccountByTaskId",$taskId);
        if(empty($taskId)){
            return false;
        }
        $arrParam = [
            'taskId'=>$taskId,
        ];
        $ret = $this->_objDsQudaoOutTask->get($arrParam,[],"order by id desc limit 1");
        if($ret===false){
            Qdlib_Util_Log::warning('syncTask', 'Qdlib_Service_Qudao_OutTask', 'getList', 'getList db error', json_encode(['taskId'=>$taskId]));
            return false;
        }
        if(empty($ret)){
            Qdlib_Util_Log::warning('syncTask', 'Qdlib_Service_Qudao_OutTask', 'getList', 'getList empty', json_encode(['taskId'=>$taskId]));
            return false;
        }
        if(empty($ret['ids'])){
            Qdlib_Util_Log::warning('syncTask', 'Qdlib_Service_Qudao_OutTask', 'getAccountByTaskId', 'ids empty', json_encode($ret));
            return false;
        }

        $channel = $ret['channel'];
        $field = self::$taskMaps[$ret['actType']]['field'];
        $syncField = self::$taskMaps[$ret['actType']]['syncField'];
        $forceIndex = self::$taskMaps[$ret['actType']]['forceIndex'];
        $ds = self::$taskMaps[$ret['actType']]['ds'];
        self::$dsObj = new $ds();
        //根据账号分组ids
        $list = [];
        //这里要根据$ret['ids']获取account,并组织数组。
        $ids = array_map('intval', $ret['ids']);
        $arrConds = array(
            'channel' => $channel,
            $field." in (" . implode(',', $ids) . ")",
        );
        $res = self::$dsObj->getList($arrConds,['account',$syncField],null,$forceIndex);
        if($res===false){
            Qdlib_Util_Log::warning('syncTask','Qdlib_Service_Qudao_OutTask','getList','getList db false',json_encode
            ($arrConds));
            return false;
        }
        if(empty($res)){
            Qdlib_Util_Log::warning('syncTask','Qdlib_Service_Qudao_OutTask','getList','getList db empty',json_encode($arrConds));
            return false;
        }
        foreach ($res as $val){
            $list[$val['account']][] = $val[$syncField];
        }
        return array(
            'actType' => $ret['actType'],
            'channel' => $channel,
            'accounts' => $list,
        );
    }
}