<?php
/**
 * OCS SDK 
 * User: <EMAIL>
 * Date: 2019-11-18
 * Time: 08:07 AM
 */

class Zb_Service_OCS 
{  
    const SERVICE_APP = 'ocs';
    const OCS_SERVICE_LINE = 1; // 一课业务线
    const OCS_NCM_MOD_MAP = 'MOD_MAP';
    const OCS_DEFAULT_GROUP = 'public';


    

    public static function getToken($callerModule, $targetModule, $targetUri = '')
    {
        $arrModMap = Zb_Service_NCM::Get(self::OCS_SERVICE_LINE, self::SERVICE_APP, self::OCS_DEFAULT_GROUP, self::OCS_NCM_MOD_MAP);

        $callerId = isset($arrModMap[$callerModule]) ? $arrModMap[$callerModule] : 0;
        $targetId = isset($arrModMap[$targetModule]) ? $arrModMap[$targetModule] : 0;

        if ($callerId == 0 || $targetId == 0) {
            return false;
        }

        $tokenList = Zb_Service_NCM::Get(self::OCS_SERVICE_LINE, self::SERVICE_APP, self::OCS_DEFAULT_GROUP, $callerId.'_'.$targetId);

        if ($tokenList === false) {
            Bd_Log::warning("Error:[Ocs Sdk get ncm kv failed], Detail[key :$callerModule _ $targetModule , intKey : $callerId _ $targetId]");
            return false;
        }

        $tokenKey = $targetUri == '' ? md5($targetModule) : md5($targetUri);
        $token = isset($tokenList[$tokenKey]) ? $tokenList[$tokenKey] : '';

        if ($token == '') {
            return false;
        }
        $tm = time();
        $ocsSign = md5($callerModule.$targetModule.$tm.$token);

        return ['tm' => $tm, 'ocsSign' => $ocsSign];
    }

   
}