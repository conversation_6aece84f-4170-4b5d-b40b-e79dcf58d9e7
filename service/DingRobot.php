<?php
/**
 * @file DingRobot.php
 * <AUTHOR>
 * @date 2018年4月2日 上午10:50:51
 * @version $Revision$
 * @brief 钉钉机器人消息通知
 * @see https://open-doc.dingtalk.com/docs/doc.htm?treeId=257&articleId=105735&docType=1
 *
 **/
class Hkzb_Service_DingRobot {
    const ID_TEST = 1;
    const ID_ZHIBO_ALARM = 2;
    const ID_PLAYBACK_ALARM = 3;
    const ID_JXZT_ALARM = 4;//群名：教学中台报警，机器人名：报警
    const ID_JXZT_REPORT = 5;//群名：教学中台报告，机器人名：报告
    const ID_JXZT_GROUP = 6;//群名: 分组每日提醒
    const ID_JXZT_CLOUDREPORT = 7;//群名：双云双活选课，机器人名：双云
    const ID_JXZT_SMALLCLASS = 8;//群名：小班教学业务-通知报警，机器人名：小班教学机器人
    const ID_JXZT_SHARE_CHECK = 9; //群名: 共享直播报警群, 机器人名: 课程巡警

    public static $arrAccessToken = array(
        self::ID_TEST => '05f10533ca023fd4fbf6d11ed41eb191676232f936af53304f03e1df515e4f2d',
        self::ID_ZHIBO_ALARM => '56e6cb5340e35dcce9f3574236cfc774941e1cdbc815c4c35ba89fa239f06a4c',
        self::ID_PLAYBACK_ALARM => 'ccce794b5de89b8b4791b888e9e3560a10e6a7b3b12f8f33994fa48608ca50a4',
        self::ID_JXZT_ALARM => 'b7f7c6a51534b61d5ac1bb869069fb32a177456029a6ddffdf59ee2a78b0f5d5',
        self::ID_JXZT_REPORT=> '68dc79fa7317e9300f6ab07baacba3878211cc69f9f44e172173dcdcc6550f3b',
        self::ID_JXZT_GROUP=> 'b76b9bf57d1794cd0797c8101026f0c84290f950122cb5b6b821fa5e1e238e6d',
        self::ID_JXZT_CLOUDREPORT => '0301b1e61d9cfd466abc1a383559c61f34c5964218ec859db02c25b7a21eb598',
        self::ID_JXZT_SMALLCLASS => '251c27d9bf87853a777a82d7d24dea484c093b14e21b0a4762866729430c65d4',
        self::ID_JXZT_SHARE_CHECK => 'dac152527322f3f0d07dd208867c0ee09a0ca3586a1119bc215af0124991d944',
    );

    /**
     * 发送钉钉消息
     * @param integer $robotId 机器人id，Hkzb_Service_DingRobot::ID_xxx常量定义
     * @param array $msg 消息体 ，见说明文档https://open-doc.dingtalk.com/docs/doc.htm?treeId=257&articleId=105735&docType=1
     * @return boolean 发送成功true，否则false
     */
    public static function sendMsg($robotId, $msg) {
        //兼容直接传token，有时需要临时测试
        $accessToken = self::$arrAccessToken[$robotId];
        $accessToken = !empty($accessToken) ? $accessToken : $robotId;
        if ($accessToken == '') {
            Bd_Log::warning("unvalid robotId, robotId[$robotId]");
            return false;
        }
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://oapi.dingtalk.com/robot/send?access_token='.$accessToken);
        curl_setopt($ch,CURLOPT_PROXY, 'proxy.zuoyebang.com');
        curl_setopt($ch,CURLOPT_PROXYPORT, 80);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3);
        curl_setopt($ch, CURLOPT_TIMEOUT, 3);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array ('Content-Type: application/json;charset=utf-8'));
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($msg));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt ($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt ($ch, CURLOPT_SSL_VERIFYPEER, 0);
        $data = curl_exec($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        if (!$data) {
            Bd_Log::warning("send ding msg fail, robotId[$robotId] ret[$data]");
            curl_close($ch);
            return false;
        }
        if ($status != 200) {
            Bd_Log::warning("send ding msg fail, robotId[$robotId] ret[$data] status[$status]");
            curl_close($ch);
            return false;
        }
        $ret = @json_decode($data, true);
        if (!isset($ret['errcode']) || $ret['errcode'] != 0) {
            Bd_Log::warning("send ding msg fail, robotId[$robotId] ret[$data]");
            curl_close($ch);
            return false;
        }
        curl_close($ch);
        return true;
    }

    /**
     * 发送文字消息
     * @param integer $robotId 机器人id，Hkzb_Service_DingRobot::ID_xxx常量定义
     * @param string $text 消息文本
     * @param bool $atAll 是否 @ 所有人
     * @param array $atList 需要 @ 的手机号列表
     * @return boolean
     */
    public static function sendTextMsg($robotId, $text, $atAll = false, $atList = [])
    {
        $text = trim(strval($text));
        if (strlen($text) == 0) {
            Bd_Log::warning("text is empty, text[$text]");
            return false;
        }
        $msg = array(
            'msgtype' => 'text',
            'text' => array(
                'content' => $text,
            ),
            'at' => [
                'atMobiles' => !empty($atList) && is_array($atList) ? array_map('strval', $atList) : [],
                'isAtAll' => $atAll
            ],
        );
        return self::sendMsg($robotId, $msg);
    }

    /**
     * 发送文字消息
     * @param integer $robotId 机器人id，Hkzb_Service_DingRobot::ID_xxx常量定义
     * @param string $title 标题
     * @param string $text 消息文本, markdown格式，只支持markdown语法的子集，见文档https://open-doc.dingtalk.com/docs/doc.htm?treeId=257&articleId=105735&docType=1
     * @return boolean
     */
    public static function sendMarkDownMsg($robotId, $title, $text) {
        $msg = array(
            'msgtype' => 'markdown',
            'markdown' => array(
                'title' => $title,
                'text' => $text,
            ),
        );
        return self::sendMsg($robotId, $msg);
    }
}
