<?php

/**
 * @file   CommentMis.php
 * <AUTHOR>
 * @date   2018/7/11
 * @brief
 **/

class Zb_Service_Comment_CommentMis extends Zb_Service_Comment_CommentBase{

    public function __construct($appId,$appSecret){
        return parent::__construct($appId,$appSecret);
    }

    /**
     * 写评论接口
     * @param int      $uid        评论uid
     * @param string   $content    评论内容
     * @param array    $arrImage   评论图片
     * @param int      $replyId    回复Id
     * @param string   $operator   操作人
     * @param int      $operatorUid 操作人uid
     * @return array/false
     */
    public  function setComment($curAppId,$topicId,$uid, $content,$arrImage = array(),$replyId = 0,$operator = '',$operatorUid = 0) {
        if(strlen($curAppId) <= 0 || intval($topicId) <= 0 || intval($uid) <= 0 || (strlen($content) <= 0 && empty($arrImage)) || $operatorUid <= 0 || $operator == ''){
            Bd_Log::warning("param error Detail:[curAppId:$curAppId topicId:$topicId uid:$uid content:$content arrImage:".json_encode($arrImage)." operatorUid:$operatorUid operator:$operator]");
            return false;
        }
        //提交接口
        $serviceUri = '/comment/misapi/submit';

        $arrParams = array(
            'curAppId'  => strval($curAppId),
            'topicId'   => intval($topicId),
            'uid'       => intval($uid),
            'content'   => strval($content),
            'arrImage'  => $arrImage,
            'replyId'   => intval($replyId),
            'operator'  => strval($operator),
            'operatorUid' => intval($operatorUid),
        );

        return $this->sendParams($serviceUri,$arrParams);
    }

    /**
     * 设置为精选 
     * @param int      $replyId 评论id
     * @param int      $operator操作人
     * @return array/false
     */
    public  function setPerfectYes($curAppId,$topicId,$replyId,$operator,$operatorUid) {
        return $this->setPerfect($curAppId,$topicId,$replyId,1,$operator,$operatorUid,Zb_Const_Comment::LOG_TYPE_PERFECT);
    }
    /**
     * 设置为非精选 
     * @param int      $replyId 评论id
     * @param int      $operator 操作人
     * @return array/false
     */
    public  function setPerfectNo($curAppId,$topicId,$replyId,$operator,$operatorUid) {
        return $this->setPerfect($curAppId,$topicId,$replyId,0,$operator,$operatorUid,Zb_Const_Comment::LOG_TYPE_UNPERFECT);
    }

    /**
     * 设置为未通过
     * @param int      $replyId   评论id
     * @param int      $operator  操作人
     * @return array/false
     */
    public  function setStatusNotPass($curAppId,$topicId,$replyId,$operator,$operatorUid) {
        return $this->setStatus($curAppId,$topicId,$replyId,Zb_Const_Comment::STATUS_NOT_PASS,$operator,$operatorUid,Zb_Const_Comment::LOG_TYPE_UNPASS);
    }
    /**
     * 设置为已通过
     * @param int      $replyId    评论id
     * @param int      $operator   操作人
     * @return array/false
     */
    public  function setStatusPass($curAppId,$topicId,$replyId,$operator,$operatorUid) {
        return $this->setStatus($curAppId,$topicId,$replyId,Zb_Const_Comment::STATUS_PASS,$operator,$operatorUid,Zb_Const_Comment::LOG_TYPE_PASS);
    }
    /**
     * 设置删除
     * @param int      $replyId    评论id
     * @param int      $operator   操作人
     * @return array/false
     */
    public  function setStatusDelete($curAppId,$topicId,$replyId,$operator,$operatorUid) {
        return $this->setStatus($curAppId,$topicId,$replyId,Zb_Const_Comment::STATUS_DELETE,$operator,$operatorUid,Zb_Const_Comment::LOG_TYPE_DELETE);
    }
    /**
     * 按状态获取评论
     * @param int      $status     当前状态
     * @param int      $offset     偏移量
     * @param int      $limit      条数
     * @return list/false
     */
    public function getCommentListByStatus($curAppId,$topicId,$checkStatus,$status = 0,$arrFields = array(),$order = '',$by = '',$offset = 0,$limit = 20){
        $serviceUri = '/comment/misapi/statuslist';
        //获取头信息
        $arrParams = array(
            'curAppId'    => strval($curAppId),
            'topicId'     => intval($topicId),
            'checkStatus' => intval($checkStatus),
            'status'  => intval($status),
            'arrFields'=> $arrFields,
            'order'    => strval($order),
            'by'       => strval($by),
            'limit'    => intval($limit),
            'offset'   => intval($offset),
        );
        return $this->sendParams($serviceUri,$arrParams);
    }
    /**
     * 按状态获取评论总数
     * @param int      $status     当前状态
     * @param int      $checkStatus审核状态
     * @return list/false
     */
    public function getCommentCntByStatus($curAppId,$topicId,$checkStatus,$status = 0){
        $serviceUri = '/comment/misapi/statuscnt';
        //获取头信息
        $arrParams = array(
            'curAppId'    => strval($curAppId),
            'topicId'     => intval($topicId),
            'checkStatus' => intval($checkStatus),
            'status'      => intval($status),
        );
        return $this->sendParams($serviceUri,$arrParams);
    }
    /**
     * 按状态获取评论
     * @param int      $offset     偏移量
     * @param int      $limit      条数
     * @return list/false
     */
    public function getCommentListByPerfect($curAppId,$topicId,$arrFields = array(),$order = '',$by = '',$offset = 0,$limit = 20){
        $serviceUri = '/comment/misapi/statuslist';
        $arrParams = array(
            'curAppId' => strval($curAppId),
            'topicId' => intval($topicId),
            'perfect' => 1,
            'arrFields'=> $arrFields,
            'order'    => strval($order),
            'by'       => strval($by),
            'limit'    => intval($limit),
            'offset'   => intval($offset),
        );
        return $this->sendParams($serviceUri,$arrParams);
    }
    /**
     * 按精选获取总数
     * @return list/false
     */
    public function getCommentCntByPerfect($curAppId,$topicId){
        $serviceUri = '/comment/misapi/statuscnt';
        $arrParams = array(
            'curAppId' => strval($curAppId),
            'topicId' => intval($topicId),
            'perfect' => 1,
        );
        return $this->sendParams($serviceUri,$arrParams);
    }

    /** 
     * 按时间获取总数
     * @return list/false
     */
    public function getCommentCntByTime($curAppId,$topicId,$startTime,$stopTime){
        $serviceUri = '/comment/misapi/timecnt';
        $arrParams = array(
            'curAppId' => strval($curAppId),
            'topicId' => intval($topicId),
            'startTime'=> intval($startTime),
            'stopTime' => intval($stopTime),
        );
        return $this->sendParams($serviceUri,$arrParams);
    }

    /*----------------------App信息--------------------*/
    /**
     * 获取topicInfo
     * @param string   $appId      appId
     * @param string   $secret     secret
     * @return array/false
     */
    public function getAppInfo($curAppId){
        if(strlen($curAppId) <= 0){
            Bd_Log::warning("param error Detail:[curAppId:$curAppId]");
            return false;
        }
        $arrParams = array(
            'curAppId' => strval($curAppId),
        );
        $serviceUri = '/comment/appapi/getappinfo';
        return $this->sendParams($serviceUri,$arrParams);
    }
    /*
     *获取appList
    */
    public function getAppList(){
        $serviceUri = '/comment/appapi/getapplist';
        return $this->sendParams($serviceUri);
    }
    /*
     * 添加app
     * @param string $topic 产品id
     * @param string $name  名称
     * @param string $email 邮箱
     * @param string $subjectUrl 主题地址
     */
    public function editApp($topic,$name,$email,$subjectUrl,$operatorUid,$operator){
        if(intval($topic) < 0 || strlen($name) <= 0 || strlen($email) <= 0 || strlen($subjectUrl) <= 0 || intval($operatorUid) <= 0 || strlen($operator) <= 0){
            Bd_Log::warning("param error Detail:[topic:$topic name:$name email:$email subjectUrl:$subjectUrl operatorUid:$operatorUid operator:$operator]");
            return false;
        } 
        $serviceUri = '/comment/appapi/editapp';
        $arrParams  = array(
            'topic' => intval($topic),
            'name'  => strval($name),
            'email' => strval($email),
            'subjectUrl' => strval($subjectUrl),
            'operatorUid'=> intval($operatorUid),
            'operator'   => strval($operator),
        );
        return $this->sendParams($serviceUri,$arrParams);
    }


    /*
     * 更改app状态
     * @param string $topic 产品id
     */
    public function forbidApp($topic,$operatorUid,$operator){
        return $this->setAppStatus($topic,Zb_Const_Comment::APP_STATUS_FORBID,$operatorUid,$operator);
    }


    /*
     * 更改app状态
     * @param string $topic 产品id
     */
    public function continueApp($topic,$operatorUid,$operator){
        return $this->setAppStatus($topic,Zb_Const_Comment::APP_STATUS_OK,$operatorUid,$operator);
    }
}
