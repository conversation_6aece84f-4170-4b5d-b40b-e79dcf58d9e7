<?php
/**
 * Created by IntelliJ IDEA.
 * User: <EMAIL>
 * Date: 2020/2/18
 * Time: 2:22 PM
 */
class Qdlib_Service_Batch_Kuaishou_CampaignStatus extends Qdlib_Service_Batch_Base implements Qdlib_Service_Batch_Interface {

    protected $objKuaishouCampaign;

    protected $statusMap;

    public function __construct($task)
    {
        parent::__construct($task);
        $this->objKuaishouCampaign = new Qdlib_Ds_Ad_QudaoKuaishouCampaign();

        //参数-枚举值映射关系
        $this->statusMap = array(
            1   => Qdlib_Const_TripartiteEnum::KUAISHOU_PUTSTATUS_ENABLE,
            2   => Qdlib_Const_TripartiteEnum::KUAISHOU_PUTSTATUS_DISABLE,
            3   => Qdlib_Const_TripartiteEnum::KUAISHOU_PUTSTATUS_DELETE,
        );
    }

    public function getUpdateQdapi() :array
    {
        $arrConds = array(
            Qdlib_Util_DB::whereIn('campaign_id', $this->ids),
            //'deleted'       => 0,
            'channel'       => Qdlib_Const_Ads::KUAISHOU,
        );
        $ret = $this->objKuaishouCampaign->getQudaoKuaiShouCampaignList($arrConds, ['campaignId', 'account']);
        if (empty($ret)) {
            throw new Exception('查询批量操作id列表失败');
        }

        //写入不存在id
        if (count($ret) != count($this->ids)) {
            $ids = array_column($ret, 'campaignId');
            $noExists = array_diff(array_keys($this->ids), $ids);

            $where = array(
                'taskId' => $this->task['id'],
                Qdlib_Util_DB::whereIn('pid', $noExists),
            );
            $update = $this->objBatchUpdateResult->updateQudaoBatchUpdateResult($where, array(
                'errFlag'       => Qdlib_Const_Ads::ERROR_CODE_STATUS_ERROR,
                'errno'         => Qdlib_Const_Ads::ERROR_CODE_STATUS_ERROR,
                'errMsg'        => Qdlib_Const_Ads::$errorCodeMap[Qdlib_Const_Ads::ERROR_CODE_STATUS_ERROR],
                'updateTime'    => time()
            ));
            if ($update === false) {
                throw new Exception('不存在任务数据库更新失败');
            }
        }

        //写入账号数据
        $aData = [];
        foreach ($ret as $val) {
            $value = $this->getOptStatus($val['campaignId']);
            $aData[$val['account']][$value][] = $val['campaignId'];
        }
        $paramsAll = [];
        foreach ($aData as $account => $val) {
            foreach ($val as $value => $ids) {
                $paramsAll[] = array(
                    'channel'       => Qdlib_Const_Ads::KUAISHOU,
                    'account'       => $account,
                    'campaignIds'   => $ids,
                    'optStatus'     => $value,
                );
            }
        }
        $pathInfo = '/qdapi/campaigns/status';
        return [$pathInfo, $paramsAll, 'campaignIds'];
    }

    public function dealUpdateRet() :bool
    {
        return true;
    }

    public function getCheckErrorIds($checkIds) :array {
        $list = $this->objKuaishouCampaign->getQudaoKuaiShouCampaignList(array(
            Qdlib_Util_DB::whereIn('campaign_id', $checkIds),
            'channel'       => Qdlib_Const_Ads::KUAISHOU,
        ), ['campaignId', 'putStatus']);

        if (empty($list)) {
            throw new Exception('检测状态查询数据库失败');
        }

        $errIds = [];
        foreach ($list as $val) {
            $objective = $this->getOptStatus($val['campaignId']);
            $objective = $this->statusMap[$objective];
            if ($val['putStatus'] != $objective) {
                Qdlib_Util_Log::warning('script_batch', 'kuaishouCreative', 'checkErrId', '状态未改变', json_encode(compact('val', 'objective')));
                $errIds[] = $val['campaignId'];
            }

        }
        return $errIds;
    }

    public function getNames($ids) :array
    {
        if (empty($ids)) {
            return [];
        }
        $list = $this->objKuaishouCampaign->getQudaoKuaiShouCampaignList([
            Qdlib_Util_DB::whereIn('campaign_id', $ids),
        ], ['campaignId', 'campaignName']);
        if (empty($list)) {
            Qdlib_Util_Log::fatal('script', 'Qdlib_Service_Batch_Kuaishou_CampaignStatus', 'getNames', '获取名称失败', json_encode(['ids' => $ids]));
            return [];
        }
        return array_column($list, 'campaignName', 'campaignId');
    }
}