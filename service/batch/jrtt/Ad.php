<?php
/**
 * Created by IntelliJ IDEA.
 * User: <EMAIL>
 * Date: 2020/2/18
 * Time: 2:22 PM
 */
class Qdlib_Service_Batch_Jrtt_Ad extends Qdlib_Service_Batch_Base implements Qdlib_Service_Batch_Interface {

    protected $objJrttAds;

    protected $statusMap;

    public function __construct($task)
    {
        parent::__construct($task);
        $this->objJrttAds = new Qdlib_Ds_Ad_QudaoJrttAds();

        //参数-枚举值映射关系
        $this->statusMap = array(
            1   => Qdlib_Const_TripartiteEnum::AD_STATUS_ENABLE,
            2   => Qdlib_Const_TripartiteEnum::AD_STATUS_DISABLE,
            3   => Qdlib_Const_TripartiteEnum::AD_STATUS_DELETE,
        );
    }

    public function getUpdateQdapi() :array
    {
        $arrConds = array(
            Qdlib_Util_DB::whereIn('ad_id', $this->ids),
            //'deleted'       => 0,
            //'channel'       => Qdlib_Const_Ads::JRTT,
        );
        $ret = $this->objJrttAds->getQudaoJrttAdsList($arrConds, ['adId', 'account', 'thirdUpdateTime', 'budgetMode']);

        if (empty($ret)) {
            throw new Exception('查询批量操作id列表失败');
        }

        //写入不存在id
        if (count($ret) != count($this->ids)) {
            $ids = array_column($ret, 'adId');
            $noExists = array_diff(array_keys($this->ids), $ids);

            $where = array(
                'taskId' => $this->task['id'],
                Qdlib_Util_DB::whereIn('pid', $noExists),
            );
            $update = $this->objBatchUpdateResult->updateQudaoBatchUpdateResult($where, array(
                'errFlag'       => Qdlib_Const_Ads::ERROR_CODE_STATUS_ERROR,
                'errno'         => Qdlib_Const_Ads::ERROR_CODE_STATUS_ERROR,
                'errMsg'        => Qdlib_Const_Ads::$errorCodeMap[Qdlib_Const_Ads::ERROR_CODE_STATUS_ERROR],
                'updateTime'    => time()
            ));
            if ($update === false) {
                throw new Exception('不存在任务数据库更新失败');
            }
        }

        //写入账号数据
        $aData = [];
        foreach ($ret as $val) {
            $value = $this->getOptStatus($val['adId']);
            $aData[$val['account']][$value][] = $val['adId'];
        }

        list($pathInfo, $paramsAll, $field) = $this->getParamsAll($ret);

        return [$pathInfo, $paramsAll, $field];
    }

    protected function getParamsAll($ret)
    {
        $paramsAll = [];
        switch ($this->task['flag']) {
            case Qdlib_Const_Ads::AD_HANDLE_JRTT_AD_STATUS:
                $pathInfo = '/qdapi/ad/status';
                $aData = [];
                foreach ($ret as $val) {
                    $value = $this->getOptStatus($val['adId']);
                    $aData[$val['account']][$value][] = $val['adId'];
                }
                foreach ($aData as $account => $val) {
                    foreach ($val as $value => $ids) {
                        $paramsAll[] = array(
                            'channel'       => Qdlib_Const_Ads::JRTT,
                            'account'       => $account,
                            'adIds'         => $ids,
                            'optStatus'     => $value,
                        );
                    }
                }
                $field = 'adIds';
                break;
            case Qdlib_Const_Ads::AD_HANDLE_JRTT_AD_BID:
                $pathInfo = '/qdapi/ad/update';
                $field = 'adId';
                foreach ($ret as $account => $val) {
                    $paramsAll[] = array(
                        'channel'       => Qdlib_Const_Ads::JRTT,
                        'account'       => $val['account'],
                        'adId'          => $val['adId'],
                        'bid'           => $this->getOptStatus($val['adId']),
                        'modifyTime'    => $val['thirdUpdateTime'],
                    );
                }
                break;
            case Qdlib_Const_Ads::AD_HANDLE_JRTT_AD_BUDGET:
                $pathInfo = '/qdapi/ad/budget';
                $field = 'adIds';
                foreach ($ret as $val) {
                    $paramsAll[] = array(
                        'channel'       => Qdlib_Const_Ads::JRTT,
                        'account'       => $val['account'],
                        'adIds'         => [$val['adId']],
                        'budgets'       => [$this->getOptStatus($val['adId'])],
                    );
                }

                break;
            case Qdlib_Const_Ads::AD_HANDLE_JRTT_AD_SCHEDULE_TIME:
                $pathInfo = '/qdapi/ad/update';
                $field = 'adId';
                foreach ($ret as $key => $val) {
                    $paramsAll[] = array(
                        'channel'       => Qdlib_Const_Ads::JRTT,
                        'account'       => $val['account'],
                        'adId'          => $val['adId'],
                        'scheduleTime'  => $this->getOptStatus($val['adId']),
                    );
                }
                break;
            case Qdlib_Const_Ads::AD_HANDLE_JRTT_AD_SCHEDULE_DATE:
                $pathInfo = '/qdapi/ad/update';
                $field = 'adId';
                foreach ($ret as $account => $val) {
                    $value = $this->getOptStatus($val['adId']);
                    $value = explode('|', $value);
                    $params = array(
                        'channel'       => Qdlib_Const_Ads::JRTT,
                        'account'       => $val['account'],
                        'adId'          => $val['adId'],
                    );
                    if (count($value) == 3) {
                        $params['scheduleType'] = 2;
                        $params['startTime'] = date('Y-m-d H:i', strtotime($value[1]));
                        $params['endTime'] = date('Y-m-d', strtotime($value[2])) . ' 23:59';
                    } else {
                        $params['scheduleType'] = 1;
                    }
                    $paramsAll[] = $params;

                }
                break;
            default:
                throw new Exception('今日头条广告不支持的更新' . $this->task['flag']);
        }
        return [$pathInfo, $paramsAll, $field];
    }

    public function dealUpdateRet() :bool
    {
        return true;
    }

    public function getCheckErrorIds($checkIds) :array {
        $list = $this->objJrttAds->getQudaoJrttAdsList(array(
            Qdlib_Util_DB::whereIn('ad_id', $checkIds),
            'channel'       => Qdlib_Const_Ads::JRTT,
        ), ['adId', 'status', 'budget', 'bid', 'scheduleTime', 'scheduleType', 'startDate', 'endDate', 'optStatus', 'deleted']);

        if (empty($list)) {
            throw new Exception('检测状态查询数据库失败');
        }

        $errIds = [];
        switch ($this->task['flag']) {
            case Qdlib_Const_Ads::AD_HANDLE_JRTT_AD_STATUS:
                foreach ($list as $val) {
                    $objective = $this->getOptStatus($val['adId']);

                    if ($objective == 3) {
                        if ($val['deleted'] != 1) {
                            Qdlib_Util_Log::warning('script_batch', 'jrttAd', 'checkErrId', '未删除', json_encode(compact('val', 'objective')));
                        }
                    } else {
                        $objective = $this->statusMap[$objective];
                        if ($val['optStatus'] != $objective) {
                            Qdlib_Util_Log::warning('script_batch', 'jrttAd', 'checkErrId', '状态未改变', json_encode(compact('val', 'objective')));
                            $errIds[] = $val['adId'];
                        }
                    }
                }
                break;
            case Qdlib_Const_Ads::AD_HANDLE_JRTT_AD_BID:
                foreach ($list as $val) {
                    $objective = round($this->getOptStatus($val['adId']) * 100);
                    if ($val['bid'] != $objective) {
                        Qdlib_Util_Log::warning('script_batch', 'jrttAd', 'checkErrId', 'bid未改变', json_encode(compact('val', 'objective')));
                        $errIds[] = $val['adId'];
                    }
                }
                break;
            case Qdlib_Const_Ads::AD_HANDLE_JRTT_AD_BUDGET:
                foreach ($list as $val) {
                    $objective = round($this->getOptStatus($val['adId']) * 100);
                    if ($val['budget'] != $objective) {
                        Qdlib_Util_Log::warning('script_batch', 'jrttAd', 'checkErrId', '预算未改变', json_encode(compact('val', 'objective')));
                        $errIds[] = $val['adId'];
                    }
                }
                break;
            case Qdlib_Const_Ads::AD_HANDLE_JRTT_AD_SCHEDULE_TIME:
                foreach ($list as $val) {
                    $objective = $this->getOptStatus($val['adId']);
                    if ($val['scheduleTime'] != $objective) {
                        Qdlib_Util_Log::warning('script_batch', 'jrttAd', 'checkErrId', '投放时段未改变', json_encode(compact('val', 'objective')));
                        $errIds[] = $val['adId'];
                    }
                }
                break;
            case Qdlib_Const_Ads::AD_HANDLE_JRTT_AD_SCHEDULE_DATE:
                foreach ($list as $val) {
                    $objective = $this->getOptStatus($val['adId']);
                    $objective = explode('|', $objective);
                    if (count($objective) == 3) {
                        if (Qdlib_Const_TripartiteEnum::$adScheduleTypeNum[$objective[0]] != $val['scheduleType'] || strtotime($objective[1]) != $val['startDate'] || strtotime($objective[2]) + 3600 * 24 - 60 != $val['endDate']) {
                            Qdlib_Util_Log::warning('script_batch', 'jrttAd', 'checkErrId', '投放时间未改变', json_encode(compact('val', 'objective')));
                            $errIds[] = $val['adId'];
                        }
                    } else {
                        if (Qdlib_Const_TripartiteEnum::$adScheduleTypeNum[$objective[0]] != $val['scheduleType']) {
                            $errIds[] = $val['adId'];
                        }
                    }
                }
                break;
            default:
                throw new Exception('今日头条广告不支持的更新' . $this->task['flag']);
        }

        return $errIds;
    }

    public function getNames($ids) :array
    {
        if (empty($ids)) {
            return [];
        }
        $list = $this->objJrttAds->getQudaoJrttAdsList([
            Qdlib_Util_DB::whereIn('ad_id', $ids),
        ], ['adId', 'adName']);
        if (empty($list)) {
            Qdlib_Util_Log::fatal('script', 'Qdlib_Service_Batch_Jrtt_Ads', 'getNames', '获取名称失败', json_encode(['ids' => $ids]));
            return [];
        }
        return array_column($list, 'adName', 'adId');
    }
}