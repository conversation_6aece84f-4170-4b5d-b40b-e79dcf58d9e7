<?php
/**
 * Created by PhpStorm.
 * User: 作业帮
 * Date: 2019/2/13
 * Time: 14:56
 */

/**
 * @file: PushMsg.php
 * @author: <EMAIL>
 * @brief: 长链接广播模式发布类
 * @detail: 为方便业务层广播消息，用此类型长连接可以对班级（全量）、分班（指定）、降级（未分班情况等）广播,以及对个人发送信息
 *           此类为Fudao product专用，默认参数不可修改
 */
class Hkzb_Service_MQSMsg
{
    const CMD_BROADCAST_MSG         = 16;     //命令号：发送消息-广播模式
    const CMD_SEND_MSG              = 12;     //命令号：发送消息-个人模式
    const MQS_PRODUCT_FUDAO         = 'FUDAO';//长连接product
    const MQS_PRODUCT_CHAT          = 'FUDAO_CHAT';//长连接product
    //广播模式常量 add  2019/1/28
    const BROADCAST_MODE_ALL        = 0; //全量广播：全体人员可见
    const BROADCAST_MODE_FEW        = 1; //降级广播：
    const BROADCAST_MODE_CLASS      = 2; //分班广播：只分班可见
    //消息生存时长
    const MSG_DATA_EXPIRE           = 120;            //(单位：秒)
    //消息清理等级
    const MSG_CLEAN_TYPE_STABLE     = 1; //未读不可清理
    const MSG_CLEAN_TYPE_VOLATILE   = 2; //未读系统可清理
    //消息投递类型
    const MSG_DELIVERY_TYPE_ALL     = 0; //全部投递
    const MSG_DELIVERY_TYPE_ONLINE  = 1; //只投递在线
    //错误码
    const ERRNO_PARAM               = -1; // 参数错误
    const ERRNO_RAL                 = -2; // ral网络交互错误
    const ERRNO_MQS                 = -3; // mqs处理失败
    //返回值预处理
    public $ret = array(
        'err_no' => 0,
        'err_msg' => 'success',
        'msgId' => 0,
    );


    /**
     * @param $lessonId          int 32 样例： 236542                      -接收者定位
     * @param array $classIds    array 样例： array(123,132);             -接收者定位
     * @param array $toUids      array 样例： array(200001112,231155124); -接收者定位
     * @param $fromUid           int 64 样例： 231155124                   -发送者
     * @param $msgBody           array                                    -发送内容
     * @param $msgType           int 32 样例： 300021                      -发送内容
     * @param $cleanType
     * @param $deliveryType
     * @param int $broadcastMode 类型int32 0：全班广播模式 1：单分班广播模式
     * @return array
     * @brief 广播发送长连接消息-简明函数
     * @调用说明：classIds没有时可填空数组，toUids没有时可空数组
     * @调用示例：1.学生聊天：$lessonId,$classIds,$toUids(班主任Uid)，$broadcastMode=1
     *            2.主讲发送答题卡：$lessonId,$classIds=array(),$toUids(所有班主任Uids），$broadcastMode=0
     *            其他类似情况请按示例填写
     */
    public function sendToClasses($lessonId,$classIds,$toUids=array(),$fromUid,$msgBody,$msgType,$broadcastMode=0, $cleanType = NULL, $deliveryType = NULL){
        /*
         * 参数检测
         */
        if(!is_int($lessonId) || $lessonId <= 0 || !is_array($classIds) || !is_array($toUids) || !is_int($fromUid)
            || !is_array($msgBody) || !is_int($msgType) || !is_int($broadcastMode)){
            $this->ret['err_no'] = self::ERRNO_PARAM;
            $this->ret['err_msg'] = "param error";
            return $this->ret;
        }
        /*
         * 根据参数区分广播模式
         */
        if(0 == $broadcastMode){
            $ret =  $this->sendToAllClasses($lessonId, $toUids, $fromUid, $msgBody, $msgType, $cleanType, $deliveryType);
        }else{
            $ret = $this->sendToFewClasses($lessonId, $classIds, $toUids, $fromUid, $msgBody, $msgType, $cleanType, $deliveryType);
        }
        /*
         * 返回结果
         */
        return $ret;
    }

    /**
     * @param $lessonId          int 32 样例： 236542                      -接收者定位
     * @param $classIds          array 样例： array(123,132);             -接收者定位
     * @param array $toUids      类型array 样例： array(200001112,231155124); -接收者定位
     * @param $fromUid           int 64 样例： 231155124                   -发送者
     * @param $msgBody           array                                    -发送内容
     * @param $msgType           int 32 样例： 300021                      -发送内容
     * @param int $cleanType     清理配置                                     -发送配置
     * @param int $deliveryType  投递配置                                     -发送配置
     * @param array $pushInfo    附加信息                                     -发送配置
     * @return array
     * @breif 分班或降级广播
     * @detail 1.当classIds参数不为空，则为指定class广播；反之，则为虚拟分班（降级）广播
     *          2.班主任需要另加在toUids中
     */
    public function sendToFewClasses($lessonId,$classIds,$toUids=array(),$fromUid,$msgBody,$msgType,$cleanType=self::MSG_CLEAN_TYPE_VOLATILE,$deliveryType=self::MSG_DELIVERY_TYPE_ONLINE,$pushInfo=array()){
        Bd_Log::addNotice('broadcastMsgToSingle', 1);
        if(NULL == $cleanType){
            $cleanType = self::MSG_CLEAN_TYPE_VOLATILE;
        }
        if(NULL == $deliveryType){
            $deliveryType=self::MSG_DELIVERY_TYPE_ONLINE;
        }
        /*
         * 参数校验start
         */
        //必备参数校验
        if (empty($msgType) || $fromUid <= 0 || !is_array($pushInfo) || !is_array($classIds) || !is_array($toUids)) {
            $this->ret['err_no'] = self::ERRNO_PARAM;
            $this->ret['err_msg'] = "param error";
            return $this->ret;
        }
        //如果没有传章节id，则初始化章节id
        if ($lessonId <= 0) {
            $arrRequest = Saf_SmartMain::getCgi();
            $lessonId = intval($arrRequest['request_param']['lessonId']);
        }
        //强校验lessonId
        if ($lessonId <= 0) {
            Bd_Log::warning("double write failed, can't get to_group_id");
            $this->ret['err_no'] = self::ERRNO_PARAM;
            $this->ret['err_msg'] = "param error";
            return $this->ret;
        }
        //强校验classIds
        $broadcastMode     = self::BROADCAST_MODE_FEW;
        foreach($classIds as $key=>$perClassId){
            if(empty($perClassId)){
                unset($classIds[$key]);
            }
        }
        if(empty($classIds)){
            $broadcastMode = self::BROADCAST_MODE_FEW;
        }else{
            if(!is_array($classIds)){
                Bd_Log::warning("classIds is error");
                $this->ret['err_no'] = self::ERRNO_PARAM;
                $this->ret['err_msg'] = "param error";
                return $this->ret;
            }
            $broadcastMode = self::BROADCAST_MODE_CLASS;
        }
        //校验pushInfo
        $pushInfoPs = array();
        if(!empty($pushInfo)){
            $pushInfoPs = array(
                'push_title'   => isset($pushInfo['pushTitle'])?strval($pushInfo['pushTitle']):'',
                'push_content' => isset($pushInfo['pushContent'])?$pushInfo['pushContent']:'',
                'push_uids'    => isset($pushInfo['pushUids'])?$pushInfo['pushUids']:array(),
            );
        }
        /*
         * 参数校验end
         */

        //封装消息与封装日志信息
        $msgLogKv = $this->setMessageKv($lessonId,$classIds,$fromUid,$msgType,$msgBody);

        /*
         * 发送消息start
         */
        $msgId = $this->send(self::CMD_BROADCAST_MSG,$lessonId,$classIds,$toUids,$fromUid,$msgBody,$pushInfoPs,$broadcastMode,$cleanType,$deliveryType);
        if(false === $msgId){
            Bd_Log::warning("send msg failed via ral");
            $this->ret['err_no']   = self::ERRNO_RAL;
            $this->ret['err_msg'] = 'talk with mqs error';
            return $this->ret;
        }
        Bd_Log::addNotice('fudao_mqs_msgkv_' . $msgId, json_encode($msgLogKv));
        $this->ret['msgId'] = $msgId;
        /*
         * 发送消息end
         */

        /*
         * 写入缓存队列start
         */
        $this->afterSent($msgId,$msgLogKv,$msgBody,$msgType,$lessonId,$classIds);
        /*
         * 写入缓存队列end
         */

        return $this->ret;
    }

    /**
     * @param $lessonId          int 32 样例： 236542                      -接收者定位
     * @param array $toUids      array 样例： array(123,132);             -接收者定位
     * @param $fromUid           int 64 样例： 231155124                   -发送者
     * @param $msgBody           array                                    -发送内容
     * @param $msgType           int 32 样例： 300021                      -发送内容
     * @param int $cleanType     清理配置                                     -发送配置
     * @param int $deliveryType  投递配置                                     -发送配置
     * @param array $pushInfo    附加信息                                     -发送配置
     * @return array
     * @brief   全量广播
     * @detail  1.广播给全班
     *          2.班主任需要另加在toUids中
     */
    public function sendToAllClasses($lessonId,$toUids=array(),$fromUid,$msgBody,$msgType,$cleanType=self::MSG_CLEAN_TYPE_STABLE,$deliveryType=self::MSG_DELIVERY_TYPE_ALL,$pushInfo=array())
    {
        Bd_Log::addNotice('broadcastMsgToAll', 1);
        if(NULL == $cleanType){
            $cleanType = self::MSG_CLEAN_TYPE_STABLE;
        }
        if(NULL == $deliveryType){
            $deliveryType=self::MSG_DELIVERY_TYPE_ALL;
        }
        /*
         * 参数校验start
         */
        //必备参数校验
        if (empty($msgType) || $fromUid <= 0 || !is_array($pushInfo) || !is_array($toUids)) {
            $this->ret['err_no'] = self::ERRNO_PARAM;
            $this->ret['err_msg'] = "param error";
            return $this->ret;
        }
        //如果没有传章节id，则初始化章节id
        if ($lessonId <= 0) {
            $arrRequest = Saf_SmartMain::getCgi();
            $lessonId = intval($arrRequest['request_param']['lessonId']);
        }
        //强校验lessonId
        if ($lessonId <= 0) {
            Bd_Log::warning("double write failed, can't get to_group_id");
            $this->ret['err_no'] = self::ERRNO_PARAM;
            $this->ret['err_msg'] = "param error";
            return $this->ret;
        }
        //校验pushInfo
        if(!empty($pushInfo)){
            $pushInfo = array(
                'push_title'   => isset($pushInfo['pushTitle'])?strval($pushInfo['pushTitle']):'',
                'push_content' => isset($pushInfo['pushContent'])?$pushInfo['pushContent']:'',
                'push_uids'    => isset($pushInfo['pushUids'])?$pushInfo['pushUids']:array(),
            );
        }
        /*
         * 参数校验end
         */
        //封装消息与封装日志信息
        $msgLogKv = $this->setMessageKv($lessonId,array(),$fromUid,$msgType,$msgBody);
        /*
         * 发送消息start
         */
        $msgId = $this->send(self::CMD_BROADCAST_MSG,$lessonId,array(),$toUids,$fromUid,$msgBody,$pushInfo,self::BROADCAST_MODE_ALL,$cleanType,$deliveryType);
        if(false === $msgId){
            Bd_Log::warning("send msg failed via ral");
            $this->ret['err_no']   = self::ERRNO_RAL;
            $this->ret['err_msg'] = 'talk with mqs error';
            return $this->ret;
        }
        Bd_Log::addNotice('fudao_mqs_msgkv_' . $msgId, json_encode($msgLogKv));
        $this->ret['msgId'] = $msgId;
        /*
         * 发送消息end
         */
        /*
         * 写入缓存队列start
         */
        $this->afterSent($msgId,$msgLogKv,$msgBody,$msgType,$lessonId,array());
        /*
         * 写入缓存队列end
         */
        return $this->ret;
    }

    /**
     * @param int $lessonId
     * @param array $toUids
     * @param  $fromUid
     * @param array $msgBody
     * @param int $msgType
     * @param int $cleanType
     * @param int $deliveryType
     * @param array $pushInfo
     * @param bool $isAfter
     * @return array
     * @throws
     * @brief 对单个人发送消息
     */
    public function sendToUids($lessonId, $toUids = array(), $fromUid, $msgBody, $msgType, $cleanType = self::MSG_CLEAN_TYPE_STABLE,
                               $deliveryType = self::MSG_DELIVERY_TYPE_ALL, $pushInfo = array(), $isAfter = true)
    {
        Bd_Log::addNotice('broadcastMsgToAll', 1);
        /*
         * 参数校验start
         */
        //必备参数校验
        if (empty($msgType) || $fromUid <= 0 || !is_array($pushInfo) || !is_array($toUids) || empty($toUids)) {
            $this->ret['err_no'] = self::ERRNO_PARAM;
            $this->ret['err_msg'] = "param error";
            return $this->ret;
        }
        //如果没有传章节id，则初始化章节id
        if ($lessonId <= 0) {
            $arrRequest = Saf_SmartMain::getCgi();
            $lessonId = intval($arrRequest['request_param']['lessonId']);
        }
        //强校验lessonId
        if ($lessonId <= 0) {
            Bd_Log::warning("double write failed, can't get to_group_id");
            $this->ret['err_no'] = self::ERRNO_PARAM;
            $this->ret['err_msg'] = "param error";
            return $this->ret;
        }
        //校验pushInfo
        if(!empty($pushInfo)){
            $pushInfo = array(
                'push_title'   => isset($pushInfo['pushTitle'])?strval($pushInfo['pushTitle']):'',
                'push_content' => isset($pushInfo['pushContent'])?$pushInfo['pushContent']:'',
                'push_uids'    => isset($pushInfo['pushUids'])?$pushInfo['pushUids']:array(),
            );
        }
        /*
         * 参数校验end
         */
        //封装消息与封装日志信息
        $msgLogKv = $this->setMessageKv($lessonId,array(),$fromUid,$msgType,$msgBody);
        /*
         * 发送消息start
         */
        $msgId = $this->send(self::CMD_SEND_MSG,$lessonId,array(),$toUids,$fromUid,$msgBody,$pushInfo,0,$cleanType,$deliveryType);
        if(false === $msgId){
            Bd_Log::warning("send msg failed via ral");
            $this->ret['err_no']   = self::ERRNO_RAL;
            $this->ret['err_msg'] = 'talk with mqs error';
            return $this->ret;
        }
        Bd_Log::addNotice('fudao_mqs_msgkv_' . $msgId, json_encode($msgLogKv));
        $this->ret['msgId'] = $msgId;
        /*
         * 发送消息end
         */
        /*
         * 写入缓存队列start
         */
        $isAfter && $this->afterSent($msgId, $msgLogKv, $msgBody, $msgType, $lessonId, array());
        /*
         * 写入缓存队列end
         */
        return $this->ret;
    }

    /**
     * @param $msgId
     * @param $msgLogKv    日志内容
     * @param $msgBody     消息内容
     * @param $signalNo    消息类别
     * @param $lessonId    章节Id
     * @param $classIds
     * @brief * 发送消息成功后的操作
     * 1. 写入缓存队列
     */
    private function afterSent($msgId,$msgLogKv,$msgBody,$signalNo,$lessonId,$classIds)
    {
        try {
            //非聊天的信令直接存储
            if (Hkzb_Const_FudaoMsgSignal::NEW_MSG != $signalNo) {
                Hkzb_Ds_Fudao_ShortMessage::pushShortMessage($lessonId, $signalNo, $msgId, $msgLogKv);
                return;
            }
            if (empty($classIds)) {
                return;
            }
            $classId = intval($classIds[0]);
            //聊天的信令，将聊天内容存放在有过期时间的redis队列中
            $msgListCacheKey = sprintf(Hkzb_Ds_Fudao_ShortMessage::MSG_LIST_CACHE_KEY, $classId);
            //$objCache = Hk_Service_RedisClient::getInstance("zhibo");
//            $objCacheV2 =  Liveservice_Util_RedisService::getZhiboInstance();
            $objCacheV2 =  Liveservice_Util_StoredService::getZhiboInstance();
            $objCacheV2->lpush($msgListCacheKey, json_encode($msgBody));
            $objCacheV2->expire($msgListCacheKey, Hkzb_Ds_Fudao_ShortMessage::MSG_LIST_CACHED_EXPIRE);
            $listLength = $objCacheV2->llen($msgListCacheKey);
            if ($listLength > Hkzb_Ds_Fudao_ShortMessage::MSG_LIST_CACHE_MAX_LEN) {
                $objCacheV2->rpop($msgListCacheKey);
            }
        } catch (Exception $ex) {

        }

    }

    /**
     * @param $lessonId
     * @param array $classIds
     * @param $fromUid
     * @param $signalNo
     * @param $msgBody
     * @return array
     * @brief 封装消息、封装日志信息
     */
    private function setMessageKv($lessonId,$classIds=array(),$fromUid,$signalNo,&$msgBody)
    {
        // 【注意】：请不要改动里面的任何key的顺序！！！！顺序！！！！！！！顺序！！！！！！！！！！！
        $msgBody = array(
            'from_uid' => $fromUid,
            'sig_no' => $signalNo,
            'data' => $msgBody,
            'to_lessonid' => $lessonId,
            'to_classid' => json_encode($classIds),
        );
        // 用于日志记录的kv结构 (长连接不需要此结构)
        $msgLogKv = array(
            'from_uid' => $fromUid,
            'to_uid' => 0,
            'to_group_id' => $lessonId,
            'msg_id' => 0,
            'msg_type' => 0,
            'msg_content' => json_encode($msgBody),
            'msg_time' => intval(microtime(true) * 1000),
            'old_protocol' => 1, //旧版协议
        );
        return $msgLogKv;
    }

    /**
     * @param $cmdno
     * @param $lessonId
     * @param array $classIds
     * @param $toUids
     * @param $msgData
     * @param $pushInfo
     * @param int $fromUid
     * @param $broadcastMode      广播模式
     * @param $cleanType          清理配置
     * @param $deliveryType       投递类型
     * @return bool|int|null
     * @brief 发送消息
     */
    private function send($cmdno=self::CMD_BROADCAST_MSG,$lessonId,$classIds=array(),$toUids=array(),$fromUid,$msgData,$pushInfo,$broadcastMode,$cleanType,$deliveryType)
    {
        if (empty($msgData)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'Msg Data is empty');
        }
        if ( empty($lessonId) || $lessonId<=0 ) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'lessonId is empty');
        }

        //使用普通还是降级长连接
        $product = self::MQS_PRODUCT_FUDAO;
        if( self::MSG_CLEAN_TYPE_VOLATILE== $cleanType){
            //降级长连接
            $product = self::MQS_PRODUCT_CHAT;

        }else{
            $product = self::MQS_PRODUCT_FUDAO;
        }
        $product = 'mqs_' . strtolower($product);
        $requestPack = array(
            //必须参数
            'cmdno' => $cmdno,
            'product' => self::MQS_PRODUCT_FUDAO,
            'msg_data' => json_encode($msgData),
            'msg_expire_time' => self::MSG_DATA_EXPIRE,

            //可选参数
            'from_uid'         => $fromUid,
            'msg_clean_type'   => $cleanType,
            'msg_delivery_type'=> $deliveryType
        );

        //广播必须参数
        if(self::CMD_BROADCAST_MSG == $cmdno){
            $requestPack['to_lessonid'] = $lessonId;
            $requestPack['broadcast_mode'] = $broadcastMode;
        }

        if(!empty($classIds)){
            $requestPack['to_classids'] = $classIds;
        }
        if(!empty($toUids)){
            $requestPack['to_uids']     = $toUids;
        }
        if (!empty($pushInfo)) {
            $requestPack['push_info'] = $pushInfo;
        }
        $ret = $this->_requestMqs($requestPack,$product);

        $msgId = (false !== $ret && intval($ret['msg_id']) > 0) ? intval($ret['msg_id']) : null;

        return is_null($msgId) ? false : $msgId;
    }


    /**
     * @param $requestPack
     * @param $product
     * @return bool
     * @brief  请求Mqs并获取返回结果
     */
    private function _requestMqs($requestPack, $product)
    {
        Hk_Util_Log::start('ds_send_msg');
        $ret = ral($product, MAIN_APP, $requestPack);
        Hk_Util_Log::stop('ds_send_msg');

        if (false === $ret) {
            $errno = ral_get_errno();
            $errmsg = ral_get_error();
            $protocolStatus = ral_get_protocol_code();
            Bd_Log::warning("Request mqs failed! [Request pack]: " . json_encode($requestPack) . "[Ral Error]: (errno:$errno errmsg:$errmsg protocol_status:$protocolStatus)");
            return false;
        }

        if (0 != $ret['err_no']) {
            Bd_Log::warning("Request mqs failed! [Error Msg]: ({$ret['err_no']}) " . $ret['err_msg'] . " [Request pack]: " . json_encode($requestPack));
            return false;
        }
        return $ret;
    }
}