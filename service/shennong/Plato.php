<?php
/**
 * Created by IntelliJ IDEA.
 * @Description Sku信息
 * @Date 2021/11/3 10:27 上午
 * @<NAME_EMAIL>
 */

class Qdlib_Service_Shennong_Plato
{
    public static function skuBiz($skuIds,$saleChannel)
    {
        $results = $ret = [];
        if (empty($skuIds) || empty($saleChannel)) {
            return $ret;
        }
        $skuIds = array_unique($skuIds);
        //这里要获取神农的sku详情及库存接口
        $params['userId'] = 0;
        $params['saleChannelId'] = $saleChannel;
        $params['businessType'] = 0;
        $params['nowTime'] = time();
        $params['orderType'] = 'convention';

        foreach (array_chunk($skuIds,20) as $sku){
            $skuArr = [];
            foreach ($sku as $skuId){
                $skuArr[] = ['skuId'=>$skuId,'count'=>1];
            }
            $params['skuIdCount'] = json_encode($skuArr);
            list($isTrue, $errNo, $data) = Qdlib_Service_Shennong_Common::request('/shennong/plato/skucheckbiz', $params);
            if(!$isTrue){
                //这里要报警
                Qdlib_Util_Log::warning('shennong', 'Qdlib_Service_Shennong_Plato', 'skuBiz', '请求shennong接口失败', json_encode($params));
                return false;
            }
            if(count($data['bizList'])!==count($sku)){
                Qdlib_Util_Log::warning('shennong', 'Qdlib_Service_Shennong_Plato', 'skuBiz', '请求shennong接口返回与请求sku个数有差异', json_encode(['resCnt'=>count($data['bizList']),'reqCnt'=>count($sku)]));
                return false;
            }
            $ret = array_merge($ret,$data['bizList']);
        }
        foreach ($ret as $bizInfo){
            $skuId = $bizInfo['skuId'];
            $results[$skuId] = $bizInfo;
        }
        return $results;
    }

    public static function getBizCanBuy($skuIds,$saleChannel=0)
    {
        $results = [];
        if (empty($skuIds) || empty($saleChannel)) {
            return $results;
        }
        $info = self::skuBiz($skuIds,$saleChannel);
        if(!$info){
            return false;
        }
        foreach ($info as $skuId=>$val){
            $results[$skuId][$saleChannel]['canBuy'] = $val['canBuy'];
            $results[$skuId][$saleChannel]['saleStopTime'] = $val['biz']['stopTime'];
        }
        return $results;
    }

}