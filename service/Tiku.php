<?php

/**
 * 题库对外通用服务.
 *
 * <AUTHOR>
 * @version 1.0.0
 */
final class Hkzb_Service_Tiku
{
    const BNS = 'course';
    
    /**
     * @var string
     */
    protected static $APP_ID;
    /**
     * @var string
     */
    protected static $APP_SECRET;
    
    /**
     * @param string $app
     * @param string $secret
     */
    public static function init($app, $secret)
    {
        self::$APP_ID = $app;
        self::$APP_SECRET = $secret;
    }
    
    /**
     * @param string $service 服务名
     * @param array $params 请求参数
     * @param string $method
     * @param array $headers
     *
     * @return array
     */
    public static function call($service, array $params = [], $method = 'POST', array $headers = [])
    {
        $uri = self::buildPathInfo($service);
        if ($headers) {
            $headers['pathinfo'] = $uri;
        }
        
        //ral打包方式不支持非字符串索引，否则会pack报错
        if ($service === 'questioncreate' && !array_key_exists('category', $params)) {
            $params = ['questions' => $params];
        } elseif ($service === 'questionedit' && !array_key_exists('tid', $params)) {
            $params = ['questions' => $params];
        }
        
        return self::invoke(self::BNS, $headers ? $headers : $uri, $params, $method, false);
    }
    
    /**
     * @param string $service
     *
     * @return string
     */
    protected static function buildPathInfo($service)
    {
        $module = 'service';
        if ($service === 'questionquery') {
            $module = 'questioninfoapi';
            $service = 'gettidsquestinfo';
        }
        
        return '/zbtikuapi/' . $module . '/' . $service;
    }
    
    
    /**
     * @param string $service
     * @param string|array $uri
     * @param mixed $payload
     * @param string $method
     * @param bool $raw
     *
     * @return mixed
     * @throws Exception
     */
    protected static function invoke($service, $uri, $payload = null, $method = 'GET', $raw = false)
    {
        $headers = [];
        if (is_string($uri)) {
            $headers['pathinfo'] = $uri;
        } elseif (is_array($uri)) {
            $headers = $uri;
        }
        //鉴权
        if (!isset($payload['appId'])) {
            $payload['appId'] = self::$APP_ID;
        }
        $secret = isset($payload['appSecret']) ? $payload['appSecret'] : self::$APP_SECRET;
        if (empty($payload['appId']) || empty($secret)) {
            Bd_Log::warning('appId or secret is invalid');
            
            return false;
        }
        
        //请求时间
        $headers[Hkzb_Ds_ZbtikuApi_Common_Sign::SIGN_SEND_TIMESTAMP] = isset($_SERVER['REQUEST_TIME']) ? $_SERVER['REQUEST_TIME'] : time();
        
        //签名
        $sign = (new Hkzb_Ds_ZbtikuApi_Common_Sign())->createSign(
            $secret,
            $method,
            $headers['pathinfo'],
            $payload,
            $headers
        );
        $headers[Hkzb_Ds_ZbtikuApi_Common_Sign::SIGN_SEND_AUTHORIZATION] = $sign;
        
        try {
            ral_set_logid(self::getLogId());
            ral_set_log(RAL_LOG_MODULE, defined('MAIN_APP') ? MAIN_APP : 'unknown');
            
            $response = ral($service, $method, $payload, mt_rand(), $headers);
            if (false === $response) {
                Bd_Log::warning(sprintf(
                    '[ral:%s]Fetch response failed. Code: %s, Message: %s, ProtocolCode: %s',
                    $service,
                    ral_get_errno(),
                    ral_get_error(),
                    ral_get_protocol_code()
                ));
                
                return false;
            }
            
            return $raw ? $response : self::decode($response);
        } catch (Exception $e) {
            Bd_Log::fatal($e->getMessage(), $e->getCode());
            
            throw $e;
        } catch (Throwable $e) {
            Bd_Log::fatal($e->getMessage(), $e->getCode());
            
            return false;
        }
    }
    
    
    /**
     * @return int|string
     */
    protected static function getLogId()
    {
        return Bd_Log::genLogID();
    }
    
    /**
     * @param mixed $value
     * @param int $option
     *
     * @return string
     */
    protected static function encode($value, $option = JSON_UNESCAPED_UNICODE)
    {
        $json = \json_encode($value, $option);
        static::handleJsonError(\json_last_error());
        
        return $json;
    }
    
    /**
     * @param string $value
     * @param bool $asArray
     *
     * @return mixed
     */
    protected static function decode($value, $asArray = true)
    {
        $result = \json_decode($value, $asArray);
        static::handleJsonError(\json_last_error());
        
        return $result;
    }
    
    /**
     * @param int $lastError
     *
     * @throws InvalidArgumentException
     */
    protected static function handleJsonError($lastError)
    {
        if ($lastError === JSON_ERROR_NONE) {
            return;
        }
        
        throw new InvalidArgumentException(
            \function_exists('json_last_error_msg') ? json_last_error_msg() : 'Unknown JSON encoding/decoding error.',
            $lastError
        );
    }
}
