<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   Lesson.php
 * <AUTHOR>
 * @date   2018/7/2 下午10:06
 * @brief
 **/


class Zb_Service_Dal_Lesson {

    //章节状态
    const STATUS_UNFINISH       = 0; //未结束
    const STATUS_FINISHED       = 1; //已结束（正常结束）
    const STATUS_DELETED        = 2; //已删除（异常结束）

    //上课状态
    const LESSON_CLASSING_NO  = 0; //章节非上课中
    const LESSON_CLASSING_YES = 1; //章节上课中

    private static $service     = 'zbcore_dal';
    private static $serviceUri  = '/zbcore/api/api';
    private static $module      = 'dal';
    private static $entity      = 'lesson';


    /**
     * 课程章节信息接口
     *
     * @param array     $lessonIds      待查询章节ID
     * @param array     $lessonFields   接口返回的Lesson字段
     * @param array     $options        扩展数据
     * @return array
     */
    public static function getKVByLessonId($lessonIds, $lessonFields, $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getKV');
        $arrParams = array(
            'lessonIds'     => $lessonIds,
            'lessonFields'        => $lessonFields,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 根据日期KV获取数据
     * @param string|int $date 日期，格式：Ymd， like 20181126
     * @param array $courseFields 课程字段列表，为保证性能请按需提供
     * @param array $lessonFields 章节字段列表，为保证性能请按需提供
     * @param array $options 若isRalMulti元素不为空，则仅返回ral_multi所有的参数而不发出请求
     * @param int $offset 每次获取的起始偏移量
     * @param int $limit 每次获取的数量(最大100)
     * @return array
     */
    public static function getKVByDate($date, $courseFields, $lessonFields, $options=array(), $offset= 0, $limit=0) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getKVByDate');
        $arrParams = array(
            'date'     => $date,
            'courseFields'        => $courseFields,
            'lessonFields'        => $lessonFields,
            'pn'                => intval($offset),
            'rn'                => intval($limit),
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 根据日期获取KV分页参数
     * @param string|int $date 日期，格式：Ymd， like 20181126
     * @return array ['total'=>1000,'pageMax'=>100]
     */
    public static function getDateCnt($date) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getDateCnt');
        $arrParams = array(
            'date'     => $date
        );

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }
}
