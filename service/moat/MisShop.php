<?php

/**
 * 商品
 */
class Qdlib_Service_Moat_MisShop
{
    /**
     * 店铺基本信息列表
     * https://ssv.zuoyebang.cc/static/open-sell/#/docs/list?releaseId=1627359652&node=4_67_70_&docType=1
     */
    public static function getshopbaselist(string $shopIds = '', string $source = '', $pn = 1, $rn = 80)
    {
        return Qdlib_Service_Moat_Common::request('/misshop/api/getshopbaselist', [
            'shopIds' => $shopIds,
            'source' => $source,
            'pn' => $pn,
            'rn' => $rn,
        ]);
    }

    public static function retrunGetShopBaseListOrFalse(string $shopIds = '', string $source = '', $pn = 1, $rn = 80)
    {
        list($isTrue, , $data) = self::getshopbaselist($shopIds, $source, $pn, $rn);
        if (!$isTrue || empty($data)) {
            return false;
        }
        return $data;
    }
}