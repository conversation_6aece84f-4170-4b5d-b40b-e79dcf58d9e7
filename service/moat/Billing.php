<?php
/**
 * Created by IntelliJ IDEA.
 * @Description billing订单（将废弃）
 * @Date 2021/10/26 4:08 下午
 * @<NAME_EMAIL>
 */

class Qdlib_Service_Moat_Billing {

    const ZB_TRANSACTION_ID = 2002; // 发号器服务id

    // 订单详情接口
    public static function orderDetail($params)
    {
        return Qdlib_Service_Moat_Common::request('/billing/trade/detailpro', $params);
    }

    // 商品列表
    public static function skuidInfoList($params)
    {
        return Qdlib_Service_Moat_Common::request('/billing/sku/getskulistfromids', $params);
    }

    // 预下单
    public static function prepayment($params)
    {
        return Qdlib_Service_Moat_Common::request('/billing/payment/prepayment', $params);
    }

    // 下单校验+下单操作
    public static function placeOrder($params)
    {
        $params['transactionId'] = Qdlib_Service_Moat_Common::single(self::ZB_TRANSACTION_ID);
        return Qdlib_Service_Moat_Common::request('/billing/pay/placeorder', $params);
    }

    // 关单
    public static function closeOrder($params)
    {
        return Qdlib_Service_Moat_Common::request('/billing/pay/closeorder', $params);
    }

    // 检查是否关单成功
    public static function checkCloseOrder($params)
    {
        return Qdlib_Service_Moat_Common::request('/billing/pay/checkcloseorder', $params);
    }

    // 检查是否支付成功
    public static function checkCommonOrder($params)
    {
        return Qdlib_Service_Moat_Common::request('/billing/pay/checkcommonorder', $params);
    }

    // 获取sku以及赠品信息
    public static function getBatchSkuSellInfo($billingParams)
    {
        if (!is_array($billingParams['skuList']) && empty($billingParams['skuList'])) {
            return null;
        }
        $skuArr = array_chunk($billingParams['skuList'], 10, false);
        unset($billingParams['skuList']);
        $skuParams = [];
        foreach ($skuArr as $item) {
            $tmp = $billingParams;
            $tmp['skuIds'] = join($item, ',');
            $key = ($tmp['skuIds']);
            $skuParams[$key] = $tmp;
        }
        $headers = array(
            'cookie' => $_COOKIE,
            'pathinfo' => "/billing/sku/getskuandgiftbizinfo",
        );
        $skuData = Qdlib_Service_Moat_Common::sendMulti($headers, $skuParams);
        $returnData = [];
        $errnoData = [];
        foreach ($skuData as $key => $sku) {
            if ($sku['errNo'] == 0 && is_array($sku['data']) && !empty($sku['data'])) {
                $returnData = array_merge($returnData, $sku['data']);
            } else {
                $errnoData[] = [
                    'errSkuIds' => explode(",", $key),
                    'errNo' => $sku['errNo'],
                    'errstr' => $sku['errstr']
                ];
            }

        }
        return ['skuList' => $returnData, 'errSku' => $errnoData];
    }
}