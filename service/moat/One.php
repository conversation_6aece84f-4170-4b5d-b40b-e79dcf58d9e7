<?php

/**
 * 订单聚合
 */
class Qdlib_Service_Moat_One
{
    /**
     * 订单详情接口
     * http://ssv.zuoyebang.cc/static/open-sell/index.html#/detail-api?draftId=380
     */
    public static function detail(int $userId, array $orderIds)
    {
        return Qdlib_Service_Moat_Common::request('/one/union/detail', [
            'userId' => $userId,
            'orderIds' => $orderIds,
        ]);
    }

    public static function returnDetailOrFalse(int $userId, int $orderId)
    {
        list($isTrue, , $data) = self::detail($userId, [$orderId]);
        if (!$isTrue) {
            return false;
        }
        return empty($data[$orderId]['skuList']) ? false : $data[$orderId];
    }

    /**
     * 3.0订单详情接口
     * https://ssv.zuoyebang.cc/static/open-sell/index.html#/docs/list?releaseId=1662520955&node=4_72_326_&docType=1
     * @param int $userId
     * @param array $orderIds
     * @return array
     */
    /*public static function detailV3(int $userId, array $orderIds)
    {
        return Qdlib_Service_Moat_Common::request('/one/adapter/oneuniondetail', [
            'userId' => $userId,
            'orderIds' => json_encode($orderIds),
            'nowms' => 1,
        ]);
    }*/

    public static function detailV3(int $userId, array $orderIds)
    {
        return Qdlib_Service_Gotoufang_Order::getOrderDetail($userId,$orderIds);
    }

    public static function returnDetailV3OrFalse(int $userId, int $orderId)
    {
        list($isTrue, , $data) = self::detailV3($userId, [$orderId]);
        if (!$isTrue) {
            return false;
        }
        return empty($data[$orderId]['skuList']) ? false : $data[$orderId];
    }

    /**
     * 订单列表接口
     * https://ssv.zuoyebang.cc/static/open-sell/index.html#/docs/list?releaseId=1621421270&node=4_72_73_&docType=1
     */
    public static function orderList(int $userId, int $page, int $pageSize)
    {
        return Qdlib_Service_Moat_Common::request('/one/union/list', [
            'userId' => $userId,
            'tabStatus' => 0,
            'page' => $page,
            'pageSize' => $pageSize,
        ]);
    }

    public static function orderV3List(int $userId, int $page, int $pageSize)
    {
        return Qdlib_Service_Moat_Common::request('/one/adapter/oneunionlist', [
            'userId' => $userId,
            'tabStatus' => 0,
            'page' => $page,
            'pageSize' => $pageSize,
        ]);
    }

    public static function orderListByBusinessId($userId,$businessId,$offset=0,$limit=100){
        return Qdlib_Service_Moat_Common::request('/one/open/search', [
            'businessId' => $businessId,
            'userIdList' => [$userId],
            'offset' => $offset,
            'limit' => $limit,
        ]);
    }

    public static function returnOrderListByBusinessIdOrFalse($userId,$businessId)
    {
        $page = 1;
        $limit = 100;
        $res = [];
        do{
            $offset = ($page -1) * $limit;
            list($isTrue, , $data) = self::orderListByBusinessId($userId,$businessId, $offset,$limit);
            if (!$isTrue) {
                return false;
            }
            if(empty($data['list'])){
                return $res;
            }
            $res = array_merge($res,$data['list']);
            $totalPage = ceil($data['total']/$limit);
            if($totalPage<=$page){
                break;
            }
            $page++;
        }while(true);
        return $res;
    }
}