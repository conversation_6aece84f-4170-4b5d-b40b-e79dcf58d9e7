<?php

/**
 * 商品
 */
class Qdlib_Service_Moat_Newgoodsplatform
{
    const ENTITY_TYPE_NORMAL = 1;    //普通库存
    const ENTITY_TYPE_YK = 6;    //一课库存

    const REDIS_KEY_SKUINFO_PREFIX = 'moat:skuinfo:skuid:';
    const REDIS_KEY_SKUINFO_TTL = 300;

    /**
     * 渠道库存查询
     * http://ssv.zuoyebang.cc/static/open-sell/index.html#/detail-api?draftId=455
     * @param array $entityIds skuid数组
     * @param int $entityType
     * @param int $saleChannelId
     * @return array
     */
    public static function getEntityStock(array $entityIds, int $entityType = self::ENTITY_TYPE_NORMAL, int $saleChannelId = 0)
    {
        return Qdlib_Service_Moat_Common::request('/newgoodsplatform/goodsskuapi/getentitystock', [
            'entityIds' => $entityIds,
            'entityType' => $entityType,
            'saleChannelId' => $saleChannelId,
        ]);
    }

    /**
     * 获取代销商渠道组库存
     * https://ssv.zuoyebang.cc/static/open-sell/index.html#/docs/list?releaseId=1648627801&node=4_67_201_&docType=1
     */
    public static function getAgentStock(array $entityIds, int $entityType = self::ENTITY_TYPE_NORMAL, int $agentId = 0)
    {
        return Qdlib_Service_Moat_Common::request('/goodsservice/api/salegoals/agencylistbyid', [
            'entityIdsStr' => json_encode($entityIds),
            'entityType' => $entityType,
            'agencyId' => $agentId,
        ]);
    }


    /**
     * 获取商品信息的接口
     * @wiki http://ssv.zuoyebang.cc/static/open-sell/index.html#/detail-api?releaseId=1620823475
     * @param array $skuIds
     * @return array
     */
    public static function getGoodsSkuKvBySkuIdV2($skuIds)
    {
        list($isTrue, $errNo, $data) = Qdlib_Service_Moat_Common::request('/newgoodsplatform/sku/getgoodsskukvbyskuidv2', ['skuIds' => $skuIds]);
        if (!$isTrue || empty($data)) {
            return [];
        }
        return $data;
    }

    /**
     * sku KV接口
     * http://yapi.zuoyebang.cc/project/349/interface/api/30098
     */
    public static function getgoodsskukvbyskuid(array $skuIds, int $saleChannelId, int $interfaceId = 0)
    {
        return Qdlib_Service_Moat_Common::request('/newgoodsplatform/goodsskuapi/getgoodsskukvbyskuid', [
            'raw' => [
                'skuIds' => $skuIds,
                'saleChannelId' => $saleChannelId,
                'interfaceId' => $interfaceId,
            ],
        ], 'get');
    }

    /**
     * 获取商品所有分类
     * http://yapi.zuoyebang.cc/project/349/interface/api/40027
     */
    public static function getallcategory()
    {
        return Qdlib_Service_Moat_Common::request('/newgoodsplatform/goodscategoryfield/getallcategory', [], 'get');
    }

    /**
     * 通过spu对应skuIdList
     * https://ssv.zuoyebang.cc/static/open-sell/index.html#/docs/list?releaseId=1621413964&node=4_67_70_&docType=1
     */
    public static function getskuidlistbyspuids(array $spuIds, $source = 0)
    {
        return Qdlib_Service_Moat_Common::request('/newgoodsplatform/goodsskuapi/getskuidlistbyspuids', [
            'raw' => [
                'spuIds' => $spuIds,
                'source' => $source,
            ],
        ], 'get');
    }

    /**
     * 获取sku对应的年级和期次
     * getGoodsSkuKvBySkuIdV2
     */
    public static function getSkuList(array $skuIds)
    {
        list($isTrue, $errNo, $data) = Qdlib_Service_Moat_Common::request('/newgoodsplatform/sku/getgoodsskukvbyskuidv2', ['skuIds' => $skuIds]);
        if (!$isTrue) {
            return [$isTrue, $errNo, $data];
        }

        $result = [];
        $unitArr = $unitSkuIds = $zskuToSuit = $zunitSkuIds = $skuToSuit = [];

        if (is_array($data)) {
            //判断是否有组合sku
            foreach ($data as $skuInfo){
                if($skuInfo['extData']['isOmsVc']==1 && $skuInfo['producerSys']==35){
                    $unitArr[] = $skuInfo['skuId'];
                }
            }
            if (count($unitArr)>0){
                Qdlib_Util_Log::addNotice('getSkuList_suit' , json_encode($unitArr, JSON_UNESCAPED_UNICODE));
                list($isTrue, $errNo, $suitData) = Qdlib_Service_Moat_Portals::getSuitBySku($unitArr);
                if (!$isTrue) {
                    return [$isTrue,$errNo , $suitData];
                }
                if(!empty($suitData)){
                    foreach ($suitData as $suitVal){
                        if($suitVal['skuId']>=0){
                            $zunitSkuIds[$suitVal['skuId']] = $suitVal['suitSkuId'];
                            $unitSkuIds[$suitVal['suitSkuId']] = $suitVal['skuId'];
                        }
                    }
                    list($isTrue, $errNo, $unitSkuData) = Qdlib_Service_Moat_Common::request('/newgoodsplatform/sku/getgoodsskukvbyskuidv2', ['skuIds' => array_keys($zunitSkuIds)]);
                    if (!$isTrue) {
                        return [$isTrue, $errNo, $unitSkuData];
                    }

                    foreach ($unitSkuData as $val){
                        $zskuToSuit[$val['skuId']] = $val;
                    }

                    foreach ($unitSkuIds as $key=>$val){
                        $kskuid = $val;
                        $skuToSuit[$key] = $zskuToSuit[$kskuid];
                    }



                }
            }
            Qdlib_Util_Log::addNotice('stock_skuToSuit', json_encode($skuToSuit, JSON_UNESCAPED_UNICODE));

            foreach ($data as $skuInfo) {
                $item = [
                    'skuId' => $skuInfo['skuId'],
                    'gradeId' => -1,
                    'seasonId' => -1,
                ];
                if(isset($skuToSuit[$skuInfo['skuId']]) && !empty($skuToSuit[$skuInfo['skuId']])){
                    $skuInfo = $skuToSuit[$skuInfo['skuId']];
                    Qdlib_Util_Log::addNotice('skuToSuit_to_trans_'.$skuInfo['skuId'], json_encode($skuInfo, JSON_UNESCAPED_UNICODE));
                }

                if (isset($skuInfo['attributeTags']) && is_array($skuInfo['attributeTags'])) {
                    foreach ($skuInfo['attributeTags'] as $attributeTag) {
                        if ('grade' == $attributeTag['key'] && isset($attributeTag['values'][0]['code'])) {
                            $item['gradeId'] = (int)$attributeTag['values'][0]['code'];
                        }
                    }
                }
                if (isset($skuInfo['labelTags']) && is_array($skuInfo['labelTags'])) {
                    foreach ($skuInfo['labelTags'] as $labelTag) {
                        if ('semester' == $labelTag['key'] && isset($labelTag['values'][0]['code'])) {
                            $item['seasonId'] = $labelTag['values'][0]['code'];
                        }
                    }
                }
                $result[] = $item;
            }
        }
        return [$isTrue, $errNo, $result];
    }

    /**
     * 批量获取sku详情
     * 不存在的sku不会缓存
     * @return array|false
     */
    public static function batchGetSkuInfo(array $skuIds, $cache = true, $maxRetryTimes = 10)
    {
        $skus = [];
        if ($cache) {
            $keys = [];
            foreach ($skuIds as $skuId) {
                $keys[] = self::REDIS_KEY_SKUINFO_PREFIX . (string)$skuId;
            }
            $values = Qdlib_Util_Cache::getQudaoRedis()->mget($keys);
            if (is_array($values) && count($values) == count($keys)) {
                foreach ($values as $index => $value) {
                    if ($value && $value = json_decode($value, true)) {
                        $skus[] = $value;
                        unset($skuIds[$index]);
                    }
                }
            }
        }

        if (empty($skuIds)) {
            return $skus;
        }

        $retry = 0;
        foreach (array_chunk($skuIds, 20) as $chunkSkuIds) {
            while (true) {
                list($isTrue, , $data) = Qdlib_Service_Moat_Common::request('/newgoodsplatform/sku/getgoodsskukvbyskuidv2', ['skuIds' => $chunkSkuIds]);
                if (!$isTrue) {
                    $retry++;
                    if ($retry >= $maxRetryTimes) {
                        return false;
                    }
                    continue;
                }

                if (is_array($data)) {
                    foreach ($data as $skuInfo) {
                        $skus[] = $skuInfo;

                        if ($cache) {
                            Qdlib_Util_Cache::getQudaoRedis()->set(self::REDIS_KEY_SKUINFO_PREFIX . (string)$skuInfo['skuId'], json_encode($skuInfo, JSON_UNESCAPED_UNICODE), self::REDIS_KEY_SKUINFO_TTL);
                        }
                    }
                }
                break;
            }
        }
        return $skus;
    }
}