<?php

/**
 * 交易售后2.0
 */
class Qdlib_Service_Moat_Trade
{
    /**
     * 订单详情
     * https://yapi.zuoyebang.cc/project/18/interface/api/78
     */
    public static function orderinfo(int $userId, int $orderId)
    {
        return Qdlib_Service_Moat_Common::request('/trade/api/orderinfo', [
            'uid' => $userId,
            'orderId' => $orderId,
        ]);
    }

    /**
     * 普通退款接口
     * http://ssv.zuoyebang.cc/static/open-sell/index.html#/detail-api?draftId=196
     */
    public static function mixedRefund(array $params)
    {
        return Qdlib_Service_Moat_Common::request('/trade/api/mixedRefund', $params);
    }

    /**
     * 用户转班接口
     * http://ssv.zuoyebang.cc/static/open-sell/index.html#/detail-api?draftId=197
     */
    public static function transferclass(array $params)
    {
        return Qdlib_Service_Moat_Common::request('/trade/api/transferclass', $params);
    }
}