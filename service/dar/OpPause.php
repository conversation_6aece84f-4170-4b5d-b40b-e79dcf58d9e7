<?php
/**
 * Copyright (c) 2018 zuoyebang.com, Inc. All Rights Reserved
 * @author: <EMAIL>
 * @file: zb/service/dar/OpPause.php
 * @date: 2020/12/08
 * @file: 14:22
 * @desc: 暂停或恢复为paid
 */

class Zb_Service_Dar_OpPause {

    private static $service     = 'zbcore_dar';
    private static $serviceUri  = '/zbcore/api/api';
    private static $module      = 'dar';
    private static $entity      = 'opPause';


    public static function pause($input) {
        $arrParams = $input;
        return self::helper($arrParams, "pause");
    }

    public static function recovery($input) {
        $arrParams = $input;
        return self::helper($arrParams, "recovery");
    }

    private static function helper($arrParams, $api) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, $api);
        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }
        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }
}
