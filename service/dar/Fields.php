<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   Fields.php
 * <AUTHOR>
 * @date   2018/7/3 下午9:55
 * @brief
 **/



class Zb_Service_Dar_Fields {

    /**
     * @var array 接口可以提供的课程数据字段
     */
    private static $tradeFields = array(
        'tradeId',              // 主订单ID
        'orderId',              // 交易流水ID
        'tccId',                // 交易ID
        'userId',               // 学生ID
        'subOrderGroup',        // 子订单组
        'bizInfoGroup',         // 营销策略分组
        'deductInfoGroup',      // 优惠券分组
        'amountPayable',        // 应付款总额
        'amountDiscount',       // 折扣总额
        'amountTrade',          // 总支付金额
        'amountTransfer',       // 转班转移金额
        'payMap',               // 支付流水分布
        'tradeTime',            // 支付时间
        'paymentChannel',       // 支付渠道
        'paymentDiscount',      // 支付折扣金额
        'status',               // 状态
        'createTime',           // 创建时间
        'updateTime',           // 更新时间
        'orderType',            // 主订单类型，区分预付、普通订单
        'bizInfoGift',          // 营销策略赠品
        'giftCouponIds',        // 赠送优惠券IDs
        'businessType',         //订单购买类型 普通购买/拼团
        'orderSource',          // 订单来源
        'bizInfo',              //订单来源
        'expirationTime',       // 支付关单时
        'closeInfo',            //关单信息
        'cancelInfo',           //预约取消信息
        'holdingInfo',          //代扣信息
        'tradeIdList',          //连报关系
        'withholdTid',          // 签约代扣模板
        'addressDeficiency',    // 是否缺失必要地址信息
        'withholdTid',          //签约代扣模板
        'cdKey',                // 课程兑换码
        'extData',              //订单扩展信息
		'skip',					// 压测标识
		'currencyType',		    // 币种类型
		'currencyInfo',		    // 币种信息
        'orderChannel',		    // 订单来源渠道
        'reviewStatus',         // 审核状态
        'tuanInfo',             // 团购信息
        'zomsInfo',             // zoms信息
        'tradeVersion',         //交易版本信息
        'saleChannel',          // 订单售卖策略渠道
        'ups',                  // 订单用户角色
        'unionMap',             // 订单中联报商品组合
        'expandIncrAmount',     // 预约订单定金膨胀幅度
        'relateBookInfo',       // 预约信息
        'originalOrderId',      // 原单id
        'originalSkuOrderInfo', // 拆单后原单商品订单分布信息
    );

    private static $preTradeFields  = array(
        'userId',               // 学生UID
        'orderId',              // 交易流水ID
        'tradeId',              // 主订单ID
        'preTradeId',           // 预付子订单ID
        'prePrice',             // 预付金额
        'expandingPrice',       // 膨胀金额
        'skuId',                // 单品ID
        'courseId',             // 课程ID
        'tradeTime',            // 交易订单
        'status',               // 状态
        'itemTag',              // 预付课程标签
        'refundStartTime',      // 开始退款时间
        'refundFinishTime',     // 退款完成时间
        'refundSource',         // 退款渠道
        'tradeRecordId',        // 旧表子订单ID
        'createTime',           // 创建时间
        'updateTime',           // 更新时间
        'tradeRecordId',        //fudao库子订单ID
        'boundSubTradeId',      //绑定尾款子订单ID
        'businessType',         //订单购买类型 普通购买/拼团
        'orderChannel',
        "entityType",           // 实体：1 课程 2 教材 3 赠品
    );

    /**
     * @var array 接口可以提供的章节数据字段
     */
    private static $subTradeFields  = array(
        "studentUid",           // 学生UID
        'subTradeId',           // 子订单ID
        'tradeId',              // 主订单ID
        'orderId',              // 交易流水订单ID
        'userId',               // 学生UID
        'skuId',                // 单品ID
        'courseId',             // 课程ID
        'itemTag',              // 课程标签
        'buyCnt',               // 购买数量
        'price',                // 原价
        'tradeFee',             // 交易金额
        'tradeFeeMap',          // 交易金额流水分布
        'tradeTime',            // 交易时间
        'preTradeFee',          // 预付金额
        'preTradeInfo',         // 预付信息
        'refundFee',            // 退款金额
        'refundInfo',           // 退款信息
        'status',               // 订单状态
        'changeOrderType',      // 调课类型
        'changeInfo',           // 调课信息
        'logInfo',              // 日志信息
        'giftInfo',             // 赠品信息
        'courseInfo',           // 课程信息
        'createTime',           // 创建时间
        'updateTime',           // 更新时间
        "changeFromSubTradeId", // 调课来源子订单ID
        "changeFromTradeId",    // 调课来源订单ID
        "changeFromCourseId",   // 被调课的课程ID
        "changeToCourseId",     // 调出课程ID
        "changeTime",           // 最近一次调课时间
        "changeOriginalSubTradeId",//调入课原始子订单ID
        "changeOriginalSkuId",  //调入课原始SKUID
        "changeOriginalTradeId",  // 调入课原始订单ID
        "refundStartTime",      // 退课时间
        "refundComment",        // 退课原因
        "refundSource",         // 退课来源
        "refundChannel",        // 退课渠道 1 原路退回 2 退回余额
        "tradeRecordId",        // fudao库子订单ID
        "tradeRecordParentId",  // fudao库父订单ID
        "groupId",              // 组ID，教材+课程+赠品是一组
        "entityType",           // 实体：1 课程 2 教材 3 赠品
        "isImportCourse",       // 是否导入
        "isPresentCourse",      // 是否赠品课程
        "specialSkuType",       // 是否特惠课程
        "changeTimes",          // 调课次数
        "changeReason",         // 调课原因
        "changeUid",            // 调课用户
        "changeSource",         // 调课来源 app callcenter
        "addressInfo",          // 地址信息
        "materialSubTradeId",   // 课程子订单关联教材子订单ID，逐步废弃
        "giftSubTradeIds",      // 课程子订单关联赠品子订单IDs
        "bindCourseSubTradeIds",// 实物子订单绑定课程子订单列表
        //"giftBindCourseSubTradeIds",// 实物子订单绑定课程子订单列表
        "specialSkuType",       //  特殊SKU类型
        'resendSubTradeId',     // 补寄的子订单ID
        'materialSubTradeIds',  // 课程随材子订单IDs
        'isAbnormal',           // 是否异动调课订单
        'bindCourseSubTradeId', // 随材关联的课程子订单ID
        //'materialBindCourseSubTradeId', // 随材关联的课程子订单ID
        'batchId',              // 物流下发批次号
        'setBatchId',           // 设置物流批次号
        'businessType',         //订单购买类型 普通购买/拼团
        'disableRefund',        // 不可退，0 可退  1 不可退
        'disableChange',        // 不可调，0 可调 1 不可调
        'disableRefundComment', // 不可退原因
        'disableChangeComment', // 不可调原因
        'orderSource',          // 订单来源
        'expressCode',          //子订单物流状态码
        'expressStatus',        //子订单物流状态
        'expressCompany',       //子订单物流平台
        'expressNumber',        //子订单物流单号
        'expressRemindTime',    //子订单物流催单时间
        'expressExpectTime',    //子订单物流预计发货时间
        'expressSignTime',      //子订单物流签收时间
        'realDepositFee',       // 实物已抵押金额
        'outerOrderInfo',       //
        'historyData',          // 历史数据
        'changeBizInfo',        //子订单转班详情
        'isTransfering',        // 当前子订单是否处在转班状态中
        'orderChannel',         //订单来源部门,一课 或者浣熊
        'productId',            //product Id ,dak分配
        'productType',          // product type  ,dak分配
        'productInfo',          //product 扩展信息
        'isTransfering',        //是否处在转班过程中
        'addressDeficiency',    // 是否缺失必要地址信息
        'currencyType',		    // 币种类型
	    'currencyInfo',		    // 币种信息
	    'performanceInfo',		// 履约信息
	    'performanceRet',		// 履约结果
        'all',                  // 当拥有该字段时，返回结果不会进行转出子订的过滤, 且返回字段中不会存在该域
        'changeBind',           // 由于新的转班处理中不再对实物和赠品子订单进行该绑，如果设置该值，将会对返回值进行逻辑该绑，目前只在subTraded/getDeductList中生效
        'privilegeId',          // 特权ID
        'compensationRefundFee',//价补金额
        'additionalRefundFee',  // 多次退款金额
        'specialRefundLock',    // 特殊退款中
	    'paymentDiscount',		// 第三方优惠金额
	    'forwardMark',          // 正向履约标记
	    'reverseMark',          // 逆向履约标记
	    'discountDetail',       // 优惠明细
        'skuSellingPrice',       // 预约订单预约时商品价格，只记录在课程子订单上
        'expandIncrement',       // 预约商品定金膨胀幅度
        'bindTailId',
        'bookInfo',
        'depositInfo',          // 拆单后商品抵扣成本、运费信息
        'skuName',
        'pause',
        'pauseStatusTime',      // 退款暂停时间
        'originalOrderId', //原单ID
    );

    private static $refundSummaryFields     = array(
        "refundBatchId",        // 调课来源 app callcenter
        "refundStatus",         // 退款流水处理状态
        "userId",               // 用户ID
        "tradeId",              // 主订单ID
        "createTime",           // 创建时间
        "updateTime",           // 更新时间
    );

    private static $refundDetailFields      = array(
        "refundBatchId",        // 调课来源 app callcenter
        "refundStatus",         // 退款流水处理状态
        "userId",               // 用户ID
        "skuId",                // 商品ID
        "subTradeId",           // 子订单ID
        "tradeId",              // 主订单ID
        "refundSource",         // 退款来源 app 用户自己退 callcenter 客服退
        "refundReason",         // 退款原因
        "refundUid",            // 发起退款用户UID
        "startTime",            // 发起退款时间
        "finishTime",         // 完成退款时间
        "totalTradeFee",        // 支付金额
        "totalDepositFee",      // 抵扣金额
        "totalConsumeFee",      // 总消费金额
        "refundFee",            // 总退款金额
        "finalTradeFee",        // 尾款支付金额
        "preTradeFee",          // 预付支付金额
        "depositedFee",         // 抵押可提取金额（退实物子订单时有值，课时子订单无）
        "lessonCnt",            // 核心章节数
        "lessonUsed",           // 已上章节数
        "lessonLeft",           // 剩余章节数
        "depositDetail",        // 抵扣明细
        "refundPayDetail",      // 退款详情
        "requestNo",            // pay系统的唯一请求ID编号，使用之前相同的ID分配器，保持ID和旧系统一致
        "createTime",           // 创建时间
        "updateTime",           // 更新时间
        "refundType",           // 退款类型，1 表示正常退款操作 2表示因转班而引起的退款
        "transferAmount",       // 当退款类型为转班引起退款时，转移到转入子订单的金额数目
        "refundBizDetail",      //退款策略字段
	"refundFeeType",        // 退款货币类型
    );

    private static $sortFields = array(
        'tradeTime_desc',       // 支付金额
        'tradeTime_asc',        // 支付金额
        'status_desc',          // 状态
        'status_asc',           // 状态
    );

    private static function doCheck($arrInput, $allFields) {
        if(!is_array($arrInput)){ //避免php_error
            return NULL;
        }
        return array_diff($arrInput, $allFields);
    }

    public static function chkTradeFields($arrInput) {
        return self::doCheck($arrInput, self::$tradeFields);
    }

    public static function chkSubTradeFields($arrInput) {
        return self::doCheck($arrInput, self::$subTradeFields);
    }

    public static function chkPreTradeFields($arrInput) {
        return self::doCheck($arrInput, self::$preTradeFields);
    }

    public static function chkSortFields($arrInput) {
        return self::doCheck($arrInput, self::$sortFields);
    }

    public static function chkRefundSummaryFields($arrInput) {
        return self::doCheck($arrInput, self::$refundSummaryFields);
    }

    public static function chkRefundDetailFields($arrInput) {
        return self::doCheck($arrInput, self::$refundDetailFields);
    }
}
