<?php
/**
 * Copyright (c) 2018 zuoyebang.com, Inc. All Rights Reserved
 * @author: liuxin<PERSON>@zuoyebang.com
 * @file: zb/service/dar/OpTransfer.php
 * @date: 2019/3/6
 * @file: 8:40 PM
 * @desc: 退款或取消订单操作数据连接层
 */

class Zb_Service_Dar_OpRefund {

    private static $service     = 'zbcore_dar';
    private static $serviceUri  = '/zbcore/api/api';
    private static $module      = 'dar';
    private static $entity      = 'opRefund';


    public static function init($input) {
        $arrParams = $input;
        return self::helper($arrParams, "init");
    }

    public static function commit($input) {
        $arrParams = $input;
        return self::helper($arrParams, "commit");
    }

    public static function cancel($input) {
        $arrParams = $input;
        return self::helper($arrParams, "cancel");
    }

    /**
     * 退款回调dar数据操作入口
     * @param int $userId 用户ID
     * @param int $refundBatchId 退款批次号
     * @param int $finishTime 退款完成时间
     * @param array $refundList 退款信息列表
     * @return array
     */
    public static function callback($userId, $refundBatchId, $finishTime, $refundList) {
        $arrParams = array(
            "userId" => $userId,
            "refundBatchId" => $refundBatchId,
            "finishTime" => $finishTime,
            "refundList" => $refundList,
        );
        return self::helper($arrParams, "callback");
    }


    private static function helper($arrParams, $api) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, $api);
        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }
        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }
}
