<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   SubTrade.php
 * <AUTHOR>
 * @date   2018/7/30 上午11:45
 * @brief
 **/

class Zb_Service_Dar_SubTrade {

    private static $service     = 'zbcore_dar';
    private static $serviceUri  = '/zbcore/api/api';
    private static $module      = 'dar';
    private static $entity      = 'subTrade';


    /**
     * 学生的购课信息
     *
     * @param array     $studentCourseIds   学生课程ID映射数组，array([studentUid]_[courseUid], ...)。eg. ["2183318929_89864", ...]
     * @param array     $subTradeFields     接口返回的Trade字段
     * @param array     $options            扩展数据, 控制
     * @param array     $orderChannel       订单来源
     * @return array
     */
    public static function getKVByCourseIds($studentCourseIds, $subTradeFields, $options=array(), $orderChannel=[0]) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getKVByCourseIds');
        $arrParams = array(
            'studentCourseIds'  => $studentCourseIds,
            'fields'            => $subTradeFields,
            'orderChannel'      => $orderChannel,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }



    /**
     * 批量获取单品子订单详情接口
     *******************************
     * !!!只查itemTag 是C_的子订单!!!*
     *******************************
     * @param $userSkuIds
     * @param array $subTradeFields
     * @param array $options
     * @param array $arrStatus
     * @param int $onlyCourse
     * @return array
     */
    public static function getKVBySkuIds($userSkuIds, $subTradeFields=array(), $options=array(), $arrStatus=array(), $onlyCourse = 1) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getKVBySkuIds');
        $arrParams = array(
            'userSkuIds'        => $userSkuIds,
            'subTradeFields'    => $subTradeFields,
            'arrStatus'         => $arrStatus,
            'onlyCourse'        => $onlyCourse,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }



    /**
     * 批量获取子订单详情接口
     *
     * @param  array    $userSubTradeIds
     * @param  array    $subTradeFields
     * @param  array    $preTradeFields
     * @param  array    $options
     * @return array
     */
    public static function getKVBySubTradeIds($userSubTradeIds, $subTradeFields=array(), $preTradeFields=array(), $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getKVBySubTradeIds');
        $arrParams = array(
            'userSubTradeIds'   => $userSubTradeIds,
            'subTradeFields'    => $subTradeFields,
            'preTradeFields'    => $preTradeFields,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }


    /**
     * 获取课程购买状态
     *
     * @param  int      $userId
     * @param  array    $courseIds
     * @param  array    $options
     * @return array    返回购买状态列表，未购买过 0， 已购买过 1
     */
    public static function getStatusKVByCourseIds($userId, $courseIds, $options=array(), $orderChannel=[0]) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getStatusKVByCourseIds');
        $arrParams = array(
            'userId'            => $userId,
            'courseIds'         => $courseIds,
            'orderChannel'      => $orderChannel,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 获取课程购买状态
     *
     * @param  int      $userId
     * @param  array    $skuIds
     * @param  array    $options
     * @return array    返回购买状态列表，未购买过 0， 已购买过 1
     */
    public static function getStatusKVBySkuIds($userId, $skuIds, $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getStatusKVBySkuIds');
        $arrParams = array(
            'userId'            => $userId,
            'skuIds'            => $skuIds,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }


    /**
     * 获取赠品订单列表
     *
     * @param  int      $userId
     * @param  int      $subTradeId
     * @param  array    $fields
     * @param  array    $options
     * @return array
     */
    public static function getGiftListBySubTradeId($userId, $subTradeId, $fields, $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getGiftListBySubTradeId');
        $arrParams = array(
            'userId'            => $userId,
            'subTradeId'        => $subTradeId,
            'fields'            => $fields
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 获取子订单下绑定的实物子订单或赠品子订单
     * @param $userId
     * @param $tradeId
     * @param $subTradeId
     * @param array $fields
     * @return array
     */
    public static function getDeductionListBySubTradeId($userId, $tradeId, $subTradeId, $fields=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getDeductionListBySubTradeId');
        $arrParams = array(
            'userId'            => $userId,
            'tradeId'           => $tradeId,
            'subTradeId'        => $subTradeId,
            'fields'            => $fields
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 获取赠品订单列表
     *
     * @param  int      $userId
     * @param  int      $skuId
     * @param  int      $tradeId
     * @param  array    $fields
     * @param  array    $options
     * @return array
     */
    public static function getChangeListBySkuId($userId, $skuId, $tradeId, $fields, $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getChangeListBySkuId');
        $arrParams = array(
            'userId'            => $userId,
            'skuId'             => $skuId,
            'tradeId'           => $tradeId,
            'fields'            => $fields
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

	/**
     * 获取子订单转班记录列表
     *
     * @param  int      $userId
     * @param  int      $skuId
     * @param  int      $tradeId
     * @param  array    $fields
     * @param  array    $options
     * @return array
     */
    public static function getChangeListBySubTradeId($userId, $subTradeId, $changeType, $fields, $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getChangeListBySubTradeId');
        $arrParams = array(
            'userId'            => $userId,
            'subTradeId'        => $subTradeId,
            'changeType'        => $changeType,
            'fields'            => $fields
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 根据用户Id+课程Id+订单状态 获取用户流水列表
     * @param $userCourseIds
     * @param $fields
     * @param $arrStatus
     * @param array $options
     * @return array
     */
    public static function getKVByUserCourseIdsWithFilter($userCourseIds, $fields, $arrStatus, $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getKVByUserCourseIdsWithFilter');
        $arrParams = array(
            'userCourseIds'   => $userCourseIds,
            'subTradeFields'  => $fields,
            'arrStatus'       => $arrStatus,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }


    /**
     * 根据用户和课程id获取子订单列表（默认获取已支付子订单）
     * @param $userId
     * @param $courseId
     * @param $fields
     * @param array $options
     * @return array
     */
    public static function getSubTradeListByUserCourseId($userId, $courseId, $fields, $options=array(), $orderChannel=[0]) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getSubTradeListByUserCourseId');
        $arrParams = array(
            'userId'    => $userId,
            'courseId'  => $courseId,
            'fields'    => $fields,
            'orderChannel'  => $orderChannel,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }


    /**
     * 根据用户ID获取代扣预约列表，目前只返回SKU_ID，COURSE_ID
     * @param       $userId
     * @param array $aField
     * @return array
     */
    public static function getHoldingListByUserId($userId, $aField=array())
    {
        $aHeader    = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getHoldingListByUserId');
        $aParameter = array(
            'userId'            => $userId,
            'fields'            => $aField
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $aParameter, $aHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $aParameter, $aHeader);
    }

    /**
     * 根据用户ID trade_id 获取用户子订单列表
     * @param       $userId
     * @param       $tradeId
     * @param array $aField
     * @return array
     */
    public static function getListByUserId($userId, $tradeId,$aField=array())
    {
        $aHeader    = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getListByUserId');
        $aParameter = array(
            'userId'            => $userId,
            'tradeId'           => $tradeId,
            'fields'            => $aField
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $aParameter, $aHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $aParameter, $aHeader);
    }

    public static function getRelation($userId, $tradeId, $subTradeId) {
        $aHeader    = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getRelation');
        $aParameter = array(
            'userId'            => $userId,
            'tradeId'           => $tradeId,
            'subTradeId'        => $subTradeId,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $aParameter, $aHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $aParameter, $aHeader);
    }

    public static function getBoundleRefundList($userId, $tradeId, $subTradeId) {
        $aHeader    = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getBoundleRefundList');
        $aParameter = array(
            'userId'            => $userId,
            'tradeId'           => $tradeId,
            'subTradeId'        => $subTradeId,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $aParameter, $aHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $aParameter, $aHeader);
    }
}
