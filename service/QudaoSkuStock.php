<?php


class Qdlib_Service_QudaoSkuStock
{
    private $objRedis;
    private $promotePlanObj;
    private $teamLogObj;
    private $db;
    private $objCache;
    private $skuIdPool;
    private $objOrg;
    private $objUser;
    //增加NORUN 标识
    const QD_REG_TEAM_SKU_DAY = 'QD_REG_TEAM_SKU_DAY_KEY_%s_%s'; //预测售卖天数
    const QD_TEAM_PROMOTE_PLAN_KEY = 'QD_TEAM_PROMOTE_PLAN_KEY_NO_RUN_%s'; //计划缓存

    const QD_STOCK_TEAM_SKU_KEY = 'QD_STOCK_TEAM_SKU_KEY_NO_RUN_%s_%d'; //渠道分配库存
    const QD_SELL_TEAM_SKU_KEY = 'QD_SELL_TEAM_SKU_KEY_NO_RUN_%s_%d';
    const QD_SELL_TEAM_LOCK_KEY = 'QD_SELL_TEAM_LOCK_NO_RUN_%s_%d';
    const QD_SELL_TRADE_LOCK_KEY = 'QD_SELL_TRADE_LOCK_KEY_NO_RUN_%d_%d_%d';

    const LOCK_EXPIRE_TIME = 6;
    const STOCK_LOCK_EXPIRE_TIME = 8;

    const SELL_NUM_EXPIRE_TIME = 60 * 60 * 24 * 2;
    const QD_SELL_TEAM_SKU_SHARE_LIST = 'QD_SELL_TEAM_SKU_SHARE_LIST_NO_RUN_%s_%d';

    private static $tradeStatusMap = [
        Qdlib_Ds_QudaoCourseOrder::SUB_TRADE_STATUS_PAID,
        Qdlib_Ds_QudaoCourseOrder::SUB_TRADE_STATUS_REFUNDED

    ];

    static $teamIdCache = [];

    public function __construct()
    {
        $this->objRedis = Qdlib_Util_Cache::getQudaoRedis();
        $this->promotePlanObj = new Qdlib_Ds_QudaoPromotePlan();
        $this->teamLogObj = new Qdlib_Ds_Stock_TeamSellLog();
        $this->objCache = new Qdlib_Common_Cache();
        $this->skuIdPool = new Qdlib_Ds_SkuidPool();
        $this->objOrg = new Qdlib_Ds_AuthOrg();
        $this->objUser = new Qdlib_Ds_AuthUser();


    }

    /**
     * 设置团队库存
     * @param $teamId
     * @param $skuId
     * @param $addCnt //增量
     * @return bool
     */
    public function setQudaoTeamStock($teamId, $skuId, $addCnt)
    {
        $sellCacheKey = sprintf(self::QD_STOCK_TEAM_SKU_KEY, $teamId, $skuId);
        $cacheRes = $this->objCache->set($sellCacheKey, ['stockNum' => $addCnt, 'time' => time()], self::SELL_NUM_EXPIRE_TIME);
        return $cacheRes;
    }

    public function setRegTeamSellNum($teamSku, $dt, $sellHourMap)
    {
        $sellCacheKey = sprintf(self::QD_REG_TEAM_SKU_DAY, $teamSku, $dt);
        $ret = $this->objRedis->set($sellCacheKey, json_encode($sellHourMap), self::SELL_NUM_EXPIRE_TIME * 4);
        if ($ret) {
            Qdlib_Util_Log::addNotice($sellCacheKey, "写入缓存成功");
        } else {
            Qdlib_Util_Log::addNotice($sellCacheKey, "写入缓存失败");
        }
        return $ret;
    }

    public function getRegTeamSellNum($teamSku, $dt)
    {
        $sellCacheKey = sprintf(self::QD_REG_TEAM_SKU_DAY, $teamSku, $dt);
        $ret = $this->objRedis->get($sellCacheKey);
        if ($ret === null) {
            Qdlib_Util_Log::error("sku", "skuReg", "getRegTeamSellNum", "{$sellCacheKey}");

            return null;
        }
        if ($ret === false) {
            Qdlib_Util_Log::addNotice($sellCacheKey, "miss");
            return false;
        }
        return json_decode($ret, true);
    }

    public function getQudaoCanTeamSellNum($teamId, $skuId, $dbCache = true)
    {
        //获取当前缓存
        $sellCacheKey = sprintf(self::QD_STOCK_TEAM_SKU_KEY, $teamId, $skuId);
        //修正缓存

        //存入缓存
        if ($dbCache === false) {
            $cacheRes = $this->objRedis->get($sellCacheKey);

        } else {
            $cacheRes = $this->objCache->get($sellCacheKey);

        }

        Qdlib_Util_Log::addNotice("getQudaoCanTeamSellNum_" . $sellCacheKey, $cacheRes);
        if ($cacheRes === false) {
            return false;
        }

        if (empty($cacheRes)) {
            return null;
        }
        $cacheResArr = json_decode($cacheRes, true);
        if (!is_array($cacheResArr)) {
            return 0;
        }

        return $cacheResArr['stockNum'];
    }

    public function getTeamPromotePlanCache($promotePlan)
    {
        $sellCacheKey = sprintf(self::QD_TEAM_PROMOTE_PLAN_KEY, $promotePlan);
        $cacheRes = $this->objCache->get($sellCacheKey);
        if ($cacheRes === false) {
            return false;
        }
        $cacheResArr = json_decode($cacheRes, true);
        if (!is_array($cacheResArr)) {
            return [];
        }

        return $cacheResArr;
    }

    public function setTeamPromotePlanCache($promotePlan, $teamArr)
    {
        $sellCacheKey = sprintf(self::QD_TEAM_PROMOTE_PLAN_KEY, $promotePlan);
        $cacheRes = $this->objCache->set($sellCacheKey, $teamArr, self::SELL_NUM_EXPIRE_TIME);
        Qdlib_Util_Log::addNotice("写入{$promotePlan}cache", $cacheRes);
        return $cacheRes;
    }

    public function getSellStock($teamId, $skuId, $saleChannel, $businessLines)
    {

        try {
            if (empty($teamId)) {
                Qdlib_Util_Log::addNotice("未找到团队", $teamId);
                return true;
            }
            $res = $this->getTeamIsCanBuy($teamId, $skuId, false);
            if ($res === false) {
                $teamList = $this->getQudaoTeamShareList($teamId, $skuId);
                if ($teamList !== false) {

                    foreach ($teamList as $item) {
                        $tempRes = $this->getTeamIsCanBuy($item['teamId'], $skuId, false);
                        if ($tempRes) {
                            return true;

                        }

                    }
                    return false;

                }
                return false;

            }
        } catch (Exception $e) {
            //todo fatal
            //todo 打点
            $tags = [
                'teamId' => $teamId ?? 'other',
                'skuId' => $skuId,
                'errnoLine' => $e->getLine(),
                'method' => 'SellStock',

            ];
            Qdlib_Service_Monitor::setErrorCode('skuStock', $e->getCode(), $e->getMessage(), $tags);
            $tags = [
                'teamId' => $teamId ?? 'other',
                'skuId' => $skuId,
                'errnoLine' => $e->getLine(),
                'method' => 'SellStock',

            ];
            Qdlib_Service_Monitor::setErrorCode('skuStock', $e->getCode(), $e->getMessage(), $tags);
            return null;
        }
        return true;

    }


    public function getLv2TeamIdByLastfrom($lastfrom) {
        if (empty($lastfrom)) {
            return false;
        }

        $teamId = $this->getLv2TeamIdByLastfromNew($lastfrom);
        Qdlib_Util_Log::addNotice("{$lastfrom}团队new", $teamId);
        return $teamId;
    }

    /**
     * 获取用户当前团队ID
     * @param $lastfrom
     * @return false|int
     */
    public function getUserCurrentLv2TeamIdByLastfrom($lastfrom) {
        if (empty($lastfrom)) {
            return false;
        }

        $planInfo = $this->promotePlanObj->getLastFrom($lastfrom);
        if (empty($planInfo)) {
            return false;
        }

        $user = $this->objUser->getInfoFromCacheByUname($planInfo["uname"]);
        if (empty($user)) {
            return false;
        }

        $org = $this->objOrg->getInfoFromCache($user['orgId']);
        if (empty($org)) {
            return false;
        }
        $teamId = false;
        if (isset($org['lv2']) && $org['lv2'] >0) {
            $teamId = intval($org['lv2']);
        }
        Qdlib_Util_Log::addNotice("getUserCurrentLv2TeamIdByLastfrom：{$lastfrom} 团队new", $teamId);
        return $teamId;
    }

    private function getLv2TeamIdByLastfromNew($lastfrom) {
        $newOrg = $this->promotePlanObj->getLastfromOrgInfo($lastfrom);
        $itemId = false;
        if (isset($newOrg['lv2']) && $newOrg['lv2'] >0) {
            $itemId = intval($newOrg['lv2']);
        }
        return $itemId;
    }

    public function getLv2TeamIdByLastfromOld($lastfrom)
    {
        if (empty($lastfrom)) {
            return false;
        }
        if (isset(self::$teamIdCache[$lastfrom])) {
            return self::$teamIdCache[$lastfrom];
        }

        $frPreFixArr = Qdlib_Service_PromotePlan::checkPlan($lastfrom);
        $frPreFix = $frPreFixArr['frPrefix'];
        $planNum = $frPreFixArr['planNum'];
        Qdlib_Util_Log::addNotice("input_frPreFix", $frPreFix);
        $teamIdArr = $this->getTeamPromotePlanCache($frPreFix);
        Qdlib_Util_Log::addNotice("output_frPreFix", json_encode($teamIdArr));

        if ($teamIdArr === false || !is_array($teamIdArr)) {
            return false;
        }
        $teamId = false;

        foreach ($teamIdArr as $item) {
            if (!isset($item['teamId'])) {
                return false;
            }
            if ($planNum >= (int)$item['planNumMin'] && $planNum <= (int)$item['planNumMax']) {
                $teamId = (int)$item['teamId'];
            }
        }

        if ($teamId != false) {
            if (count(self::$teamIdCache) > 1000) {
                self::$teamIdCache = [];
            }
            self::$teamIdCache[$lastfrom] = $teamId;

        }

        Qdlib_Util_Log::addNotice("{$lastfrom}团队", $teamId);
        return $teamId;

    }

    private function setTeamPromotePlan()
    {
    }

    private function getTeamIsCanBuy($teamId, $skuId, $dbCache = true)
    {

        $stockNum = $this->getQudaoCanTeamSellNum($teamId, $skuId, $dbCache);
        if ($stockNum === false) {
            throw new Exception('stockNum处理失败', Qdlib_Common_ExceptionCodes::CACHE_ERROR);
        }
        if ($stockNum === null) {
            Qdlib_Util_Log::addNotice("该团队下sku库存未设置", "{$teamId}_{$skuId}");
            throw new Exception('该团队下sku库存未设置', Qdlib_Common_ExceptionCodes::STATUS_ERROR);

        }
        $sellNum = $this->getQudaoTeamSellNum($teamId, $skuId, $dbCache);
        if ($sellNum === false) {
            throw new Exception('sellNum处理失败', Qdlib_Common_ExceptionCodes::CACHE_ERROR);
        }
        if ($sellNum === null) {
            throw new Exception('sellNum获取失败', Qdlib_Common_ExceptionCodes::CACHE_ERROR);
        }

        if ($stockNum - $sellNum > 0) {
            return true;
        }
        return false;
    }

    public function getQudaoTeamShareList($teamId, $skuId)
    {
        $sellCacheKey = sprintf(self::QD_SELL_TEAM_SKU_SHARE_LIST, $teamId, $skuId);

        $teamList = $this->objCache->get($sellCacheKey);
        if ($teamList === false) {
            throw new Exception('sellTeamShare处理失败', Qdlib_Common_ExceptionCodes::CACHE_ERROR);
        }
        if (empty($teamList)) {
            return false;
        }
        return json_decode($teamList, true);

    }

    public function delQudaoTeamShareList($teamId, $skuId)
    {
        //获取当前缓存
        $sellCacheKey = sprintf(self::QD_SELL_TEAM_SKU_SHARE_LIST, $teamId, $skuId);
        Qdlib_Util_Log::addNotice("删除key{$teamId}", $sellCacheKey);
        $cacheRes = $this->objCache->del($sellCacheKey);
        if ($cacheRes === false) {
            return false;
        }
        return true;
    }

    public function delQudaoTeamIdSell($teamId, $skuId)
    {
        $sellCacheKey = sprintf(self::QD_STOCK_TEAM_SKU_KEY, $teamId, $skuId);
        Qdlib_Util_Log::addNotice("删除key{$teamId}", $sellCacheKey);
        $cacheRes = $this->objCache->del($sellCacheKey);
        if ($cacheRes === false) {
            return false;
        }
        return true;
    }

    public function setQudaoTeamShareList($teamId, $skuId, $teamIdArr)
    {
        //获取当前缓存
        $sellCacheKey = sprintf(self::QD_SELL_TEAM_SKU_SHARE_LIST, $teamId, $skuId);
        //修正缓存

        //存入缓存

        $cacheRes = $this->objCache->set($sellCacheKey, $teamIdArr, self::SELL_NUM_EXPIRE_TIME);
        if ($cacheRes === false) {
            return false;
        }
        return true;
    }

    public function getQudaoTeamSellNum($teamId, $skuId, $dbCache = true)
    {
        //获取当前缓存
        $sellCacheKey = sprintf(self::QD_SELL_TEAM_SKU_KEY, $teamId, $skuId);
        //修正缓存

        //存入缓存
        if ($dbCache === false) {
            $cacheRes = $this->objRedis->get($sellCacheKey);
            if ($cacheRes === null) {
                return null;
            }

        } else {
            $cacheRes = $this->objCache->get($sellCacheKey);
        }

        if ($cacheRes === false) {
            return false;
        }
        $cacheResArr = json_decode($cacheRes, true);
        if (!is_array($cacheResArr)) {
            return 0;
        }

        return $cacheResArr['sellNum'];
    }

    private function setQudaoTeamSellNum($teamId, $skuId, $sellNum)
    {
        //获取当前缓存
        $sellCacheKey = sprintf(self::QD_SELL_TEAM_SKU_KEY, $teamId, $skuId);
        //修正缓存
        $newCacheResArr['cacheTime'] = time();
        $newCacheResArr['sellNum'] = $sellNum;

        //存入缓存
        $res = $this->objCache->set($sellCacheKey, $newCacheResArr, self::SELL_NUM_EXPIRE_TIME);
        if ($res) {
            Qdlib_Service_Monitor::setRedisServerCnt("sell_team_stock", $sellNum, ['teamId' => $teamId, 'skuId' => $skuId]);
            $canSellRes = $this->getQudaoCanTeamSellNum($teamId,$skuId);
            $nowCanSell =  $canSellRes-$sellNum;
            Qdlib_Service_Monitor::setRedisServerCnt("team_can_sell_stock", $nowCanSell, ['teamId' => $teamId, 'skuId' => $skuId]);
            Qdlib_Util_Log::addNotice($sellCacheKey, $sellNum);
        }

        return $res;
    }

    private function tryTeamSellLock($teamId, $skuId)
    {
        $lockKey = sprintf(self::QD_SELL_TEAM_LOCK_KEY, $teamId, $skuId);
        $ret = $this->objRedis->set($lockKey, 1, ['NX', 'EX' => self::STOCK_LOCK_EXPIRE_TIME]);
        if ($ret == false) {
            throw new Exception('加锁失败', Qdlib_Common_ExceptionCodes::CURL_ERROR);

        }
        return $ret;
    }

    private function tryTradeSellLock($trade, $subTrade, $status)
    {
        $lockKey = sprintf(self::QD_SELL_TRADE_LOCK_KEY, $trade, $subTrade, $status);
        $ret = $this->objRedis->set($lockKey, 1, ['NX', 'EX' => self::LOCK_EXPIRE_TIME]);
        if ($ret == false) {

            return false;
        }
        return $ret;
    }

    protected function unTradeLock($trade, $subTrade, $status)
    {
        $lockKey = sprintf(self::QD_SELL_TRADE_LOCK_KEY, $trade, $subTrade, $status);
        $this->objRedis->del($lockKey);
    }

    protected function unLock($teamId, $skuId)
    {
        $lockKey = sprintf(self::QD_SELL_TEAM_LOCK_KEY, $teamId, $skuId);
        $this->objRedis->del($lockKey);
    }

    public function saveTradeTeamStock($tradeInfo)
    {
        //加锁
        Qdlib_Util_Log::addNotice("渠道库存记录", "{$tradeInfo['tradeId']}_{$tradeInfo['subTradeId']}_{$tradeInfo['subTradeStatus']}");


        if (!self::orderStatusFilter($tradeInfo['subTradeStatus'])) {
            Qdlib_Util_Log::addNotice("非库存需要记录订单", "{$tradeInfo['tradeId']}_{$tradeInfo['subTradeId']}_{$tradeInfo['subTradeStatus']}");
            return true;
        }
        if (empty($tradeInfo['tradeId'])) {
            Qdlib_Util_Log::addNotice("tradeId错误", "{$tradeInfo['tradeId']}_{$tradeInfo['subTradeId']}_{$tradeInfo['subTradeStatus']}");

            return true;
        }
        if (empty($tradeInfo['subTradeId'])) {
            Qdlib_Util_Log::addNotice("subTradeId错误", "{$tradeInfo['tradeId']}_{$tradeInfo['subTradeId']}_{$tradeInfo['subTradeStatus']}");

            return true;
        }
        //nmq重发 tradeId
        if(!$this->tryTradeSellLock($tradeInfo['tradeId'],$tradeInfo['subTradeId'],$tradeInfo['tradeStatus'])){
            Qdlib_Util_Log::addNotice("获取锁失败", "{$tradeInfo['tradeId']}_{$tradeInfo['subTradeId']}_{$tradeInfo['tradeStatus']}");
            return true;
        };
        $skuId = $tradeInfo['skuId'];
        if (intval($skuId) <= 0) {
            Qdlib_Util_Log::addNotice("skuid不合法", "{$tradeInfo['tradeId']}_{$tradeInfo['subTradeId']}_{$tradeInfo['subTradeStatus']}");
            return false;
        }
        $teamId = $this->getUserCurrentLv2TeamIdByLastfrom($tradeInfo['lastfrom']);
        $orderType = ($tradeInfo['subTradeStatus']);

        //获取团队id
        //  $tradeInfo['lastfrom'];
        if (intval($teamId) <= 0) {
            Qdlib_Util_Log::addNotice("团队id获取失败", "{$tradeInfo['tradeId']}_{$tradeInfo['subTradeId']}_{$tradeInfo['subTradeStatus']}");
            //写入其他库存
            $teamId = 'other';
            return $this->saveTeamSellStockAndLog($teamId, $skuId, $orderType, $tradeInfo, $teamId);
        }
        // 订单类型是退单则检查 teamid 从订单日志里拿
        if ($orderType === Qdlib_Ds_QudaoCourseOrder::SUB_TRADE_STATUS_REFUNDED) {
            $teamId = $this->getTeamSellStockPaidLog($tradeInfo['subTradeId'], $tradeInfo['tradeId'], Qdlib_Ds_QudaoCourseOrder::SUB_TRADE_STATUS_PAID, $tradeInfo['skuId'],$tradeInfo['lastfrom']);
            if ($teamId !== false && $teamId !==0) {
                return $this->saveTeamSellStockAndLog($teamId, $skuId, $orderType, $tradeInfo, $teamId);
            }
            if ($teamId===0){
                return true;
            }
            $this->unTradeLock($tradeInfo['tradeId'],$tradeInfo['subTradeId'],$tradeInfo['tradeStatus']);
            return false;
        }
        // 根据teamId获取库存

        $shareTeamId = $this->getCanBuyTeamId($teamId, $skuId);
        Qdlib_Util_Log::addNotice("共享团队id", $shareTeamId);

        $stockRes = $this->saveTeamSellStockAndLog($shareTeamId, $skuId, $orderType, $tradeInfo, $teamId);
        //释放锁
          $this->unTradeLock($tradeInfo['tradeId'],$tradeInfo['subTradeId'],$tradeInfo['tradeStatus']);
        return $stockRes;
    }


    private function getCanBuyTeamId($teamId, $skuId)
    {
        try {
            $res = $this->getTeamIsCanBuy($teamId, $skuId);
            if ($res === false) {
                $teamList = $this->getQudaoTeamShareList($teamId, $skuId);
                if ($teamList !== false) {
                    foreach ($teamList as $item) {
                        $tempRes = $this->getTeamIsCanBuy($item['teamId'], $skuId);
                        if ($tempRes) {
                            return $item['teamId'];
                        }

                    }

                }
                return 'other';
            }
            return $teamId;

        } catch (Exception $e) {
            $tags = [
                'teamId' => $teamId,
                'skuId' => $skuId,
                'errnoLine' => $e->getLine(),
                'method' => 'getCanBuyTeamId',

            ];
            Qdlib_Service_Monitor::setErrorCode('skuStock', $e->getCode(), $e->getMessage(), $tags);

            return 'other';
        }
    }

    public function getDBInstance()
    {
        if (!$this->db) {
            $this->db = Qdlib_Util_DB::getQudaoDb();
        }

        return $this->db;
    }

    private function saveTeamSellStockAndLog($teamId, $skuId, $orderType, $tradeInfo, $fromTeamId)
    {
        $this->db = $this->getDBInstance();

        try {

            $this->db->startTransaction();

            $res = $this->saveLog($tradeInfo, $teamId, $fromTeamId);
            if (false !== $res) {
                $sellNum =  $this->getTeamSellSum($teamId,$skuId);
                $setStock = $this->saveTeamSellStock($teamId, $skuId, $sellNum);
                if (!$setStock) {
                    throw new Exception('写入缓存失败', 1);

                }

            }
            $this->db->commit();


        } catch (Exception $e) {
            $this->db->rollback();
            //Qdlib_Util_Log::error("order", "teamSell", "saveTradeTeamStock", $e->getMessage(), json_encode($tradeInfo));

            $tags = [
                'teamId' => $teamId,
                'skuId' => $skuId,
                'errnoLine' => $e->getLine(),
                'method' => 'saveTeamSellStockAndLog',

            ];
            Qdlib_Service_Monitor::setErrorCode('skuStock', $e->getCode(), $e->getMessage(), $tags);
            //todo 异常打点
            return false;

        }
        return true;
    }

    private function getTeamSellSum($teamId,$skuId){
        $arrConds=[
            'skuId'      => $skuId,
            'orderType'  => Qdlib_Ds_QudaoCourseOrder::SUB_TRADE_STATUS_PAID,
            'teamId'    => strval($teamId)

        ];
        $paidNum = $this->teamLogObj->getTeamSellLogTotal($arrConds);
        if (false === $paidNum) {
            throw new Exception('查询失败 请重试', Qdlib_Common_ExceptionCodes::DB_ERROR);
        }
        $arrConds=[
            'skuId'      => $skuId,
            'orderType'  => Qdlib_Ds_QudaoCourseOrder::SUB_TRADE_STATUS_REFUNDED,
            'teamId'    => strval($teamId)

        ];
        $refundedNum = $this->teamLogObj->getTeamSellLogTotal($arrConds);
        if (false === $refundedNum) {
            throw new Exception('查询失败 请重试', Qdlib_Common_ExceptionCodes::DB_ERROR);
        }
        return (int)$paidNum - (int)$refundedNum;
    }
    private function getTeamSellStockPaidLog($subTradeId, $tradeId, $subTradeStatus, $skuId,$lastfrom='')
    {
        //查询写入
        $arrConds = [
            'subTradeId' => $subTradeId,
            //'tradeId' => $tradeId,
            'orderType' => $subTradeStatus,
            'skuId' => $skuId,

        ];
        $resLogRecord = $this->teamLogObj->getTeamSellLogInfoByCond($arrConds);
        if (false === $resLogRecord) {
            Qdlib_Util_Log::error("skuStock", "Qdlib_Service_QudaoSkuStock", "getTeamSellStockPaidLog", "退单未查询到已支付订单", "{$arrConds['tradeId']}_{$arrConds['subTradeId']}_{$arrConds['subTradeStatus']}");
            return false;
        }
        if ($resLogRecord && isset($resLogRecord['teamId'])) {
            return $resLogRecord['teamId'];
        };
        Qdlib_Util_Log::addNotice("getTeamSellStockPaidLog_empty", json_encode($arrConds));
        if(stripos($lastfrom,'oms')!==false){
            Qdlib_Util_Log::warning("skuStock", "Qdlib_Service_QudaoSkuStock", "getTeamSellStockPaidLog", "退单ID未在db中查询到", "{$arrConds['tradeId']}_{$arrConds['subTradeId']}_{$arrConds['subTradeStatus']}");
        }else{
            Qdlib_Util_Log::fatal("skuStock", "Qdlib_Service_QudaoSkuStock", "getTeamSellStockPaidLog", "退单ID未在db中查询到", "{$arrConds['tradeId']}_{$arrConds['subTradeId']}_{$arrConds['subTradeStatus']}");
        }

        return 0;
    }

    private function saveLog($tradeInfo, $teamId, $fromTeamId)
    {
        //查询写入
        $arrConds = [
            'teamId' => $teamId,
            'fromTeamId' => $fromTeamId,

            'lastfrom' => $tradeInfo['lastfrom'],
            'subTradeId' => ($tradeInfo['subTradeId']),
            'tradeId' => ($tradeInfo['tradeId']),
            'userId' => ($tradeInfo['userId']),
            'orderType' => ($tradeInfo['subTradeStatus']),
            'skuId' => ($tradeInfo['skuId']),
        ];

        $arrCondsNew = [
            'subTradeId' => ($tradeInfo['subTradeId']),
            'userId' => ($tradeInfo['userId']),
            'orderType' => ($tradeInfo['subTradeStatus']),
            'skuId' => ($tradeInfo['skuId']),
        ];

        $resLogRecord = $this->teamLogObj->getTeamSellLogInfoByCond($arrCondsNew);
        if (false === $resLogRecord) {
            throw new Exception('查询失败 请重试', Qdlib_Common_ExceptionCodes::DB_ERROR);
        }

        if ($resLogRecord) {
            Qdlib_Util_Log::addNotice("库存订单已经记录", "{$tradeInfo['tradeId']}_{$tradeInfo['subTradeId']}_{$tradeInfo['subTradeStatus']}");
            return false;
        }
        $arrParams = $arrConds;
        $arrParams['createTime'] = time();
        $arrParams['tradeTime'] = (int)$tradeInfo['subTradeCreateTime'];


        $res = $this->teamLogObj->addTeamSellLog($arrParams);
        if ($res === false) {
            Qdlib_Util_Log::addNotice("库存订单写入失败", "{$tradeInfo['tradeId']}_{$tradeInfo['subTradeId']}_{$tradeInfo['subTradeStatus']}");
            throw new Exception('库存订单写入 请重试', Qdlib_Common_ExceptionCodes::DB_ERROR);
        }
        return true;
    }

    private function saveTeamSellStock($teamId, $skuId, $sellNum)
    {
        //获取锁
        $this->tryTeamSellLock($teamId, $skuId);
        //写入cache
        $ret = $this->setQudaoTeamSellNum($teamId, $skuId, $sellNum);
        //解放锁
        $this->unLock($teamId, $skuId);
        return $ret;

    }


    private static function orderStatusFilter($tradeStatus)
    {
        if (!in_array($tradeStatus, self::$tradeStatusMap)) {
            return false;
        }
        return true;
    }

    private static function getFrPreFix($lastfrom)
    {
        $frArr = explode("_", $lastfrom);
        if (count($frArr) > 4) {
            return $frArr[0] . '_' . $frArr[1] . '_' . $frArr[2] . '_' . $frArr[3];

        }
        return $lastfrom;

    }

    public function getTeamBySkuIdArr($skuIdArr)
    {
        $arrConds = [];
        if (!empty($skuIdArr)) {
            $arrConds[] = "sku_id in (" . implode($skuIdArr, ',') . ")";
        }
        $teamSkuList = (new Qdlib_Ds_Stock_TeamSkuStockConf())->getTeamSkuStockConfList($arrConds);
        $skuTeamList = [];
        $orgIdNameList = $this->geOrgList();
        foreach ($skuIdArr as $skuId) {
            $otherSellNum = $this->getQudaoTeamSellNum('other', $skuId);
            $skuTeamList[$skuId][] = [
                'teamId' => 0,
                'teamName' => '其他',
                'residueStockCnt' => 0,
                'sellCnt' => $otherSellNum,

            ];
        }

        foreach ($teamSkuList as $item) {
            $sellNum = $this->getQudaoTeamSellNum($item['teamId'], $item['skuId']);
            $skuTeamList[$item['skuId']][] = [
                'teamId' => $item['teamId'],
                'teamName' => $orgIdNameList[$item['teamId']] ?? "失效团队",
                'residueStockCnt' => $item['sellCnt'] - $sellNum,
                'sellCnt' => $sellNum,

            ];
        }

        $teamSkuArr = [];
        foreach ($skuIdArr as $skuId) {
            $teamSkuArr[$skuId] = $skuTeamList[$skuId] ?? [];
        }
        return $teamSkuArr;
    }

    private function geOrgList()
    {
        $orgList = (new Qdlib_Ds_AuthOrg())->getAuthOrgList(['deleted' => 0]);
        $orgIdNameList = [];
        foreach ($orgList as $orgItem) {
            $orgIdNameList[$orgItem['id']] = $orgItem['name'];
        }
        return $orgIdNameList;
    }
}