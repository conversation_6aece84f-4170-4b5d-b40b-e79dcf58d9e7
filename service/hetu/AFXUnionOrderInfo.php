<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * <AUTHOR>
 * @version     2021-08-09
 * @brief       订单详情
 **/
class Qdlib_Service_Hetu_AFXUnionOrderInfo
{
    private $objDorisUnionOrder;
    private $objAct;
    private $objLpcLeadCourseLink;
    protected $orderId;
    protected $userId;

    public function __construct($objAct, $objDorisUnionOrder = null, $objLpcLeadCourseLink = null)
    {
        if(is_null($objDorisUnionOrder)) {
            $objDorisUnionOrder = new Qdlib_Ds_Hetu_AFXDorisUnionOrder();
        }
        if(is_null($objLpcLeadCourseLink)) {
            $objLpcLeadCourseLink = new Qdlib_Ds_Hetu_LpcLeadsCourseLink();
        }
        $this->objDorisUnionOrder = $objDorisUnionOrder;
        $this->objLpcLeadCourseLink = $objLpcLeadCourseLink;
        $this->objAct = $objAct;
    }

    protected function getSkuInfoAfterText()
    {
        $afterText = [];
        $afterList = Qdlib_Service_Moat_Asc::returnAfterDetailListByOrderIdV3OrFalse($this->userId,$this->orderIds);

        if ($afterList === false) {
            Qdlib_Util_Log::error("hetu-order", "Qdlib_Service_Hetu_AFXUnionOrderInfo", "getSkuInfoAfterText", "returnAfterDetailListByOrderIdV3OrFalse", json_encode(['orderIds' => $this->orderIds, 'uid' => $this->userId]));
            return $afterText;
        }

        if (empty($afterList)) {
            return $afterText;
        }
        foreach ($afterList as $afterId => $item) {
            if (!in_array($item['status'],[3])) {
                continue;
            }
            if (empty($item['skuInfo']) || empty($item['skuInfo'])) {
                continue;
            }
            $afterType = $item['processAfterType'] > 0?$item['processAfterType']:$item['checkAfterType'];
            $type = Qdlib_Const_Hetu_AfterType::$map[$afterType];
            foreach ($item['skuInfo'] as  $value){
                $skuOrderId = $value['skuRowId'];
                $afterText[$skuOrderId][] = [
                    "key" => $type,
                    "value" => $value['applyQuantity'],
                ];
            }
        }
        return $afterText;
    }

    protected function formatSkuList($orderInfo, $lpcLeads, $skuOrders, $afterText)
    {
        #$isV3Order = isset($orderInfo['domain']) && $orderInfo['domain'] < 3;
        $skues = $orderInfo['skuList'];

        /*
         * playForm
         * 1 - 直播. 包括是否转化, 是否加微, 到课节数, 完课节数
         * 2 - 录播. 只包含是否转化
         */
        $itemSkuIdList = [];
        foreach ($skues as $sku) {
            $itemSkuIdList[] = $sku['skuId'];
        }
        $itemSkuTmpData = Qdlib_Service_Moat_Newgoodsplatform::getGoodsSkuKvBySkuIdV2($itemSkuIdList);
        $itemSkuData = [];
        foreach ($itemSkuTmpData as $tData){
            $itemSkuData[$tData['skuId']] = $tData;
        }
        $skuList = [];
        $total = 0;
        foreach ($skues as $sku) {
            $lpcLead = $lpcLeads[$sku['skuOrderId']] ?? [];
            $skuOrder = $skuOrders[$sku['skuOrderId']] ?? [];

            if($sku['orderBusinessStatus']==0){
                $sku['status'] = 0; //0待支付
            }
            if(in_array($sku['orderBusinessStatus'],[1,2,3,4,5,7])){
                $sku['status'] = 1; //1已支付
            }
            if($sku['orderBusinessStatus']==6){
                $sku['status'] = 2; //2已退款
            }
            if($sku['orderBusinessStatus']==10){
                $sku['status'] = 5; //5已关单
            }

            $intersect = array_intersect(array_values($sku['afterType']),[1,2,7,8]);
            if($sku['refundStatus']==2 && empty($intersect)){
                $sku['status'] = 21; //21部分退款
            }
            if($sku['refundStatus']==3 && empty($intersect)){
                $sku['status'] = 2; //2已退款
            }
            $tSeasonName = "-";
            foreach ($itemSkuData[$sku['skuId']]['labelTags'] as $labelTag) {
                if($labelTag['name'] != '学期') {
                    continue;
                }
                if(isset( $labelTag['values'][0]['codeName'])) {
                    $tSeasonName = $labelTag['values'][0]['codeName'];
                    break;
                }
            }
            $tCourseStartTime = '-';
            foreach ($itemSkuData[$sku['skuId']]['specTags'] as $specTag) {
                if($specTag['name'] != '开课时间') {
                    continue;
                }
                if(isset( $specTag['values'][0]['code'])) {
                    $tCourseStartTime =  date('Y-m-d H:i:s', $specTag['values'][0]['code']);
                    break;
                }
            }
            $skuItem = [
                // sku信息
                'skuOrderId' => $sku['skuOrderId'],
                'skuId' => $sku['skuId'],
                'skuName' => $sku['skuName'],
                'sellTotal' => sprintf('%.2f', $sku['goodsAmount'] / 100),
                'payTotal' => sprintf('%.2f', $sku['paidAmount'] / 100),
                'status' => $sku['status'],
                'statusName' => Qdlib_Ds_Hetu_AFXUnionOrder::STATUS_NAMES[$sku['status']] ?: '-',
                'courseStartTime' => $tCourseStartTime,
                'seasonName' => $tSeasonName,
                'learnSeason' => $skuOrder['learnSeason'] ?? 0,
                'purchaseNum' => $sku['quantity'],
                'afterTypeText' => isset($afterText[$sku['skuOrderId']]) ? $afterText[$sku['skuOrderId']] : [],

                // 指标信息
                'isAddWeixin' => $lpcLead['isAddWeixin'] ?? 0,
                'isAddWeixinName' => Qdlib_Ds_Hetu_LpcLeadsCourseLink::LPC_ADD_WEIXIN_NAMES[($lpcLead['isAddWeixin'] ?? 0) > 0],
                'transPv' => $lpcLead['transPv'] ?? 0,
                'isTransName' => Qdlib_Ds_Hetu_LpcLeadsCourseLink::LPC_TRANSFORM_NAMES[($lpcLead['transPv'] ?? 0) > 0],
                'attendLessonCnt10' => $lpcLead['attendLessonCnt10'] ?? 0,
                'finishLessonCnt' => $lpcLead['finishLessonCnt'] ?? 0,

            ];
            if ((int)$sku['status'] == 1) {
                $total += $sku['paidAmount'] / 100;
            }
            $skuList[] = $skuItem;
        }
        $res=[];
        $res['payableAmount'] = sprintf('%.2f', $total);
        $res['skuList'] = $skuList;
        return $res;
    }

    public function execute($arrInput)
    {
        $this->orderId = $arrInput['orderId'];
        $skuOrderList = $this->objDorisUnionOrder->getListByOrderId($arrInput['orderId']);

        if (empty($skuOrderList)) {
            return [];
        }

        // todo: 判断是否有权限查询该用户信息
        $skuOrder = current($skuOrderList);
        $order = $this->formatOrder($skuOrder);
        $order['skuList'] = [];

        $this->userId = $skuOrder['userId'];
        //通过orderId(即新版MQ命令的businessId),获取该BusinessId下的所有orderId
        $ordersInfo = Qdlib_Service_Moat_One::returnOrderListByBusinessIdOrFalse($this->userId,$this->orderId);
        if ($ordersInfo===false){
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::NETWORK_ERROR, '获取订单失败');
        }
        if(empty($ordersInfo)){
            return [];
        }

        $this->orderIds = array_unique(array_column($ordersInfo,'orderId'));
        // 查询订单详情
        list($isTrue, $errno, $detail) = Qdlib_Service_Moat_One::detailV3($this->userId, $this->orderIds);
        if (false === $isTrue) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::NETWORK_ERROR, '获取订单详情失败');
        }
        //要获取是否是书课包对应的课程订单
        foreach ($detail as $v){
            if(isset($v['zomsInfo']['businessId']) && $v['zomsInfo']['businessId']>0){
                $this->lastBuessinessId = $v['zomsInfo']['businessId'];
            }else{
                $this->lastBuessinessId = 0;
            }
        }
        if($this->lastBuessinessId>0){
            $lastOrdersInfo = Qdlib_Service_Moat_One::returnOrderListByBusinessIdOrFalse($this->userId,$this->lastBuessinessId);
            if ($lastOrdersInfo==false){
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::NETWORK_ERROR, '获取订单失败');
            }
            $this->lastOrderIds = array_unique(array_column($lastOrdersInfo,'orderId'));
            // 查询订单详情
            list($isTrue, $errno, $lastDetail) = Qdlib_Service_Moat_One::detailV3($this->userId, $this->lastOrderIds);
            if (false === $isTrue) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::NETWORK_ERROR, '获取订单详情失败');
            }
            $this->orderIds = array_merge($this->orderIds,$this->lastOrderIds);
            $detail = $detail + $lastDetail;
        }

        $orderInfo = [];
        $skuOrderMerge = [];
        foreach ($this->orderIds as $orderId) {
            if (empty($detail[$orderId])) {
                return $order;
            }
            $orderInfo = $detail[$orderId];
            if (empty($orderInfo['skuList'])) {
                return $order;
            }
            foreach ($orderInfo['skuList'] as $item) {
                $skuOrderMerge[$item['skuOrderId']] = $item;
            }
        }
        $orderInfo['skuList'] = $skuOrderMerge;
        // 根据子订单查询指标列表
        $skuOrderIds = array_column($orderInfo['skuList'], 'skuOrderId');
        $lpcLeadList = $this->objLpcLeadCourseLink->getBySubTradeIds($skuOrderIds);

        $lpcLeads = Qdlib_Util_Array::columnIndex($lpcLeadList, 'subTradeId');
        $skuOrders = Qdlib_Util_Array::columnIndex($skuOrderList, 'skuOrderId');
        $afterText = $this->getSkuInfoAfterText();

        // 合并sku列表和指标列表
        $res = $this->formatSkuList($orderInfo, $lpcLeads, $skuOrders,$afterText);
        $order['skuList'] = $res['skuList'];
        if ($res['payableAmount'] != 0) {
            $order['payableAmount'] = $res['payableAmount'];
        }
        return $order;
    }

    protected function formatOrder($unionOrder)
    {
        $act = $this->objAct->getInfoById($unionOrder['actId']);
        $thirdOrderInfo = $unionOrder['thirdOrderInfo'];
        return [
            'orderId' => $unionOrder['orderId'],
            'source' => $unionOrder['source'],
            'sourceName' => Qdlib_Ds_Hetu_AFXUnionOrder::SOURCE_NAMES[$unionOrder['source']] ?: '-',
            'finalStatus' => $unionOrder['finalStatus'],
            'finalStatusName' => Qdlib_Ds_Hetu_AFXUnionOrder::FINAL_STATUS_NAMES[$unionOrder['finalStatus']] ?? '-',
            'payTime' => date('Y-m-d H:i:s', $unionOrder['payTime']),
            'payableAmount' => sprintf('%.2f', $unionOrder['payableAmount'] / 100),

            // 活动信息
            'actId' => $unionOrder['actId'] ?: '-',
            'actName' => $act['name'] ?: '-',
            'actStatus' => $act['status'] ?: -1,
            'actStatusName' => $act['statusName'] ?: '-',

            // 兑换码信息
            'cdkey' => $unionOrder['cdkey'] ?: '-',
            'cdkeyAmount' => sprintf('%.2f', $unionOrder['cdkeyAmount'] / 100),

            // 人工导单: 第三方订单信息
            'thirdOrderId' => $thirdOrderInfo['order_id'] ?: '-',
            'operator' => $thirdOrderInfo['operator'] ?: '-'
        ];
    }
}