<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * <AUTHOR>
 * @version     2021-08-09
 * @brief       物流信息
 **/
class Qdlib_Service_Hetu_AFXExpressInfo
{
    private $objDorisUnionOrder;
    private $objDorisUnionSkuOrder;
    public function __construct($objDorisUnionOrder = null, $objDorisUnionSkuOrder = null)
    {
        if(is_null($objDorisUnionOrder)) {
            $objDorisUnionOrder = new Qdlib_Ds_Hetu_AFXDorisUnionOrder();
        }
        if(is_null($objDorisUnionSkuOrder)) {
            $objDorisUnionSkuOrder = new Qdlib_Ds_Hetu_AFXDorisUnionSkuOrder();
        }
        $this->objDorisUnionOrder = $objDorisUnionOrder;
        $this->objDorisUnionSkuOrder = $objDorisUnionSkuOrder;
    }

    public function execute($arrInput)
    {
        $orderId = $arrInput['orderId'];
        $orderList = $this->objDorisUnionOrder->getListByOrderId($orderId);
        if(empty($orderList)) {
            return [];
        }
        $order = current($orderList);

        $orderIds = [];
        foreach ($orderList as $item) {
            if (empty($item['splitOrderId'])) {
                $orderIds[$item['orderId']] = $item['orderId'];
            } else {
                $orderIds[$item['splitOrderId']] = $item['splitOrderId'];
            }
        }

        $expressList = [];
        foreach ($orderIds as $orderId) {
            foreach ($this->_execute($orderId) as $expressInfo) {
                $expressList[] = $expressInfo;
            }
        }

        return [
            'orderId' => $order['orderId'],
            'source' => $order['source'],
            'sourceName' => Qdlib_Ds_Hetu_AFXUnionOrder::SOURCE_NAMES[$order['source']] ?: '-',
            'expressList' => $expressList,
        ];
    }

    public function _execute($orderId)
    {
        $orderList = $this->objDorisUnionOrder->getListByOrderId($orderId);
        if(empty($orderList)) {
            return [];
        }
        $order = current($orderList);
        // todo: 校验数据权限

        $changedToOrders = $this->objDorisUnionSkuOrder->getByOriginalOrderId($orderId);
        $changedSkuOrderIds = array_column($changedToOrders, 'skuOrderId', 'changeFromSkuOrderId');

        $expressInfo = Qdlib_Service_Moat_Common::returnDataOrFalse(Qdlib_Service_Moat_Wms::expressStatusOrderObtain($order['userId'], $orderId));
        if (false === $expressInfo) {
            return [];
        }
        return $this->format($expressInfo['orderInfo'], $changedSkuOrderIds);
    }

    protected function format($subOrders, $changedSkuOrderIds)
    {
        $expressList = [];
        foreach ($subOrders as $subOrderId => $subOrder) {
            $express = current(current($subOrder) ?: []) ?: [];

            // 普通订单: 无转班信息, 取当前的子订单物流信息
            // 转班订单: 有转班信息, 取转班后的子订单物流信息
            if(!empty($changedSkuOrderIds) && !isset($changedSkuOrderIds[$express['orderId']])) {
                continue;
            }

            $materialDetails = $express['materialDetails'] ?? [];
            foreach ($materialDetails as $materialDetail) {
                $expressInfo = [
                    'orderId' => $express['pid'],
                    'subOrderId' => $express['orderId'],
                    'materialId' => $materialDetail['materialId'],
                    'materialName' => $materialDetail['materialName'] ?: '-',
                    'quantity' => $materialDetail['quantity'],
                    'status' => $express['status'],
                    'statusName' => $express['statusName'] ?: '-',
                    'receiverPhone' => Qdlib_Util_Tool::hidePhone($express['receiverPhone']) ?: '-',
                    'receiverName' => Qdlib_Util_Tool::hideName($express['receiverName']) ?: '-',
                    'receiverAddress' => Qdlib_Util_Tool::hideAddressV2($express['receiverAddress']) ?: '-',
                    'sendType' => $express['sendType'] ?: '-',
                    'expressNumber' => $express['expressNumber'] ?: '-',
                ];
                $expressList []= $expressInfo;
            }
        }
        return $expressList;
    }
}
