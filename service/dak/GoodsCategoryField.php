<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   GoodsCategoryField.php
 * <AUTHOR> (<EMAIL>)
 * @date   2019年2月11日18:57:56
 * @brief
 **/


class Zb_Service_Dak_GoodsCategoryField
{

    private static $service = 'zbcore';
    private static $serviceUri = '/zbcore/api/api';
    private static $module = 'dak';
    private static $entity = 'GoodsCategoryField';


    /**
     *  通过 field_id 或 field_key  (两字段只能存在一种)
     *  批量获取field+tags KV 数据
     *
     * @param $courseId array
     * @param $fields
     * @param $optionsnnn
     * @return array
     */
    public static function getCategoryFieldKVByCategoryIdList($categoryIdList, $options = array())
    {
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getCategoryFieldKVByCategoryIdList');
        $arrParams = array(
            'categoryIdList' => $categoryIdList,
        );

        if (!empty($options['isRalMulti'])) {
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

}
