<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Date: 2018/7/10
 * Time: 下午3:43
 */

class Zb_Service_Dak_Fields
{
    private static $SkuFields = array(
        'skuId',            // 商品id
        'skuName',          // 商品名称
        'skuType',          // 商品类型
        'skuContent',       // 单品详情
        'skuResource',      // 单品多媒体资源
        'skuDescription',   // 单品描述信息
        'skuPrice',         // 单品售卖价格
        'skuOriginalPrice', // 单品原价
        'skuCostPrice',     // 成本价工本费等
        'startTime',        // 售卖开始时间
        'stopTime',         // 售卖接收时间
        'saleCnt',          // 销售量
        'stockCnt',         // 库存数量
        'status',           // 售卖状态
        'forSale',          // 是否卖品
        'isInner',          // 控制公司内网可见
        'createTime',
        'updateTime',
        'spuId',
    );

    private static function doCheck($arrInput, $allFields) {
        return array_diff($arrInput, $allFields);
    }

    public static function chkSkuFields($arrInput) {
        return self::doCheck($arrInput, self::$SkuFields);
    }


}