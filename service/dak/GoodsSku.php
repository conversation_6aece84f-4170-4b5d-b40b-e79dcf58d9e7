<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   GoodsSkuBack.php
 * <AUTHOR>
 * @date   2018/7/2 下午10:06
 * @brief
 **/


class Zb_Service_Dak_GoodsSku {

    private static $service     = 'zbcore';
    private static $serviceUri  = '/zbcore/api/api';
    private static $module      = 'dak';
    private static $entity      = 'goodsSku';


    /**
     * 业务方SKU同步 商品服务SKU
     * @param $arrInput array
     *  skuName:
        skuType:
        appId:
        categoryId:
        stockCnt:
        itemId:1
        skuContent:[['productId'=>1,'productName'=>'提升课','price'=>30,'costPrice'=>30,'type'=>1]]
        canChange:1
        canRefund:1
        channelType:[['user':1111,'from':2222],['user':3333,'from':4444]]
        specTags:[grade=>[2,3],subject=>[4,5],skuId=>"20190101",pintuan=>"拼团商品"]
        labelTags:[grade=>[2,3],subject=>[4,5],skuId=>"20190101",pintuan=>"拼团商品"]
        isInner:0
        price:1
        skuOriginPrice:1
        startTime:11111
        stopTime:22222
        onlineStrategyId:1

     * @param $options
     * @return array
     */
    public static function syncGoodsSku($arrInput, $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'syncGoodsSku');

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrInput, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrInput, $arrHeader);
    }
    
    /**
     * 通过skuId 获取sku信息 KV接口 | 数据来源 DB
     * @param type $inParams
     * @param type $options
     * @return type
     */
    public static function getGoodsSkuKVBySkuId($inParams, $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getGoodsSkuKVBySkuId');

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $inParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $inParams, $arrHeader);
    }
    

}
