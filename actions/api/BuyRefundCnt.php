<?php
/**************************************************************************
 *
 * Copyright (c) 2020 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   BuyRefundCnt.php
 * <AUTHOR>
 * @date   2020/3/29 20:52
 * @brief  【代理商或分销员】带来的购课用户或购课后退课总数
 **/
class Action_BuyRefundCnt extends Afxmis_BaseAction
{
    protected $_needInnerIp = true;
    protected $_needLogin = false;
    protected $_needAFXAuthUser = false;

    public function invoke()
    {
        $arrInput = [
            'appId' => isset($this->_requestParam['appId']) ? $this->_requestParam['appId'] : '',
            'uid'   => isset($this->_requestParam['uid']) ? $this->_requestParam['uid'] : '',
            'date'  => isset($this->_requestParam['date']) ? $this->_requestParam['date'] : '',
        ];

        Hk_Util_Log::start('ps_buyRefundCnt');
        $obj       = new Service_Page_Api_V1_BuyRefundCnt();
        $arrOutput = $obj->execute($arrInput);
        Hk_Util_Log::stop('ps_buyRefundCnt');
        Bd_Log::addNotice('arrOutput', json_encode($arrOutput, JSON_UNESCAPED_UNICODE));

        return $arrOutput;
    }
}