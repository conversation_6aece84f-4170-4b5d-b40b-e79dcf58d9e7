<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   OfflineTaskUnionOrderList.php
 * <AUTHOR>
 * @brief  导出订单列表
 **/
class Action_OfflineTaskUnionOrderList extends Afxmis_BaseAction
{
    protected $_needInnerIp = true;
    protected $_needLogin = false;
    protected $_needAFXAuthUser = false;

    public function invoke()
    {
        $arrInput = [
            'founderUid' => isset($this->_requestParam['founderUid']) ? intval($this->_requestParam['founderUid']) : 0,
            'founderId' => isset($this->_requestParam['founderId']) ? intval($this->_requestParam['founderId']) : 0,
            'founderRelation' => isset($this->_requestParam['founderRelation']) ? strval($this->_requestParam['founderRelation']) : '',
            'afxInstId' => isset($this->_userInfo['instId'])&& intval($this->_userInfo['instId'])>0 ? intval($this->_userInfo['instId']) : intval($this->_requestParam['instId']),
            'afxroleId' => isset($this->_userInfo['roleId']) ? intval($this->_userInfo['roleId']) : 0,

            // 应用方ID
            'appId'     => isset($this->_requestParam['appId']) ? intval($this->_requestParam['appId']) : 0,
            // 售卖类型: H5(含帮帮vip)/兑换码/手动导单
            'source' => isset($this->_requestParam['source']) ? (int)$this->_requestParam['source'] : 0,
            // 主订单ID
            'orderId' => isset($this->_requestParam['orderId']) ? (int)$this->_requestParam['orderId'] : 0,
            // 主订单最终状态
            'finalStatus'     => isset($this->_requestParam['finalStatus']) ? (int)$this->_requestParam['finalStatus'] : 0,
            // 负责人
            // 'cadre'          => isset($this->_requestParam['cadre']) ? (string)$this->_requestParam['cadre'] : '',
            // 代理商
            'agentUid'      => isset($this->_requestParam['agentUid']) ? (int)$this->_requestParam['agentUid'] : (int)$this->_requestParam['founderUid'],
            // 买家uid
            'userId'       => isset($this->_requestParam['userId']) ? (int)$this->_requestParam['userId'] : 0,
            // 买家手机号
            'mobile'         => isset($this->_requestParam['mobile']) ? (string)$this->_requestParam['mobile'] : 0,
            // 兑换码
            'cdkey'          => isset($this->_requestParam['cdkey']) ? (string)$this->_requestParam['cdkey'] : '',

            // 支付起止时间
            'sTime'          => isset($this->_requestParam['sTime']) ? (int)$this->_requestParam['sTime'] : 0,
            'eTime'          => isset($this->_requestParam['eTime']) ? (int)$this->_requestParam['eTime'] : 0,

            // 开课起止时间
            'sCourseStartTime'          => isset($this->_requestParam['sCourseStartTime']) ? (int)$this->_requestParam['sCourseStartTime'] : 0,
            'eCourseEndTime'          => isset($this->_requestParam['eCourseEndTime']) ? (int)$this->_requestParam['eCourseEndTime'] : 0,
            'pn'             => isset($this->_requestParam['pn']) ? (int)$this->_requestParam['pn'] : 0,
            'rn'             => isset($this->_requestParam['rn']) ? (int)$this->_requestParam['rn'] : 10,
        ];

        if (ral_get_idc() == 'test') {
            $arrInput['agentUid'] = 0;
        }
        Bd_Log::addNotice('afxInstId', $arrInput['afxInstId']);

        Hk_Util_Log::start('afxmis_admin_offlinetaskunionorderlist');
        $objAct = new Service_Data_AFXAct();
        $objUser = new Service_Data_AFXUser();
        $objUserCommon = new Afxmis_User_Common();
        $objUnionOrderListByDoris = new Qdlib_Service_Hetu_AFXUnionOrderListByDoris($objUser, $objUserCommon, $objAct);
        $arrOutPut = $objUnionOrderListByDoris->execute($arrInput, true);
        Hk_Util_Log::stop('afxmis_admin_offlinetaskunionorderlist');


        return $arrOutPut;
    }
}
