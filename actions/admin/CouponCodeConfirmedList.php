<?php
/**************************************************************************
 *
 * Copyright (c) 2021 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   CouponCodeConfirmedList.php
 * <AUTHOR>
 * @date   2021/08/04 14:27
 * @brief  已认领兑换码查询列表
 **/
class Action_CouponCodeConfirmedList  extends Afxmis_BaseAction
{
    public function invoke()
    {
        try{
            $this->check();
        } catch (Hk_Util_Exception $e) {
            throw new Qdlib_Common_Exception($e->getErrNo(), ':输入参数不符合校验规则', $e->getErrArg());
        }
        Hk_Util_Log::start('afxmis_admin_couponcodeconfirmedlist');
        $obj = new Service_Page_Act_V1_CouponCodeConfirmedList();
        $arrOutput = $obj->execute($this->_requestParam);
        Hk_Util_Log::stop('afxmis_admin_couponcodeconfirmedlist');
        return $arrOutput;
    }

    private function check()
    {
        $statusDict = Qdlib_Const_Hetu_CouponCode::$statusDict;
        $statusDict[0] = '已认领过的全部';
        unset($statusDict[Qdlib_Const_Hetu_CouponCode::STATUS_FOR_ASSIGN]);
        unset($statusDict[Qdlib_Const_Hetu_CouponCode::STATUS_FOR_CONFIRM]);
        unset($statusDict[Qdlib_Const_Hetu_CouponCode::STATUS_UNKNOWN]);
        $rule = [
            "codeName"   => [
                Qdlib_Util_ParamChecker::RULE_IS_STRING,
            ],
            "remark" => [
                Qdlib_Util_ParamChecker::RULE_IS_STRING,
            ],
            "status" => [
                Qdlib_Util_ParamChecker::RULE_ENUM => array_keys($statusDict),
            ],
        ];
        list($ret,$pageParams) = Qdlib_Util_ParamChecker::check($this->_requestParam, $rule);
        $this->_requestParam = array_merge($this->_requestParam, $pageParams, ['uid' => intval($this->_userInfo['uid'])]);
    }
}
