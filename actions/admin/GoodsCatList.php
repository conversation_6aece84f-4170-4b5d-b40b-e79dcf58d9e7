<?php

/**
 * @file   GoodsCatList.php
 * <AUTHOR>
 * @brief 分类商品信息列
 * */
class Action_GoodsCatList extends Afxmis_BaseAction
{

    public function invoke()
    {
        $arrInput = array(
            'uid' => $this->_userInfo['uid'],
            'userId' => $this->_userInfo['id'],
            'sourceId' => isset($this->_requestParam['sourceId']) ? intval($this->_requestParam['sourceId']) : 0,
            'categoryPid' => isset($this->_requestParam['categoryPid']) ? intval($this->_requestParam['categoryPid']) : 0,
            'categoryId' => isset($this->_requestParam['categoryId']) ? intval($this->_requestParam['categoryId']) : 0,
            'appId' => isset($this->_requestParam['appId']) ? intval($this->_requestParam['appId']) : 0,
            'pn' => isset($this->_requestParam['pn']) ? intval($this->_requestParam['pn']) : 1,
            'rn' => isset($this->_requestParam['rn']) ? intval($this->_requestParam['rn']) : 20,
            'act' => 'goodscatlist',
        );
        Hk_Util_Log::start('ps_goods_goodscatlist');
        $objPS = new Service_Page_Goods_V1_Goods();
        $arrOutput = $objPS->execute($arrInput);
        Hk_Util_Log::stop('ps_goods_goodscatlist');

        return $arrOutput;
    }

}
