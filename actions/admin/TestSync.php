<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   DurationList.php
 * <AUTHOR>
 * @brief 获取经销商列表
 **/
class Action_TestSync extends Afxmis_BaseAction
{
    public function invoke()
    {

       // Hk_Util_Log::start('afxmis_admin_usersave');
       // $obj       = new Service_Page_User_V1_Save();
        //$arrOutPut = $obj->execute($arrInput);
        //Hk_Util_Log::stop('afxmis_admin_usersave');
        //Bd_Log::addNotice('arrOutput', json_encode($arrOutPut, JSON_UNESCAPED_UNICODE));
        /*$service = new Service_Page_Duration_V1_VideoDuration();
        $service ->execute();*/
        $uid = 2000053610;
        $db = new Hk_Ds_User_Ucloud();
        $user = $db -> getUserInfo($uid, true);

        //打印信息
        var_dump($user);
        die;


        return true;
    }
}
