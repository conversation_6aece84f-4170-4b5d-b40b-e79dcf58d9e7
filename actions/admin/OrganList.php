<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   OrganList.php
 * <AUTHOR>
 * @brief  机构列表
 **/
class Action_OrganList extends Afxmis_BaseAction
{
    public function invoke()
    {
        $arrInput = [
            'appId'     => $this->appId,
            'founderId' => isset($this->_userInfo['id']) ? intval($this->_userInfo['id']) : 0,
            'uid'       => $this->_userInfo['uid'],
            'province'  => isset($this->_requestParam['province']) ? (string)$this->_requestParam['province'] : '',
            'city'      => isset($this->_requestParam['city']) ? (string)$this->_requestParam['city'] : '',
            'area'      => isset($this->_requestParam['area']) ? (string)$this->_requestParam['area'] : '',
            'name'      => isset($this->_requestParam['name']) ? (string)$this->_requestParam['name'] : '',
            'type'      => isset($this->_requestParam['type']) ? (int)$this->_requestParam['type'] : 0,
            'mobile'    => isset($this->_requestParam['mobile']) ? (int)$this->_requestParam['mobile'] : 0,
            'contacts'  => isset($this->_requestParam['contacts']) ? (string)$this->_requestParam['contacts'] : '',
            'pn'        => isset($this->_requestParam['pn']) ? (int)$this->_requestParam['pn'] : 1,
            'rn'        => isset($this->_requestParam['rn']) ? (int)$this->_requestParam['rn'] : 10,
        ];

        Hk_Util_Log::start('afxmis_admin_organlist');
        $obj       = new Service_Page_Organ_V1_List();
        $arrOutPut = $obj->execute($arrInput);
        Hk_Util_Log::stop('afxmis_admin_organlist');

        return $arrOutPut;
    }
}
