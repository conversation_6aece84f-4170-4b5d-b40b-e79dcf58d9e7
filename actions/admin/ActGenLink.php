<?php
/**************************************************************************
 *
 * Copyright (c) 2020 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   ActGenLink.php
 * <AUTHOR>
 * @date   2020/4/26 14:37
 * @brief  活动下生成素材链接
 **/
class Action_ActGenLink  extends Afxmis_BaseAction
{

    public function invoke()
    {
        $arrInput = array(
            'founderCadre' => isset($this->_userInfo['cadre']) ? strval($this->_userInfo['cadre']) : '',

            'appId' => isset($this->_requestParam['appId']) ? intval($this->_requestParam['appId']) : 0,
            'actId' => isset($this->_requestParam['actId']) ? intval($this->_requestParam['actId']) : 0,
            'type'  => isset($this->_requestParam['type']) ? intval($this->_requestParam['type']) : 0, //0/自己 1/下级
            'isCSV' => isset($this->_requestParam['isCSV']) ? intval($this->_requestParam['isCSV']) : 0,
            'isExportExcel' => isset($this->_requestParam['isExportExcel']) ? intval($this->_requestParam['isExportExcel']) : 0,
            'uid'   => $this->_userInfo['uid'],
            'instId' => $this->_userInfo['instId'],
            'roleId' => $this->_userInfo['roleId'],
            'ids'   => isset($this->_requestParam['ids']) ? explode('_', $this->_requestParam['ids']) : [],
            'channelLabel' => isset($this->_requestParam['channelLabel']) ? intval($this->_requestParam['channelLabel']) : 0,
            'linkNum' => isset($this->_requestParam['linkNum']) ? intval($this->_requestParam['linkNum']) : 0, //选择自己时生成短链的最大数
            'linkGroup' => isset($this->_requestParam['linkGroup']) ? strval($this->_requestParam['linkGroup']) : '', //生成多分销链接区段1,2,3,4,5
        );
        Hk_Util_Log::start('ps_actGenLink');
        $objPs = new Service_Page_Admin_V1_ActGenLink();
        $arrOutput = $objPs->execute($arrInput);
        Hk_Util_Log::stop('ps_actGenLink');

        return $arrOutput;
    }

}
