<?php
/**************************************************************************
 *
 * Copyright (c) 2021 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   CouponCodeConfirmLog.php
 * <AUTHOR>
 * @date   2021/08/03 14:36
 * @brief  领取记录
 **/
class Action_CouponCodeConfirmLog  extends Afxmis_BaseAction
{

    public function invoke()
    {
        $arrInput = array(
            'uid'   => intval($this->_userInfo['uid']),
            'page' => isset($this->_requestParam['page']) ? intval($this->_requestParam['page']) : 1,
            'pageSize' => isset($this->_requestParam['pageSize']) ? intval($this->_requestParam['pageSize']) : 20,
        );
        Hk_Util_Log::start('afxmis_admin_couponcodeconfirmlog');
        $obj = new Service_Page_Act_V1_CouponCodeConfirmLog();
        $arrOutput = $obj->execute($arrInput);
        Hk_Util_Log::stop('afxmis_admin_couponcodeconfirmlog');

        return $arrOutput;
    }

}
