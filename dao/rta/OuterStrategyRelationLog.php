<?php

class Qdlib_Dao_Rta_OuterStrategyRelationLog extends Qdlib_Dao_Qudao
{
    public function __construct()
    {
        parent::__construct();
        $this->_table = "tblQudaoRtaOuterStrategyRelationLog";
        $this->arrFieldsMap = [
            'id' => 'id',
            'uname' => 'uname',
            'taskId' => 'task_id',
            'uid' => 'uid',
            'version' => 'version',
            'rtaId' => 'rta_id',
            'bindType' => 'bind_type',
            'type' => 'type',
            'callbackStatus' => 'callback_status',
            'callbackCount' => 'callback_count',
            'callbackErrno' => 'callback_errno',
            'callbackMsg' => 'callback_msg',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'deleted' => 'deleted',
            'ext' => 'ext',
        ];
        $this->arrTypesMap = [
            'id' => Hk_Service_Db::TYPE_INT,
            'uname' => Hk_Service_Db::TYPE_STR,
            'taskId' => Hk_Service_Db::TYPE_STR,
            'uid' => Hk_Service_Db::TYPE_INT,
            'version' => Hk_Service_Db::TYPE_INT,
            'rtaId' => Hk_Service_Db::TYPE_STR,
            'bindType' => Hk_Service_Db::TYPE_INT,
            'type' => Hk_Service_Db::TYPE_INT,
            'callbackStatus' => Hk_Service_Db::TYPE_INT,
            'callbackCount' => Hk_Service_Db::TYPE_INT,
            'callbackErrno' => Hk_Service_Db::TYPE_INT,
            'callbackMsg' => Hk_Service_Db::TYPE_STR,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'deleted' => Hk_Service_Db::TYPE_INT,
            'ext' => Hk_Service_Db::TYPE_INT,
        ];
    }
}
