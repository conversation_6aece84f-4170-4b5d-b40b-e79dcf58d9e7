<?php
/**
 * Copyright (c) 2016 zuoyebang.com, Inc. All Rights Reserved
 * Created by PhpStorm.
 * @file:LessonAllState.php
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @date: 2016/7/1
 * @time: 17:12
 */

class Zhibo_Dao_LessonAllState extends Hk_Common_BaseDao
{

    public static $allFields = array(
        'id',
        'studentUid',
        'courseId',
        'teacherUid',
        'assistantUid',
        'classId',
        'preRightNum',
        'attendNum',
        'homeworkNum',
        'answerRate',
        'report',
        'createTime',
        'updateTime',
        'extData',
    );

    public function __construct()
    {
        $this->_db = Hk_Service_Db::getDB(Zhibo_Static::DBCLUSTER_FUDAO, false, Zhibo_Static::DBLOG_FUDAO, Zhibo_Static::DBLOG_AUTOROTATE_FLAG);
        $this->_table = "tblLessonAllState";

        $this->arrFieldsMap = array(
            'id' => 'id',
            'studentUid' => 'student_uid',
            'courseId' => 'course_id',
            'teacherUid' => 'teacher_uid',
            'assistantUid' => 'assistant_uid',
            'classId' => 'class_id',
            'preRightNum' => 'pre_right_num',
            'attendNum' => 'attend_num',
            'homeworkNum' => 'homework_num',
            'answerRate' => 'answer_rate',
            'report' => 'report',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'extData' => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id' => Hk_Service_Db::TYPE_INT,
            'studentUid' => Hk_Service_Db::TYPE_INT,
            'courseId' => Hk_Service_Db::TYPE_INT,
            'teacherUid' => Hk_Service_Db::TYPE_INT,
            'assistantUid' => Hk_Service_Db::TYPE_INT,
            'classId' => Hk_Service_Db::TYPE_INT,
            'preRightNum' => Hk_Service_Db::TYPE_INT,
            'attendNum' => Hk_Service_Db::TYPE_INT,
            'homeworkNum' => Hk_Service_Db::TYPE_INT,
            'answerRate' => Hk_Service_Db::TYPE_INT,
            'report' => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'extData' => Hk_Service_Db::TYPE_JSON,
        );
    }
}