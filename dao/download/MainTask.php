<?php

class Qdlib_Dao_Download_MainTask extends Hk_Common_BaseDao
{
    public function __construct()
    {
        parent::__construct();
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Qdlib_Util_DB::DBLOG_QUDAO;
        $this->_table = "tblQudaoMainTask";
        $this->arrFieldsMap = [
            'id' => 'id',
            'taskName' => 'task_name',
            'taskNickname' => 'task_nickname',
            'taskId' => 'task_id',
            'taskType' => 'task_type',
            'status' => 'status',
            'input' => 'input',
            'selectInfo' => 'select_info',
            'output' => 'output',
            'url' => 'url',
            'repeatCount' => 'repeat_count',
            'repeatTime' => 'repeat_time',
            'deleted' => 'deleted',
            'uname' => 'uname',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
        ];
        $this->arrTypesMap = [
            'id' => Hk_Service_Db::TYPE_INT,
            'taskName' => Hk_Service_Db::TYPE_STR,
            'taskNickname' => Hk_Service_Db::TYPE_STR,
            'taskId' => Hk_Service_Db::TYPE_STR,
            'taskType' => Hk_Service_Db::TYPE_STR,
            'status' => Hk_Service_Db::TYPE_INT,
            'input' => Hk_Service_Db::TYPE_JSON,
            'selectInfo' => Hk_Service_Db::TYPE_JSON,
            'output' => Hk_Service_Db::TYPE_JSON,
            'url' => Hk_Service_Db::TYPE_STR,
            'repeatCount' => Hk_Service_Db::TYPE_INT,
            'repeatTime' => Hk_Service_Db::TYPE_INT,
            'deleted' => Hk_Service_Db::TYPE_INT,
            'uname' => Hk_Service_Db::TYPE_STR,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
        ];
    }

    public function getAllFields()
    {
        return array_keys($this->arrFieldsMap);
    }

    public function reconnect() {
        if (!Zb_Util_Helper::isCli()) {
            return true;
        }
        if(empty($this->_db)) {
            $this->_db     = Qdlib_Util_DB::getQudaoDb(false);
        }
        Qdlib_Util_DB::reconnect($this->_db);
    }
}
