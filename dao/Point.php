<?php
/**
 * Copyright (c) 2016 zuoyebang.com, Inc. All Rights Reserved
 * Created by PhpStorm.
 * @file:Point.php
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @date: 2016/7/1
 * @time: 17:21
 */

class Zhibo_Dao_Point extends Hk_Common_BaseDao
{

    public function __construct()
    {
        $this->_db = Hk_Service_Db::getDB(Zhibo_Static::DBCLUSTER_FUDAO, false, Zhibo_Static::DBLOG_FUDAO, Zhibo_Static::DBLOG_AUTOROTATE_FLAG);
        $this->_table = "tblPoint";

        $this->arrFieldsMap = array(
            'pointId' => 'point_id',
            'pointName' => 'point_name',
            'content' => 'content',
            'source' => 'source',
            'gradeId' => 'grade_id',
            'subjectId' => 'subject_id',
            'bookVersion' => 'book_version_name',
            'parentPointId' => 'parent_point_id',
            'rank' => 'rank',
            'deleted' => 'deleted',
            'createTime' => 'create_time',
            'extData' => 'ext_data',
        );

        $this->arrTypesMap = array(
            'pointId' => Hk_Service_Db::TYPE_INT,
            'content' => Hk_Service_Db::TYPE_JSON,
            'source' => Hk_Service_Db::TYPE_INT,
            'gradeId' => Hk_Service_Db::TYPE_INT,
            'subjectId' => Hk_Service_Db::TYPE_INT,
            'parentPointId' => Hk_Service_Db::TYPE_INT,
            'rank' => Hk_Service_Db::TYPE_INT,
            'deleted' => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'extData' => Hk_Service_Db::TYPE_JSON,
        );
    }
}