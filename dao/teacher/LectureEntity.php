<?php
/**
 * Created by PhpStorm.
 * User: 作业帮
 * Author:<PERSON><PERSON><PERSON>@zuoyebang.com
 * Date: 2019/08/20
 */

class Hkzb_Dao_Teacher_LectureEntity extends Hk_Common_BaseDao {

    public function __construct($sameConnect = true) {
        $this->_dbName      = 'jxzt_teacher/jxzt_teacher';
        $this->_db          = NULL;
        $this->_logFile     = Zb_Const_Db::DBLOG_ZB;
        if ($sameConnect == false) {
            $this->_db = Hk_Service_Db::getDB($this->_dbName, true);
        }
        $this->_table       = "tblLectureEntity";
        $this->arrFieldsMap = array(
            'entityId'      => 'entity_id',
            'entityName'    => 'entity_name',
            'entityNew'     => 'entity_new',
            'entityOri'     => 'entity_ori',
            'entityUrl'     => 'entity_url',
            'fileSize'      => 'file_size',
            'lectureId'     => 'lecture_id',
            'courseId'      => 'course_id',
            'lessonId'      => 'lesson_id',
            'status'        => 'status',
            'lectureType'   => 'lecture_type',
            'deleted'       => 'deleted',
            'deleteUid'     => 'delete_uid',
            'deleteUname'   => 'delete_uname',
            'createTime'    => 'create_time',
            'updateTime'    => 'update_time',
            'deleteTime'    => 'delete_time',
        );

        $this->arrTypesMap = array(
            'entityId'    => Hk_Service_Db::TYPE_INT,
            'entityName'  => Hk_Service_Db::TYPE_STR,
            'entityNew'   => Hk_Service_Db::TYPE_STR,
            'entityOri'   => Hk_Service_Db::TYPE_STR,
            'entityUrl'   => Hk_Service_Db::TYPE_STR,
            'fileSize'    => Hk_Service_Db::TYPE_INT,
            'lectureId'   => Hk_Service_Db::TYPE_INT,
            'courseId'    => Hk_Service_Db::TYPE_INT,
            'lessonId'    => Hk_Service_Db::TYPE_INT,
            'status'      => Hk_Service_Db::TYPE_INT,
            'lectureType' => Hk_Service_Db::TYPE_INT,
            'deleted'     => Hk_Service_Db::TYPE_INT,
            'deleteUid'   => Hk_Service_Db::TYPE_INT,
            'deleteUname' => Hk_Service_Db::TYPE_STR,
            'createTime'  => Hk_Service_Db::TYPE_INT,
            'updateTime'  => Hk_Service_Db::TYPE_INT,
            'deleteTime'  => Hk_Service_Db::TYPE_INT,
        );
    }

    public function getAllFields(){
        return array_keys($this->arrFieldsMap);
    }

    public function getConn(){
        return $this->_db;
    }
}
