<?php
/***************************************************************************
 *
 * Copyright (c) 2017 zybang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file        AssistantComment.php
 * <AUTHOR>
 * @create_date 2017-07-11
 * @brief       助教老师评语
 *
 **/

class Hkzb_Dao_Fudao_AssistantComment extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblAssistantComment";
        $this->arrFieldsMap = array(
            'id'         => 'id',
            'teacherUid' => 'teacher_uid',
            'lessonId'   => 'lesson_id',
            'rank'       => 'rank',
            'tags'       => 'tags',
            'content'    => 'content',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'extData'    => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'         => Hk_Service_Db::TYPE_INT,
            'teacherUid' => Hk_Service_Db::TYPE_INT,
            'lessonId'   => Hk_Service_Db::TYPE_INT,
            'rank'       => Hk_Service_Db::TYPE_INT,
            'tags'       => Hk_Service_Db::TYPE_INT,
            'content'    => Hk_Service_Db::TYPE_JSON,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'extData'    => Hk_Service_Db::TYPE_JSON,
        );
    }
}