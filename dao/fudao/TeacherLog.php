<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 zybang.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file TeacherLog.php
 * <AUTHOR>
 * @date 2015/12/14 19:50:15
 * @brief 老师记录
 *  
 **/

class Hkzb_Dao_Fudao_TeacherLog extends Hk_Common_BaseMultiDao
{
    public function __construct()
    {
        $this->_dbName = 'jxzt_teacher/jxzt_teacher';
        //$this->_db     = Hk_Service_Db::getDB($this->_dbName);
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblTeacherLog";
        $this->_tableName   = 'tblTeacherLog';
        $this->_partionKey  = 'teacherUid';
        $this->_partionType = self::TYPE_TABLE_PARTION_MOD;
        $this->_partionNum  = 20;
        $this->arrFieldsMap = array(
            'phone'      => 'phone',
            'teacherUid' => 'teacher_uid',
            'courseId'   => 'course_id',
            'lessonId'   => 'lesson_id',
            'action'     => 'action',
            'message'    => 'message',
            'createTime' => 'create_time',
            'role'       => 'role',
            'liveroomId' => 'liveroom_id',
        );

        $this->arrTypesMap = array(
            'phone'      => Hk_Service_Db::TYPE_STR,
            'teacherUid' => Hk_Service_Db::TYPE_INT,
            'courseId'   => Hk_Service_Db::TYPE_INT,
            'lessonId'   => Hk_Service_Db::TYPE_INT,
            'action'     => Hk_Service_Db::TYPE_INT,
            'message'    => Hk_Service_Db::TYPE_STR,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'role'       => Hk_Service_Db::TYPE_INT,
            'liveroomId' => Hk_Service_Db::TYPE_INT,
        );
    }
}