<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file SortCourse.php
 * <AUTHOR>
 * @date 2015/11/17 13:26:46
 * @brief 课程排序
 *  
 **/

class Hkzb_Dao_Fudao_SortCourse extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = 'fudao/zyb_fudao';
        //$this->_db     = Hk_Service_Db::getDB($this->_dbName);
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblSortCourse";
        $this->arrFieldsMap = array(
            'type'        => 'type',
            'grade'       => 'grade',
            'subject'     => 'subject',
            'sortIds'     => 'sort_ids',
            'createTime'  => 'create_time',
            'updateTime'  => 'update_time',
            'operatorUid' => 'operator_uid',
            'operator'    => 'operator',
            'extData'     => 'ext_data',
        );

        $this->arrTypesMap = array(
            'type'        => Hk_Service_Db::TYPE_INT,
            'grade'       => Hk_Service_Db::TYPE_INT,
            'subject'     => Hk_Service_Db::TYPE_INT,
            'sortIds'     => Hk_Service_Db::TYPE_JSON,
            'createTime'  => Hk_Service_Db::TYPE_INT,
            'updateTime'  => Hk_Service_Db::TYPE_INT,
            'operatorUid' => Hk_Service_Db::TYPE_INT,
            'operator'    => Hk_Service_Db::TYPE_STR,
            'extData'     => Hk_Service_Db::TYPE_JSON,
        );
    }
}