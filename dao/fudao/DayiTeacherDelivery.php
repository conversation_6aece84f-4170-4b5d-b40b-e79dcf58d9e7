<?php
/***************************************************************************
 *
 * Copyright (c) 2015 zybang.com, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file DayiTeacherDelivery.php
 * <AUTHOR>
 * @date 2015/12/14 19:50:15
 * @update_date 2017-11-9  by koux<PERSON><PERSON>eng
 * @brief 助教答疑配发
 *
 **/

class Hkzb_Dao_Fudao_DayiTeacherDelivery extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblDayiTeacherDelivery";
        $this->arrFieldsMap = array(
            'id'           => 'id',
            'teacherUid'   => 'teacher_uid',
            'topGrade'     => 'top_grade',
            'grade'        => 'grade',
            'subject'      => 'subject',
            'week'         => 'week',
            'shift'        => 'shift',
            'status'       => 'status',
            'createTime'   => 'create_time',
            'updateTime'   => 'update_time',
            'operatorUid'  => 'operator_uid',
            'operator'     => 'operator',
            'extData'      => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'            => Hk_Service_Db::TYPE_INT,
            'teacherUid'    => Hk_Service_Db::TYPE_INT,
            'grade'         => Hk_Service_Db::TYPE_INT,
            'topGrade'      => Hk_Service_Db::TYPE_INT,
            'subject'       => Hk_Service_Db::TYPE_INT,
            'week'          => Hk_Service_Db::TYPE_INT,
            'shift'         => Hk_Service_Db::TYPE_INT,
            'status'        => Hk_Service_Db::TYPE_INT,
            'createTime'    => Hk_Service_Db::TYPE_INT,
            'updateTime'    => Hk_Service_Db::TYPE_INT,
            'operatorUid'   => Hk_Service_Db::TYPE_INT,
            'operator'      => Hk_Service_Db::TYPE_STR,
            'extData'       => Hk_Service_Db::TYPE_JSON,
        );
    }
}