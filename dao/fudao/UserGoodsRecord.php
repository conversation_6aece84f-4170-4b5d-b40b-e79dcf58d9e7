<?php
/**
 * Copyright (c) 2017 zuoyebang.com, Inc. All Rights Reserved
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @file: UserGoodsRecord.php
 * @date: 2017/6/1
 * @time: 13:51
 * @desc:
 */

class Hkzb_Dao_Fudao_UserGoodsRecord extends Hk_Common_BaseDao
{

    public static $arrFields = array(
        'id',
        'uid',
        'goodsId',
        'goodsName',
        'createTime',
        'updateTime',
        'extData',
    );

    public function __construct()
    {
        $this->_dbName  = Zhibo_Static::DBCLUSTER_FUDAO;
        $this->_db      = null;
        $this->_logFile = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table   = 'tblUserGoodsRecord';

        $this->arrFieldsMap = array(
            'goodsId'    => 'goods_id',
            'goodsName'  => 'goods_name',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'extData'    => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'         => Hk_Service_Db::TYPE_INT,
            'uid'        => Hk_Service_Db::TYPE_INT,
            'goodsId'    => Hk_Service_Db::TYPE_INT,
            'goodsName'  => Hk_Service_Db::TYPE_STR,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'extData'    => Hk_Service_Db::TYPE_JSON,
        );
    }
}