<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file: PDFTask.php
 * @author: huanghe <<EMAIL>>
 * @date: 2018/3/5 下午9:28
 * @brief: PDF任务模型
 */
class Hkzb_Dao_Fudao_PDFTask extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = 'fudao/zyb_fudao';
        $this->_db = null;
        $this->_logFile = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table = "tblPDFTask";

        $this->arrFieldsMap = array(
            'taskId' => 'task_id',
            'taskParams' => 'task_params',
            'taskType' => 'task_type',
            'taskStatus' => 'task_status',
            'sign' => 'sign',
            'comment' => 'comment',
            'pdfUrl' => 'pdf_url',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'creatorUid' => 'creator_uid'
        );

        $this->arrTypesMap = array(
            'taskId' => Hk_Service_Db::TYPE_INT,
            'taskParams' => Hk_Service_Db::TYPE_JSON,
            'taskType' => Hk_Service_Db::TYPE_INT,
            'taskStatus' => Hk_Service_Db::TYPE_INT,
            'comment' => Hk_Service_Db::TYPE_STR,
            'pdfUrl' => Hk_Service_Db::TYPE_STR,
            'sign' => Hk_Service_Db::TYPE_STR,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'creatorUid' => Hk_Service_Db::TYPE_INT,
        );
    }
}