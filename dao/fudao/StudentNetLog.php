<?php
/***************************************************************************
 * 
 * Copyright (c) 2016 Zuoyebang.com, Inc. All Rights Reserved
 * 
 **************************************************************************/

/**
 * @file StudentNetLog.php
 * <AUTHOR>
 * @date 2016/11/1
 * @brief 主讲端网络日志记录——前端主要写表，统计离线数据使用
 *  
 **/

class Hkzb_Dao_Fudao_StudentNetLog extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = 'gnmis/gnmis';
        //$this->_db     = Hk_Service_Db::getDB($this->_dbName);
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblGuoyuanPlayerLog";
        $this->arrFieldsMap = array(
            'lessonId'  => 'lesson_id',
            'uid'       => 'uid',
            'creatTime' => 'ctime',
            'action'    => 'action',
            'os'        => 'os',
            'extBit'    => 'ext_bit',
            'extData'   => 'ext_data',
        );

        $this->arrTypesMap = array(
            'lessonId'  => Hk_Service_Db::TYPE_INT,
            'uid'       => Hk_Service_Db::TYPE_INT,
            'creatTime' => Hk_Service_Db::TYPE_INT,
            'action'    => Hk_Service_Db::TYPE_STR,
            'os'        => Hk_Service_Db::TYPE_STR,
            'extBit'    => Hk_Service_Db::TYPE_INT,
            'extData'   => Hk_Service_Db::TYPE_STR,
        );
    }
}