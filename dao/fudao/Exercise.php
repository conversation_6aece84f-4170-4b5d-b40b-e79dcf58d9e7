<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Exercise.php
 * <AUTHOR>
 * @date 2015/11/17 14:03:05
 * @brief 习题
 *  
 **/

class Hkzb_Dao_Fudao_Exercise extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = 'fudao/zyb_fudao';
        //$this->_db     = Hk_Service_Db::getDB($this->_dbName);
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblExercise";
        $this->arrFieldsMap = array(
            'exerciseId'  => 'exercise_id',
            'tid'         => 'tid',
            'courseId'    => 'course_id',
            'lessonId'    => 'lesson_id',
            'question'    => 'question',
            'answer'      => 'answer',
            'score'       => 'score',
            'purpose'     => 'purpose',
            'type'        => 'type',
            'relatePoint' => 'relate_point',
            'deleted'     => 'deleted',
            'createTime'  => 'create_time',
            'updateTime'  => 'update_time',
            'operatorUid' => 'operator_uid',
            'operator'    => 'operator',
            'extData'     => 'ext_data',
        );

        $this->arrTypesMap = array(
            'exerciseId'  => Hk_Service_Db::TYPE_INT,
            'tid'         => Hk_Service_Db::TYPE_INT,
            'courseId'    => Hk_Service_Db::TYPE_INT,
            'lessonId'    => Hk_Service_Db::TYPE_INT,
            'question'    => Hk_Service_Db::TYPE_JSON,
            'answer'      => Hk_Service_Db::TYPE_STR,
            'score'       => Hk_Service_Db::TYPE_INT,
            'purpose'     => Hk_Service_Db::TYPE_INT,
            'type'        => Hk_Service_Db::TYPE_INT,
            'deleted'     => Hk_Service_Db::TYPE_INT,
            'createTime'  => Hk_Service_Db::TYPE_INT,
            'updateTime'  => Hk_Service_Db::TYPE_INT,
            'operatorUid' => Hk_Service_Db::TYPE_INT,
            'extData'     => Hk_Service_Db::TYPE_JSON,
        );
    }
}