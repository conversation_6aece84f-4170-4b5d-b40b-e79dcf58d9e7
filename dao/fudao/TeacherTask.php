<?php

/**
 * @file    TeacherGuide.php
 * <AUTHOR>
 * @date    2017-08-17
 * @brief   主讲老师新手引导
 *
 **/
class Hkzb_Dao_Fudao_TeacherTask extends Hk_Common_BaseDao
{

    public function __construct()
    {
        $this->_dbName      = 'jxzt_teacher/jxzt_teacher';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblTeacherTask";
        $this->arrFieldsMap = array(
            'id'         => 'id',
            'teacherUid' => 'teacher_uid',
            'taskType'   => 'task_type',
            'taskData'   => 'task_data',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
        );

        $this->arrTypesMap = array(
            'id'         => Hk_Service_Db::TYPE_INT,
            'teacherUid' => Hk_Service_Db::TYPE_INT,
            'taskType'   => Hk_Service_Db::TYPE_INT,
            'taskData'   => Hk_Service_Db::TYPE_JSON,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
        );
    }
}