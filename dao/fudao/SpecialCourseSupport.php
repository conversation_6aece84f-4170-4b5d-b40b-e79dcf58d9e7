<?php

/**
 * @file        SpecialCourseSupport.php
 * <AUTHOR>
 * @create_date 2017-03-19
 * @brief       特惠课拉新记录表
 *
 **/
class Hkzb_Dao_Fudao_SpecialCourseSupport extends Hk_Common_BaseDao
{

    public static $allFields = array(
        'id',
        'courseId',
        'studentUid',
        'status',
        'groupId',
        'deleted',
        'createTime',
        'updateTime',
    );

    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblSpecialCourseSupport";
        $this->arrFieldsMap = array(
            'id'            => 'id',
            'courseId'      => 'course_id',
            'studentUid'    => 'student_uid',
            'status'        => 'status',
            'groupId'       => 'group_id',
            'deleted'       => 'deleted',
            'createTime'    => 'create_time',
            'updateTime'    => 'update_time',
        );

        $this->arrTypesMap = array(
            'id'            => Hk_Service_Db::TYPE_INT,
            'courseId'      => Hk_Service_Db::TYPE_INT,
            'studentUid'    => Hk_Service_Db::TYPE_INT,
            'status'        => Hk_Service_Db::TYPE_INT,
            'groupId'       => Hk_Service_Db::TYPE_INT,
            'deleted'       => Hk_Service_Db::TYPE_INT,
            'createTime'    => Hk_Service_Db::TYPE_INT,
            'updateTime'    => Hk_Service_Db::TYPE_INT,
        );
    }
}