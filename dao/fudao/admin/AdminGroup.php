<?php
/**
 * @file AdminInfo.php.
 * <AUTHOR>
 * @date: 2017/11/17
 */
class Hkzb_Dao_Fudao_Admin_AdminGroup extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = 'fudao/zyb_fudao';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblAdminGroup";
        $this->arrFieldsMap = array(
            'id'            => 'id',
            'rbacId'        => 'rbacId',
            'title'         => 'title',
            'rPrivileges'   => 'rPrivileges',
            'sysType'       => 'sys_type',

        );

        $this->arrTypesMap = array(
            'id'                => Hk_Service_Db::TYPE_INT,
            'rbacId'            => Hk_Service_Db::TYPE_INT,
            'title'             => Hk_Service_Db::TYPE_STR,
            'rPrivileges'       => Hk_Service_Db::TYPE_STR,
            'sysType'           => Hk_Service_Db::TYPE_INT,

        );
    }

}