<?php
/**
 * File: StuConvertTask.php
 * User: y<PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * Date: 2018/5/16 15:23
 * Brief: 体验课学生转换率任务表
 */

class Hkzb_Dao_Fudao_StuConvertTask extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = 'fudao/zyb_fudao';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblStudentConvertTask";
        $this->arrFieldsMap = array(
            'id'         => 'id',
            'courseId'   => 'course_id',
            'studentUid' => 'student_uid',
            'status'     => 'status',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
        );

        $this->arrTypesMap = array(
            'id'         => Hk_Service_Db::TYPE_INT,
            'courseId'   => Hk_Service_Db::TYPE_INT,
            'studentUid' => Hk_Service_Db::TYPE_INT,
            'status'     => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
        );
    }
}