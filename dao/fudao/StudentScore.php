<?php
/***************************************************************************
 *
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   StudentDetail.php
 * <AUTHOR>
 * @date   2015-11-26
 * @brief  学生成绩信息(辅导员维护)
 *
 **/
class Hkzb_Dao_Fudao_StudentScore extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = 'fudao/zyb_fudao';
        //$this->_db          = Hk_Service_Db::getDB($this->_dbName);
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblStudentScore";
        $this->arrFieldsMap = array(
            'id'          => 'id',
            'studentUid'  => 'student_uid',
            'subjectId'   => 'subject_id',
            'examType'    => 'exam_type',
            'examTime'    => 'exam_time',
            'score'       => 'score',
            'classRank'   => 'class_rank',
            'gradeRank'   => 'grade_rank',
            'deleted'     => 'deleted',
            'createTime'  => 'create_time',
            'updateTime'  => 'update_time',
            'operatorUid' => 'operator_uid',
            'operator'    => 'operator',
            'extData'     => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'          => Hk_Service_Db::TYPE_INT,
            'studentUid'  => Hk_Service_Db::TYPE_INT,
            'subjectId'   => Hk_Service_Db::TYPE_INT,
            'examType'    => Hk_Service_Db::TYPE_STR,
            'examTime'    => Hk_Service_Db::TYPE_INT,
            'score'       => Hk_Service_Db::TYPE_INT,
            'classRank'   => Hk_Service_Db::TYPE_INT,
            'gradeRank'   => Hk_Service_Db::TYPE_INT,
            'deleted'     => Hk_Service_Db::TYPE_INT,
            'createTime'  => Hk_Service_Db::TYPE_INT,
            'updateTime'  => Hk_Service_Db::TYPE_INT,
            'operatorUid' => Hk_Service_Db::TYPE_INT,
            'operator'    => Hk_Service_Db::TYPE_STR,
            'extData'     => Hk_Service_Db::TYPE_JSON,
        );
    }
}