<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   CourseLesson.php
 * <AUTHOR>
 * @date   2018/03/16 13:43:45
 * @brief  课节
 *
 **/
class Hkzb_Dao_Fudao_CourseLesson extends Hk_Common_BaseDao
{
    public function __construct($sameConnect = true)
    {
        $this->_dbName  = 'fudao/miscourse';
        $this->_db      = null;
        $this->_logFile = Hkzb_Util_FuDao::DBLOG_FUDAO;
        if ($sameConnect == false) {
            $this->_db = Hk_Service_Db::getDB($this->_dbName, true);
        }
        $this->_table       = "tblCourseLesson";
        $this->arrFieldsMap = array(
            'id'            => 'id',
            'courseId'      => 'course_id',
            'lessonId'      => 'lesson_id',
            'type'          => 'type',
            'deleted'       => 'deleted',
            'createTime'    => 'create_time',
            'updateTime'    => 'update_time',
            'creatorUid'    => 'creator_uid',
            'creatorName'   => 'creator_name',
            'updatorUid'    => 'updator_uid',
            'updatorName'   => 'updator_name',
        );

        $this->arrTypesMap = array(
            'id'            => Hk_Service_Db::TYPE_INT,
            'courseId'      => Hk_Service_Db::TYPE_INT,
            'lessonId'      => Hk_Service_Db::TYPE_INT,
            'type'          => Hk_Service_Db::TYPE_INT,
            'deleted'       => Hk_Service_Db::TYPE_INT,
            'createTime'    => Hk_Service_Db::TYPE_INT,
            'updateTime'    => Hk_Service_Db::TYPE_INT,
            'creatorUid'    => Hk_Service_Db::TYPE_INT,
            'creatorName'   => Hk_Service_Db::TYPE_STR,
            'updatorUid'    => Hk_Service_Db::TYPE_INT,
            'updatorName'   => Hk_Service_Db::TYPE_INT,
        );
    }
}
