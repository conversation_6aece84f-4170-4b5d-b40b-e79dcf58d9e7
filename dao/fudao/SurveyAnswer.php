<?php
/**
 * File: SurveyAnswer.php
 * User: lihongjie
 * Date: 2018/2/7
 * Time: 下午7:02
 * Desc: 课后评价回答
 */
class Hkzb_Dao_Fudao_SurveyAnswer extends Hk_Common_BaseMultiDao
{
    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblSurveyAnswer";
        $this->_tableName   = "tblSurveyAnswer";
        $this->_partionKey  = "lessonId";
        $this->_partionType = self::TYPE_TABLE_PARTION_MUL;
        $this->_partionNum  = 20000;
        $this->arrFieldsMap = array(
            'id'            => 'id',
            'surveyId'      => 'survey_id',
            'questionId'    => 'question_id',
            'studentUid'    => 'student_uid',
            'lessonId'      => 'lesson_id',
            'type'          => 'type',
            'answer'        => 'answer',
            'deleted'       => 'deleted',
            'createTime'    => 'create_time',
        );

        $this->arrTypesMap = array(
            'id'            => Hk_Service_Db::TYPE_INT,
            'surveyId'      => Hk_Service_Db::TYPE_INT,
            'questionId'    => Hk_Service_Db::TYPE_INT,
            'studentUid'    => Hk_Service_Db::TYPE_INT,
            'lessonId'      => Hk_Service_Db::TYPE_INT,
            'type'          => Hk_Service_Db::TYPE_INT,
            'answer'        => Hk_Service_Db::TYPE_STR,
            'deleted'       => Hk_Service_Db::TYPE_INT,
            'createTime'    => Hk_Service_Db::TYPE_INT,
        );
    }
}