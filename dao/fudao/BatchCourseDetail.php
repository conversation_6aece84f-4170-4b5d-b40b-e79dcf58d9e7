<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Course.php
 * <AUTHOR>
 * @date 2015/11/17 13:26:46
 * @brief 批量课程报名详细记录
 *  
 **/

class Hkzb_Dao_Fudao_BatchCourseDetail extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = 'fudao/zyb_fudao';
        //$this->_db     = Hk_Service_Db::getDB($this->_dbName);
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblBatchCourseDetail";
        $this->arrFieldsMap = array(
            'id'         => 'id',
            'courseId'   => 'course_id',
            'studentUid' => 'student_uid',
            'batchId'    => 'batch_id',
            'isAdd'      => 'is_add',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'extData'    => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'         => Hk_Service_Db::TYPE_INT,
            'courseId'   => Hk_Service_Db::TYPE_INT,
            'studentUid' => Hk_Service_Db::TYPE_INT,
            'batchId'    => Hk_Service_Db::TYPE_INT,
            'isAdd'      => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'extData'    => Hk_Service_Db::TYPE_JSON,
        );
    }
}