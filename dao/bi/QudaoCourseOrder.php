<?php

class Qdlib_Dao_Bi_QudaoCourseOrder extends Hk_Common_BaseDao {

    public function __construct() {
        $this->_dbName  = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db      = null;
        $this->_logFile = Qdlib_Util_DB::DBLOG_QUDAO;

        $this->_table = "tblBiQudaoCourseOrder";

        $this->arrFieldsMap = [
            'id'                    => 'id',
            'orifrom'               => 'orifrom',
            'lastfrom'              => 'lastfrom',
            'paymentChannel'        => 'payment_channel',
            'tradeStatus'           => 'trade_status',
            'tradeType'             => 'trade_type',
            'userId'                => 'user_id',
            'subTradeId'            => 'sub_trade_id',
            'tradeId'               => 'trade_id',
            'skuId'                 => 'sku_id',
            'courseId'              => 'course_id',
            'tradeTime'             => 'trade_time',
            'tradeDate'             => 'trade_date',
            'subTradeStatus'        => 'sub_trade_status',
            'changeOrderType'       => 'change_order_type',
            'refundFinishTime'      => 'refund_finish_time',
            'refundFinishDate'      => 'refund_finish_date',
            'finalSubTradeId'       => 'final_sub_trade_id',
            'changeClassTime'       => 'change_class_time',
            'changeClassDate'       => 'change_class_date',
            'originalSubTradeId'    => 'original_sub_trade_id',
            'subTradeCreateTime'    => 'sub_trade_create_time',
            'subTradeCreateDate'    => 'sub_trade_create_date',
            'subTradeUpdateTime'    => 'sub_trade_update_time',
            'subTradeUpdateDate'    => 'sub_trade_update_date',
            'orderChannel'          => 'order_channel',
            'sendProvince'          => 'send_province',
            'sendCity'              => 'send_city',
            'cClassType'            => 'c_class_type',
            'cMainGradeId'          => 'c_main_grade_id',
            'cMainSubjectId'        => 'c_main_subject_id',
            'cCourseType'           => 'c_course_type',
            'cLearnSeason'          => 'c_learn_season',
            'cCourseStartTime'      => 'c_course_start_time',
            'cCourseStopTime'       => 'c_course_stop_time',
            'agentId'               => 'agent_id',
            'channel'               => 'channel',
            'account'               => 'account',
            'attrClassType'         => 'attr_class_type',
            'attrLxWay'             => 'attr_lx_way',
            'attrSubject'           => 'attr_subject',
            'attrPageType'          => 'attr_page_type',
            'attrPageName'          => 'attr_page_name',
            'cLearnYear'            => 'c_learn_year',
            'cGradePeriodId'        => 'c_grade_period_id',
            'cIsJumpGrade'          => 'c_is_jump_grade',
            'cIsRightPrice'         => 'c_is_right_price',
            'cIsShorttrain'         => 'c_is_shorttrain',
            'price'                 => 'price',
            'payment'               => 'payment',
            'refundMoney'           => 'refund_money',
            'pageId'                => 'page_id',
            'clickId'               => 'click_id',
            'isAd'                  => 'is_ad',
            'campaignId'            => 'campaign_id',
            'adgroupId'             => 'adgroup_id',
            'adId'                  => 'ad_id',
            'creativeId'            => 'creative_id',
            'tradeDateHour'         => 'trade_date_hour',
            'refundFinishDateHour'  => 'refund_finish_date_hour',
            'changeClassDateHour'   => 'change_class_date_hour',
            'subTradeCreateDateHour'=> 'sub_trade_create_date_hour',
            'subTradeUpdateDateHour'=> 'sub_trade_update_date_hour',
        ];

        $this->arrTypesMap = [
            'id'                    => Hk_Service_Db::TYPE_INT,
            'orifrom'               => Hk_Service_Db::TYPE_STR,
            'lastfrom'              => Hk_Service_Db::TYPE_STR,
            'paymentChannel'        => Hk_Service_Db::TYPE_STR,
            'tradeStatus'           => Hk_Service_Db::TYPE_STR,
            'tradeType'             => Hk_Service_Db::TYPE_STR,
            'userId'                => Hk_Service_Db::TYPE_INT,
            'subTradeId'            => Hk_Service_Db::TYPE_INT,
            'tradeId'               => Hk_Service_Db::TYPE_INT,
            'skuId'                 => Hk_Service_Db::TYPE_INT,
            'courseId'              => Hk_Service_Db::TYPE_INT,
            'tradeTime'             => Hk_Service_Db::TYPE_INT,
            'tradeDate'             => Hk_Service_Db::TYPE_STR,
            'subTradeStatus'        => Hk_Service_Db::TYPE_STR,
            'changeOrderType'       => Hk_Service_Db::TYPE_STR,
            'refundFinishTime'      => Hk_Service_Db::TYPE_INT,
            'refundFinishDate'      => Hk_Service_Db::TYPE_STR,
            'finalSubTradeId'       => Hk_Service_Db::TYPE_STR,
            'changeClassTime'       => Hk_Service_Db::TYPE_INT,
            'changeClassDate'       => Hk_Service_Db::TYPE_STR,
            'originalSubTradeId'    => Hk_Service_Db::TYPE_INT,
            'subTradeCreateTime'    => Hk_Service_Db::TYPE_INT,
            'subTradeCreateDate'    => Hk_Service_Db::TYPE_STR,
            'subTradeUpdateTime'    => Hk_Service_Db::TYPE_INT,
            'subTradeUpdateDate'    => Hk_Service_Db::TYPE_STR,
            'orderChannel'          => Hk_Service_Db::TYPE_INT,
            'sendProvince'          => Hk_Service_Db::TYPE_STR,
            'sendCity'              => Hk_Service_Db::TYPE_STR,
            'cClassType'            => Hk_Service_Db::TYPE_INT,
            'cMainGradeId'          => Hk_Service_Db::TYPE_INT,
            'cMainSubjectId'        => Hk_Service_Db::TYPE_INT,
            'cCourseType'           => Hk_Service_Db::TYPE_INT,
            'cLearnSeason'          => Hk_Service_Db::TYPE_INT,
            'cCourseStartTime'      => Hk_Service_Db::TYPE_INT,
            'cCourseStopTime'       => Hk_Service_Db::TYPE_INT,
            'agentId'               => Hk_Service_Db::TYPE_INT,
            'channel'               => Hk_Service_Db::TYPE_STR,
            'account'               => Hk_Service_Db::TYPE_STR,
            'attrClassType'         => Hk_Service_Db::TYPE_STR,
            'attrLxWay'             => Hk_Service_Db::TYPE_STR,
            'attrSubject'           => Hk_Service_Db::TYPE_STR,
            'attrPageType'          => Hk_Service_Db::TYPE_STR,
            'attrPageName'          => Hk_Service_Db::TYPE_STR,
            'cLearnYear'            => Hk_Service_Db::TYPE_INT,
            'cGradePeriodId'        => Hk_Service_Db::TYPE_INT,
            'cIsJumpGrade'          => Hk_Service_Db::TYPE_STR,
            'cIsRightPrice'         => Hk_Service_Db::TYPE_STR,
            'cIsShorttrain'         => Hk_Service_Db::TYPE_STR,
            'price'                 => Hk_Service_Db::TYPE_INT,
            'payment'               => Hk_Service_Db::TYPE_INT,
            'refundMoney'           => Hk_Service_Db::TYPE_INT,
            'pageId'                => Hk_Service_Db::TYPE_STR,
            'clickId'               => Hk_Service_Db::TYPE_STR,
            'isAd'                  => Hk_Service_Db::TYPE_STR,
            'campaignId'            => Hk_Service_Db::TYPE_INT,
            'adgroupId'             => Hk_Service_Db::TYPE_INT,
            'adId'                  => Hk_Service_Db::TYPE_INT,
            'creativeId'            => Hk_Service_Db::TYPE_INT,
            'tradeDateHour'         => Hk_Service_Db::TYPE_STR,
            'refundFinishDateHour'  => Hk_Service_Db::TYPE_STR,
            'changeClassDateHour'   => Hk_Service_Db::TYPE_STR,
            'subTradeCreateDateHour'=> Hk_Service_Db::TYPE_STR,
            'subTradeUpdateDateHour'=> Hk_Service_Db::TYPE_STR,
        ];
    }

    public function getAllFields() {
        return array_keys($this->arrFieldsMap);
    }

    public function reconnect() {
        if(empty($this->_db)) {
            $this->_db     = Qdlib_Util_DB::getQudaoDb(false);
        }
        Qdlib_Util_DB::reconnect($this->_db);
    }

    public function __get($name) {
        return $this->$name;
    }
}
