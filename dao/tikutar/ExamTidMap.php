<?php
/**
 * @file ExamTidMap.php.
 * <AUTHOR>
 * @date: 2018/5/18
 */
class Hkzb_Dao_TikuTar_ExamTidMap extends Hk_Common_BaseDao
{
    public $arrFields;

    public function __construct()
    {
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_dbName = 'resource/fudao_teachresource';
        $this->_db = Hk_Service_Db::getDB($this->_dbName);
        $this->_table = "tblExamTidMap";
        $this->arrFieldsMap = array(
            'id'         => 'id',
            'tid'        => 'tid',
            'zbTid'      => 'zb_tid',
            'tmpId'      => 'tmp_id',
            'score'      => 'score',
            'rank'       => 'rank',
            'deleted'    => 'deleted',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'ext'        => 'ext',
        );

        $this->arrTypesMap = array(
            'id' => Hk_Service_Db::TYPE_INT,
            'tid' => Hk_Service_Db::TYPE_INT,
            'zbTid' => Hk_Service_Db::TYPE_INT,
            'tmpId' => Hk_Service_Db::TYPE_INT,
            'score' => Hk_Service_Db::TYPE_JSON,
            'rank' => Hk_Service_Db::TYPE_INT,
            'deleted' => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'ext' => Hk_Service_Db::TYPE_JSON,
        );

        $this->arrFields = array_keys($this->arrFieldsMap);
    }
}