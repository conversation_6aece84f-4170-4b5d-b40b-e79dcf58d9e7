<?php
/**************************************************************************
 *
 * Copyright (c) 2020 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   DaoTrait.php
 * <AUTHOR>
 * @date   2021/5/13 16:46
 * @brief
 * @mixin Hk_Common_BaseDao
 **/
trait Qdlib_Dao_Trait_DaoTrait
{
    public function batchInsertRecords($dataList)
    {
        if (!($this instanceof Hk_Common_BaseDao)) {
            $errMsg = 'current class not instanceof Hk_Common_BaseDao';
            Qdlib_Util_Log::warning('qdlib', __CLASS__, __METHOD__, $errMsg);
            return false;
        }
        if (empty($this->_db)) {
            $this->_db = Hk_Service_Db::getDB($this->_dbName, $this->_new, $this->_logFile, $this->_autoRotate);
        }
        if (!is_array($dataList) || empty($dataList)) {
            return false;
        }

        $fields = [];
        $fieldsCnt = 0;
        foreach ($dataList as $k => $v) {
            if (!is_array($v) || empty($v)) {
                return false;
            }
            $v = $this->checkFieldType($v);
            $v = Hk_Service_Db::mapRow($v, $this->arrFieldsMap);
            // 按照key排序
            ksort($v);
            if (!$fields) {
                $fields = array_keys($v);
                sort($fields);
                $fieldsCnt = count($fields);
            } elseif (count($v) != $fieldsCnt) {
                Qdlib_Util_Log::warning('qdlib', __CLASS__, __METHOD__, sprintf("Error:[fieldsCnt error], Detail:[fields:%s,currentRow:%s]", json_encode($fieldsCnt), json_encode($v)));
                return false;
            }
            foreach ($fields as $field){
                if(!array_key_exists($field,$v)){
                    Qdlib_Util_Log::warning('qdlib', __CLASS__, __METHOD__, sprintf("Error:[fields not exist error], Detail:[field:%s,currentRow:%s]", json_encode($field), json_encode($v)));
                    return false;
                }
            }
            foreach ($v as $kk => $vv) {
                if (is_string($vv)) {
                    $v[$kk] = "'" . $this->_db->escapeString($vv) . "'";
                }
            }

            // 字段映射转换
            $dataList[$k] = implode(',', $v);
        }

        // 拼接sql
        $sql = sprintf('INSERT INTO %s(`%s`) VALUES(%s)',$this->_table,implode('`,`',$fields),implode('),(', $dataList));
        $ret = $this->_db->query($sql);
        if ($ret === false) {
            $errMsg = "Error:[multiInsert db failed], Detal:[db:'{$this->_dbName}', errno:'{$this->_db->errno}', " .
                "error:'{$this->_db->error}', lastSQL:'{$this->_db->lastSQL}', lastCost:'{$this->_db->lastCost}', " .
                "isConnected:'" . var_export($this->_db->isConnected, true) . "']";
            Qdlib_Util_Log::warning('qdlib', __CLASS__, __METHOD__, $errMsg);
        }

        return $ret === false ? false : true;
    }

    protected function checkFieldType($arrFields) {
        $arrTypesMap = $this->arrTypesMap;
        if (empty($arrFields) || !is_array($arrFields) || empty($arrTypesMap)) {
            return $arrFields;
        }

        foreach ($arrTypesMap as $field => $type) {
            if (isset($arrFields[$field])) {
                switch ($type) {
                    case Hk_Service_Db::TYPE_INT:
                        $arrFields[$field] = intval($arrFields[$field]);
                        break;
                    case Hk_Service_Db::TYPE_JSON:
                        if (is_array($arrFields[$field])) {
                            $ret = json_encode($arrFields[$field]);
                            if ($ret === false) {
                                Qdlib_Util_Log::warning('qdlib', __CLASS__, __METHOD__,"Error:[json_encode failed], Detail:[field:'{$field}', type:'{$type}', value:'{$arrFields[$field]}']");
                                $ret = '';
                            }
                            $arrFields[$field] = $ret;
                        }
                        if (!is_string($arrFields[$field])) {
                            Qdlib_Util_Log::warning('qdlib', __CLASS__, __METHOD__,"Error:[field type error], Detail:[field:'{$field}' should be json string]");
                        }
                        break;
                    default:
                        break;
                }
            }
        }

        return $arrFields;
    }
}