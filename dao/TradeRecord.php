<?php
/**
 * Copyright (c) 2016 zuoyebang.com, Inc. All Rights Reserved
 * Created by PhpStorm.
 * @file:TradeRecord.php
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @date: 2016/7/1
 * @time: 17:34
 */

class Zhibo_Dao_TradeRecord extends Hk_Common_BaseDao
{
    public static $allFields = array(
        'orderId',
        'studentUid',
        'courseId',
        'grade',
        'subject',
        'teacherUid',
        'assistantUid',
        'payment',
        'status',
        'createTime',
        'updateTime',
        'extData',
        'sendStatus'
    );
    public function __construct()
    {
        $this->_db = Hk_Service_Db::getDB(Zhibo_Static::DBCLUSTER_FUDAO, false, Zhibo_Static::DBLOG_FUDAO, Zhibo_Static::DBLOG_AUTOROTATE_FLAG);
        $this->_table = "tblTradeRecord";

        $this->arrFieldsMap = array(
            'orderId' => 'order_id',
            'studentUid' => 'student_uid',
            'courseId' => 'course_id',
            'grade' => 'grade',
            'subject' => 'subject',
            'teacherUid' => 'teacher_uid',
            'assistantUid' => 'assistant_uid',
            'payment' => 'payment',
            'tradeChannel' => 'trade_channel',
            'tradeCode' => 'trade_code',
            'status' => 'status',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'extData' => 'ext_data',
            'sendStatus' => 'send_status',
        );

        $this->arrTypesMap = array(
            'orderId' => Hk_Service_Db::TYPE_INT,
            'studentUid' => Hk_Service_Db::TYPE_INT,
            'courseId' => Hk_Service_Db::TYPE_INT,
            'grade' => Hk_Service_Db::TYPE_INT,
            'subject' => Hk_Service_Db::TYPE_INT,
            'teacherUid' => Hk_Service_Db::TYPE_INT,
            'assistantUid' => Hk_Service_Db::TYPE_INT,
            'payment' => Hk_Service_Db::TYPE_INT,
            'status' => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'extData' => Hk_Service_Db::TYPE_JSON,
            'sendStatus' => Hk_Service_Db::TYPE_INT,
        );
    }
}