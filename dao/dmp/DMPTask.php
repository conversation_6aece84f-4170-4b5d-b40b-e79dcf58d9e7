<?php
/**
 * Created by Growth AutoCode.
 * User: <EMAIL>
 * Date: 2020/06/15
 * Time: 16:24
 */
class Qdlib_Dao_Dmp_DMPTask extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table = "tblQudaoDMPTask";

        $this->arrFieldsMap = array(
            'id'               => 'id',
            'dmpId'            => 'dmp_id',
            'dt'               => 'dt',
            'channel'          => 'channel',
            'path'             => 'path',
            'account'          => 'account',
            'dataSourceId'     => 'data_source_id',
            'customAudienceId' => 'custom_audience_id',
            'stage'            => 'stage',
            'appendNum'        => 'append_num',
            'reduceNum'        => 'reduce_num',
            'resetNum'         => 'reset_num',
            'oldNum'           => 'old_num',
            'createTime'       => 'create_time',
            'fileCreateTime'   => 'file_create_time',
            'updateTime'       => 'update_time',
            'publishTime'      => 'publish_time',
            'finishTime'       => 'finish_time',
            'forceRun'         => 'force_run',
            'isCreate'         => 'is_create',
            'updateType'       => 'update_type',
            'changeLogId'      => 'change_log_id',
            'fileBaseNum'      => 'file_base_num',
            'uploadNum'        => 'upload_num',
            'coverNum'         => 'cover_num',
            'groupId'          => 'group_id',
            'status'           => 'status',
            'warnMsg'          => 'warn_msg',
            'files'            => 'files',
            'systemStatus'     => 'system_status',

        );

        $this->arrTypesMap = array(
            'id'               => Hk_Service_Db::TYPE_INT,
            'dmpId'            => Hk_Service_Db::TYPE_INT,
            'dt'               => Hk_Service_Db::TYPE_INT,
            'channel'          => Hk_Service_Db::TYPE_STR,
            'path'             => Hk_Service_Db::TYPE_STR,
            'account'          => Hk_Service_Db::TYPE_STR,
            'dataSourceId'     => Hk_Service_Db::TYPE_STR,
            'customAudienceId' => Hk_Service_Db::TYPE_STR,
            'stage'            => Hk_Service_Db::TYPE_INT,
            'appendNum'        => Hk_Service_Db::TYPE_INT,
            'reduceNum'        => Hk_Service_Db::TYPE_INT,
            'resetNum'         => Hk_Service_Db::TYPE_INT,
            'oldNum'           => Hk_Service_Db::TYPE_INT,
            'createTime'       => Hk_Service_Db::TYPE_INT,
            'fileCreateTime'   => Hk_Service_Db::TYPE_INT,
            'updateTime'       => Hk_Service_Db::TYPE_INT,
            'publishTime'      => Hk_Service_Db::TYPE_INT,
            'finishTime'       => Hk_Service_Db::TYPE_INT,
            'forceRun'         => Hk_Service_Db::TYPE_INT,
            'isCreate'         => Hk_Service_Db::TYPE_INT,
            'updateType'       => Hk_Service_Db::TYPE_INT,
            'changeLogId'      => Hk_Service_Db::TYPE_INT,
            'fileBaseNum'      => Hk_Service_Db::TYPE_INT,
            'uploadNum'        => Hk_Service_Db::TYPE_INT,
            'coverNum'         => Hk_Service_Db::TYPE_INT,
            'groupId'          => Hk_Service_Db::TYPE_INT,
            'status'           => Hk_Service_Db::TYPE_INT,
            'warnMsg'          => Hk_Service_Db::TYPE_STR,
            'files'            => Hk_Service_Db::TYPE_STR,
            'systemStatus'     => Hk_Service_Db::TYPE_INT,

        );
    }

    public function reconnect() {
        if (!Zb_Util_Helper::isCli()) {
            return true;
        }
        if(empty($this->_db)) {
            $this->_db     = Qdlib_Util_DB::getQudaoDb(false);
        }
        Qdlib_Util_DB::reconnect($this->_db);
    }
}