<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Date: 2019/6/4
 * Time: 11:16
 */

class Oplib_Dao_CommonActivityAcquire extends Hk_Common_BaseDao{
    // 表字段信息
    private static $arrFields = [
        'id'               => 'id',
        'activityId'       => 'activity_id',
        'pageId'           => 'page_id',
        'uid'              => 'uid',
        'type'             => 'type',
        'ext'              => 'ext',
        'createTime'       => 'create_time',
    ];

    public function __construct()
    {
        //$this->_dbName  = 'revent/zb_operation';
        $this->_dbName  = Oplib_Const_DbConfig::DB_YK_ZBUI;
        $this->_db      = null;
        $this->_table   = 'tblCommonActivityAcquire';
        $this->_logFile = "zhibo-sql/zhibo_sql.log";

        $this->arrFieldsMap = self::$arrFields;

        $this->arrTypesMap = [
            'id'             => Hk_Service_Db::TYPE_INT,
            'activityId'     => Hk_Service_Db::TYPE_INT,
            'pageId'         => Hk_Service_Db::TYPE_INT,
            'uid'            => Hk_Service_Db::TYPE_STR,
            'type'           => Hk_Service_Db::TYPE_STR,
            'ext'            => Hk_Service_Db::TYPE_JSON,
            'createTime'     => Hk_Service_Db::TYPE_INT,
        ];
    }

}
