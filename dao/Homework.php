<?php
/**
 * Copyright (c) 2016 zuoyebang.com, Inc. All Rights Reserved
 * Created by PhpStorm.
 * @file:Homework.php
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @date: 2016/7/1
 * @time: 17:05
 */

class Zhibo_Dao_Homework extends Hk_Common_BaseDao
{

    public function __construct()
    {
        $this->_db = Hk_Service_Db::getDB(Zhibo_Static::DBCLUSTER_FUDAO, false, Zhibo_Static::DBLOG_FUDAO, Zhibo_Static::DBLOG_AUTOROTATE_FLAG);
        $this->_table = "tblHomework";

        $this->arrFieldsMap = array(
            'tid' => 'tid',
            'content' => 'content',
            'gradeId' => 'grade_id',
            'subjectId' => 'subject_id',
            'channel' => 'channel',
            'deleted' => 'deleted',
            'createTime' => 'create_time',
            'extData' => 'ext_data',
        );

        $this->arrTypesMap = array(
            'tid' => Hk_Service_Db::TYPE_INT,
            'content' => Hk_Service_Db::TYPE_JSON,
            'gradeId' => Hk_Service_Db::TYPE_INT,
            'subjectId' => Hk_Service_Db::TYPE_INT,
            'channel' => Hk_Service_Db::TYPE_INT,
            'deleted' => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'extData' => Hk_Service_Db::TYPE_JSON,
        );
    }
}