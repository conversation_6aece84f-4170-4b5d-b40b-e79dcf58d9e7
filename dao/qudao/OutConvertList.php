<?php
/**
 * Created by PhpStorm.
 * User: chenping
 * Date: 2020-03-27
 * Time:
 */

class Qdlib_Dao_Qudao_OutConvertList extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Qdlib_Util_DB::DBLOG_QUDAO;

        $this->_table = "tblQudaoOutConvertList";
        $this->arrFieldsMap = array(
            'id'                        => 'id',
            'channel'                   => 'channel',
            'account'                   => 'account',
            'uniqueId'                  => 'unique_id',
            'convertId'                 => 'convert_id',
            'convertName'               => 'convert_name',
            'configuredStatus'          => 'configured_status',
            'systemStatus'              => 'system_status',
            'convertCreatedTime'        => 'convert_created_time',
            'convertLastModifiedTime'   => 'convert_last_modified_time',
            'reportStatus'              => 'report_status',
            'createTime'                => 'create_time',
            'updateTime'                => 'update_time',
            'ext'                       => 'ext',
        );

        $this->arrTypesMap = array(
            'id'                        => Hk_Service_Db::TYPE_INT,
            'channel'                   => Hk_Service_Db::TYPE_STR,
            'account'                   => Hk_Service_Db::TYPE_STR,
            'uniqueId'                  => Hk_Service_Db::TYPE_STR,
            'convertId'                 => Hk_Service_Db::TYPE_INT,
            'convertName'               => Hk_Service_Db::TYPE_STR,
            'configuredStatus'          => Hk_Service_Db::TYPE_STR,
            'systemStatus'              => Hk_Service_Db::TYPE_STR,
            'convertCreatedTime'        => Hk_Service_Db::TYPE_INT,
            'convertLastModifiedTime'   => Hk_Service_Db::TYPE_INT,
            'reportStatus'              => Hk_Service_Db::TYPE_INT,
            'createTime'                => Hk_Service_Db::TYPE_INT,
            'updateTime'                => Hk_Service_Db::TYPE_INT,
            'ext'                       => Hk_Service_Db::TYPE_JSON,
        );
    }

    public function getAllFields() {
        return array_keys($this->arrFieldsMap);
    }
    public function __get($name)
    {
        return $this->$name;
    }

    public function reconnect() {
        if(empty($this->_db)) {
            $this->_db     = Qdlib_Util_DB::getQudaoDb(false);
        }
        Qdlib_Util_DB::reconnect($this->_db);
    }
}