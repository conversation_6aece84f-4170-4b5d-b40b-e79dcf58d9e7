<?php
/**
 * Created by PhpStorm.
 * User: chenping
 * Date: 2020-03-27
 * Time:
 */

class Qdlib_Dao_Qudao_OutAccountFund extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Qdlib_Util_DB::DBLOG_QUDAO;

        $this->_table = "tblQudaoOutAccountFund";
        $this->arrFieldsMap = array(
            'id'                => 'id',
            'dt'                => 'dt',
            'channel'           => 'channel',
            'account'           => 'account',
            'flag'              => 'flag',
            'uniqueId'          => 'unique_id',
            'reportStatus'      => 'report_status',
            'createTime'        => 'create_time',
            'updateTime'        => 'update_time',
            'ext'               => 'ext',
        );

        $this->arrTypesMap = array(
            'id'                => Hk_Service_Db::TYPE_INT,
            'dt'                => Hk_Service_Db::TYPE_INT,
            'channel'           => Hk_Service_Db::TYPE_STR,
            'account'           => Hk_Service_Db::TYPE_STR,
            'flag'              => Hk_Service_Db::TYPE_STR,
            'uniqueId'          => Hk_Service_Db::TYPE_STR,
            'reportStatus'      => Hk_Service_Db::TYPE_INT,
            'createTime'        => Hk_Service_Db::TYPE_INT,
            'updateTime'        => Hk_Service_Db::TYPE_INT,
            'ext'               => Hk_Service_Db::TYPE_JSON,
        );
    }

    public function getAllFields() {
        return array_keys($this->arrFieldsMap);
    }
    public function __get($name)
    {
        return $this->$name;
    }

    public function reconnect() {
        if(empty($this->_db)) {
            $this->_db     = Qdlib_Util_DB::getQudaoDb(false);
        }
        Qdlib_Util_DB::reconnect($this->_db);
    }
}