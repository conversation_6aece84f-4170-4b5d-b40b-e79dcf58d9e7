<?php


class Qdlib_Dao_Qudao_SuitesQuery extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Qdlib_Util_DB::DBLOG_QUDAO;

        $this->_table = "tblQudaoSuitesQuery";
        $this->arrFieldsMap = array(
            'id'                => 'id',
            'channel'           => 'channel',
            'uniqueId'          => 'unique_id',
            'systemStatus'      => 'system_status',
            'adModifiedTime'    => 'ad_modified_time',
            'adCreatedTime'     => 'ad_created_time',
            'createTime'        => 'create_time',
            'updateTime'        => 'update_time',
            'deleted'           => 'deleted',
            'ext'               => 'ext',
            'reportStatus'      => 'report_status',
        );

        $this->arrTypesMap = array(
            'id'                => Hk_Service_Db::TYPE_INT,
            'channel'           => Hk_Service_Db::TYPE_STR,
            'uniqueId'          => Hk_Service_Db::TYPE_STR,
            'systemStatus'      => Hk_Service_Db::TYPE_STR,
            'adModifiedTime'    => Hk_Service_Db::TYPE_INT,
            'adCreatedTime'     => Hk_Service_Db::TYPE_INT,
            'createTime'        => Hk_Service_Db::TYPE_INT,
            'updateTime'        => Hk_Service_Db::TYPE_INT,
            'deleted'           => Hk_Service_Db::TYPE_INT,
            'ext'               => Hk_Service_Db::TYPE_JSON,
            'reportStatus'      => Hk_Service_Db::TYPE_INT,
        );
    }

    public function getAllFields() {
        return array_keys($this->arrFieldsMap);
    }
    public function __get($name)
    {
        return $this->$name;
    }

    public function reconnect() {
        if(empty($this->_db)) {
            $this->_db     = Qdlib_Util_DB::getQudaoDb(false);
        }
        Qdlib_Util_DB::reconnect($this->_db);
    }
}