<?php


class Qdlib_Dao_Qudao_OutHuaWeiTask extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Qdlib_Util_DB::DBLOG_QUDAO;

        $this->_table = "tblQudaoOutHuaWeiTask";
        $this->arrFieldsMap = array(
            'id'            => 'id',
            'channel'       => 'channel',
            'account'       => 'account',
            'uniqueId'      => 'unique_id',
            'taskId'        => 'task_id',
            'taskName'      => 'task_name',
            'appId'         => 'app_id',
            'appName'       => 'app_name',
            'ext'           => 'ext',
            'createTime'    => 'create_time',
            'updateTime'    => 'update_time',
            'reportStatus'  => 'report_status',
        );

        $this->arrTypesMap = array(
            'id'            => Hk_Service_Db::TYPE_INT,
            'channel'       => Hk_Service_Db::TYPE_STR,
            'account'       => Hk_Service_Db::TYPE_STR,
            'uniqueId'      => Hk_Service_Db::TYPE_STR,
            'taskId'        => Hk_Service_Db::TYPE_INT,
            'taskName'      => Hk_Service_Db::TYPE_STR,
            'appId'         => Hk_Service_Db::TYPE_INT,
            'appName'       => Hk_Service_Db::TYPE_STR,
            'ext'           => Hk_Service_Db::TYPE_JSON,
            'createTime'    => Hk_Service_Db::TYPE_INT,
            'updateTime'    => Hk_Service_Db::TYPE_INT,
            'reportStatus'  => Hk_Service_Db::TYPE_INT,
        );
    }

    public function getAllFields() {
        return array_keys($this->arrFieldsMap);
    }
    public function __get($name)
    {
        return $this->$name;
    }

    public function reconnect() {
        if(empty($this->_db)) {
            $this->_db     = Qdlib_Util_DB::getQudaoDb(false);
        }
        Qdlib_Util_DB::reconnect($this->_db);
    }
}