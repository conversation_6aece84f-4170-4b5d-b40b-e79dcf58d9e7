<?php
/**
 * Created by PhpStorm.
 * User: duxiang
 * Date: 2020-02-16
 * Time: 19:12
 */

class  Qdlib_Dao_Qudao_OutCreative extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Qdlib_Util_DB::DBLOG_QUDAO;

        $this->_table = "tblQudaoOutCreative";

        $this->arrFieldsMap = array(
            'id'                        => 'id',
            'channel'                   => 'channel',
            'account'                   => 'account',
            'campaignId'                => 'campaign_id',
            'creativeId'                => 'creative_id',
            'creativeName'              => 'creative_name',
            'configuredStatus'          => 'configured_status',
            'systemStatus'              => 'system_status',
            'reportStatus'              => 'report_status',
            'creativeCreatedTime'       => 'creative_created_time',
            'creativeLastModifiedTime'  => 'creative_last_modified_time',
            'adId'                      => 'ad_id',
            'createTime'                => 'create_time',
            'updateTime'                => 'update_time',
            'ext'                       => 'ext',
        );

        $this->arrTypesMap = array(
            'id'                        => Hk_Service_Db::TYPE_INT,
            'channel'                   => Hk_Service_Db::TYPE_STR,
            'account'                   => Hk_Service_Db::TYPE_STR,
            'campaignId'                => Hk_Service_Db::TYPE_INT,
            'creativeId'                => Hk_Service_Db::TYPE_INT,
            'creativeName'              => Hk_Service_Db::TYPE_STR,
            'configuredStatus'          => Hk_Service_Db::TYPE_STR,
            'systemStatus'              => Hk_Service_Db::TYPE_STR,
            'reportStatus'              => Hk_Service_Db::TYPE_STR,
            'creativeCreatedTime'       => Hk_Service_Db::TYPE_INT,
            'creativeLastModifiedTime'  => Hk_Service_Db::TYPE_INT,
            'adId'                      => Hk_Service_Db::TYPE_INT,
            'createTime'                => Hk_Service_Db::TYPE_INT,
            'updateTime'                => Hk_Service_Db::TYPE_INT,
            'ext'                       => Hk_Service_Db::TYPE_JSON,
        );
    }

    public function getAllFields() {
        return array_keys($this->arrFieldsMap);
    }

    public function reconnect() {
        if(empty($this->_db)) {
            $this->_db     = Qdlib_Util_DB::getQudaoDb(false);
        }
        Qdlib_Util_DB::reconnect($this->_db);
    }
    public function __get($name)
    {
        return $this->$name;
    }
}