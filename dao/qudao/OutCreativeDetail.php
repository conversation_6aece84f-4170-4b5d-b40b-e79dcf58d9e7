<?php
/**
 * Created by PhpStorm.
 * User: duxiang
 * Date: 2020-02-16
 * Time: 19:12
 */

class  Qdlib_Dao_Qudao_OutCreativeDetail extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Qdlib_Util_DB::DBLOG_QUDAO;

        $this->_table = "tblQudaoOutCreativeDetail";

        $this->arrFieldsMap = array(
            'id'                        => 'id',
            'channel'                   => 'channel',
            'account'                   => 'account',
            'adId'                      => 'ad_id',
            'detailModifyTime'          => 'detail_modify_time',
            'creativeMaterialMode'      => 'creative_material_mode',
            'advancedCreativeType'      => 'advanced_creative_type',
            'proceduralPackageId'       => 'procedural_package_id',
            'reportStatus'              => 'report_status',
            'createTime'                => 'create_time',
            'updateTime'                => 'update_time',
            'ext'                       => 'ext',
        );

        $this->arrTypesMap = array(
            'id'                        => Hk_Service_Db::TYPE_INT,
            'channel'                   => Hk_Service_Db::TYPE_STR,
            'account'                   => Hk_Service_Db::TYPE_STR,
            'adId'                      => Hk_Service_Db::TYPE_INT,
            'detailModifyTime'          => Hk_Service_Db::TYPE_STR,
            'creativeMaterialMode'      => Hk_Service_Db::TYPE_STR,
            'advancedCreativeType'      => Hk_Service_Db::TYPE_STR,
            'proceduralPackageId'       => Hk_Service_Db::TYPE_INT,
            'reportStatus'              => Hk_Service_Db::TYPE_INT,
            'createTime'                => Hk_Service_Db::TYPE_INT,
            'updateTime'                => Hk_Service_Db::TYPE_INT,
            'ext'                       => Hk_Service_Db::TYPE_JSON,
        );
    }

    public function getAllFields() {
        return array_keys($this->arrFieldsMap);
    }

    public function reconnect() {
        if(empty($this->_db)) {
            $this->_db     = Qdlib_Util_DB::getQudaoDb(false);
        }
        Qdlib_Util_DB::reconnect($this->_db);
    }
    public function __get($name)
    {
        return $this->$name;
    }
}