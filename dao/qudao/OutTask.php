<?php
/**
 * Created by PhpStorm.
 * User: chenping
 * Date: 2020-02-17
 */

class Qdlib_Dao_Qudao_OutTask extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Qdlib_Util_DB::DBLOG_QUDAO;

        $this->_table = "tblQudaoOutTask";
        $this->arrFieldsMap = array(
            'id'            => 'id',
            'channel'       => 'channel',
            'taskId'        => 'task_id',
            'actType'       => 'act_type',
            'ids'           => 'ids',
            'taskPartitions'    => 'task_partitions',
            'taskPartition'     => 'task_partition',
            'taskStatus'    => 'task_status',
            'createTime'    => 'create_time',
            'updateTime'    => 'update_time',
            'taskResult'    => 'task_result',
        );

        $this->arrTypesMap = array(
            'id'            => Hk_Service_Db::TYPE_INT,
            'channel'       => Hk_Service_Db::TYPE_STR,
            'taskId'        => Hk_Service_Db::TYPE_INT,
            'actType'       => Hk_Service_Db::TYPE_STR,
            'ids'           => Hk_Service_Db::TYPE_JSON,
            'taskPartitions'    => Hk_Service_Db::TYPE_INT,
            'taskPartition'     => Hk_Service_Db::TYPE_INT,
            'taskStatus'    => Hk_Service_Db::TYPE_INT,
            'createTime'    => Hk_Service_Db::TYPE_INT,
            'updateTime'    => Hk_Service_Db::TYPE_INT,
            'taskResult'    => Hk_Service_Db::TYPE_JSON,
        );
    }

    public function getAllFields() {
        return array_keys($this->arrFieldsMap);
    }

    public function reconnect() {
        if(empty($this->_db)) {
            $this->_db     = Qdlib_Util_DB::getQudaoDb(false);
        }
        Qdlib_Util_DB::reconnect($this->_db);
    }
    public function __get($name)
    {
        return $this->$name;
    }
}