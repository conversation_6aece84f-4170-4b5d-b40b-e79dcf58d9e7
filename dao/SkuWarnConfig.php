<?php

class Qdlib_Dao_SkuWarnConfig extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table = "tblSkuWarnConfig";

        $this->arrFieldsMap = array(
            'id'           => 'id',
            'projectId'    => 'project_id',
            'projectName'  => 'project_name',
            'teamIds'      => 'team_ids',
            'warnInterval' => 'warn_interval',
            'warnConfig'   => 'warn_config',
            'groupId'      => 'group_id',
            'status'       => 'status',
            'reminder'     => 'reminder',
            'createTime'   => 'create_time',
            'updateTime'   => 'update_time',
        );

        $this->arrTypesMap = array(
            'id'           => Hk_Service_Db::TYPE_INT,
            'projectId'    => Hk_Service_Db::TYPE_INT,
            'projectName'  => Hk_Service_Db::TYPE_STR,
            'teamIds'      => Hk_Service_Db::TYPE_STR,
            'warnInterval' => Hk_Service_Db::TYPE_INT,
            'warnConfig'   => Hk_Service_Db::TYPE_STR,
            'groupId'      => Hk_Service_Db::TYPE_INT,
            'status'       => Hk_Service_Db::TYPE_INT,
            'reminder'     => Hk_Service_Db::TYPE_STR,
            'createTime'   => Hk_Service_Db::TYPE_INT,
            'updateTime'   => Hk_Service_Db::TYPE_INT,
        );
    }
}