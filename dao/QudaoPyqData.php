<?php

class Qdlib_Dao_QudaoPyqData extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Hkzb_Util_FuDao::DBLOG_FUDAO;

        $this->_table = "tblQudaoPyqData";
        $this->arrFieldsMap = array(
            'id'                    => 'id',
            'agencyAccountId'     => 'agency_account_id',
            'account'              => 'account',
            'wechatCampaignId'    => 'wechat_campaign_id',
            'thirdAdId'            => 'third_ad_id',
            'adgroupId'            => 'adgroup_id',
            'dt'                    => 'dt',
            'hour'                  => 'hour',
            'orifrom'               => 'orifrom',
            'pageUrl'               => 'page_url',
            'showPv'                => 'show_pv',
            'showUv'                => 'show_uv',
            'clickPv'               => 'click_pv',
            'clickUv'               => 'click_uv',
            'cost'                  => 'cost',
            'download'              => 'download',
            'conversion'            => 'conversion',
            'activation'            => 'activation',
            'createTime'            => 'create_time',
            'updateTime'            => 'update_time',
            'systemStatus'          => 'system_status',
            'dataSource'            => 'data_source',
            'extData'               => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'                    => Hk_Service_Db::TYPE_INT,
            'agencyAccountId'     => Hk_Service_Db::TYPE_STR,
            'account'              => Hk_Service_Db::TYPE_STR,
            'wechatCampaignId'    => Hk_Service_Db::TYPE_INT,
            'thirdAdId'            => Hk_Service_Db::TYPE_INT,
            'adgroupId'            => Hk_Service_Db::TYPE_INT,
            'dt'                    => Hk_Service_Db::TYPE_INT,
            'hour'                  => Hk_Service_Db::TYPE_INT,
            'orifrom'               => Hk_Service_Db::TYPE_STR,
            'pageUrl'               => Hk_Service_Db::TYPE_STR,
            'showPv'                => Hk_Service_Db::TYPE_INT,
            'showUv'                => Hk_Service_Db::TYPE_INT,
            'clickPv'               => Hk_Service_Db::TYPE_INT,
            'clickUv'               => Hk_Service_Db::TYPE_INT,
            'cost'                   => Hk_Service_Db::TYPE_INT,
            'download'              => Hk_Service_Db::TYPE_INT,
            'conversion'            => Hk_Service_Db::TYPE_INT,
            'activation'            => Hk_Service_Db::TYPE_INT,
            'createTime'            => Hk_Service_Db::TYPE_INT,
            'updateTime'            => Hk_Service_Db::TYPE_INT,
            'systemStatus'          => Hk_Service_Db::TYPE_INT,
            'dataSource'            => Hk_Service_Db::TYPE_STR,
            'extData'               => Hk_Service_Db::TYPE_JSON,
        );
    }

    public function getAllFields() {
        return array_keys($this->arrFieldsMap);
    }
    public function reconnect() {
        if(empty($this->_db)) {
            $this->_db     = Qdlib_Util_DB::getQudaoDb(false);
        }
        Qdlib_Util_DB::reconnect($this->_db);
    }
}