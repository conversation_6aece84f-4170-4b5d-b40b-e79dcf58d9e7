<?php

class Qdlib_Dao_SeriesSkuRelations extends Hk_Common_BaseDao
{
    public function __construct()
    {
        parent::__construct();
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table = "tblSeriesSkuRelations";
        $this->arrFieldsMap = [
            'id' => 'id',
            'seriesId' => 'series_id',
            'name' => 'name',
            'channelId' => 'channel_id',
            'thirdSkuId' => 'third_sku_id',
            'lastfrom' => 'lastfrom',
            'startTime' => 'start_time',
            'stopTime' => 'stop_time',
            'price' => 'price',
            'quantity' => 'quantity',
            'validQuantity' => 'valid_quantity',
            'status' => 'status',
            'batchStatus' => 'batch_status',
            'exportStatus' => 'export_status',
            'operator' => 'operator',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
        ];
        $this->arrTypesMap = [
            'id' => Hk_Service_Db::TYPE_INT,
            'seriesId' => Hk_Service_Db::TYPE_INT,
            'name' => Hk_Service_Db::TYPE_STR,
            'channelId' => Hk_Service_Db::TYPE_INT,
            'thirdSkuId' => Hk_Service_Db::TYPE_STR,
            'lastfrom' => Hk_Service_Db::TYPE_STR,
            'startTime' => Hk_Service_Db::TYPE_INT,
            'stopTime' => Hk_Service_Db::TYPE_INT,
            'price' => Hk_Service_Db::TYPE_INT,
            'quantity' => Hk_Service_Db::TYPE_INT,
            'validQuantity' => Hk_Service_Db::TYPE_INT,
            'status' => Hk_Service_Db::TYPE_INT,
            'batchStatus' => Hk_Service_Db::TYPE_INT,
            'exportStatus' => Hk_Service_Db::TYPE_INT,
            'operator' => Hk_Service_Db::TYPE_STR,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
        ];
    }
}