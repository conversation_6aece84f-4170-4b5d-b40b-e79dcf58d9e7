<?php

/**
* @file: GuoyuanRefundTradeCode.php
* @desc: 直播退款汇总
* @date: 2018年5月3日 下午5:39:20
* @author: <PERSON><PERSON><PERSON><PERSON><PERSON> <lian<PERSON><PERSON><PERSON>@zuoyebang.com>
*/

class Hkzb_Dao_Gnmis_GuoyuanRefundTradeCode extends Hk_Common_BaseDao
{
    public static $allFields = array(
        'refundTradeId','orderUid','orderId','source','packOrderIds','packChargeIds',
        'updateTime','createTime','refundFee','status','deleted','extData','requestNo','refundRecordId'
    );
    
    public function __construct(){
        $this->_dbName      = 'gnmis/gnmis';
        $this->_db          = Hk_Service_Db::getDB($this->_dbName,false,"gnmis-sql/gnmis_sql.log");
        $this->_table       = 'tblGuoyuanRefundTradeCode';
        $this->arrFieldsMap = array(
            'refundTradeId'     => 'refund_trade_id',
            'orderUid'          => 'order_uid',
            'orderId'           => 'order_id',
            'source'            => 'source',
            'packOrderIds'      => 'pack_order_ids',
            'packChargeIds'     => 'pack_charge_ids',
            'updateTime'        => 'update_time',
            'createTime'        => 'create_time',
            'refundFee'         => 'refund_fee',
            'status'            => 'status',
            'deleted'           => 'deleted',
            'extData'           => 'ext_data',
            'requestNo'         => 'request_no',
            'refundRecordId'    => 'refund_record_id',
        );
        
        $this->arrTypesMap = array(
            'refundTradeId'         => Hk_Service_Db::TYPE_INT,
            'orderUid'              => Hk_Service_Db::TYPE_INT,
            'orderId'               => Hk_Service_Db::TYPE_INT,
            'source'                => Hk_Service_Db::TYPE_STR,
            'packOrderIds'          => Hk_Service_Db::TYPE_JSON,
            'packChargeIds'         => Hk_Service_Db::TYPE_JSON,
            'updateTime'            => Hk_Service_Db::TYPE_INT,
            'createTime'            => Hk_Service_Db::TYPE_INT,
            'refundFee'             => Hk_Service_Db::TYPE_INT,
            'status'                => Hk_Service_Db::TYPE_INT,
            'deleted'               => Hk_Service_Db::TYPE_INT,
            'extData'               => Hk_Service_Db::TYPE_JSON,
            'requestNo'             => Hk_Service_Db::TYPE_INT,
            'refundRecordId'        => Hk_Service_Db::TYPE_INT,
        );
    }
}