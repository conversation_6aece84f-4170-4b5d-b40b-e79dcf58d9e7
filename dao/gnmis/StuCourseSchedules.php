<?php

/**
 * @file: StuCourseSchedules.php
 * @author: <EMAIL>
 * @date: 2017-11-09
 * @brief：销售配课关系表
 */
class Hkzb_Dao_Gnmis_StuCourseSchedules extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName      = 'gnmis/gnmis';
        $this->_db          = Hk_Service_Db::getDB($this->_dbName, false, 'gnmis-sql/gnmis_sql.log');;
        $this->_table       = 'tblStuCourseSchedules';
        $this->_logFile     = 'gnmis-sql/gnmis_sql.log';
        $this->arrFieldsMap = [
            'id'           => 'id',
            'courseId'     => 'course_id',
            'necessary'    => 'necessary',
            'createTime'   => 'create_time',
            'updateTime'   => 'update_time',
            'deleted'      => 'deleted',
            'scIds'        => 'sc_ids',
            'extBit'       => 'ext_bit',
        ];

        $this->arrTypesMap  = [
            'id'           => Hk_Service_Db::TYPE_INT,
            'courseId'     => Hk_Service_Db::TYPE_INT,
            'necessary'    => Hk_Service_Db::TYPE_INT,
            'createTime'   => Hk_Service_Db::TYPE_INT,
            'updateTime'   => Hk_Service_Db::TYPE_INT,
            'deleted'      => Hk_Service_Db::TYPE_INT,
            'scIds'        => Hk_Service_Db::TYPE_JSON,
            'extBit'       => Hk_Service_Db::TYPE_INT,
        ];
    }
}
