<?php

/**
 * @file   GuoyuanWorkflowOperation.php
 * <AUTHOR>
 * @date   2016-07-23
 * @brief  果园工作流操作记录
 *
 **/
class Hkzb_Dao_Gnmis_GuoyuanWorkflowOperation extends Hk_Common_BaseDao
{
    public static $allFields = array(
        'id', 'workflowId', 'createUser', 'createTime', 'opType', 'deleted', 'abstract', 'extBit', 'extData'
    );

    public function __construct() {
        $this->_dbName      = 'gnmis/gnmis';
        $this->_db          = Hk_Service_Db::getDB($this->_dbName,false,"gnmis-sql/gnmis_sql.log");
        $this->_table       = "tblGuoyuanWorkflowOperation";
        $this->arrFieldsMap = array(
            'id'         => 'id',
            'workflowId' => 'workflow_id',
            'createUser' => 'create_user',
            'createTime' => 'create_time',
            'opType'     => 'op_type',
            'deleted'    => 'deleted',
            'abstract'   => 'abstract',
            'extBit'     => 'ext_bit',
            'extData'    => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'         => Hk_Service_Db::TYPE_INT,
            'workflowId' => Hk_Service_Db::TYPE_INT,
            'createUser' => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'opType'     => Hk_Service_Db::TYPE_INT,
            'deleted'    => Hk_Service_Db::TYPE_INT,
            'abstract'   => Hk_Service_Db::TYPE_STR,
            'extBit'     => Hk_Service_Db::TYPE_INT,
            'extData'    => Hk_Service_Db::TYPE_JSON,
        );
    }
}
