<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Time: 2017/7/27 17:27
 */

class Zhibo_Dao_LmsLessonLecture extends Hk_Common_BaseDao
{

    public function __construct() {
        $this->_dbName = 'fudao/zyb_fudao';
        $this->_db     = null;
        $this->_table = "tblLmsLessonLecture";
        $this->arrFieldsMap = array(
            'id'        => 'id',
            'lectureId' => 'lecture_id',
            'typeId'    => 'type_id',
            'type'      => 'type',
            'deleted'   => 'deleted',
        );

        $this->arrTypesMap = array(
            'id'        => Hk_Service_Db::TYPE_INT,
            'lectureId' => Hk_Service_Db::TYPE_INT,
            'typeId'    => Hk_Service_Db::TYPE_INT,
            'type'      => Hk_Service_Db::TYPE_INT,
            'deleted'   => Hk_Service_Db::TYPE_INT,
        );
    }
}