<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * Created by PhpStorm.
 * File : ActTemplate.php
 * Author: <EMAIL>
 * Date: 2019/4/19
 * Time: 11:05
 * Desc: 活动模板dao层
 */
class Oplib_Dao_PageTemplate extends Hk_Common_BaseDao
{

    // 表字段信息
    private static $arrFields = array(
        'id'            => 'id',
        'name'          => 'name',
        'actType'       => 'act_type',
        'pid'           => 'pid',
        'templateData'  => 'template_data',
        'operatorUid'   => 'operator_uid',
        'operator'      => 'operator',
        'status'        => 'status',
        'createTime'    => 'create_time',
        'updateTime'    => 'update_time',
        'ext'           => 'ext',
    );

    /**
     * 构造方法
     * <AUTHOR>
     * @DateTime 2018-08-21
     */
    public function __construct()
    {
        $this->_dbName  = 'zyb_yk_actplat/zhibo_buildtool';
        $this->_db      = null;
        $this->_table   = 'tblPageTemplate';
        $this->_logFile = Hkzb_Util_FuDao::DBLOG_FUDAO;

        $this->arrFieldsMap = self::$arrFields;

        $this->arrTypesMap = [
            'id'            => Hk_Service_Db::TYPE_INT,
            'name'          => Hk_Service_Db::TYPE_STR,
            'actType'       => Hk_Service_Db::TYPE_INT,
            'pid'           => Hk_Service_Db::TYPE_STR,
            'templateData'  => Hk_Service_Db::TYPE_JSON,
            'status'        => Hk_Service_Db::TYPE_INT,
            'operatorUid'   => Hk_Service_Db::TYPE_INT,
            'operator'      => Hk_Service_Db::TYPE_STR,
            'createTime'    => Hk_Service_Db::TYPE_INT,
            'updateTime'    => Hk_Service_Db::TYPE_INT,
            'ext'           => Hk_Service_Db::TYPE_JSON,
        ];
    }

    /**
     * 获取表的字段信息
     * <AUTHOR>
     * @DateTime 2018-08-22
     * @return   array
     */
    public static function getAllFields()
    {
        return array_keys(self::$arrFields);
    }
}
