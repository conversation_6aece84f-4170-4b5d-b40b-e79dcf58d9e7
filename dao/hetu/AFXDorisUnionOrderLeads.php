<?php
/**
 * <AUTHOR>
 * @link http://dataworks.zuoyebang.cc/#/data-table/view-show?env=toufangv2&database=zhibo_laxin&table=dwd_fenxiao_afx_unionorder_v5
 * @link http://dataworks.zuoyebang.cc/#/data-table/view-show?env=toufangv2&database=zhibo_laxin&table=dwd_fenxiao_afx_unionorder_v6
 * @brief 分销订单视图表
 */

class Qdlib_Dao_Hetu_AFXDorisUnionOrderLeads extends Qdlib_Dao_Hetu_Doris
{
    const ARR_ALL_FIELDS = [
        'orderId',
        'skuOrderId',
        'leadsId',
        'isValidLeads',
    ];

    public function __construct()
    {
        if (Qdlib_Util_Tool::getEnviron() == 'test') {
            $this->_table = 'dwd_fenxiao_afx_unionorder_leads';
        } else {
            $this->_table = 'zhibo_laxin.dwd_fenxiao_afx_unionorder_leads';
        }
        $this->arrFieldsMap = array(
            'orderId'      => 'order_id',
            'skuOrderId'   => 'sku_order_id',
            'leadsId'      => 'leads_id',
            'isValidLeads' => 'is_valid_leads', //是否有效例子,1是,0否
        );

        $this->arrTypesMap = array(
            'orderId'      => Hk_Service_Db::TYPE_INT,
            'skuOrderId'   => Hk_Service_Db::TYPE_INT,
            'leadsId'      =>  Hk_Service_Db::TYPE_INT,
            'isValidLeads' => Hk_Service_Db::TYPE_INT,
        );
    }
}
