<?php

class Qdlib_Dao_Hetu_AFXActInstToggle extends Qdlib_Dao_Hetu_Hetu
{
    public function __construct()
    {
        parent::__construct();
        $this->_table = 'tblAFXActInstToggle';
        $this->arrFieldsMap = [
            'id' => 'id',
            'appId' => 'app_id',
            'actId' => 'act_id',
            'type' => 'type',
            'lastId' => 'last_id',
            'instIds' => 'inst_ids',
            'lastInstIds' => 'last_inst_ids',
            'currentInstIds' => 'current_inst_ids',
            'successInstIds' => 'success_inst_ids',
            'status' => 'status',
            'operator' => 'operator',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
        ];
        $this->arrTypesMap = [
            'id' => Hk_Service_Db::TYPE_INT,
            'appId' => Hk_Service_Db::TYPE_INT,
            'actId' => Hk_Service_Db::TYPE_INT,
            'type' => Hk_Service_Db::TYPE_INT,
            'lastId' => Hk_Service_Db::TYPE_INT,
            'instIds' => Hk_Service_Db::TYPE_STR,
            'lastInstIds' => Hk_Service_Db::TYPE_STR,
            'currentInstIds' => Hk_Service_Db::TYPE_STR,
            'successInstIds' => Hk_Service_Db::TYPE_STR,
            'status' => Hk_Service_Db::TYPE_INT,
            'operator' => Hk_Service_Db::TYPE_STR,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
        ];
    }
}