<?php
/**
 * Created by Growth AutoCode.
 * User: zhangchangguo<PERSON>@zuoyebang.com
 * Date: 2021/07/26
 * Time: 20:44
 */
class Qdlib_Dao_Hetu_CouponCodeTask extends Qdlib_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_HETU;
        $this->_db = null;
        $this->_logFile = Qdlib_Util_DB::DBLOG_QUDAO;
        $this->_table = "tblCouponCodeTask";

        $this->arrFieldsMap = array(
            'recordId'    => 'record_id',
            'recordDesc'  => 'record_desc',
            'applyNum'  => 'apply_num',
            'createName'  => 'create_name',
            'createTime'  => 'create_time',
            'processStep' => 'process_step',
            'itemId'      => 'item_id',
            'discount'  => 'discount',
            'itemDeleted' => 'item_deleted',

        );

        $this->arrTypesMap = array(
            'recordId'    => Hk_Service_Db::TYPE_INT,
            'recordDesc'  => Hk_Service_Db::TYPE_STR,
            'applyNum'  => Hk_Service_Db::TYPE_INT,
            'createName'  => Hk_Service_Db::TYPE_STR,
            'createTime'  => Hk_Service_Db::TYPE_INT,
            'processStep' => Hk_Service_Db::TYPE_STR,
            'itemId'      => Hk_Service_Db::TYPE_INT,
            'discount' => Hk_Service_Db::TYPE_INT,
            'itemDeleted' => Hk_Service_Db::TYPE_INT,

        );
     }
}