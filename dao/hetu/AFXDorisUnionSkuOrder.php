<?php

/**
 * <AUTHOR>
 * @link http://dataworks.zuoyebang.cc/#/data-table/table-show?env=toufangv2&database=zhibo_laxin&table=ods_fenxiao_afx_unionskuorder_v5
 * @brief 分销子订表
 */
class Qdlib_Dao_Hetu_AFXDorisUnionSkuOrder extends Qdlib_Dao_Hetu_Doris
{
    const ARR_ALL_FIELDS = [
        'id',
        'userId',
        'orderId',
        'skuOrderId',
        'shopId',
        'skuId',
        'productType',
        'status',
        'finalStatus',
        'sellTotal',
        'payTotal',
        'addressInfo',
        'changeOrderType',
        'originalOrderId',
        'originalSkuOrderId',
        'originalSkuId',
        'changeTime',
        'changeFromOrderId',
        'changeToOrderId',
        'changeFromSkuOrderId',
        'changeToSkuOrderId',
    ];

    public function __construct()
    {
        if (Qdlib_Util_Tool::getEnviron() == 'test') {
            $this->_table = 'ods_fenxiao_afx_unionskuorder_v5';
        } else {
            $this->_table = 'zhibo_laxin.ods_fenxiao_afx_unionskuorder_v5';
        }

        $this->arrFieldsMap = [
            'id' => 'id',
            'userId' => 'user_id',
            'orderId' => 'order_id',
            'skuOrderId' => 'sku_order_id',
            'shopId' => 'shop_id',
            'skuId' => 'sku_id',
            'productType' => 'product_type',
            'status' => 'status',
            'finalStatus' => 'final_status',
            'sellTotal' => 'sell_total',
            'payTotal' => 'pay_total',
            'addressInfo' => 'address_info',
            'changeOrderType' => 'change_order_type',
            'originalOrderId' => 'original_order_id',
            'originalSkuOrderId' => 'original_sku_order_id',
            'originalSkuId' => 'original_sku_id',
            'changeTime' => 'change_time',
            'changeFromOrderId' => 'change_from_order_id',
            'changeToOrderId' => 'change_to_order_id',
            'changeFromSkuOrderId' => 'change_from_sku_order_id',
            'changeToSkuOrderId' => 'change_to_sku_order_id',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'deleted' => 'deleted',
            'ext' => 'ext',
        ];
        $this->arrTypesMap = [
            'id' => Hk_Service_Db::TYPE_INT,
            'userId' => Hk_Service_Db::TYPE_INT,
            'orderId' => Hk_Service_Db::TYPE_INT,
            'skuOrderId' => Hk_Service_Db::TYPE_INT,
            'shopId' => Hk_Service_Db::TYPE_INT,
            'skuId' => Hk_Service_Db::TYPE_INT,
            'productType' => Hk_Service_Db::TYPE_INT,
            'status' => Hk_Service_Db::TYPE_INT,
            'finalStatus' => Hk_Service_Db::TYPE_INT,
            'sellTotal' => Hk_Service_Db::TYPE_INT,
            'payTotal' => Hk_Service_Db::TYPE_INT,
            'addressInfo' => Hk_Service_Db::TYPE_STR,
            'changeOrderType' => Hk_Service_Db::TYPE_INT,
            'originalOrderId' => Hk_Service_Db::TYPE_INT,
            'originalSkuOrderId' => Hk_Service_Db::TYPE_INT,
            'originalSkuId' => Hk_Service_Db::TYPE_INT,
            'changeTime' => Hk_Service_Db::TYPE_INT,
            'changeFromOrderId' => Hk_Service_Db::TYPE_INT,
            'changeToOrderId' => Hk_Service_Db::TYPE_INT,
            'changeFromSkuOrderId' => Hk_Service_Db::TYPE_INT,
            'changeToSkuOrderId' => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'deleted' => Hk_Service_Db::TYPE_INT,
            'ext' => Hk_Service_Db::TYPE_JSON,
        ];
    }
}