<?php

class Qdlib_Dao_Hetu_AFXUnionOrder extends Qdlib_Dao_Hetu_Hetu
{
    public function __construct()
    {
        parent::__construct();
        $this->_table = 'tblAFXUnionOrder';
        $this->arrFieldsMap = [
            'id' => 'id',
            'source' => 'source',
            'lastfrom' => 'lastfrom',
            'shareCode' => 'share_code',
            'actId' => 'act_id',
            'userId' => 'user_id',
            'orderId' => 'order_id',
            'shopId' => 'shop_id',
            'domain' => 'domain',
            'status' => 'status',
            'afterType' => 'after_type',
            'finalStatus' => 'final_status',
            'orderTime' => 'order_time',
            'payTime' => 'pay_time',
            'agentId' => 'agent_id',
            'agentUid' => 'agent_uid',
            'appId' => 'app_id',
            'cadre' => 'cadre',
            'payableAmount' => 'payable_amount',
            'businessLine' => 'business_line',
            'projectLv1' => 'project_lv1',
            'projectLv2' => 'project_lv2',
            'cdkey' => 'cdkey',
            'cdkeyAmount' => 'cdkey_amount',
            'thirdOrderInfo' => 'third_order_info',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'deleted' => 'deleted',
            'ext' => 'ext',
            'userDescExt' => 'user_desc_ext', // 用户信息,以工作台为准
        ];
        $this->arrTypesMap = [
            'id' => Hk_Service_Db::TYPE_INT,
            'source' => Hk_Service_Db::TYPE_INT,
            'lastfrom' => Hk_Service_Db::TYPE_STR,
            'shareCode' => Hk_Service_Db::TYPE_STR,
            'actId' => Hk_Service_Db::TYPE_INT,
            'userId' => Hk_Service_Db::TYPE_INT,
            'orderId' => Hk_Service_Db::TYPE_INT,
            'shopId' => Hk_Service_Db::TYPE_INT,
            'domain' => Hk_Service_Db::TYPE_INT,
            'status' => Hk_Service_Db::TYPE_INT,
            'afterType' => Hk_Service_Db::TYPE_STR,
            'finalStatus' => Hk_Service_Db::TYPE_INT,
            'orderTime' => Hk_Service_Db::TYPE_INT,
            'payTime' => Hk_Service_Db::TYPE_INT,
            'agentId' => Hk_Service_Db::TYPE_INT,
            'agentUid' => Hk_Service_Db::TYPE_INT,
            'appId' => Hk_Service_Db::TYPE_INT,
            'cadre' => Hk_Service_Db::TYPE_STR,
            'payableAmount' => Hk_Service_Db::TYPE_INT,
            'businessLine' => Hk_Service_Db::TYPE_INT,
            'projectLv1' => Hk_Service_Db::TYPE_INT,
            'projectLv2' => Hk_Service_Db::TYPE_INT,
            'cdkey' => Hk_Service_Db::TYPE_STR,
            'cdkeyAmount' => Hk_Service_Db::TYPE_INT,
            'thirdOrderInfo' => Hk_Service_Db::TYPE_JSON,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'deleted' => Hk_Service_Db::TYPE_INT,
            'ext' => Hk_Service_Db::TYPE_JSON,
            'userDescExt' => Hk_Service_Db::TYPE_STR,
        ];
    }
}