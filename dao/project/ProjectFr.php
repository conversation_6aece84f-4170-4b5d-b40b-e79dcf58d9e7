<?php
class Qdlib_Dao_Project_ProjectFr extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table = "tblQudaoProjectFr";

        $this->arrFieldsMap = array(
            'id'               => 'id',
            'projectLv2'       => 'project_lv2',
            'business'         => 'business',
            'channel'          => 'channel',
            'putForm'          => 'put_form',
            'frPrefix'         => 'frPrefix',
            'createTime'       => 'create_time',
            'updateTime'       => 'update_time',
            'operator'         => 'operator',
            'deleted'          => 'deleted',
            'extData'          => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'              => Hk_Service_Db::TYPE_INT,
            'projectLv2'      => Hk_Service_Db::TYPE_INT,
            'business'        => Hk_Service_Db::TYPE_STR,
            'channel'         => Hk_Service_Db::TYPE_STR,
            'faculty'         => Hk_Service_Db::TYPE_STR,
            'putForm'         => Hk_Service_Db::TYPE_STR,
            'frPrefix'        => Hk_Service_Db::TYPE_STR,
            'createTime'      => Hk_Service_Db::TYPE_INT,
            'updateTime'      => Hk_Service_Db::TYPE_INT,
            'deleted'         => Hk_Service_Db::TYPE_INT,
            'operator'        => Hk_Service_Db::TYPE_STR,
            'extData'         => Hk_Service_Db::TYPE_JSON,

        );
    }

    public function getAllFields()
    {
        return array_keys($this->arrFieldsMap);
    }

    public function reconnect() {
        if (!Zb_Util_Helper::isCli()) {
            return true;
        }
        if(empty($this->_db)) {
            $this->_db     = Qdlib_Util_DB::getQudaoDb(false);
        }
        Qdlib_Util_DB::reconnect($this->_db);
    }
}