<?php

/**
 * Created by Growth AutoCode.
 * User: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * Date: 2020/09/23
 * Time: 16:19
 */
class Qdlib_Dao_SkuRcdConf extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table = "tblSkuRcdConf";

        $this->arrFieldsMap = array(
            'id'              => 'id',
            'uniqid'          => 'uniqid',
            'type'            => 'type',
            'apiName'         => 'api_name',
            'selectFilter'    => 'select_filter',
            'isAb'            => 'is_ab',
            'interfaceStatus' => 'interface_status',
            'compileStatus'   => 'compile_status',
            'projectId'       => 'project_id',
            'editUid'         => 'edit_uid',
            'editName'        => 'edit_name',
            'editTime'        => 'edit_time',
            'createTime'      => 'create_time',
            'updateTime'      => 'update_time',
            'saleChannel'     => 'sale_channel',
            'createName'      => 'create_name',
            'createUid'       => 'create_uid',
            'interfaceAttr'   => 'interface_attr',
        );

        $this->arrTypesMap = array(
            'id'              => Hk_Service_Db::TYPE_INT,
            'uniqid'          => Hk_Service_Db::TYPE_STR,
            'type'            => Hk_Service_Db::TYPE_INT,
            'apiName'         => Hk_Service_Db::TYPE_STR,
            'selectFilter'    => Hk_Service_Db::TYPE_JSON,
            'isAb'            => Hk_Service_Db::TYPE_INT,
            'interfaceStatus' => Hk_Service_Db::TYPE_INT,
            'compileStatus'   => Hk_Service_Db::TYPE_INT,
            'projectId'       => Hk_Service_Db::TYPE_INT,
            'editUid'         => Hk_Service_Db::TYPE_STR,
            'editName'        => Hk_Service_Db::TYPE_STR,
            'editTime'        => Hk_Service_Db::TYPE_INT,
            'createTime'      => Hk_Service_Db::TYPE_INT,
            'updateTime'      => Hk_Service_Db::TYPE_INT,
            'saleChannel'     => Hk_Service_Db::TYPE_JSON,
            'createName'      => Hk_Service_Db::TYPE_STR,
            'createUid'       => Hk_Service_Db::TYPE_STR,
            'interfaceAttr'   => Hk_Service_Db::TYPE_INT,
        );
    }

    public function selectFilterFieldToZh($oldRow, $newRow)
    {
        Qdlib_Util_Log::addNotice('oldRow', json_encode($oldRow));
        Qdlib_Util_Log::addNotice('newRow', json_encode($newRow));
        $newRow = is_string($newRow) ? json_decode($newRow, true) : $newRow;
        $selectFilters = [
            'gradeList'   => '年级',
            'seasonList'  => '学期',
            'subject'     => '学科',
            "isAllGrade"  => "全部年级",
            "isAllSeason" => "全部学期",
            "tagList"     => "标签列表",

        ];
        return [
            'before' => self::rowToZh($oldRow, $selectFilters, 'before'),
            'after'  => self::rowToZh($newRow, $selectFilters, 'after')
        ];

    }

    private static function rowToZh($row, $selectFilters, $type)
    {
        $version = $row['version'] ?? 0;
        Qdlib_Util_Log::addNotice("{$type}version", $version);

        $rowZh = [];
        if ($version == 0) {
            foreach ($row as $i => $item) {
                foreach ($item as $key => $value) {
                    if (!empty($value) && isset($selectFilters[$key])) {
                        $rowZh[$i][$selectFilters[$key]] = $value;

                    }
                }

            }

        } elseif ($version == 1) {
            foreach ($row as $key => $value) {
                if (!empty($value) && isset($selectFilters[$key])) {
                    $rowZh[0][$selectFilters[$key]] = $value;

                }
            }
        }
        Qdlib_Util_Log::addNotice("{$type}rowZh", json_encode($rowZh));
        return $rowZh;
    }
}