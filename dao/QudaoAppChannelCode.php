<?php

class Qdlib_Dao_QudaoAppChannelCode extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Qdlib_Util_DB::DBLOG_QUDAO;
        $this->_table = "tblQudaoAppChannelCode";

        $this->arrFieldsMap = array(
            'id' => 'id',
            'channelCode' => 'channel_code',
            'channel' => 'channel',
            'channelOne' => 'channel_one',
            'channelTwo' => 'channel_two',
            'channelThree' => 'channel_three',
            'channelLv1Id' => 'channel_lv1_id',
            'channelLv2Id' => 'channel_lv2_id',
            'channelLv3Id' => 'channel_lv3_id',
            'channelOneName' => 'channel_one_name',
            'channelTwoName' => 'channel_two_name',
            'channelThreeName' => 'channel_three_name',
            'business' => 'business',
            'os' => 'os',
            'agenter' => 'agenter',
//            'agentId'             => 'agent_id',
            'app' => 'app',
            'appId' => 'app_id',
            'account' => 'account',
            'accountName' => 'account_name',
            'extra' => 'extra',
            'applicant' => 'applicant',
            'applicantionTime' => 'applicantion_time',
            'appDownloadUrl' => 'app_download_url',
            'zmtcStatus' => 'zmtc_status',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'deleted' => 'deleted',
        );

        $this->arrTypesMap = array(
            'id' => Hk_Service_Db::TYPE_INT,
            'channelCode' => Hk_Service_Db::TYPE_STR,
            'channel' => Hk_Service_Db::TYPE_STR,
            'channelOne' => Hk_Service_Db::TYPE_STR,
            'channelTwo' => Hk_Service_Db::TYPE_STR,
            'channelThree' => Hk_Service_Db::TYPE_STR,
            'channelOneName' => Hk_Service_Db::TYPE_STR,
            'channelTwoName' => Hk_Service_Db::TYPE_STR,
            'channelLv1Id' => Hk_Service_Db::TYPE_INT,
            'channelLv2Id' => Hk_Service_Db::TYPE_INT,
            'channelLv3Id' => Hk_Service_Db::TYPE_INT,
            'channelThreeName' => Hk_Service_Db::TYPE_STR,
            'business' => Hk_Service_Db::TYPE_STR,
            'os' => Hk_Service_Db::TYPE_STR,
            'agenter' => Hk_Service_Db::TYPE_STR,
            'agentId' => Hk_Service_Db::TYPE_INT,
            'app' => Hk_Service_Db::TYPE_STR,
            'appId' => Hk_Service_Db::TYPE_STR,
            'account' => Hk_Service_Db::TYPE_STR,
            'accountName' => Hk_Service_Db::TYPE_STR,
            'extra' => Hk_Service_Db::TYPE_JSON,
            'applicant' => Hk_Service_Db::TYPE_STR,
            'applicantionTime' => Hk_Service_Db::TYPE_INT,
            'appDownloadUrl' => Hk_Service_Db::TYPE_STR,
            'zmtcStatus' => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'deleted' => Hk_Service_Db::TYPE_INT,
        );
    }

    public function getTable()
    {
        return $this->_table;
    }

    public function getAllFields()
    {
        return $this->arrFieldsMap;
    }
}