<?php
/**
 * Created by PhpStorm.
 * User: zuoyebang
 * Date: 2018/6/15
 * Time: 15:18
 */
class Hkzb_Dao_ActivityCash_ActivityReturnCash extends Hk_Common_BaseDao{

    public function __construct()
    {
        $this->_dbName  = 'fudao/zyb_fudao';
        $this->_db      = null;
        $this->_logFile = Hkzb_Util_FuDao::DBLOG_FUDAO;

        $this->_table       = "tblActivityReturnCash";
        $this->arrFieldsMap = array(
            'id'                => 'id',
            'activityId'        => 'activity_id',
            'studentUid'        => 'student_uid',
            'keyId'             => 'key_id',
            'status'            => 'status',
            'amount'            => 'amount',
            'comment'           => 'comment',
            'createTime'        => 'create_time',
            'updateTime'        => 'update_time',
            'extData'           => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'            => Hk_Service_Db::TYPE_INT,
            'activityId'    => Hk_Service_Db::TYPE_INT,
            'studentUid'    => Hk_Service_Db::TYPE_INT,
            'keyId'         => Hk_Service_Db::TYPE_INT,
            'status'        => Hk_Service_Db::TYPE_INT,
            'amount'        => Hk_Service_Db::TYPE_STR,
            'comment'       => Hk_Service_Db::TYPE_STR,
            'createTime'    => Hk_Service_Db::TYPE_INT,
            'updateTime'    => Hk_Service_Db::TYPE_INT,
            'extData'       => Hk_Service_Db::TYPE_JSON,
        );
    }
}
