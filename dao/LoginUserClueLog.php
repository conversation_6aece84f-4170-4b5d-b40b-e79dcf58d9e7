<?php

class Qdlib_Dao_LoginUserClueLog extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Qdlib_Util_DB::DBLOG_QUDAO;

        $this->_table = "tblLoginUserClueLog";
        $this->arrFieldsMap = array(
            'id' => 'id',
            'uid' => 'uid',
            'clickId' => 'click_id',
            'orifrom' => 'orifrom',
            'actType' => 'act_type',
            'plat' => 'plat',
            'loginTime' => 'login_time',
            'createTime' => 'create_time',
            'callbackStatus' =>'callback_status',
            'callbackTime' =>'callback_time',
            'dataSource' => 'data_source',
            'offset' => 'offset',
            'extData' => 'ext_data',

        );

        $this->arrTypesMap = array(
            'id' => Hk_Service_Db::TYPE_INT,
            'uid' => Hk_Service_Db::TYPE_INT,
            'clickId' => Hk_Service_Db::TYPE_STR,
            'orifrom' => Hk_Service_Db::TYPE_STR,
            'actType' => Hk_Service_Db::TYPE_STR,
            'plat' => Hk_Service_Db::TYPE_STR,
            'loginTime' => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'callbackStatus' => Hk_Service_Db::TYPE_INT,
            'callbackTime' => Hk_Service_Db::TYPE_INT,
            'dataSource' => Hk_Service_Db::TYPE_STR,
            'offset' => Hk_Service_Db::TYPE_INT,
            'extData' => Hk_Service_Db::TYPE_JSON,


        );
    }

    public function reconnect() {
        if(empty($this->_db)) {
            $this->_db     = Qdlib_Util_DB::getQudaoDb(false);
        }
        Qdlib_Util_DB::reconnect($this->_db);
    }
}