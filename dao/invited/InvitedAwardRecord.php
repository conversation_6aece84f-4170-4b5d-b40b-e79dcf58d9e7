<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Date: 2018/3/7
 * Time: 16:31
 */

class Hkzb_Dao_Invited_InvitedAwardRecord extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = Hk_Service_Db::getDB($this->_dbName);
        $this->_table       = "tblInvitedAwardRecord";
        $this->arrFieldsMap = array(
            'uid'         => 'uid',
            'num'         => 'num',
            'status'      => 'status',
            'totalMoney'  => 'total_money',
            'inviteId'    => 'invite_id',
            'activityId'  => 'activity_id',
            'courseId'    => 'course_id',
            'courseName'  => 'course_name',
            'createTime'  => 'create_time',
            'updateTime'  => 'update_time',
            'extData'     => 'ext_data'
        );

        $this->arrTypesMap = array(
            'uid'        => Hk_Service_Db::TYPE_INT,
            'award'      => Hk_Service_Db::TYPE_INT,
            'status'     => Hk_Service_Db::TYPE_INT,
            'inviteCode' => Hk_Service_Db::TYPE_INT,
            'activityId' =>  Hk_Service_Db::TYPE_INT,
            'inviteId'   => Hk_Service_Db::TYPE_INT,
            'courseId'   => Hk_Service_Db::TYPE_INT,
            'courseName' => Hk_Service_Db::TYPE_STR,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'extData'    => Hk_Service_Db::TYPE_JSON,

        );
    }
}