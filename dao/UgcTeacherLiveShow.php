<?php
/**
 * @file   UgcTeacherLiveShow.php
 * <AUTHOR>
 * @date   2018-08-30
 * 直播问答 - 直播信息
 *
 **/
class Oplib_Dao_UgcTeacherLiveShow extends Hk_Common_BaseDao
{
	// 表字段信息
	private static $arrFields = [
		'id'             => 'id',
		'tid'            => 'tid',
		'courseId'       => 'course_id',
		'lessonId'       => 'lesson_id',
		'title'          => 'title',
		'playbackUrl'    => 'playback_url',
		'playbackStatus' => 'playback_status',
		'playCount'      => 'play_count',
		'playTime'       => 'play_time',
		'createTime'     => 'create_time',
		'updateTime'     => 'update_time',
	];

	public function __construct($sameConnect = true)
	{
		$this->_dbName  = 'revent/zb_operation';
		$this->_db      = null;

		if ($sameConnect == false) {
			$this->_db = Hk_Service_Db::getDB($this->_dbName, true);
		}

		$this->_table = "tblUgcTeacherLiveShow";

		$this->arrFieldsMap = self::$arrFields;

		$this->arrTypesMap = array(
			'id'             => Hk_Service_Db::TYPE_INT,
			'tid'            => Hk_Service_Db::TYPE_INT,
			'courseId'       => Hk_Service_Db::TYPE_INT,
			'lessonId'       => Hk_Service_Db::TYPE_INT,
			'title'          => Hk_Service_Db::TYPE_STR,
			'playbackUrl'    => Hk_Service_Db::TYPE_STR,
			'playbackStatus' => Hk_Service_Db::TYPE_INT,
			'playCount'      => Hk_Service_Db::TYPE_INT,
			'playTime'       => Hk_Service_Db::TYPE_INT,
			'createTime'     => Hk_Service_Db::TYPE_INT,
			'updateTime'     => Hk_Service_Db::TYPE_INT,
		);
	}

	/**
	 * 获取表名
	 * <AUTHOR>
	 * @DateTime 2018-09-04
	 * @return   string
	 */
	public function getTableName()
	{
		return $this->_table;
	}

	/**
	 * 获取表的字段信息
	 * <AUTHOR>
	 * @DateTime 2018-08-22
	 * @return   array
	 */
	public static function getFields()
	{
		return array_values(self::$arrFields);
	}
}