<?php

class Qdlib_Dao_Ad_QudaoRaise extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table = "tblQudaoOutRaise";

        $this->arrFieldsMap = array(
            'id'                    => 'id',
            'channel'               => 'channel',
            'account'               => 'account',
            'adId'                  => 'ad_id',
            'raiseStatus'           => 'raise_status',
            'raiseBudget'           => 'raise_budget',
            'estimateNum'           => 'estimate_num',
            'createTime'            => 'create_time',
            'updateTime'            => 'update_time',
            'startTime'             => 'start_time',
            'endTime'               => 'end_time',
            'cost'                  => 'cost',
            'showPv'                => 'show_pv',
            'clickPv'               => 'click_pv',
            'conversion'            => 'conversion',
            'ctr'                   => 'ctr',
            'cvr'                   => 'cvr',
            'reportStatus'          => 'report_status',
            'ext'                   => 'ext',
        );

        $this->arrTypesMap = array(
            'id'                    => Hk_Service_Db::TYPE_STR,
            'channel'               => Hk_Service_Db::TYPE_STR,
            'account'               => Hk_Service_Db::TYPE_STR,
            'adId'                  => Hk_Service_Db::TYPE_INT,
            'raiseStatus'           => Hk_Service_Db::TYPE_STR,
            'raiseBudget'           => Hk_Service_Db::TYPE_INT,
            'estimateNum'           => Hk_Service_Db::TYPE_INT,
            'createTime'            => Hk_Service_Db::TYPE_INT,
            'updateTime'            => Hk_Service_Db::TYPE_INT,
            'startTime'             => Hk_Service_Db::TYPE_INT,
            'endTime'               => Hk_Service_Db::TYPE_INT,
            'cost'                  => Hk_Service_Db::TYPE_INT,
            'showPv'                => Hk_Service_Db::TYPE_INT,
            'clickPv'               => Hk_Service_Db::TYPE_INT,
            'conversion'            => Hk_Service_Db::TYPE_INT,
            'ctr'                   => Hk_Service_Db::TYPE_INT,
            'cvr'                   => Hk_Service_Db::TYPE_INT,
            'reportStatus'          => Hk_Service_Db::TYPE_INT,
            'ext'                   => Hk_Service_Db::TYPE_JSON,
        );
     }

    public function getAllFields() {
        return array_keys($this->arrFieldsMap);
    }
    public function __get($name)
    {
        return $this->$name;
    }

    public function reconnect() {
        if(empty($this->_db)) {
            $this->_db     = Qdlib_Util_DB::getQudaoDb(false);
        }
        Qdlib_Util_DB::reconnect($this->_db);
    }
}