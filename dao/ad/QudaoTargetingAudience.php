<?php
/**
 * Created by Growth AutoCode.
 * User: <EMAIL>
 * Date: 2020/02/24
 * Time: 17:33
 */
class Qdlib_Dao_Ad_QudaoTargetingAudience extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table = "tblQudaoTargetingAudience";

        $this->arrFieldsMap = array(
            'id'          => 'id',
            'channel'     => 'channel',
            'flag'        => 'flag',
            'targetingId' => 'targeting_id',
            'audienceId'  => 'audience_id',
            'type'        => 'type',
            'createTime'  => 'create_time',

        );

        $this->arrTypesMap = array(
            'id'          => Hk_Service_Db::TYPE_INT,
            'channel'     => Hk_Service_Db::TYPE_STR,
            'flag'        => Hk_Service_Db::TYPE_INT,
            'targetingId' => Hk_Service_Db::TYPE_INT,
            'audienceId'  => Hk_Service_Db::TYPE_INT,
            'type'        => Hk_Service_Db::TYPE_INT,
            'createTime'  => Hk_Service_Db::TYPE_INT,

        );
    }

    public function reconnect() {
        if(empty($this->_db)) {
            $this->_db     = Qdlib_Util_DB::getQudaoDb(false);
        }
        Qdlib_Util_DB::reconnect($this->_db);
    }
}