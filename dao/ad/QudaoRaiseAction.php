<?php

class Qdlib_Dao_Ad_QudaoRaiseAction extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table = "tblQudaoRaiseAction";

        $this->arrFieldsMap = array(
            'id'                    => 'id',
            'channel'               => 'channel',
            'account'               => 'account',
            'adgroupId'             => 'adgroup_id',
            'adId'                  => 'ad_id',
            'raiseBudget'           => 'raise_budget',
            'actionType'            => 'action_type',
            'callbackStatus'        => 'callback_status',
            'callbackErrno'         => 'callback_errno',
            'callbackMsg'           => 'callback_msg',
            'createTime'            => 'create_time',
            'updateTime'            => 'update_time',
            'reportStatus'          => 'report_status',
            'creator'               => 'creator',
        );

        $this->arrTypesMap = array(
            'id'                    => Hk_Service_Db::TYPE_STR,
            'channel'               => Hk_Service_Db::TYPE_STR,
            'account'               => Hk_Service_Db::TYPE_STR,
            'adgroupId'             => Hk_Service_Db::TYPE_INT,
            'adId'                  => Hk_Service_Db::TYPE_INT,
            'raiseBudget'           => Hk_Service_Db::TYPE_INT,
            'actionType'            => Hk_Service_Db::TYPE_INT,
            'callbackStatus'        => Hk_Service_Db::TYPE_INT,
            'callbackErrno'         => Hk_Service_Db::TYPE_INT,
            'callbackMsg'           => Hk_Service_Db::TYPE_STR,
            'createTime'            => Hk_Service_Db::TYPE_INT,
            'updateTime'            => Hk_Service_Db::TYPE_INT,
            'reportStatus'          => Hk_Service_Db::TYPE_INT,
            'creator'               => Hk_Service_Db::TYPE_STR,
        );
     }

    public function getAllFields() {
        return array_keys($this->arrFieldsMap);
    }
    public function __get($name)
    {
        return $this->$name;
    }

    public function reconnect() {
        if(empty($this->_db)) {
            $this->_db     = Qdlib_Util_DB::getQudaoDb(false);
        }
        Qdlib_Util_DB::reconnect($this->_db);
    }
}