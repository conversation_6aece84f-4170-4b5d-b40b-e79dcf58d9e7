<?php
/**
 * Created by Growth AutoCode.
 * User: <EMAIL>
 * Date: 2020/02/18
 * Time: 11:18
 */
class Qdlib_Dao_Ad_QudaoJrttCreative extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table = "tblQudaoJrttCreative";

        $this->arrFieldsMap = array(
            'id'                   => 'id',
            'adId'                 => 'ad_id',
            'account'              => 'account',
            'channel'              => 'channel',
            'creativeTitle'        => 'creative_title',
            'status'               => 'status',
            'adcreativeStatus'     => 'adcreative_status',
            'imageMode'            => 'image_mode',
            'creativeMaterialMode' => 'creative_material_mode',
            'thirdCreateTime'      => 'third_create_time',
            'thirdUpdateTime'      => 'third_update_time',
            'createTime'           => 'create_time',
            'updateTime'           => 'update_time',
            'deleted'              => 'deleted',
            'systemStatus'         => 'system_status',

        );

        $this->arrTypesMap = array(
            'id'                   => Hk_Service_Db::TYPE_INT,
            'adId'                 => Hk_Service_Db::TYPE_INT,
            'account'              => Hk_Service_Db::TYPE_STR,
            'channel'              => Hk_Service_Db::TYPE_STR,
            'creativeTitle'        => Hk_Service_Db::TYPE_STR,
            'status'               => Hk_Service_Db::TYPE_STR,
            'adcreativeStatus'     => Hk_Service_Db::TYPE_STR,
            'imageMode'            => Hk_Service_Db::TYPE_STR,
            'creativeMaterialMode' => Hk_Service_Db::TYPE_STR,
            'thirdCreateTime'      => Hk_Service_Db::TYPE_INT,
            'thirdUpdateTime'      => Hk_Service_Db::TYPE_INT,
            'createTime'           => Hk_Service_Db::TYPE_INT,
            'updateTime'           => Hk_Service_Db::TYPE_INT,
            'deleted'              => Hk_Service_Db::TYPE_INT,
            'systemStatus'         => Hk_Service_Db::TYPE_INT,

        );
     }
}