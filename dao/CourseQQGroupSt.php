<?php

class Oplib_Dao_CourseQQGroupSt extends Hk_Common_BaseDao
{
	// 表字段信息
	private static $arrFields = [
		'id'                => 'id',
		'courseId'          => 'course_id',
		'skuId'             => 'sku_id',
		'courseType'        => 'course_type',
		'grade'             => 'grade',
		'subject'           => 'subject',
        'learnYear'         => 'learn_year',
        'learnSeason'       => 'learn_season',
        'assignNum'         => 'assign_num',
        'promotionText'     => 'promotion_text',
        'serviceText'       => 'service_text',
        'status'            => 'status',
        'sms'               => 'sms',
        'courseName'        => 'course_name',
        'teacherUids'       => 'teacher_uids',
		'createTime'        => 'create_time',
		'updatorUid'        => 'updator_uid',
		'updatorName'       => 'updator_name',
		'updateTime'        => 'update_time',
		'extData'           => 'ext_data',
    ];

	public function __construct()
	{
		$this->_dbName  = 'zyb_yk_actplat/zhibo_qqgroup';
		$this->_db      = null;
		$this->_table   = 'tblCourseQQGroupSt';
		$this->_logFile = Hkzb_Util_FuDao::DBLOG_FUDAO;

		$this->arrFieldsMap = self::$arrFields;

		$this->arrTypesMap = [
			'id'                => Hk_Service_Db::TYPE_INT,
			'courseId'          => Hk_Service_Db::TYPE_INT,
			'skuId'             => Hk_Service_Db::TYPE_INT,
			'courseType'        => Hk_Service_Db::TYPE_INT,
            'grade'             => Hk_Service_Db::TYPE_INT,
            'subject'           => Hk_Service_Db::TYPE_INT,
            'learnYear'         => Hk_Service_Db::TYPE_INT,
            'learnSeason'       => Hk_Service_Db::TYPE_INT,
            'assignNum'         => Hk_Service_Db::TYPE_INT,
            'promotionText'     => Hk_Service_Db::TYPE_STR,
            'serviceText'       => Hk_Service_Db::TYPE_JSON,
            'status'            => Hk_Service_Db::TYPE_INT,
            'sms'               => Hk_Service_Db::TYPE_JSON,
            'courseName'        => Hk_Service_Db::TYPE_STR,
            'teacherUids'       => Hk_Service_Db::TYPE_JSON,
            'createTime'        => Hk_Service_Db::TYPE_INT,
            'updatorUid'        => Hk_Service_Db::TYPE_INT,
            'updatorName'       => Hk_Service_Db::TYPE_STR,
            'updateTime'        => Hk_Service_Db::TYPE_INT,
            'extData'           => Hk_Service_Db::TYPE_JSON,
		];
	}
}