<?php
/**
 * Created by IntelliJ IDEA.
 * User: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * Date: 2020/9/17
 * Time: 4:12 PM
 */
class Qdlib_Dao_SitePubComponent extends Hk_Common_BaseDao
{
    public function __construct()
    {
        parent::__construct();

        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Qdlib_Util_DB::DBLOG_QUDAO;
        $this->_table = "tblSitePubComponent";

        $this->arrFieldsMap = array(
            'id'         => 'id',
            'cId'        => 'cid',
            'name'       => 'name',
            'chName'     => 'ch_name',
            'content'    => 'content',
            'deleted'    => 'deleted',
            'creator'    => 'creator',
            'operator'   => 'operator',
            'createTime' => 'create_time',
            'updateTime' => 'update_time'
        );

        $this->arrTypesMap = array(
            'id'         => Hk_Service_Db::TYPE_INT,
            'cId'        => Hk_Service_Db::TYPE_STR,
            'name'       => Hk_Service_Db::TYPE_STR,
            'chName'     => Hk_Service_Db::TYPE_STR,
            'content'    => Hk_Service_Db::TYPE_STR,
            'deleted'    => Hk_Service_Db::TYPE_INT,
            'creator'    => Hk_Service_Db::TYPE_STR,
            'operator'   => Hk_Service_Db::TYPE_STR,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT
        );
    }
}
