<?php
class Qdlib_Dao_Goal_Period extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table = "tblQudaoProjectGoalPeriod";

        $this->arrFieldsMap = array(
            'id'               => 'id',
            'goalId'           => 'goal_id',
            'projectLv1'       => 'project_lv1',
            'period'           => 'period',
            'faculty'          => 'faculty',
            'orgId'            => 'org_id',
            'learnSeason'      => 'learn_season',
            'learnYear'        => 'learn_year',
            'business'         => 'business',
            'startTime'        => 'start_time',
            'endTime'          => 'end_time',
            'createTime'       => 'create_time',
            'updateTime'       => 'update_time',
            'updator'          => 'updator',
            'deleted'          => 'deleted',
        );

        $this->arrTypesMap = array(
            'id'              => Hk_Service_Db::TYPE_INT,
            'projectLv1'      => Hk_Service_Db::TYPE_INT,
            'goalId'          => Hk_Service_Db::TYPE_INT,
            'period'          => Hk_Service_Db::TYPE_INT,
            'faculty'         => Hk_Service_Db::TYPE_INT,
            'learnSeason'     => Hk_Service_Db::TYPE_INT,
            'learnYear'       => Hk_Service_Db::TYPE_INT,
            'business'        => Hk_Service_Db::TYPE_STR,
            'orgId'           => Hk_Service_Db::TYPE_INT,
            'startTime'       => Hk_Service_Db::TYPE_INT,
            'endTime'         => Hk_Service_Db::TYPE_INT,
            'createTime'      => Hk_Service_Db::TYPE_INT,
            'updateTime'      => Hk_Service_Db::TYPE_INT,
            'deleted'         => Hk_Service_Db::TYPE_INT,
            'updator'        => Hk_Service_Db::TYPE_STR,

        );
    }

    public function getAllFields()
    {
        return array_keys($this->arrFieldsMap);
    }

    public function reconnect() {
        if (!Zb_Util_Helper::isCli()) {
            return true;
        }
        if(empty($this->_db)) {
            $this->_db     = Qdlib_Util_DB::getQudaoDb(false);
        }
        Qdlib_Util_DB::reconnect($this->_db);
    }
}