<?php
/**
 * Copyright (c) 2016 zuoyebang.com, Inc. All Rights Reserved
 * Created by PhpStorm.
 * @file:Lesson.php
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @date: 2016/7/1
 * @time: 17:11
 */

class Zhibo_Dao_Lesson extends Hk_Common_BaseDao
{
    public static $allFields = array(
        'lessonId',
        'lessonName',
        'courseId',
        'abstract',
        'startTime',
        'stopTime',
        'status',
        'createTime',
        'updateTime',
        'operatorUid',
        'operator',
        'extData',
    );

    public function __construct()
    {
        $this->_db = Hk_Service_Db::getDB(Zhibo_Static::DBCLUSTER_FUDAO, false, Zhibo_Static::DBLOG_FUDAO, Zhibo_Static::DBLOG_AUTOROTATE_FLAG);
        $this->_table = "tblLesson";

        $this->arrFieldsMap = array(
            'courseId' => 'course_id',
            'lessonId' => 'lesson_id',
            'lessonName' => 'lesson_name',
            'abstract' => 'abstract',
            'startTime' => 'start_time',
            'stopTime' => 'stop_time',
            'status' => 'status',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'operatorUid' => 'operator_uid',
            'operator' => 'operator',
            'extData' => 'ext_data',
        );

        $this->arrTypesMap = array(
            'lessonId' => Hk_Service_Db::TYPE_INT,
            'courseId' => Hk_Service_Db::TYPE_INT,
            'lessonName' => Hk_Service_Db::TYPE_STR,
            'abstract' => Hk_Service_Db::TYPE_STR,
            'startTime' => Hk_Service_Db::TYPE_INT,
            'stopTime' => Hk_Service_Db::TYPE_INT,
            'status' => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'operatorUid' => Hk_Service_Db::TYPE_INT,
            'operator' => Hk_Service_Db::TYPE_STR,
            'extData' => Hk_Service_Db::TYPE_JSON,
        );
    }
}