<?php
/***************************************************************************
 *
 * Copyright (c) 2015 zybang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file BatchCourse.php
 * <AUTHOR>
 * @date 2018/11/13 11:18:49
 * @brief 批量报名
 *
 **/
class Oplib_Dao_BatchCourse extends Hk_Common_BaseDao{
	public function __construct(){
		//$this->_dbName = 'revent/zb_operation';
		$this->_dbName = Oplib_Const_DbConfig::DB_YK_ZBUI;
		$this->_db = null;
		$this->_table = 'tblBatchCourse';
		$this->_logFile = Zb_Const_Db::DBLOG_ZB;
		$this->arrFieldsMap = array(
			'id'           => 'id',
			'skuId'        => 'sku_id',
			'fileName'     => 'file_name',
            'sourceSign'   => 'source_sign',
            'reason'       => 'reason',
			'num'          => 'num',
			'status'       => 'status',
			'createTime'   => 'create_time',
			'operatorUid'  => 'operator_uid',
			'operator'     => 'operator',
			'extData'      => 'ext_data',
		);

		$this->arrTypesMap = array(
			'id'           => Hk_Service_Db::TYPE_INT,
			'skuId'        => Hk_Service_Db::TYPE_INT,
			'fileName'     => Hk_Service_Db::TYPE_STR,
            'source_sign'  => Hk_Service_Db::TYPE_STR,
            'reason'       => Hk_Service_Db::TYPE_STR,
			'num'          => Hk_Service_Db::TYPE_INT,
			'status'       => Hk_Service_Db::TYPE_INT,
			'createTime'   => Hk_Service_Db::TYPE_INT,
			'operatorUid'  => Hk_Service_Db::TYPE_INT,
			'operator'     => Hk_Service_Db::TYPE_STR,
			'extData'      => Hk_Service_Db::TYPE_JSON,
		);
	}
}
