<?php
/**
 * Copyright (c) 2018 zuoyebang.com, Inc. All Rights Reserved
 * @file  : OperationTuanActSku.php
 * @author: <EMAIL>
 * @date  : 2018年10月9日 下午2:59:21
 * @desc  : 【即将废弃不要使用】 @date 20200605
 */
class Oplib_Dao_OperationTuanActSku extends Hk_Common_BaseDao
{
    public function __construct($sameConnect = true)
    {
        $this->_dbName  = 'zyb_yk_actplat/zhibo_pintuan';
        $this->_db      = null;
        $this->_logFile = Zb_Const_Db::DBLOG_ZB;
        if ($sameConnect == false) {
            $this->_db = Hk_Service_Db::getDB($this->_dbName, true);
        }
        $this->_table       = "tblOperationTuanActSku";
        $this->arrFieldsMap = array(
            'id'             => 'id',
            'posadId'        => 'posad_id',
            'tuanstId'       => 'tuanst_id',
            'skuId'          => 'sku_id',
            'isSearchPage'   => 'is_search_page',
            'weight'         => 'weight',
            'createTime'     => 'create_time',
            'updateTime'     => 'update_time',
            'updatorUid'     => 'updator_uid',
            'updatorName'    => 'updator_name',
            'stime'          => 'stime',
            'etime'          => 'etime',
        );

        $this->arrTypesMap = array(
            'id'             => Hk_Service_Db::TYPE_INT,
            'posadId'        => Hk_Service_Db::TYPE_INT,
            'tuanstId'       => Hk_Service_Db::TYPE_INT,
            'skuId'          => Hk_Service_Db::TYPE_INT,
            'isSearchPage'   => Hk_Service_Db::TYPE_INT,
            'weight'         => Hk_Service_Db::TYPE_INT,
            'createTime'     => Hk_Service_Db::TYPE_INT,
            'updateTime'     => Hk_Service_Db::TYPE_INT,
            'updatorUid'     => Hk_Service_Db::TYPE_INT,
            'updatorName'    => Hk_Service_Db::TYPE_STR,
            'status'         => Hk_Service_Db::TYPE_INT,
            'stime'          => Hk_Service_Db::TYPE_INT,
            'etime'          => Hk_Service_Db::TYPE_INT,
        );
    }
}