<?php
/**
 * @file LecturePointMap.php.
 * <AUTHOR>
 * @date: 2017/12/4
 */
class Zhibo_Dao_LecturePointMap extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = 'homework/homework';
        $this->_db = Hk_Service_Db::getDB($this->_dbName);
        $this->_table = "tblLecturePointMap";
        $this->arrFieldsMap = array(
            'id'             => 'id',
            'pointId'        => 'point_id',
            'basePointId'    => 'base_point_id',
            'source'         => 'source',
            'createTime'     => 'create_time',
            'extData'        => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'            => Hk_Service_Db::TYPE_INT,
            'pointId'       => Hk_Service_Db::TYPE_INT,
            'basePointId'   => Hk_Service_Db::TYPE_INT,
            'source'        => Hk_Service_Db::TYPE_INT,
            'createTime'    => Hk_Service_Db::TYPE_INT,
            'extData'       => Hk_Service_Db::TYPE_JSON,
        );
    }
}