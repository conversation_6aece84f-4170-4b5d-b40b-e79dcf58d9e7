<?php
/**
 * Created by IntelliJ IDEA.
 * User: <EMAIL>
 * Date: 2019/7/17
 * Time: 4:12 PM
 */
class Qdlib_Dao_SiteLog extends Hk_Common_BaseDao
{
    public function __construct()
    {
        parent::__construct();

        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Qdlib_Util_DB::DBLOG_QUDAO;
        $this->_table = "tblSiteLog";

        $this->arrFieldsMap = array(
            'id'         => 'id',
            'pageId'     => 'page_id',
            'actType'    => 'act_type',
            'actTime'    => 'act_time',
            'actUser'    => 'act_user',
            'actCode'    => 'act_code',
            'actMsg'     => 'act_msg',
            'during'     => 'during',
            'extData'    => 'ext_data',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'taskId'     => 'task_id',
        );

        $this->arrTypesMap = array(
            'id'         => Hk_Service_Db::TYPE_INT,
            'pageId'     => Hk_Service_Db::TYPE_INT,
            'actType'    => Hk_Service_Db::TYPE_STR,
            'actTime'    => Hk_Service_Db::TYPE_INT,
            'actUser'    => Hk_Service_Db::TYPE_STR,
            'extData'    => Hk_Service_Db::TYPE_STR,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'actCode'    => Hk_Service_Db::TYPE_INT,
            'actMsg'     => Hk_Service_Db::TYPE_STR,
            'during'     => Hk_Service_Db::TYPE_INT,
            'taskId'     => Hk_Service_Db::TYPE_INT,
        );
    }
}
