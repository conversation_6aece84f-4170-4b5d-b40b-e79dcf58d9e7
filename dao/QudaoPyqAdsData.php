<?php

class Qdlib_Dao_QudaoPyqAdsData extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = 'laxin_rds/zb_qudao';
        $this->_db = null;
        $this->_logFile = Hkzb_Util_FuDao::DBLOG_FUDAO;

        $this->_table = "tblQudaoAdsData";
        $this->arrFieldsMap = array(
            'id'                            => 'id',
            'channel'                       => 'channel',
            'account'                       => 'account',
            'thirdAdId'                     => 'third_ad_id',
            'thirdCampaignId'               => 'third_campaign_id',
            'beginTime'                     => 'begin_time',
            'endTime'                       => 'end_time',
            'positionId'                    => 'position_id',
            'status'                        => 'status',
            'bidAmount'                     => 'bid_amount',
            'thirdCreativeId'               => 'third_creative_id',
            'thirdCustomAudienceName'       => 'third_custom_audience_name',
            'thirdExcludedCustomAudienceName' => 'third_excluded_custom_audience_name',
            'pageType'                      => 'page_type',
            'pageUrl'                       => 'page_url',
            'orifrom'                       => 'orifrom',
            'pageSpec'                      => 'page_spec',
            'createTime'                    => 'create_time',
            'updateTime'                    => 'update_time',
            'extData'                       => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'                            => Hk_Service_Db::TYPE_INT,
            'channel'                       => Hk_Service_Db::TYPE_STR,
            'account'                       => Hk_Service_Db::TYPE_STR,
            'thirdAdId'                     => Hk_Service_Db::TYPE_INT,
            'thirdCampaignId'               => Hk_Service_Db::TYPE_INT,
            'beginTime'                     => Hk_Service_Db::TYPE_INT,
            'endTime'                       => Hk_Service_Db::TYPE_INT,
            'positionId'                    => Hk_Service_Db::TYPE_INT,
            'status'                        => Hk_Service_Db::TYPE_STR,
            'bidAmount'                     => Hk_Service_Db::TYPE_INT,
            'thirdCreativeId'               => Hk_Service_Db::TYPE_INT,
            'thirdCustomAudienceName'       => Hk_Service_Db::TYPE_STR,
            'thirdExcludedCustomAudienceName'      => Hk_Service_Db::TYPE_STR,
            'pageType'                      => Hk_Service_Db::TYPE_STR,
            'pageUrl'                       => Hk_Service_Db::TYPE_STR,
            'orifrom'                       => Hk_Service_Db::TYPE_STR,
            'pageSpec'                      => Hk_Service_Db::TYPE_STR,
            'createTime'                    => Hk_Service_Db::TYPE_INT,
            'updateTime'                    => Hk_Service_Db::TYPE_INT,
            'extData'                       => Hk_Service_Db::TYPE_JSON,
        );
    }

    public function getAllFields() {
        return array_keys($this->arrFieldsMap);
    }
}