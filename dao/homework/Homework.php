<?php


/**
 * @file    Homework.php
 * <AUTHOR>
 * @data    2017-09-06
 * @desc    题库
 *
 */
class Hkzb_Dao_Homework_Homework extends Hk_Common_BaseMultiDao {

    public function __construct() {
        $this->_dbName      = 'iknowtiku/homework';
        $this->_db          = null;
        $this->_table       = "tblHomework";
        $this->_tableName   = "tblHomework";
        $this->_partionKey  = "tid";
        $this->_partionType = self::TYPE_TABLE_PARTION_MUL;
        $this->_partionNum  = 10000000;
        $this->arrFieldsMap = array(
            'tid'           => 'tid',
            'question'      => 'question',
            'latexQuestion' => 'latexQuestion',
            'answer'        => 'answer',
            'latexAnswer'   => 'latexAnswer',
            'gradeId'       => 'gradeId',
            'courseId'      => 'courseId',
            'knowledge'     => 'knowledge',
            'difficut'      => 'difficut',
            'quality'       => 'quality',
            'source'        => 'source',
            'deleted'       => 'deleted',
            'createTime'    => 'createTime',
            'extContent'    => 'extContent',
            'ext'           => 'ext',
        );

        $this->arrTypesMap = array(
            'tid'           => Hk_Service_Db::TYPE_INT,
            'question'      => Hk_Service_Db::TYPE_JSON,
            'latexQuestion' => Hk_Service_Db::TYPE_STR,
            'answer'        => Hk_Service_Db::TYPE_STR,
            'latexAnswer'   => Hk_Service_Db::TYPE_STR,
            'gradeId'       => Hk_Service_Db::TYPE_INT,
            'courseId'      => Hk_Service_Db::TYPE_INT,
            'knowledge'     => Hk_Service_Db::TYPE_STR,
            'difficut'      => Hk_Service_Db::TYPE_INT,
            'quality'       => Hk_Service_Db::TYPE_INT,
            'source'        => Hk_Service_Db::TYPE_INT,
            'deleted'       => Hk_Service_Db::TYPE_INT,
            'createTime'    => Hk_Service_Db::TYPE_INT,
            'extContent'    => Hk_Service_Db::TYPE_JSON,
            'ext'           => Hk_Service_Db::TYPE_JSON,
        );
    }

}