<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * Created by PhpStorm.
 * File : TeacherSaysLikes.php
 * Author: <EMAIL>
 * Date: 2019/6/11
 * Time: 10:47
 * Desc:
 */
class Oplib_Dao_TeacherSay_TeacherSaysLikes extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName      = Oplib_Const_DbConfig::DB_YK_TSAY;
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblTeacherSaysLikes";
        $this->arrFieldsMap = array(
            'id'            => 'id',
            'msgId'         => 'msg_id',
            'pubUid'        => 'pub_uid',
            'pubType'       => 'pub_type',
            'type'          => 'type',
            'createTime'    => 'create_time',
            'updateTime'    => 'update_time',
        );

        $this->arrTypesMap = array(
            'id'            => Hk_Service_Db::TYPE_INT,
            'msgId'         => Hk_Service_Db::TYPE_INT,
            'pubUid'        => Hk_Service_Db::TYPE_INT,
            'pubType'       => Hk_Service_Db::TYPE_INT,
            'type'          => Hk_Service_Db::TYPE_INT,
            'createTime'    => Hk_Service_Db::TYPE_INT,
            'updateTime'    => Hk_Service_Db::TYPE_INT,
        );
    }
}
