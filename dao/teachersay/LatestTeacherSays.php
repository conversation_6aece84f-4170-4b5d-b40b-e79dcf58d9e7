<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Date: 2018/11/05
 * Time: 15:11
 */
class Oplib_Dao_TeacherSay_LatestTeacherSays extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName      = Oplib_Const_DbConfig::DB_YK_TSAY;
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblLatestTeacherSays";
        $this->arrFieldsMap = array(
            'id'            => 'id',
            'pubUid'        => 'pub_uid',
            'content'       => 'content',
            'contentType'   => 'content_type',
            'deleted'       => 'deleted',
            'createTime'    => 'create_time',
            'updateTime'    => 'update_time',
        );

        $this->arrTypesMap = array(
            'id'            => Hk_Service_Db::TYPE_INT,
            'pubUid'        => Hk_Service_Db::TYPE_INT,
            'content'       => Hk_Service_Db::TYPE_JSON,
            'contentType'   => Hk_Service_Db::TYPE_INT,
            'deleted'       => Hk_Service_Db::TYPE_INT,
            'createTime'    => Hk_Service_Db::TYPE_INT,
            'updateTime'    => Hk_Service_Db::TYPE_INT,
        );
    }
}
