<?php

class Qdlib_Dao_QudaoCourseOrder extends Hk_Common_BaseDao
{
    public function __construct()
    {
        parent::__construct();
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table = "tblQudaoCourseOrder";
        $this->arrFieldsMap = [
            'id' => 'id',
            'orifrom' => 'orifrom',
            'lastfrom' => 'lastfrom',
            'paymentChannel' => 'payment_channel',
            'tradeStatus' => 'trade_status',
            'orderType' => 'order_type',
            'userId' => 'user_id',
            'subTradeId' => 'sub_trade_id',
            'tradeId' => 'trade_id',
            'skuId' => 'sku_id',
            'courseId' => 'course_id',
            'tradeTime' => 'trade_time',
            'subTradeStatus' => 'sub_trade_status',
            'businessType' => 'business_type',
            'changeOrderType' => 'change_order_type',
            'refundFinishTime' => 'refund_finish_time',
            'changeFrom' => 'change_from',
            'changeTime' => 'change_time',
            'originalSubTradeId' => 'original_sub_trade_id',
            'subTradeCreateTime' => 'sub_trade_create_time',
            'subTradeUpdateTime' => 'sub_trade_update_time',
            'orderChannel' => 'order_channel',
            'entityType' => 'entity_type',
            'province' => 'province',
            'city' => 'city',
            'classType' => 'class_type',
            'mainGradeId' => 'main_grade_id',
            'mainSubjectId' => 'main_subject_id',
            'courseType' => 'course_type',
            'learnSeason' => 'learn_season',
            'courseStartTime' => 'course_start_time',
            'courseStopTime' => 'course_stop_time',
            'lessonStartTime' => 'lesson_start_time',
            'lessonStopTime' => 'lesson_stop_time',
            'agentId' => 'agent_id',
            'channel' => 'channel',
            'account' => 'account',
            'attrClassType' => 'attr_class_type',
            'attrLxWay' => 'attr_lx_way',
            'attrSubject' => 'attr_subject',
            'attrPageType' => 'attr_page_type',
            'attrPageName' => 'attr_page_name',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'phone' => 'phone',
            'splitTradeId' => 'split_trade_id',
            'splitSubTradeId' => 'split_sub_trade_id',
            'splitTime' => 'split_time',
            'payAmount' => 'pay_amount',
            'lastBusinessid'=> 'last_businessid',
        ];
        $this->arrTypesMap = [
            'id' => Hk_Service_Db::TYPE_INT,
            'orifrom' => Hk_Service_Db::TYPE_STR,
            'lastfrom' => Hk_Service_Db::TYPE_STR,
            'paymentChannel' => Hk_Service_Db::TYPE_INT,
            'tradeStatus' => Hk_Service_Db::TYPE_INT,
            'orderType' => Hk_Service_Db::TYPE_INT,
            'userId' => Hk_Service_Db::TYPE_INT,
            'subTradeId' => Hk_Service_Db::TYPE_INT,
            'tradeId' => Hk_Service_Db::TYPE_INT,
            'skuId' => Hk_Service_Db::TYPE_INT,
            'courseId' => Hk_Service_Db::TYPE_INT,
            'tradeTime' => Hk_Service_Db::TYPE_INT,
            'subTradeStatus' => Hk_Service_Db::TYPE_INT,
            'businessType' => Hk_Service_Db::TYPE_INT,
            'changeOrderType' => Hk_Service_Db::TYPE_INT,
            'refundFinishTime' => Hk_Service_Db::TYPE_INT,
            'changeFrom' => Hk_Service_Db::TYPE_INT,
            'changeTime' => Hk_Service_Db::TYPE_INT,
            'originalSubTradeId' => Hk_Service_Db::TYPE_INT,
            'subTradeCreateTime' => Hk_Service_Db::TYPE_INT,
            'subTradeUpdateTime' => Hk_Service_Db::TYPE_INT,
            'orderChannel' => Hk_Service_Db::TYPE_INT,
            'entityType' => Hk_Service_Db::TYPE_INT,
            'province' => Hk_Service_Db::TYPE_STR,
            'city' => Hk_Service_Db::TYPE_STR,
            'classType' => Hk_Service_Db::TYPE_INT,
            'mainGradeId' => Hk_Service_Db::TYPE_INT,
            'mainSubjectId' => Hk_Service_Db::TYPE_INT,
            'courseType' => Hk_Service_Db::TYPE_INT,
            'learnSeason' => Hk_Service_Db::TYPE_INT,
            'courseStartTime' => Hk_Service_Db::TYPE_INT,
            'courseStopTime' => Hk_Service_Db::TYPE_INT,
            'lessonStartTime' => Hk_Service_Db::TYPE_INT,
            'lessonStopTime' => Hk_Service_Db::TYPE_INT,
            'agentId' => Hk_Service_Db::TYPE_INT,
            'channel' => Hk_Service_Db::TYPE_STR,
            'account' => Hk_Service_Db::TYPE_STR,
            'attrClassType' => Hk_Service_Db::TYPE_INT,
            'attrLxWay' => Hk_Service_Db::TYPE_STR,
            'attrSubject' => Hk_Service_Db::TYPE_STR,
            'attrPageType' => Hk_Service_Db::TYPE_STR,
            'attrPageName' => Hk_Service_Db::TYPE_STR,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'phone' => Hk_Service_Db::TYPE_STR,
            'splitTradeId' => Hk_Service_Db::TYPE_INT,
            'splitSubTradeId' => Hk_Service_Db::TYPE_INT,
            'splitTime' => Hk_Service_Db::TYPE_INT,
            'payAmount' => Hk_Service_Db::TYPE_INT,
            'lastBusinessid' => Hk_Service_Db::TYPE_INT,
        ];
    }

    public function getAllFields() {
        return array_keys($this->arrFieldsMap);
    }

    public function __get($name) {
        return $this->$name;
    }

    public function reconnect() {
        if(empty($this->_db)) {
            $this->_db     = Qdlib_Util_DB::getQudaoDb(false);
        }
        Qdlib_Util_DB::reconnect($this->_db);
    }

}