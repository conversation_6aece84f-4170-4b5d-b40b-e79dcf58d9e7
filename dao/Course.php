<?php
/**
 * Copyright (c) 2016 zuoyebang.com, Inc. All Rights Reserved
 * @file:Course.php
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @date: 2016/7/4
 * @time: 19:33
 * @desc:
 */

class Zhibo_Dao_Course extends Hk_Common_BaseDao
{
    public static $allFields = array(
        'teacherUid',
        'teacherName',
        'courseId',
        'courseName',
        'grade',
        'subject',
        'type',
        'degree',
        'price',
        'registerStartTime',
        'registerStopTime',
        'studentCnt',
        'studentMaxCnt',
        'status',
        'createTime',
        'updateTime',
        'startTime',
        'operatorUid',
        'operator',
        'extData',
        '`inner`',
        'learnSeason',
        'onlineStart',
        'onlineStop',
        'pack',
        'isShow',
    );

    public function __construct()
    {
        $this->_db = Hk_Service_Db::getDB(Zhibo_Static::DBCLUSTER_FUDAO, false, Zhibo_Static::DBLOG_FUDAO, Zhibo_Static::DBLOG_AUTOROTATE_FLAG);
        $this->_table = "tblCourse";

        $this->arrFieldsMap = array(
            'teacherUid' => 'teacher_uid',
            'teacherName' => 'teacher_name',
            'courseId' => 'course_id',
            'courseName' => 'course_name',
            'grade' => 'grade',
            'subject' => 'subject',
            'type' => 'type',
            'degree' => 'degree',
            'price' => 'price',
            'startTime' => 'start_time',
            'registerStartTime' => 'register_start_time',
            'registerStopTime' => 'register_stop_time',
            'studentCnt' => 'student_cnt',
            'studentMaxCnt' => 'student_max_cnt',
            'status' => 'status',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'operatorUid' => 'operator_uid',
            'operator' => 'operator',
            'extData' => 'ext_data',
            'inner' => '`inner`',
            'learnSeason' => 'learn_season',
            'onlineStart' => 'online_start',
            'onlineStop' => 'online_stop',
            'pack' => 'pack',
            'isShow' => 'is_show',
        );

        $this->arrTypesMap = array(
            'teacherUid' => Hk_Service_Db::TYPE_STR,
            'teacherName' => Hk_Service_Db::TYPE_STR,
            'courseId' => Hk_Service_Db::TYPE_INT,
            'grade' => Hk_Service_Db::TYPE_INT,
            'subject' => Hk_Service_Db::TYPE_INT,
            'type' => Hk_Service_Db::TYPE_INT,
            'degree' => Hk_Service_Db::TYPE_INT,
            'price' => Hk_Service_Db::TYPE_INT,
            'startTime' => Hk_Service_Db::TYPE_INT,
            'registerStartTime' => Hk_Service_Db::TYPE_INT,
            'registerStopTime' => Hk_Service_Db::TYPE_INT,
            'studentCnt' => Hk_Service_Db::TYPE_INT,
            'studentMaxCnt' => Hk_Service_Db::TYPE_INT,
            'status' => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'operatorUid' => Hk_Service_Db::TYPE_INT,
            'operator' => Hk_Service_Db::TYPE_STR,
            'extData' => Hk_Service_Db::TYPE_JSON,
            'inner' => Hk_Service_Db::TYPE_INT,
            'learnSeason' => Hk_Service_Db::TYPE_STR,
            'onlineStart' => Hk_Service_Db::TYPE_INT,
            'onlineStop' => Hk_Service_Db::TYPE_INT,
            'pack' => Hk_Service_Db::TYPE_INT,
            'isShow' => Hk_Service_Db::TYPE_INT,
        );
    }
}