<?php

/**
 * @file: vtTask.php
 * <AUTHOR>
 * @Datetime: 2018/5/15 14:29
 * @brief : description
 */
class Hkzb_Dao_Vt_VideoInfo extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = 'fudao/zyb_fudao';
        $this->_db = Hk_Service_Db::getDB($this->_dbName);
        $this->_table = "tblVideoInfo";
        $this->arrFieldsMap = array(
            'id' => 'id',
            'videoId' => 'video_id',
            'videoHash' => 'video_hash',
            'extData' => 'ext_data',
            'createTime' => 'create_time',
        );

        $this->arrTypesMap = array(
            'id' => Hk_Service_Db::TYPE_INT,
            'videoId' => Hk_Service_Db::TYPE_STR,
            'videoHash' => Hk_Service_Db::TYPE_STR,
            'extData' => Hk_Service_Db::TYPE_STR,
            'createTime' => Hk_Service_Db::TYPE_INT,
        );
    }
}