<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file Interface.php
 * <AUTHOR>
 * @date 2017/11/14 15:26:46
 * @brief 粗粒度接口
 *
 **/
class Zb_Advanced_Interface {
    /**
     * 获取课程kv数据（单个）
     *
     * @param  int    $courseId  课程id
     * @param  bool   $bCache    是否启用cache（默认开启）
     * @return false|array
     */
    public function getCourseInfo($courseId, $bCache = true) {

        /*************************************
        // !!!!!!!!!!!!!!!!!!!!!WARNING!!!!!!!!!!!!!!!!!!!!!!
        // 该接口已迁至 RPC 接口
        // 1. 1月2日，15:30分停止服务器
        // 2. 1月4日，删除文件
        // 有问题请联系  胡文华
        // !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
         *************************************/

        $objAdvancedCourse = new Zb_Advanced_Course();
        $courseInfoList = $objAdvancedCourse->getCourseInfoBatch(array($courseId), $bCache);
        if(false === $courseInfoList) {
            return false;
        }

        $courseInfo = $courseInfoList[$courseId];
        return $courseInfo;
    }

    /**
     * 获取课程kv数据（批量）
     *
     * @param  array  $courseIds 课程id集合
     * @param  bool   $bCache    是否启用cache（默认开启）
     * @return false|array
     */
    public function getCourseInfoBatch($courseIds, $bCache = true) {
        /*************************************
        // !!!!!!!!!!!!!!!!!!!!!WARNING!!!!!!!!!!!!!!!!!!!!!!
        // 该类下所有接口已迁至 RPC 接口
        // 1. 1月2日，15:30分停止服务器
        // 2. 1月4日，删除文件
        // 有问题请联系  胡文华
        // !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
         *************************************/

        $objAdvancedCourse = new Zb_Advanced_Course();
        return $objAdvancedCourse->getCourseInfoBatch($courseIds, $bCache);
    }

    
    /**
     * 获取章节kv数据（单个）
     *
     * @param  int   $lessonId  章节id
     * @param  bool  $bCache    是否启用cache（默认开启）
     * @return false|array
     */
    public function getLessonInfo($lessonId, $bCache = true) {
        /*************************************
        // !!!!!!!!!!!!!!!!!!!!!WARNING!!!!!!!!!!!!!!!!!!!!!!
        // 该类下所有接口已迁至 RPC 接口
        // 1. 1月2日，15:30分停止服务器
        // 2. 1月4日，删除文件
        // 有问题请联系  胡文华
        // !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
         *************************************/

        $objAdvancedLesson = new Zb_Advanced_Lesson();
        $lessonInfoList = $objAdvancedLesson->getLessonInfoBatch(array($lessonId), $bCache);
        if(false === $lessonInfoList) {
            return false;
        }

        $lessonInfo = $lessonInfoList[$lessonId];
        return $lessonInfo;
    }
    
    /**
     * 获取章节kv数据（批量）
     *
     * @param  array  $lessonIds  章节id集合
     * @param  bool   $bCache     是否启用cache（默认开启）
     * @return false|array
     */
    public function getLessonInfoBatch($lessonIds, $bCache = true) {
        /*************************************
        // !!!!!!!!!!!!!!!!!!!!!WARNING!!!!!!!!!!!!!!!!!!!!!!
        // 该类下所有接口已迁至 RPC 接口
        // 1. 1月2日，15:30分停止服务器
        // 2. 1月4日，删除文件
        // 有问题请联系  胡文华
        // !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
         *************************************/

        $objAdvancedLesson = new Zb_Advanced_Lesson();
        return $objAdvancedLesson->getLessonInfoBatch($lessonIds, $bCache);
    }

    /**
     * @param $skuIds
     * @param bool $bCache
     * @return array
     */
    public function getAdapterSkuInfoBatch($skuIds, $bCache = true){
        $objAdvancedSku = new Zb_Advanced_Sku();
        $skuInfoList = $objAdvancedSku->getSkuInfoBatch($skuIds, $bCache);
        //这里按这次的需求整理
        $skuList = [];
        foreach($skuInfoList as $skuId => $skuInfo){
            $newSkuInfo = [];
            $newSkuInfo['productId'] = $skuId;
            $newSkuInfo['skuType'] = $skuInfo['skuType'];
            foreach($skuInfo['courseIds'] as $courseId) {
                $newSkuInfo['courseIdList'][] = $courseId;
            }
            $newSkuInfo['courseInfoList'] = $skuInfo['courseInfoList'];
            $newSkuInfo['productName'] = $skuInfo['skuName'];
            $newSkuInfo['registerStartTime'] = $skuInfo['startTime'];
            $newSkuInfo['registerStopTime'] = $skuInfo['stopTime'];
            $newSkuInfo['price']    = $skuInfo['skuPrice'];
            $newSkuInfo['inner'] = intval($skuInfo['isInner']);
            //组合从sku取数据
            $newSkuInfo['content'] = $skuInfo['skuDescription']['content'];
            $newSkuInfo['extData'] = $this->matchOldExt($skuInfo['skuResource'], $skuInfo['skuDescription']['tags']);
            $newSkuInfo['extData']['formatShow'] = 1;
            $newSkuInfo['status'] = 2;
            $newSkuInfo['hasBK'] = intval($skuInfo['hasBK']);
            if($skuInfo['status'] == 1 || $skuInfo['status'] == 2){
                $newSkuInfo['status'] = 3;
            }
            $newSkuInfo['isShow'] = 0;
            if($skuInfo['status'] == 1 || $skuInfo['status'] == 3) {
                $newSkuInfo['isShow'] = 1;
            }
            $newSkuInfo['saleCnt'] = intval($skuInfo['saleCnt']);
            $newSkuInfo['stockCnt'] = intval($skuInfo['stockCnt']);
            $newSkuInfo['leftCnt'] = intval($skuInfo['leftCnt']);
            $newSkuInfo = array_merge($newSkuInfo, $this->mergeCourseInfo($skuInfo['courseInfoList']));
            if($newSkuInfo['productType'] == 2 && strstr($newSkuInfo['learnSeason'],'暑')){
                $newSkuInfo['hasBK'] = 1;
            }
            $skuList[$skuId] = $newSkuInfo;
        }
        return $skuList;
    }

    /**
     * 查询SKU
     *
     * @param array     $searchConds  具体支持参数参考：http://wiki.afpai.com/pages/viewpage.action?pageId=4971343
     * @param int       $showDetail
     * @param int       $offset
     * @param int       $limit
     * @param string    $sort
     * @param int       $sortType
     * @param bool      $bCache
     * @return bool | array
     */
    public function searchSKU($searchConds, $showDetail=0, $offset=0, $limit=10, $sort='first_lesson_time', $sortType=2, $bCache=true) {
        $showDetail = intval($showDetail);
        $offset     = intval($offset);
        $limit      = intval($limit);
        $sort       = strval($sort);
        $sortType   = intval($sortType);
        $arrConds = array();
        if (isset($searchConds['gradeIds']) && is_array($searchConds['gradeIds']) && !empty($searchConds['gradeIds'])) {
            $arrConds['grade'] = array_map('intval', $searchConds['gradeIds']);
        }
        if (isset($searchConds['subjectIds']) && is_array($searchConds['subjectIds']) && !empty($searchConds['subjectIds'])) {
            $arrConds['subject'] = array_map('intval', $searchConds['subjectIds']);
        }
        if (isset($searchConds['teacherUids']) && is_array($searchConds['teacherUids']) && !empty($searchConds['teacherUids'])) {
            $arrConds['teacherUid'] = array_map('intval', $searchConds['teacherUids']);
        }
        if (isset($searchConds['startTime']) && $searchConds['startTime'] > 0) {
            $arrConds['startTime'] = intval($searchConds['startTime']);
        }
        if (isset($searchConds['stopTime']) && $searchConds['stopTime'] > 0) {
            $arrConds['stopTime'] = intval($searchConds['stopTime']);
        }
        if (isset($searchConds['isInner']) && array_key_exists($searchConds['isInner'], Zb_Core_Dao_Dak_SKUList::$innerMap)) {
            $arrConds['isInner'] = intval($searchConds['isInner']);
        }
        if (isset($searchConds['courseType']) && is_array($searchConds['courseType']) && !empty($searchConds['courseType'])) {
            $arrConds['courseType'] = array_map('intval', $searchConds['courseType']);
        }
        if (isset($searchConds['skuType']) && is_array($searchConds['skuType']) && !empty($searchConds['skuType'])) {
            $arrConds['skuType'] = array_map('intval', $searchConds['skuType']);
        }
        if (isset($searchConds['learnSeason']) && is_array($searchConds['learnSeason']) && !empty($searchConds['learnSeason'])) {
            $arrConds['learnSeason'] = array_map('intval', $searchConds['learnSeason']);
        }
        if (isset($searchConds['status']) && array_key_exists($searchConds['status'], Zb_Core_Dao_Dak_SKUList::$statusMap)) {
            $arrConds['status'] = intval($searchConds['status']);
        } else {
            $arrConds['status'] = Zb_Core_Dao_Dak_SKUList::STATUS_ONLINE;
        }
        if (isset($searchConds['hasRemain']) && $searchConds['hasRemain'] >= 0) {
            $arrConds['remainCnt'] = array(0,'>');
        }
        if (isset($arrConds['courseType']) && isset($arrConds['grade']) && isset($arrConds['subject'])
            && isset($arrConds['status']) && $showDetail===0 && $limit == -1)
        {
            $limit = 1000000;
        }else if ($limit > 100){
            $limit = 100;
        }
        if(isset($searchConds['price'])){
            $priceList = array_map('intval', $searchConds['price']);
            $arrConds['price'] = array(
                'min' => min($priceList),
                'max' => max($priceList),
            );
        }
        $playTypeRule = [
            Zb_Core_Dao_Dak_SKU::PLAY_TYPE_ZB,
            Zb_Core_Dao_Dak_SKU::PLAY_TYPE_DEMO,
        ];
        if (isset($searchConds['playType']) && in_array($searchConds['playType'],$playTypeRule))
        {
            $arrConds['playType'] = (int)$searchConds['playType'];
        }
        //获取sku信息
        $objSkuInterface = new Zb_Core_Ds_Dak_Interface();
        $arrSkuList = $objSkuInterface->searchSKU($arrConds, $offset, $limit, $sort, $sortType, $bCache);
        if (empty($arrSkuList)) {
            return array();
        }

        if ($showDetail === 1) {
            $skuIds = array();
            foreach($arrSkuList as $sku) {
                $skuIds[] = $sku['skuId'];
            }

            $skuIds = array_unique($skuIds);
            $objSku = new Zb_Core_Ds_Dak_SKU();

            $skuInfoList = $objSku->getSkuInfoList($skuIds, $bCache);
            foreach ($arrSkuList as &$sku) {
                $sku['detail'] = $skuInfoList[$sku['skuId']];
            }
        }

        return $arrSkuList;
    }


    /**
     * 查询SKU数量
     *
     * @param array     $searchConds  具体支持参数参考：http://wiki.afpai.com/pages/viewpage.action?pageId=4971343
     * @param bool      $bCache
     * @return bool | array
     */
    public function searchSKUCount($searchConds, $bCache=true) {
        $arrConds = array();
        if (isset($searchConds['gradeIds']) && is_array($searchConds['gradeIds']) && !empty($searchConds['gradeIds'])) {
            $arrConds['grade'] = array_map('intval', $searchConds['gradeIds']);
        }
        if (isset($searchConds['subjectIds']) && is_array($searchConds['subjectIds']) && !empty($searchConds['subjectIds'])) {
            $arrConds['subject'] = array_map('intval', $searchConds['subjectIds']);
        }
        if (isset($searchConds['teacherUids']) && is_array($searchConds['teacherUids']) && !empty($searchConds['teacherUids'])) {
            $arrConds['teacherUid'] = array_map('intval', $searchConds['teacherUids']);
        }
        if (isset($searchConds['startTime']) && $searchConds['startTime'] > 0) {
            $arrConds['startTime'] = intval($searchConds['startTime']);
        }
        if (isset($searchConds['stopTime']) && $searchConds['stopTime'] > 0) {
            $arrConds['stopTime'] = intval($searchConds['stopTime']);
        }
        if (isset($searchConds['isInner']) && array_key_exists($searchConds['isInner'], Zb_Core_Dao_Dak_SKUList::$innerMap)) {
            $arrConds['isInner'] = intval($searchConds['isInner']);
        }
        if (isset($searchConds['courseType']) && is_array($searchConds['courseType']) && !empty($searchConds['courseType'])) {
            $arrConds['courseType'] = array_map('intval', $searchConds['courseType']);
        }
        if (isset($searchConds['skuType']) && is_array($searchConds['skuType']) && !empty($searchConds['skuType'])) {
            $arrConds['skuType'] = array_map('intval', $searchConds['skuType']);
        }
        if (isset($searchConds['learnSeason']) && is_array($searchConds['learnSeason']) && !empty($searchConds['learnSeason'])) {
            $arrConds['learnSeason'] = array_map('intval', $searchConds['learnSeason']);
        }
        if (isset($searchConds['status']) && array_key_exists($searchConds['status'], Zb_Core_Dao_Dak_SKUList::$statusMap)) {
            $arrConds['status'] = intval($searchConds['status']);
        } else {
            $arrConds['status'] = Zb_Core_Dao_Dak_SKUList::STATUS_ONLINE;
        }
        if (isset($searchConds['hasRemain']) && $searchConds['hasRemain'] >= 0) {
            $arrConds['remainCnt'] = array(0,'>');
        }
                
        $playTypeRule = [
            Zb_Core_Dao_Dak_SKU::PLAY_TYPE_ZB,
            Zb_Core_Dao_Dak_SKU::PLAY_TYPE_DEMO,
        ];
        if (isset($searchConds['playType']) && in_array($searchConds['playType'],$playTypeRule))
        {
            $arrConds['playType'] = (int)$searchConds['playType'];
        }

        //获取sku信息
        $objSkuInterface = new Zb_Core_Ds_Dak_Interface();
        $skuCount = $objSkuInterface->searchSKUCount($arrConds, $bCache);
        if (empty($skuCount)) {
            return false;
        }

        return $skuCount;
    }


    private function mergeCourseInfo($courseInfoList){
        //按时间排序
        $courseInfoList = $this->arraySort($courseInfoList, 'startTime', 'asc');
        $onlineStart = 0;
        $onlineStop = 0;
        $learnSeason = '';
        $grade = [];
        $subject = [];
        $learnSeason = [];
        $productType = 0;
        foreach($courseInfoList as $courseInfo) {
            if($onlineStart  == 0 || $courseInfo['startTime'] < $onlineStart){
                $onlineStart = $courseInfo['startTime'];
            }
            if($onlineStop == 0 || $courseInfo['stopTime'] > $onlineStop) {
                $onlineStop = $courseInfo['stopTime'];
            }
            $oldCourseGrade = $courseInfo['grades'] ? $courseInfo['grades'] : [];

            $courseGrade = [];
            foreach($oldCourseGrade as $gradeId) {
                if(in_array($gradeId, [1,20,30])){
                    continue;
                }
                $courseGrade[] = $gradeId;
            }
            $courseSubject = $courseInfo['subjects'] ? $courseInfo['subjects'] : [];
            $grade = array_merge($grade, $courseGrade);
            $subject = array_merge($subject, $courseSubject);
            $learnSeason[] = $courseInfo['learnSeason'];
            if($courseInfo['courseType'] != 0) {
                $productType = $courseInfo['courseType'];
            }
        }
        $grade = array_unique($grade);
        $subject = array_unique($subject);
        $learnSeason = array_unique($learnSeason);

        $learnSeason = current($learnSeason);

        $learnSeason = Zb_Const_LearnSeason::$learnSeasonMap[$learnSeason];
        return array('grade' => $grade, 'subject' => $subject, 'learnSeason' => $learnSeason, 'onlineStart' => $onlineStart, 'onlineStop' => $onlineStop, 'productType' => $productType);
    }
     private function arraySort($list, $orderType = '', $sort = '')
    {
        $sortConf = array(
            'asc' => SORT_ASC,
            'desc' => SORT_DESC
        );
        if (empty($list)) {
            return array();
        }
        $order = array();
        foreach ($list as $info) {
            $order[]   = $info[$orderType];
        }
        $sort = $sortConf[$sort];
        array_multisort($order, $sort, $list);

        return $list;
    }
    private function matchOldExt($skuResource, $skuTags) {
        //处理资源 tags专成老数据格式
        $ext = [];
        $ext = $this->matchResource($ext, $skuResource);
        $ext = $this->matchTags($ext, $skuTags);
        return $ext;
    }
    private function matchResource($ext = [], $skuResource){
        $videoPid = array();
        if(isset($skuResource['detailVideo'])) {
            $videoPid['val'] = $skuResource['detailVideo'][0]['res'];
            $videoPid['name'] = $skuResource['detailVideo'][0]['desc'];
            $videoPid['videoLength'] = $skuResource['detailVideo'][0]['len'];
        }
        $ext['videoPid'] = $videoPid;
        $ext['videoLength'] = $videoPid['videoLength'];
        //videoimage
        $videoImg1 = array();
        if(isset($skuResource['detailVideoPic'])) {
            $videoImg1['val'] = $skuResource['detailVideoPic'][0]['res'];
            $videoImg1['name'] = $skuResource['detailVideoPic'][0]['desc'];
        }
        $ext['videoImg1'] = $videoImg1;
        //describe
        $describe = [];
        if(isset($skuResource['detailImgApp'])) {
            foreach($skuResource['detailImgApp'] as $detailImgApp) {
                $describe[] = array(
                    'val' => $detailImgApp['res'],
                    'name' => $detailImgApp['desc'],
                );
            }
        }
        $ext['describe'] = $describe;
        //describemore
        $describeMore = [];
        if(isset($skuResource['coverImgApp'])) {
            foreach($skuResource['coverImgApp'] as $coverImgApp) {
                $describeMore[] = array(
                    'val' => $coverImgApp['res'],
                    'name' => $coverImgApp['desc'],
                );
            }
        }
        $ext['describeMore'] = $describeMore;
        return $ext;
    }
    private function matchTags($ext = [], $skuTags){
        $tagNameConf = array(
            'sale'   => 'discountTag',
            'feature'=> 'feature',
            'basic'  => 'tagName',
            'module' => 'courseLessonType',
        );
        if(empty($skuTags)){
            return $ext;
        }
        foreach($skuTags as $tagName => $tagValue){
            //$ext[$tagNameConf[$tagName]] = $tagValue;
            switch($tagName) {
                case 'sale':
                    $ext[$tagNameConf[$tagName]] = $tagValue;
                    break;
                case 'feature':
                    foreach($tagValue as $valInfo) {
                        $ext[$tagNameConf[$tagName]][] = $valInfo['title'];
                    }
                    break;
                case 'basic':
                case 'module':
                    $ext[$tagNameConf[$tagName]] = $tagValue[0]['title'];
                    break;
                default:
                    break;
            }
        }
        return $ext;
    }

    /**************goods重构项目****************/
    //获取
    public function getSkuInfoBatch($skuIds, $bCache = true){
        $objAdvancedSku = new Zb_Advanced_Sku();
        $skuInfoList = $objAdvancedSku->getSkuInfoBatch($skuIds, $bCache);
        return $skuInfoList;
    }


}
